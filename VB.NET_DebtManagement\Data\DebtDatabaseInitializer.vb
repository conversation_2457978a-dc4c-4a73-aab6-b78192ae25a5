Imports System.Data.Entity

''' <summary>
''' مُهيئ قاعدة البيانات - Database Initializer
''' </summary>
Public Class DebtDatabaseInitializer
    Inherits CreateDatabaseIfNotExists(Of DebtContext)
    
    ''' <summary>
    ''' إدراج البيانات الأولية
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Protected Overrides Sub Seed(context As DebtContext)
        ' إدراج العملات الأساسية
        SeedCurrencies(context)
        
        ' إدراج المستخدم الافتراضي
        SeedUsers(context)
        
        ' إدراج الصناديق الافتراضية
        SeedCashBoxes(context)
        
        ' إدراج إعدادات النظام
        SeedSystemSettings(context)
        
        ' إدراج بيانات تجريبية (اختياري)
        If System.Configuration.ConfigurationManager.AppSettings("SeedTestData") = "true" Then
            SeedTestData(context)
        End If
        
        MyBase.Seed(context)
    End Sub
    
    ''' <summary>
    ''' إدراج العملات الأساسية
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Private Sub SeedCurrencies(context As DebtContext)
        Dim currencies As New List(Of Currency) From {
            New Currency With {
                .Code = "IQD",
                .Name = "دينار عراقي",
                .Symbol = "د.ع",
                .ExchangeRate = 1.0D,
                .IsDefault = True,
                .IsActive = True,
                .DecimalPlaces = 0,
                .SymbolPosition = "After",
                .ThousandsSeparator = ",",
                .DecimalSeparator = "."
            },
            New Currency With {
                .Code = "USD",
                .Name = "دولار أمريكي",
                .Symbol = "$",
                .ExchangeRate = 1320.0D,
                .IsDefault = False,
                .IsActive = True,
                .DecimalPlaces = 2,
                .SymbolPosition = "Before",
                .ThousandsSeparator = ",",
                .DecimalSeparator = "."
            }
        }
        
        For Each currency In currencies
            context.Currencies.Add(currency)
        Next
        
        context.SaveChanges()
    End Sub
    
    ''' <summary>
    ''' إدراج المستخدمين الافتراضيين
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Private Sub SeedUsers(context As DebtContext)
        Dim users As New List(Of User) From {
            New User With {
                .Username = "admin",
                .PasswordHash = User.HashPassword("admin123"),
                .FullName = "مدير النظام",
                .Email = "<EMAIL>",
                .Phone = "",
                .Role = "Admin",
                .IsActive = True
            },
            New User With {
                .Username = "manager",
                .PasswordHash = User.HashPassword("manager123"),
                .FullName = "مدير العمليات",
                .Email = "<EMAIL>",
                .Phone = "",
                .Role = "Manager",
                .IsActive = True
            },
            New User With {
                .Username = "accountant",
                .PasswordHash = User.HashPassword("accountant123"),
                .FullName = "المحاسب",
                .Email = "<EMAIL>",
                .Phone = "",
                .Role = "Accountant",
                .IsActive = True
            }
        }
        
        For Each user In users
            context.Users.Add(user)
        Next
        
        context.SaveChanges()
    End Sub
    
    ''' <summary>
    ''' إدراج الصناديق الافتراضية
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Private Sub SeedCashBoxes(context As DebtContext)
        Dim iqd = context.Currencies.FirstOrDefault(Function(c) c.Code = "IQD")
        Dim usd = context.Currencies.FirstOrDefault(Function(c) c.Code = "USD")
        Dim admin = context.Users.FirstOrDefault(Function(u) u.Username = "admin")
        
        If iqd IsNot Nothing AndAlso usd IsNot Nothing AndAlso admin IsNot Nothing Then
            Dim cashBoxes As New List(Of CashBox) From {
                New CashBox With {
                    .Name = "الصندوق الرئيسي - دينار",
                    .Description = "الصندوق الرئيسي للعملة العراقية",
                    .CurrencyId = iqd.Id,
                    .InitialBalance = 0,
                    .CurrentBalance = 0,
                    .MinimumBalance = 0,
                    .CashBoxType = "Cash",
                    .IsActive = True,
                    .CreatedBy = admin.Id
                },
                New CashBox With {
                    .Name = "الصندوق الرئيسي - دولار",
                    .Description = "الصندوق الرئيسي للدولار الأمريكي",
                    .CurrencyId = usd.Id,
                    .InitialBalance = 0,
                    .CurrentBalance = 0,
                    .MinimumBalance = 0,
                    .CashBoxType = "Cash",
                    .IsActive = True,
                    .CreatedBy = admin.Id
                },
                New CashBox With {
                    .Name = "حساب البنك - دينار",
                    .Description = "الحساب البنكي بالدينار العراقي",
                    .CurrencyId = iqd.Id,
                    .InitialBalance = 0,
                    .CurrentBalance = 0,
                    .MinimumBalance = 100000,
                    .CashBoxType = "Bank",
                    .BankName = "البنك التجاري العراقي",
                    .AccountNumber = "*********",
                    .IsActive = True,
                    .CreatedBy = admin.Id
                }
            }
            
            For Each cashBox In cashBoxes
                context.CashBoxes.Add(cashBox)
            Next
            
            context.SaveChanges()
        End If
    End Sub
    
    ''' <summary>
    ''' إدراج إعدادات النظام
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Private Sub SeedSystemSettings(context As DebtContext)
        Dim admin = context.Users.FirstOrDefault(Function(u) u.Username = "admin")
        
        Dim settings As New List(Of SystemSetting) From {
            ' إعدادات الشركة
            New SystemSetting With {
                .SettingKey = "CompanyName",
                .SettingValue = "شركة إدارة الديون",
                .Description = "اسم الشركة",
                .DataType = "String",
                .Category = "Company",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 1,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "CompanyPhone",
                .SettingValue = "",
                .Description = "رقم هاتف الشركة",
                .DataType = "String",
                .Category = "Company",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 2,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "CompanyEmail",
                .SettingValue = "",
                .Description = "البريد الإلكتروني للشركة",
                .DataType = "String",
                .Category = "Company",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 3,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "CompanyAddress",
                .SettingValue = "",
                .Description = "عنوان الشركة",
                .DataType = "String",
                .Category = "Company",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 4,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "CompanyLogo",
                .SettingValue = "",
                .Description = "شعار الشركة",
                .DataType = "String",
                .Category = "Company",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 5,
                .UpdatedBy = admin?.Id
            },
            ' إعدادات العملة
            New SystemSetting With {
                .SettingKey = "DefaultCurrency",
                .SettingValue = "IQD",
                .Description = "العملة الافتراضية",
                .DataType = "String",
                .Category = "Currency",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 10,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "ExchangeRateUSDToIQD",
                .SettingValue = "1320",
                .Description = "سعر صرف الدولار مقابل الدينار",
                .DataType = "Decimal",
                .Category = "Currency",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 11,
                .UpdatedBy = admin?.Id
            },
            ' إعدادات النسخ الاحتياطي
            New SystemSetting With {
                .SettingKey = "BackupFrequency",
                .SettingValue = "Daily",
                .Description = "تكرار النسخ الاحتياطي",
                .DataType = "String",
                .Category = "Backup",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 20,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "BackupPath",
                .SettingValue = "C:\DebtManagement\Backups",
                .Description = "مسار النسخ الاحتياطي",
                .DataType = "String",
                .Category = "Backup",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 21,
                .UpdatedBy = admin?.Id
            },
            ' إعدادات التقارير
            New SystemSetting With {
                .SettingKey = "ReportDateFormat",
                .SettingValue = "dd/MM/yyyy",
                .Description = "تنسيق التاريخ في التقارير",
                .DataType = "String",
                .Category = "Reports",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 30,
                .UpdatedBy = admin?.Id
            },
            New SystemSetting With {
                .SettingKey = "ReportPageSize",
                .SettingValue = "A4",
                .Description = "حجم صفحة التقرير",
                .DataType = "String",
                .Category = "Reports",
                .IsEditable = True,
                .IsVisible = True,
                .DisplayOrder = 31,
                .UpdatedBy = admin?.Id
            }
        }
        
        For Each setting In settings
            context.SystemSettings.Add(setting)
        Next
        
        context.SaveChanges()
    End Sub
    
    ''' <summary>
    ''' إدراج بيانات تجريبية
    ''' </summary>
    ''' <param name="context">سياق قاعدة البيانات</param>
    Private Sub SeedTestData(context As DebtContext)
        Dim admin = context.Users.FirstOrDefault(Function(u) u.Username = "admin")
        
        If admin IsNot Nothing Then
            ' إدراج عملاء تجريبيين
            Dim customers As New List(Of Customer) From {
                New Customer With {
                    .Name = "أحمد محمد علي",
                    .Phone = "07901234567",
                    .Email = "<EMAIL>",
                    .Address = "بغداد - الكرادة",
                    .CurrentBalanceIQD = 500000,
                    .CurrentBalanceUSD = 100,
                    .CreatedBy = admin.Id
                },
                New Customer With {
                    .Name = "فاطمة حسن محمود",
                    .Phone = "07801234567",
                    .Email = "<EMAIL>",
                    .Address = "البصرة - العشار",
                    .CurrentBalanceIQD = -200000,
                    .CurrentBalanceUSD = 0,
                    .CreatedBy = admin.Id
                }
            }
            
            For Each customer In customers
                context.Customers.Add(customer)
            Next
            
            ' إدراج موردين تجريبيين
            Dim suppliers As New List(Of Supplier) From {
                New Supplier With {
                    .Name = "شركة التجارة العامة",
                    .Phone = "07701234567",
                    .Email = "<EMAIL>",
                    .Address = "أربيل - المركز",
                    .CurrentBalanceIQD = -1000000,
                    .CurrentBalanceUSD = -500,
                    .SupplierType = "شركة",
                    .TaxNumber = "*********",
                    .CreatedBy = admin.Id
                }
            }
            
            For Each supplier In suppliers
                context.Suppliers.Add(supplier)
            Next
            
            context.SaveChanges()
        End If
    End Sub
    
End Class
