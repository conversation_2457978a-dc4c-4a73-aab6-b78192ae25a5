<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// فحص حالة الجداول
$tables = [
    'users' => 'المستخدمين',
    'currencies' => 'العملات',
    'departments' => 'الأقسام',
    'employees' => 'الموظفين',
    'customers' => 'العملاء',
    'suppliers' => 'الموردين',
    'items' => 'الأصناف',
    'warehouses' => 'المخازن',
    'sales_invoices' => 'فواتير المبيعات',
    'purchase_invoices' => 'فواتير المشتريات'
];

$tableStatus = [];
$totalTables = count($tables);
$existingTables = 0;

foreach ($tables as $table => $name) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
        $exists = !empty($result);
        $tableStatus[$table] = [
            'name' => $name,
            'exists' => $exists,
            'count' => 0
        ];
        
        if ($exists) {
            $existingTables++;
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM `$table`");
                $tableStatus[$table]['count'] = $count['count'];
            } catch (Exception $e) {
                $tableStatus[$table]['count'] = 'خطأ';
            }
        }
    } catch (Exception $e) {
        $tableStatus[$table] = [
            'name' => $name,
            'exists' => false,
            'count' => 0
        ];
    }
}

$systemHealth = round(($existingTables / $totalTables) * 100);

// فحص الملفات المهمة
$files = [
    'employees.php' => 'نظام الموظفين',
    'purchases.php' => 'نظام المشتريات',
    'sales.php' => 'نظام المبيعات',
    'customers.php' => 'نظام العملاء',
    'suppliers.php' => 'نظام الموردين',
    'inventory.php' => 'نظام المخزون'
];

$fileStatus = [];
$totalFiles = count($files);
$existingFiles = 0;

foreach ($files as $file => $name) {
    $exists = file_exists($file);
    $fileStatus[$file] = [
        'name' => $name,
        'exists' => $exists
    ];
    if ($exists) $existingFiles++;
}

$fileHealth = round(($existingFiles / $totalFiles) * 100);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حالة النظام - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .status-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .status-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .status-body {
            padding: 40px;
        }
        .health-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 36px;
            font-weight: bold;
            color: white;
        }
        .health-excellent { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .health-good { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .health-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .health-danger { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .table-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <div class="status-header">
            <h1><i class="fas fa-heartbeat fa-2x mb-3"></i><br>حالة النظام</h1>
            <p class="mb-0">مراقبة صحة وحالة جميع مكونات النظام</p>
        </div>
        
        <div class="status-body">
            <!-- الحالة العامة -->
            <div class="text-center mb-5">
                <div class="health-circle <?= $systemHealth >= 90 ? 'health-excellent' : ($systemHealth >= 70 ? 'health-good' : ($systemHealth >= 50 ? 'health-warning' : 'health-danger')) ?>">
                    <?= $systemHealth ?>%
                </div>
                <h3>
                    <?php if ($systemHealth >= 90): ?>
                        <i class="fas fa-check-circle text-success me-2"></i>النظام يعمل بشكل ممتاز
                    <?php elseif ($systemHealth >= 70): ?>
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>النظام يعمل بشكل جيد
                    <?php elseif ($systemHealth >= 50): ?>
                        <i class="fas fa-exclamation-triangle text-warning me-2"></i>النظام يحتاج إلى إصلاحات
                    <?php else: ?>
                        <i class="fas fa-times-circle text-danger me-2"></i>النظام يحتاج إلى إعداد شامل
                    <?php endif; ?>
                </h3>
                <p class="text-muted">الجداول: <?= $existingTables ?>/<?= $totalTables ?> | الملفات: <?= $existingFiles ?>/<?= $totalFiles ?></p>
            </div>

            <div class="row">
                <!-- حالة الجداول -->
                <div class="col-lg-6">
                    <div class="card table-card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-database me-2"></i>حالة الجداول (<?= $systemHealth ?>%)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($tableStatus as $table => $info): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong><?= $info['name'] ?></strong>
                                        <?php if ($info['exists'] && $info['count'] !== 'خطأ'): ?>
                                            <small class="text-muted">(<?= number_format($info['count']) ?> سجل)</small>
                                        <?php endif; ?>
                                    </div>
                                    <i class="fas fa-<?= $info['exists'] ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-lg"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- حالة الملفات -->
                <div class="col-lg-6">
                    <div class="card table-card">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-file me-2"></i>حالة الملفات (<?= $fileHealth ?>%)
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($fileStatus as $file => $info): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div>
                                        <strong><?= $info['name'] ?></strong>
                                        <br><small class="text-muted"><?= $file ?></small>
                                    </div>
                                    <i class="fas fa-<?= $info['exists'] ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-lg"></i>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إجراءات الإصلاح -->
            <?php if ($systemHealth < 100): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-tools me-2"></i>إجراءات الإصلاح المقترحة:</h5>
                    <ul class="mb-0">
                        <?php if ($systemHealth < 50): ?>
                            <li>استخدم <strong>الإعداد الفوري</strong> لإنشاء جميع الجداول والبيانات الأساسية</li>
                        <?php elseif ($systemHealth < 90): ?>
                            <li>استخدم <strong>الإعداد النهائي</strong> لإكمال الجداول المفقودة</li>
                        <?php endif; ?>
                        <?php if ($fileHealth < 100): ?>
                            <li>استخدم <strong>إنشاء الملفات المفقودة</strong> لإنشاء الصفحات المطلوبة</li>
                        <?php endif; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if ($systemHealth < 50): ?>
                    <a href="instant_setup.php" class="btn btn-danger btn-lg me-3">
                        <i class="fas fa-bolt me-2"></i>الإعداد الفوري
                    </a>
                <?php endif; ?>
                
                <?php if ($systemHealth < 90): ?>
                    <a href="final_setup.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-rocket me-2"></i>الإعداد النهائي
                    </a>
                <?php endif; ?>
                
                <?php if ($fileHealth < 100): ?>
                    <a href="create_missing_files.php" class="btn btn-warning btn-lg me-3">
                        <i class="fas fa-file-medical me-2"></i>إنشاء الملفات المفقودة
                    </a>
                <?php endif; ?>
                
                <a href="system_diagnosis.php" class="btn btn-info btn-lg me-3">
                    <i class="fas fa-stethoscope me-2"></i>تشخيص شامل
                </a>
                
                <a href="dashboard.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>

            <!-- معلومات إضافية -->
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?= $existingTables ?></h3>
                            <p class="mb-0">جداول موجودة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?= $existingFiles ?></h3>
                            <p class="mb-0">ملفات موجودة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?= round(($systemHealth + $fileHealth) / 2) ?>%</h3>
                            <p class="mb-0">الصحة العامة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث الصفحة كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
