<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // تحديث إعدادات الشركة
        $company_settings = [
            'company_name' => trim($_POST['company_name']),
            'company_phone' => trim($_POST['company_phone']),
            'company_email' => trim($_POST['company_email']),
            'company_address' => trim($_POST['company_address']),
            'exchange_rate_usd_iqd' => floatval($_POST['exchange_rate_usd_iqd']),
            'default_currency' => $_POST['default_currency']
        ];
        
        foreach ($company_settings as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO system_settings (setting_key, setting_value, updated_by) 
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                updated_by = VALUES(updated_by),
                updated_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$key, $value, $_SESSION['user_id']]);
        }
        
        // معالجة رفع الشعار
        if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'assets/uploads/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['company_logo']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
            
            if (in_array($file_extension, $allowed_extensions)) {
                $logo_filename = 'logo.' . $file_extension;
                $logo_path = $upload_dir . $logo_filename;
                
                if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $logo_path)) {
                    $stmt = $pdo->prepare("
                        INSERT INTO system_settings (setting_key, setting_value, updated_by) 
                        VALUES (?, ?, ?)
                        ON DUPLICATE KEY UPDATE 
                        setting_value = VALUES(setting_value),
                        updated_by = VALUES(updated_by),
                        updated_at = CURRENT_TIMESTAMP
                    ");
                    $stmt->execute(['company_logo', $logo_filename, $_SESSION['user_id']]);
                }
            }
        }
        
        $pdo->commit();
        $message = 'تم حفظ الإعدادات بنجاح';
        $message_type = 'success';
        logActivity('تحديث الإعدادات', 'تم تحديث إعدادات النظام');
        
    } catch (Exception $e) {
        $pdo->rollback();
        $message = 'حدث خطأ أثناء حفظ الإعدادات';
        $message_type = 'danger';
        error_log("خطأ في الإعدادات: " . $e->getMessage());
    }
}

// جلب الإعدادات الحالية
$settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("خطأ جلب الإعدادات: " . $e->getMessage());
}

// الإعدادات الافتراضية
$default_settings = [
    'company_name' => 'شركة إدارة الديون',
    'company_phone' => '',
    'company_email' => '',
    'company_address' => '',
    'exchange_rate_usd_iqd' => '1500',
    'default_currency' => 'IQD',
    'company_logo' => ''
];

foreach ($default_settings as $key => $default_value) {
    if (!isset($settings[$key])) {
        $settings[$key] = $default_value;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--gradient-primary);">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <?php if (!empty($settings['company_logo']) && file_exists('assets/uploads/' . $settings['company_logo'])): ?>
                    <img src="assets/uploads/<?php echo htmlspecialchars($settings['company_logo']); ?>" alt="شعار الشركة" style="height: 40px; margin-left: 10px;">
                <?php else: ?>
                    <i class="fas fa-calculator me-2"></i>
                <?php endif; ?>
                <?php echo htmlspecialchars($settings['company_name']); ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="avatar-circle me-2">
                            <i class="fas fa-user"></i>
                        </div>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 arabic-text">إعدادات النظام</h1>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- إعدادات الشركة -->
                        <div class="col-lg-8">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-gradient text-white" style="background: var(--gradient-primary) !important;">
                                    <h5 class="mb-0 arabic-text">
                                        <i class="fas fa-building me-2"></i>
                                        معلومات الشركة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="company_name" class="form-label arabic-text">اسم الشركة *</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" 
                                                   value="<?php echo htmlspecialchars($settings['company_name']); ?>" required>
                                            <div class="invalid-feedback">اسم الشركة مطلوب</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="company_phone" class="form-label arabic-text">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                                   value="<?php echo htmlspecialchars($settings['company_phone']); ?>">
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="company_email" class="form-label arabic-text">البريد الإلكتروني</label>
                                            <input type="email" class="form-control" id="company_email" name="company_email" 
                                                   value="<?php echo htmlspecialchars($settings['company_email']); ?>">
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="company_address" class="form-label arabic-text">العنوان</label>
                                            <input type="text" class="form-control" id="company_address" name="company_address" 
                                                   value="<?php echo htmlspecialchars($settings['company_address']); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- الإعدادات المالية -->
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-gradient text-white" style="background: var(--gradient-success) !important;">
                                    <h5 class="mb-0 arabic-text">
                                        <i class="fas fa-coins me-2"></i>
                                        الإعدادات المالية
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="exchange_rate_usd_iqd" class="form-label arabic-text">سعر صرف الدولار (مقابل الدينار)</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control numbers" id="exchange_rate_usd_iqd" 
                                                       name="exchange_rate_usd_iqd" step="0.01" min="0"
                                                       value="<?php echo htmlspecialchars($settings['exchange_rate_usd_iqd']); ?>">
                                                <span class="input-group-text">د.ع</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="default_currency" class="form-label arabic-text">العملة الافتراضية</label>
                                            <select class="form-select" id="default_currency" name="default_currency">
                                                <option value="IQD" <?php echo $settings['default_currency'] === 'IQD' ? 'selected' : ''; ?>>دينار عراقي</option>
                                                <option value="USD" <?php echo $settings['default_currency'] === 'USD' ? 'selected' : ''; ?>>دولار أمريكي</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شعار الشركة -->
                        <div class="col-lg-4">
                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-gradient text-white" style="background: var(--gradient-info) !important;">
                                    <h5 class="mb-0 arabic-text">
                                        <i class="fas fa-image me-2"></i>
                                        شعار الشركة
                                    </h5>
                                </div>
                                <div class="card-body text-center">
                                    <div class="logo-preview mb-3">
                                        <?php if (!empty($settings['company_logo']) && file_exists('assets/uploads/' . $settings['company_logo'])): ?>
                                            <img src="assets/uploads/<?php echo htmlspecialchars($settings['company_logo']); ?>" 
                                                 alt="شعار الشركة" class="img-fluid rounded" style="max-height: 150px;">
                                        <?php else: ?>
                                            <div class="logo-placeholder">
                                                <i class="fas fa-image fa-4x text-muted"></i>
                                                <p class="text-muted mt-2 arabic-text">لا يوجد شعار</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="company_logo" class="form-label arabic-text">رفع شعار جديد</label>
                                        <input type="file" class="form-control" id="company_logo" name="company_logo" 
                                               accept="image/*" onchange="previewLogo(this)">
                                        <div class="form-text arabic-text">الصيغ المدعومة: JPG, PNG, GIF (حد أقصى 2MB)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <button type="reset" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-undo me-1"></i>
                                    إعادة تعيين
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        function previewLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.querySelector('.logo-preview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="معاينة الشعار" class="img-fluid rounded" style="max-height: 150px;">`;
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
