﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression.ZipFile</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.ZipFile">
      <summary>Proporciona métodos estáticos para crear, extraer y abrir archivos zip. </summary>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String)">
      <summary>Crea un archivo zip que contiene los archivos y directorios del directorio especificado.</summary>
      <param name="sourceDirectoryName">La ruta de acceso al directorio que se va a almacenar, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="destinationArchiveFileName">La ruta de acceso del archivo que se creará, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Crea un archivo zip que contiene los archivos y directorios del directorio especificado, utiliza el nivel de compresión especificado y, opcionalmente, incluye el directorio base.</summary>
      <param name="sourceDirectoryName">La ruta de acceso al directorio que se va a almacenar, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="destinationArchiveFileName">La ruta de acceso del archivo que se creará, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la velocidad o en la eficacia de compresión al crear la entrada.</param>
      <param name="includeBaseDirectory">true para incluir el nombre de directorio de <paramref name="sourceDirectoryName" /> en la raíz del archivo; false para incluir solo el contenido del directorio.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean,System.Text.Encoding)">
      <summary>Crea un archivo zip que contiene los archivos y directorios del directorio especificado, utiliza el nivel de compresión y la codificación de caracteres especificados para los nombres de entrada y, opcionalmente, incluye el directorio base.</summary>
      <param name="sourceDirectoryName">La ruta de acceso al directorio que se va a almacenar, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="destinationArchiveFileName">La ruta de acceso del archivo que se creará, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la velocidad o en la eficacia de compresión al crear la entrada.</param>
      <param name="includeBaseDirectory">true para incluir el nombre de directorio de <paramref name="sourceDirectoryName" /> en la raíz del archivo; false para incluir solo el contenido del directorio.</param>
      <param name="entryNameEncoding">Codificación que se va a usar al leer o escribir nombres de entrada en este archivo.Especifique un valor para este parámetro únicamente cuando se necesite una codificación para la interoperabilidad con herramientas y bibliotecas de archivos zip que no admiten la codificación UTF-8 para los nombres de entrada.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String)">
      <summary>Extrae todos los archivos del archivo zip especificado en un directorio del sistema de archivos.</summary>
      <param name="sourceArchiveFileName">La ruta de acceso al archivo que se va a extraer.</param>
      <param name="destinationDirectoryName">La ruta de acceso al directorio en el que se van a colocar los archivos extraídos, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String,System.Text.Encoding)">
      <summary>Extrae todos los archivos de archivo zip especificado en un directorio del sistema de archivos y utiliza la codificación de caracteres especificada para los nombres de entrada.</summary>
      <param name="sourceArchiveFileName">La ruta de acceso al archivo que se va a extraer.</param>
      <param name="destinationDirectoryName">La ruta de acceso al directorio en el que se van a colocar los archivos extraídos, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="entryNameEncoding">Codificación que se va a usar al leer o escribir nombres de entrada en este archivo.Especifique un valor para este parámetro únicamente cuando se necesite una codificación para la interoperabilidad con herramientas y bibliotecas de archivos zip que no admiten la codificación UTF-8 para los nombres de entrada.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode)">
      <summary>Abre un archivo .zip en la ruta de acceso especificada y en el modo especificado.</summary>
      <returns>El archivo zip abierto.</returns>
      <param name="archiveFileName">La ruta de acceso al archivo que se va a abrir, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="mode">Uno de los valores de enumeración que especifica las acciones que se permiten en las entradas del archivo abierto.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode,System.Text.Encoding)">
      <summary>Abre un archivo zip en la ruta de acceso especificada, en el modo especificado, y usando la codificación de caracteres especificada para los nombres de entrada.</summary>
      <returns>El archivo zip abierto.</returns>
      <param name="archiveFileName">La ruta de acceso al archivo que se va a abrir, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="mode">Uno de los valores de enumeración que especifica las acciones que se permiten en las entradas del archivo abierto.</param>
      <param name="entryNameEncoding">Codificación que se va a usar al leer o escribir nombres de entrada en este archivo.Especifique un valor para este parámetro únicamente cuando se necesite una codificación para la interoperabilidad con herramientas y bibliotecas de archivos zip que no admiten la codificación UTF-8 para los nombres de entrada.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.OpenRead(System.String)">
      <summary>Abre un archivo zip para leer en la ruta de acceso especificada.</summary>
      <returns>El archivo zip abierto.</returns>
      <param name="archiveFileName">La ruta de acceso al archivo que se va a abrir, especificada como una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipFileExtensions">
      <summary>Proporciona métodos de extensión para las clases <see cref="T:System.IO.Compression.ZipArchive" /> y <see cref="T:System.IO.Compression.ZipArchiveEntry" />.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String)">
      <summary>Archiva un archivo comprimiéndolo y agregándolo al archivo zip.</summary>
      <returns>Un contenedor para la nueva entrada en el archivo zip.</returns>
      <param name="destination">Archivo .zip al que se agrega el archivo.</param>
      <param name="sourceFileName">Ruta de acceso al archivo que se va a archivar.Puede especificar una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="entryName">Nombre de la entrada que se va a crear en el archivo zip.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> es <see cref="F:System.String.Empty" />, contiene solo espacios en blanco o contiene al menos un carácter no válido.O bien<paramref name="entryName" /> es <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> o <paramref name="entryName" /> es null.</exception>
      <exception cref="T:System.IO.PathTooLongException">En <paramref name="sourceFileName" />, la ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso no deben superar 248 caracteres y los nombres de archivo no deben superar 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="sourceFileName" /> no es válido (por ejemplo, se encuentra en una unidad no asignada).</exception>
      <exception cref="T:System.IO.IOException">No se puede abrir el archivo especificado por <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> especifica un directorio.O bienEl autor de la llamada no tiene el permiso necesario para obtener acceso al archivo que especifica <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo especificado por <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">El parámetro <paramref name="sourceFileName" /> no tiene un formato válido.O bienEl archivo zip no admite escritura.</exception>
      <exception cref="T:System.ObjectDisposedException">El archivo .zip se ha desechado.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String,System.IO.Compression.CompressionLevel)">
      <summary>Archiva un archivo comprimiéndolo mediante el nivel especificado de compresión y agregándolo al archivo zip.</summary>
      <returns>Un contenedor para la nueva entrada en el archivo zip.</returns>
      <param name="destination">Archivo .zip al que se agrega el archivo.</param>
      <param name="sourceFileName">Ruta de acceso al archivo que se va a archivar.Puede especificar una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="entryName">Nombre de la entrada que se va a crear en el archivo zip.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al crear la entrada.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> es <see cref="F:System.String.Empty" />, contiene solo espacios en blanco o contiene al menos un carácter no válido.O bien<paramref name="entryName" /> es <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> o <paramref name="entryName" /> es null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">El valor de <paramref name="sourceFileName" /> no es válido (por ejemplo, se encuentra en una unidad no asignada).</exception>
      <exception cref="T:System.IO.PathTooLongException">En <paramref name="sourceFileName" />, la ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso no deben superar 248 caracteres y los nombres de archivo no deben superar 260 caracteres.</exception>
      <exception cref="T:System.IO.IOException">No se puede abrir el archivo especificado por <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> especifica un directorio.O bienEl autor de la llamada no tiene el permiso necesario para obtener acceso al archivo que especifica <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">No se encuentra el archivo especificado por <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">El parámetro <paramref name="sourceFileName" /> no tiene un formato válido.O bienEl archivo zip no admite escritura.</exception>
      <exception cref="T:System.ObjectDisposedException">El archivo .zip se ha desechado.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToDirectory(System.IO.Compression.ZipArchive,System.String)">
      <summary>Extrae todos los archivos del archivo zip a un directorio del sistema de archivos.</summary>
      <param name="source">El archivo zip del que se van a extraer archivos.</param>
      <param name="destinationDirectoryName">La ruta de acceso al directorio donde se van a colocar los archivos extraídos.Puede especificar una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> es <see cref="F:System.String.Empty" />, contiene solo espacios en blanco o contiene al menos un carácter no válido.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> es null.</exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso supera la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso no deben superar 248 caracteres y los nombres de archivo no deben superar 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada).</exception>
      <exception cref="T:System.IO.IOException">El directorio que especifica <paramref name="destinationDirectoryName" /> ya existe.O bienEl nombre de una entrada en el archivo es <see cref="F:System.String.Empty" />, contiene solo espacios en blanco, o contiene al menos un carácter no válido.O bienLa extracción de una entrada del archivo crearía un archivo que está fuera del directorio especificado por <paramref name="destinationDirectoryName" />. (Por ejemplo, esto puede ocurrir si el nombre de la entrada contiene descriptores de acceso del directorio primario). O bienDos o más entradas del archivo tienen el mismo nombre.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El llamador no tiene el permiso para escribir en el directorio de destino.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> contiene un formato no válido.</exception>
      <exception cref="T:System.IO.InvalidDataException">Una entrada de archivo no se encuentra o está dañada.O bienUna entrada de archivo se ha comprimido mediante un método de compresión que no se admite.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String)">
      <summary>Dibuja una entrada del archivo zip a un archivo.</summary>
      <param name="source">La entrada del archivo zip del que se va a extraer un archivo.</param>
      <param name="destinationFileName">La ruta de acceso del archivo que se va a crear a partir del contenido de la entrada.Puede especificar una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> es una cadena de longitud cero, contiene solo espacios en blanco o contiene uno o varios de los caracteres no válidos definidos por <see cref="F:System.IO.Path.InvalidPathChars" />.O bien<paramref name="destinationFileName" /> especifica un directorio.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso no deben superar 248 caracteres y los nombres de archivo no deben superar 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> ya existe.O bien Error de E/S.O bienLa entrada está actualmente abierta para escribir en ella.O bienSe ha eliminado el entrada del archivo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El autor de la llamada no tiene el permiso necesario para crear un archivo nuevo.</exception>
      <exception cref="T:System.IO.InvalidDataException">La entrada falta en el archivo o está dañada y no se puede leer.O bienLa entrada se ha comprimido mediante un método de compresión que no se admite.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha desechado el archivo zip al que pertenece esta entrada.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> tiene un formato no válido. O bienEl archivo zip para esta entrada se ha abierto en modo <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, que no permite la recuperación de entradas.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String,System.Boolean)">
      <summary>Extrae una entrada del archivo zip a un archivo, y sobrescribe opcionalmente un archivo existente que tiene el mismo nombre.</summary>
      <param name="source">La entrada del archivo zip del que se va a extraer un archivo.</param>
      <param name="destinationFileName">La ruta de acceso del archivo que se va a crear a partir del contenido de la entrada.Puede especificar una ruta de acceso relativa o absoluta.Una ruta de acceso relativa se interpreta en relación con el directorio de trabajo actual.</param>
      <param name="overwrite">true para sobrescribir un archivo existente que tiene el mismo nombre que el archivo de destino; si no, false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> es una cadena de longitud cero, contiene solo espacios en blanco o contiene uno o varios de los caracteres no válidos definidos por <see cref="F:System.IO.Path.InvalidPathChars" />.O bien<paramref name="destinationFileName" /> especifica un directorio.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> es null. </exception>
      <exception cref="T:System.IO.PathTooLongException">La ruta de acceso especificada, el nombre de archivo o ambos superan la longitud máxima definida por el sistema.Por ejemplo, en las plataformas basadas en Windows, las rutas de acceso no deben superar 248 caracteres y los nombres de archivo no deben superar 260 caracteres.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">La ruta de acceso especificada no es válida (por ejemplo, se encuentra en una unidad de red no asignada). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> ya existe y <paramref name="overwrite" /> es false.O bien Error de E/S.O bienLa entrada está actualmente abierta para escribir en ella.O bienSe ha eliminado el entrada del archivo.</exception>
      <exception cref="T:System.UnauthorizedAccessException">El autor de la llamada no tiene el permiso necesario para crear un archivo nuevo.</exception>
      <exception cref="T:System.IO.InvalidDataException">La entrada falta en el archivo o está dañada y no se puede leer.O bienLa entrada se ha comprimido mediante un método de compresión que no se admite.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha desechado el archivo zip al que pertenece esta entrada.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> tiene un formato no válido. O bienEl archivo zip para esta entrada se ha abierto en modo <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, que no permite la recuperación de entradas.</exception>
    </member>
  </members>
</doc>