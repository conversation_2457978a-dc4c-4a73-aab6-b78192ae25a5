<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$results = [];

try {
    // فحص الاتصال بقاعدة البيانات
    $results['connection'] = [
        'status' => 'success',
        'message' => 'الاتصال بقاعدة البيانات ناجح'
    ];
    
    // فحص الجداول الموجودة
    $tables = $db->fetchAll("SHOW TABLES");
    $results['tables_count'] = count($tables);
    $results['tables'] = [];
    
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        
        // فحص عدد السجلات
        try {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM `$tableName`");
            $recordCount = $count['count'];
        } catch (Exception $e) {
            $recordCount = 'خطأ';
        }
        
        // فحص بنية الجدول
        try {
            $structure = $db->fetchAll("DESCRIBE `$tableName`");
            $columnCount = count($structure);
        } catch (Exception $e) {
            $columnCount = 'خطأ';
        }
        
        $results['tables'][$tableName] = [
            'records' => $recordCount,
            'columns' => $columnCount,
            'structure' => $structure ?? []
        ];
    }
    
    // فحص المؤشرات (Indexes)
    $results['indexes'] = [];
    foreach ($tables as $table) {
        $tableName = array_values($table)[0];
        try {
            $indexes = $db->fetchAll("SHOW INDEX FROM `$tableName`");
            $results['indexes'][$tableName] = $indexes;
        } catch (Exception $e) {
            $results['indexes'][$tableName] = [];
        }
    }
    
    // فحص القيود المرجعية
    try {
        $foreignKeys = $db->fetchAll("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE CONSTRAINT_SCHEMA = DATABASE() 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $results['foreign_keys'] = $foreignKeys;
    } catch (Exception $e) {
        $results['foreign_keys'] = [];
    }
    
    // فحص حجم قاعدة البيانات
    try {
        $dbSize = $db->fetchOne("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $results['database_size'] = $dbSize['size_mb'] . ' MB';
    } catch (Exception $e) {
        $results['database_size'] = 'غير محدد';
    }
    
} catch (Exception $e) {
    $results['connection'] = [
        'status' => 'error',
        'message' => 'خطأ في الاتصال: ' . $e->getMessage()
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص قاعدة البيانات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .check-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .check-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .check-body {
            padding: 40px;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .table-card {
            border-radius: 10px;
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="check-container">
        <div class="check-header">
            <h1><i class="fas fa-database fa-2x mb-3"></i><br>فحص قاعدة البيانات</h1>
            <p class="mb-0">فحص شامل لحالة وبنية قاعدة البيانات</p>
        </div>
        
        <div class="check-body">
            <!-- حالة الاتصال -->
            <div class="card table-card">
                <div class="card-header <?= $results['connection']['status'] == 'success' ? 'bg-success' : 'bg-danger' ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-<?= $results['connection']['status'] == 'success' ? 'check-circle' : 'times-circle' ?> me-2"></i>
                        حالة الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-0"><?= htmlspecialchars($results['connection']['message']) ?></p>
                    <?php if (isset($results['database_size'])): ?>
                        <p class="mb-0 mt-2"><strong>حجم قاعدة البيانات:</strong> <?= $results['database_size'] ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($results['connection']['status'] == 'success'): ?>
                <!-- إحصائيات عامة -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary"><?= $results['tables_count'] ?></h3>
                                <p class="mb-0">إجمالي الجداول</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success"><?= count($results['foreign_keys']) ?></h3>
                                <p class="mb-0">القيود المرجعية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info"><?= $results['database_size'] ?></h3>
                                <p class="mb-0">حجم قاعدة البيانات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الجداول -->
                <div class="card table-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-table me-2"></i>الجداول (<?= $results['tables_count'] ?>)</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم الجدول</th>
                                        <th>عدد السجلات</th>
                                        <th>عدد الأعمدة</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results['tables'] as $tableName => $tableInfo): ?>
                                        <tr>
                                            <td><code><?= htmlspecialchars($tableName) ?></code></td>
                                            <td>
                                                <?php if (is_numeric($tableInfo['records'])): ?>
                                                    <span class="badge bg-info"><?= number_format($tableInfo['records']) ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger"><?= $tableInfo['records'] ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (is_numeric($tableInfo['columns'])): ?>
                                                    <span class="badge bg-secondary"><?= $tableInfo['columns'] ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger"><?= $tableInfo['columns'] ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (is_numeric($tableInfo['records']) && is_numeric($tableInfo['columns'])): ?>
                                                    <i class="fas fa-check-circle status-good"></i> سليم
                                                <?php else: ?>
                                                    <i class="fas fa-times-circle status-bad"></i> خطأ
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" 
                                                        onclick="showTableStructure('<?= $tableName ?>')">
                                                    <i class="fas fa-eye"></i> البنية
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- القيود المرجعية -->
                <?php if (!empty($results['foreign_keys'])): ?>
                    <div class="card table-card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-link me-2"></i>القيود المرجعية (<?= count($results['foreign_keys']) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الجدول</th>
                                            <th>العمود</th>
                                            <th>الجدول المرجعي</th>
                                            <th>العمود المرجعي</th>
                                            <th>اسم القيد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($results['foreign_keys'] as $fk): ?>
                                            <tr>
                                                <td><code><?= htmlspecialchars($fk['TABLE_NAME']) ?></code></td>
                                                <td><code><?= htmlspecialchars($fk['COLUMN_NAME']) ?></code></td>
                                                <td><code><?= htmlspecialchars($fk['REFERENCED_TABLE_NAME']) ?></code></td>
                                                <td><code><?= htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) ?></code></td>
                                                <td><small><?= htmlspecialchars($fk['CONSTRAINT_NAME']) ?></small></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <a href="system_diagnosis.php" class="btn btn-info btn-lg me-3">
                    <i class="fas fa-stethoscope me-2"></i>تشخيص شامل
                </a>
                <a href="control_center.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-cogs me-2"></i>مركز التحكم
                </a>
                <a href="dashboard.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </div>
        </div>
    </div>

    <!-- Modal لعرض بنية الجدول -->
    <div class="modal fade" id="tableStructureModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">بنية الجدول: <span id="tableName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="tableStructureContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const tableStructures = <?= json_encode($results['tables'] ?? []) ?>;
        
        function showTableStructure(tableName) {
            document.getElementById('tableName').textContent = tableName;
            
            const structure = tableStructures[tableName]?.structure || [];
            let html = '<div class="table-responsive"><table class="table table-sm"><thead><tr>';
            html += '<th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>';
            html += '</tr></thead><tbody>';
            
            structure.forEach(column => {
                html += '<tr>';
                html += '<td><code>' + column.Field + '</code></td>';
                html += '<td>' + column.Type + '</td>';
                html += '<td>' + column.Null + '</td>';
                html += '<td>' + column.Key + '</td>';
                html += '<td>' + (column.Default || '-') + '</td>';
                html += '<td>' + (column.Extra || '-') + '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table></div>';
            document.getElementById('tableStructureContent').innerHTML = html;
            
            new bootstrap.Modal(document.getElementById('tableStructureModal')).show();
        }
    </script>
</body>
</html>
