<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// فئات التقارير
$reportCategories = [
    'financial' => [
        'name' => 'التقارير المالية',
        'icon' => 'fas fa-chart-line',
        'color' => 'primary',
        'reports' => [
            ['name' => 'تقرير الأرباح والخسائر', 'file' => 'profit_loss_report.php', 'description' => 'تقرير شامل للأرباح والخسائر'],
            ['name' => 'تقرير التدفق النقدي', 'file' => 'cash_flow_report.php', 'description' => 'حركة النقد الوارد والصادر'],
            ['name' => 'تقرير المبيعات', 'file' => 'sales_report.php', 'description' => 'تحليل تفصيلي للمبيعات'],
            ['name' => 'تقرير المشتريات', 'file' => 'purchases_report.php', 'description' => 'تحليل تفصيلي للمشتريات'],
            ['name' => 'تقرير المصروفات', 'file' => 'expenses_report.php', 'description' => 'تحليل المصروفات حسب الفئة والقسم'],
            ['name' => 'تقرير أرصدة العملاء', 'file' => 'customers_balance_report.php', 'description' => 'أرصدة العملاء المدينة والدائنة']
        ]
    ],
    'inventory' => [
        'name' => 'تقارير المخزون',
        'icon' => 'fas fa-boxes',
        'color' => 'success',
        'reports' => [
            ['name' => 'تقرير أرصدة المخزون', 'file' => 'inventory_balance_report.php', 'description' => 'أرصدة جميع الأصناف في المخازن'],
            ['name' => 'تقرير حركات المخزون', 'file' => 'stock_movements_report.php', 'description' => 'جميع حركات المخزون الواردة والصادرة'],
            ['name' => 'تقرير الأصناف تحت الحد الأدنى', 'file' => 'low_stock_report.php', 'description' => 'الأصناف التي وصلت للحد الأدنى'],
            ['name' => 'تقرير تقييم المخزون', 'file' => 'inventory_valuation_report.php', 'description' => 'قيمة المخزون بأسعار التكلفة'],
            ['name' => 'تقرير الجرد', 'file' => 'inventory_count_report.php', 'description' => 'تقرير عمليات الجرد والتسويات'],
            ['name' => 'تقرير الأصناف الراكدة', 'file' => 'slow_moving_items_report.php', 'description' => 'الأصناف بطيئة الحركة']
        ]
    ],
    'production' => [
        'name' => 'تقارير الإنتاج',
        'icon' => 'fas fa-cogs',
        'color' => 'warning',
        'reports' => [
            ['name' => 'تقرير أوامر الإنتاج', 'file' => 'production_orders_report.php', 'description' => 'تقرير شامل لأوامر الإنتاج'],
            ['name' => 'تقرير الإنتاجية', 'file' => 'productivity_report.php', 'description' => 'تحليل الإنتاجية والكفاءة'],
            ['name' => 'تقرير تكلفة الإنتاج', 'file' => 'production_cost_report.php', 'description' => 'تحليل تكاليف الإنتاج'],
            ['name' => 'تقرير استهلاك المواد الخام', 'file' => 'material_consumption_report.php', 'description' => 'استهلاك المواد الخام في الإنتاج'],
            ['name' => 'تقرير وقت الإنتاج', 'file' => 'production_time_report.php', 'description' => 'أوقات الإنتاج والتأخيرات'],
            ['name' => 'تقرير جودة الإنتاج', 'file' => 'production_quality_report.php', 'description' => 'تقرير جودة المنتجات']
        ]
    ],
    'hr' => [
        'name' => 'تقارير الموارد البشرية',
        'icon' => 'fas fa-users',
        'color' => 'info',
        'reports' => [
            ['name' => 'تقرير الموظفين', 'file' => 'employees_report.php', 'description' => 'تقرير شامل للموظفين'],
            ['name' => 'تقرير الرواتب', 'file' => 'payroll_report.php', 'description' => 'تقرير الرواتب والمستحقات'],
            ['name' => 'تقرير الحضور والانصراف', 'file' => 'attendance_report.php', 'description' => 'تقرير حضور الموظفين'],
            ['name' => 'تقرير الإجازات', 'file' => 'leaves_report.php', 'description' => 'تقرير إجازات الموظفين'],
            ['name' => 'تقرير التقييمات', 'file' => 'evaluations_report.php', 'description' => 'تقييمات أداء الموظفين'],
            ['name' => 'تقرير التدريب', 'file' => 'training_report.php', 'description' => 'برامج التدريب والتطوير']
        ]
    ],
    'analytics' => [
        'name' => 'التقارير التحليلية',
        'icon' => 'fas fa-chart-bar',
        'color' => 'danger',
        'reports' => [
            ['name' => 'تحليل الأرباح حسب المنتج', 'file' => 'product_profit_analysis.php', 'description' => 'تحليل ربحية المنتجات'],
            ['name' => 'تحليل أداء العملاء', 'file' => 'customer_analysis.php', 'description' => 'تحليل أداء وسلوك العملاء'],
            ['name' => 'تحليل أداء الموردين', 'file' => 'supplier_analysis.php', 'description' => 'تحليل أداء الموردين'],
            ['name' => 'تحليل الاتجاهات', 'file' => 'trends_analysis.php', 'description' => 'تحليل الاتجاهات والتوقعات'],
            ['name' => 'مؤشرات الأداء الرئيسية', 'file' => 'kpi_dashboard.php', 'description' => 'لوحة مؤشرات الأداء'],
            ['name' => 'تحليل المنافسة', 'file' => 'competition_analysis.php', 'description' => 'تحليل الوضع التنافسي']
        ]
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; transition: transform 0.3s ease; }
        .card:hover { transform: translateY(-5px); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .category-card { cursor: pointer; }
        .category-icon { font-size: 3rem; margin-bottom: 15px; }
        .report-item { padding: 15px; border: 1px solid #e9ecef; border-radius: 10px; margin-bottom: 10px; transition: all 0.3s ease; }
        .report-item:hover { background-color: #f8f9fa; border-color: #007bff; }
        .report-link { text-decoration: none; color: inherit; }
        .report-link:hover { color: inherit; }
        .quick-stats { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card quick-stats">
                    <div class="card-body text-center">
                        <h2><i class="fas fa-chart-bar me-3"></i>مركز التقارير</h2>
                        <p class="mb-0">اختر نوع التقرير المطلوب من الفئات أدناه</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <h4><?= array_sum(array_map(function($cat) { return count($cat['reports']); }, $reportCategories)) ?></h4>
                        <p class="mb-0">إجمالي التقارير</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-layer-group fa-2x mb-2"></i>
                        <h4><?= count($reportCategories) ?></h4>
                        <p class="mb-0">فئات التقارير</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-2x mb-2"></i>
                        <h4>PDF, Excel</h4>
                        <p class="mb-0">صيغ التصدير</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4>فوري</h4>
                        <p class="mb-0">إنشاء التقارير</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- فئات التقارير -->
        <div class="row">
            <?php foreach ($reportCategories as $categoryKey => $category): ?>
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card category-card h-100" data-bs-toggle="collapse" data-bs-target="#<?= $categoryKey ?>Reports">
                        <div class="card-header bg-<?= $category['color'] ?> text-white text-center">
                            <i class="<?= $category['icon'] ?> category-icon"></i>
                            <h5 class="mb-0"><?= $category['name'] ?></h5>
                            <small><?= count($category['reports']) ?> تقرير متاح</small>
                        </div>
                        <div class="collapse" id="<?= $categoryKey ?>Reports">
                            <div class="card-body">
                                <?php foreach ($category['reports'] as $report): ?>
                                    <div class="report-item">
                                        <a href="<?= $report['file'] ?>" class="report-link">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1"><?= $report['name'] ?></h6>
                                                    <small class="text-muted"><?= $report['description'] ?></small>
                                                </div>
                                                <div class="text-end">
                                                    <i class="fas fa-external-link-alt text-primary"></i>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- تقارير سريعة -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>تقارير سريعة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="daily_summary.php" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-calendar-day fa-2x mb-2"></i>
                                    <span>ملخص اليوم</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="monthly_summary.php" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                                    <span>ملخص الشهر</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="top_products.php" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-star fa-2x mb-2"></i>
                                    <span>أفضل المنتجات</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="alerts_report.php" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center">
                                    <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                    <span>التنبيهات</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أدوات التقارير -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-tools me-2"></i>أدوات التقارير</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-magic fa-2x text-primary mb-3"></i>
                                        <h6>منشئ التقارير المخصصة</h6>
                                        <p class="small text-muted">أنشئ تقاريرك المخصصة بسهولة</p>
                                        <a href="custom_report_builder.php" class="btn btn-primary btn-sm">ابدأ الآن</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-clock fa-2x text-success mb-3"></i>
                                        <h6>التقارير المجدولة</h6>
                                        <p class="small text-muted">جدولة التقارير للإرسال التلقائي</p>
                                        <a href="scheduled_reports.php" class="btn btn-success btn-sm">إدارة الجدولة</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-history fa-2x text-info mb-3"></i>
                                        <h6>سجل التقارير</h6>
                                        <p class="small text-muted">عرض سجل التقارير المُنشأة</p>
                                        <a href="reports_history.php" class="btn btn-info btn-sm">عرض السجل</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i>جميع التقارير متاحة بصيغة PDF و Excel</li>
                                    <li><i class="fas fa-check text-success me-2"></i>يمكن تخصيص فترات التقارير</li>
                                    <li><i class="fas fa-check text-success me-2"></i>التقارير محدثة في الوقت الفعلي</li>
                                    <li><i class="fas fa-check text-success me-2"></i>إمكانية جدولة التقارير</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-question-circle me-2"></i>تحتاج مساعدة؟</h6>
                                <p class="small text-muted">
                                    إذا كنت تحتاج مساعدة في إنشاء التقارير أو تخصيصها، يمكنك الرجوع إلى دليل المستخدم أو الاتصال بالدعم الفني.
                                </p>
                                <a href="help.php" class="btn btn-outline-secondary btn-sm me-2">دليل المستخدم</a>
                                <a href="support.php" class="btn btn-outline-primary btn-sm">الدعم الفني</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات تفاعلية للبطاقات
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                const icon = this.querySelector('.category-icon');
                icon.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    icon.style.transform = 'rotate(0deg)';
                }, 500);
            });
        });

        // تأثير hover للتقارير
        document.querySelectorAll('.report-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(-5px)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    </script>
</body>
</html>
