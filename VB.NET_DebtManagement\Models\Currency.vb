Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج العملة - Currency Model
''' </summary>
<Table("Currencies")>
Public Class Currency
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف العملة الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' رمز العملة (ISO Code)
    ''' </summary>
    <Required(ErrorMessage:="رمز العملة مطلوب")>
    <StringLength(3, MinimumLength:=3, ErrorMessage:="رمز العملة يجب أن يكون 3 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    <Index(IsUnique:=True)>
    Public Property Code As String
    
    ''' <summary>
    ''' اسم العملة
    ''' </summary>
    <Required(ErrorMessage:="اسم العملة مطلوب")>
    <StringLength(50, ErrorMessage:="اسم العملة يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Name As String
    
    ''' <summary>
    ''' رمز العملة للعرض
    ''' </summary>
    <Required(ErrorMessage:="رمز العملة للعرض مطلوب")>
    <StringLength(10, ErrorMessage:="رمز العملة للعرض يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Symbol As String
    
    ''' <summary>
    ''' سعر الصرف مقابل العملة الأساسية
    ''' </summary>
    <Column(TypeName:="DECIMAL(10,4)")>
    Public Property ExchangeRate As Decimal = 1
    
    ''' <summary>
    ''' هل هي العملة الافتراضية
    ''' </summary>
    Public Property IsDefault As Boolean = False
    
    ''' <summary>
    ''' حالة العملة (نشطة/غير نشطة)
    ''' </summary>
    Public Property IsActive As Boolean = True
    
    ''' <summary>
    ''' عدد الخانات العشرية
    ''' </summary>
    Public Property DecimalPlaces As Integer = 2
    
    ''' <summary>
    ''' موضع الرمز (قبل أم بعد المبلغ)
    ''' </summary>
    <StringLength(10, ErrorMessage:="موضع الرمز يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property SymbolPosition As String = "After" ' Before, After
    
    ''' <summary>
    ''' فاصل الآلاف
    ''' </summary>
    <StringLength(1, ErrorMessage:="فاصل الآلاف يجب أن يكون حرف واحد")>
    <Column(TypeName:="NVARCHAR")>
    Public Property ThousandsSeparator As String = ","
    
    ''' <summary>
    ''' فاصل العشرية
    ''' </summary>
    <StringLength(1, ErrorMessage:="فاصل العشرية يجب أن يكون حرف واحد")>
    <Column(TypeName:="NVARCHAR")>
    Public Property DecimalSeparator As String = "."
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' الصناديق التي تستخدم هذه العملة
    ''' </summary>
    <InverseProperty("Currency")>
    Public Overridable Property CashBoxes As ICollection(Of CashBox) = New HashSet(Of CashBox)
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' موضع الرمز بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property SymbolPositionInArabic As String
        Get
            Select Case SymbolPosition.ToLower()
                Case "before"
                    Return "قبل المبلغ"
                Case "after"
                    Return "بعد المبلغ"
                Case Else
                    Return SymbolPosition
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة العملة بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property StatusInArabic As String
        Get
            Return If(IsActive, "نشطة", "غير نشطة")
        End Get
    End Property
    
    ''' <summary>
    ''' نوع العملة (أساسية/فرعية) (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TypeInArabic As String
        Get
            Return If(IsDefault, "أساسية", "فرعية")
        End Get
    End Property
    
    ''' <summary>
    ''' عدد الصناديق التي تستخدم هذه العملة (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property CashBoxCount As Integer
        Get
            Return If(CashBoxes?.Count, 0)
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تنسيق المبلغ حسب إعدادات العملة
    ''' </summary>
    ''' <param name="amount">المبلغ</param>
    ''' <param name="includeSymbol">هل يتم تضمين رمز العملة</param>
    ''' <returns>المبلغ منسق</returns>
    Public Function FormatAmount(amount As Decimal, Optional includeSymbol As Boolean = True) As String
        Dim formatString As String = "N" & DecimalPlaces.ToString()
        Dim formattedAmount As String = amount.ToString(formatString)
        
        ' استبدال فواصل الآلاف والعشرية
        If ThousandsSeparator <> "," Then
            formattedAmount = formattedAmount.Replace(",", ThousandsSeparator)
        End If
        
        If DecimalSeparator <> "." Then
            formattedAmount = formattedAmount.Replace(".", DecimalSeparator)
        End If
        
        If includeSymbol Then
            If SymbolPosition.ToLower() = "before" Then
                Return Symbol & " " & formattedAmount
            Else
                Return formattedAmount & " " & Symbol
            End If
        Else
            Return formattedAmount
        End If
    End Function
    
    ''' <summary>
    ''' تحويل المبلغ إلى العملة الأساسية
    ''' </summary>
    ''' <param name="amount">المبلغ</param>
    ''' <returns>المبلغ بالعملة الأساسية</returns>
    Public Function ConvertToBaseCurrency(amount As Decimal) As Decimal
        If IsDefault Then
            Return amount
        Else
            Return amount * ExchangeRate
        End If
    End Function
    
    ''' <summary>
    ''' تحويل المبلغ من العملة الأساسية
    ''' </summary>
    ''' <param name="amount">المبلغ بالعملة الأساسية</param>
    ''' <returns>المبلغ بهذه العملة</returns>
    Public Function ConvertFromBaseCurrency(amount As Decimal) As Decimal
        If IsDefault Then
            Return amount
        Else
            Return amount / ExchangeRate
        End If
    End Function
    
    ''' <summary>
    ''' تحويل المبلغ إلى عملة أخرى
    ''' </summary>
    ''' <param name="amount">المبلغ</param>
    ''' <param name="targetCurrency">العملة المستهدفة</param>
    ''' <returns>المبلغ بالعملة المستهدفة</returns>
    Public Function ConvertTo(amount As Decimal, targetCurrency As Currency) As Decimal
        If Me.Code = targetCurrency.Code Then
            Return amount
        End If
        
        ' تحويل إلى العملة الأساسية أولاً
        Dim baseAmount As Decimal = ConvertToBaseCurrency(amount)
        
        ' ثم تحويل إلى العملة المستهدفة
        Return targetCurrency.ConvertFromBaseCurrency(baseAmount)
    End Function
    
    ''' <summary>
    ''' تحديث سعر الصرف
    ''' </summary>
    ''' <param name="newRate">السعر الجديد</param>
    Public Sub UpdateExchangeRate(newRate As Decimal)
        If newRate > 0 Then
            ExchangeRate = newRate
            UpdatedAt = DateTime.Now
        End If
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(Code) Then
            errors.Add("رمز العملة مطلوب")
        ElseIf Code.Length <> 3 Then
            errors.Add("رمز العملة يجب أن يكون 3 أحرف")
        End If
        
        If String.IsNullOrWhiteSpace(Name) Then
            errors.Add("اسم العملة مطلوب")
        End If
        
        If String.IsNullOrWhiteSpace(Symbol) Then
            errors.Add("رمز العملة للعرض مطلوب")
        End If
        
        If ExchangeRate <= 0 Then
            errors.Add("سعر الصرف يجب أن يكون أكبر من صفر")
        End If
        
        If DecimalPlaces < 0 OrElse DecimalPlaces > 4 Then
            errors.Add("عدد الخانات العشرية يجب أن يكون بين 0 و 4")
        End If
        
        If Not String.IsNullOrWhiteSpace(SymbolPosition) AndAlso Not {"Before", "After"}.Contains(SymbolPosition) Then
            errors.Add("موضع الرمز غير صحيح")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' نسخ العملة
    ''' </summary>
    ''' <returns>نسخة من العملة</returns>
    Public Function Clone() As Currency
        Return New Currency With {
            .Code = Me.Code,
            .Name = Me.Name,
            .Symbol = Me.Symbol,
            .ExchangeRate = Me.ExchangeRate,
            .IsDefault = False, ' النسخة لا تكون افتراضية
            .IsActive = Me.IsActive,
            .DecimalPlaces = Me.DecimalPlaces,
            .SymbolPosition = Me.SymbolPosition,
            .ThousandsSeparator = Me.ThousandsSeparator,
            .DecimalSeparator = Me.DecimalSeparator
        }
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للعملة
    ''' </summary>
    ''' <returns>اسم العملة مع الرمز</returns>
    Public Overrides Function ToString() As String
        Return String.Format("{0} ({1})", Name, Symbol)
    End Function
    
#End Region

End Class
