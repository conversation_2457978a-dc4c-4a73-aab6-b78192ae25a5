<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// جلب معرف المعاملة
$transaction_id = $_GET['id'] ?? null;
if (!$transaction_id || !is_numeric($transaction_id)) {
    header('Location: transactions.php');
    exit();
}

// جلب بيانات المعاملة
try {
    $stmt = $pdo->prepare("
        SELECT t.*, 
               CASE 
                   WHEN t.entity_type = 'customer' THEN c.name
                   WHEN t.entity_type = 'supplier' THEN s.name
               END as entity_name,
               CASE 
                   WHEN t.entity_type = 'customer' THEN c.phone
                   WHEN t.entity_type = 'supplier' THEN s.phone
               END as entity_phone,
               CASE 
                   WHEN t.entity_type = 'customer' THEN c.email
                   WHEN t.entity_type = 'supplier' THEN s.email
               END as entity_email,
               CASE 
                   WHEN t.entity_type = 'customer' THEN c.address
                   WHEN t.entity_type = 'supplier' THEN s.address
               END as entity_address,
               cb.name as cash_box_name,
               u.full_name as created_by_name
        FROM transactions t
        LEFT JOIN customers c ON t.entity_type = 'customer' AND t.entity_id = c.id
        LEFT JOIN suppliers s ON t.entity_type = 'supplier' AND t.entity_id = s.id
        LEFT JOIN cash_boxes cb ON t.cash_box_id = cb.id
        LEFT JOIN users u ON t.created_by = u.id
        WHERE t.id = ?
    ");
    $stmt->execute([$transaction_id]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        header('Location: transactions.php');
        exit();
    }
} catch (PDOException $e) {
    header('Location: transactions.php');
    exit();
}

// جلب إعدادات الشركة
$company_settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $company_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("خطأ جلب إعدادات الشركة: " . $e->getMessage());
}

// الإعدادات الافتراضية
$default_settings = [
    'company_name' => 'شركة إدارة الديون',
    'company_phone' => '',
    'company_email' => '',
    'company_address' => '',
    'company_logo' => ''
];

foreach ($default_settings as $key => $default_value) {
    if (!isset($company_settings[$key])) {
        $company_settings[$key] = $default_value;
    }
}

// رقم الفاتورة
$invoice_number = 'INV-' . str_pad($transaction['id'], 6, '0', STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم <?php echo $invoice_number; ?> - <?php echo htmlspecialchars($company_settings['company_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        @media print {
            .no-print { display: none !important; }
            body { background: white !important; }
            .card { border: 1px solid #000 !important; box-shadow: none !important; }
            .invoice-header { background: #f8f9fa !important; }
            .table th { background: #f8f9fa !important; }
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
        }
        
        .invoice-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ffd700;
        }
        
        .company-logo {
            max-height: 80px;
            max-width: 200px;
        }
        
        .invoice-details {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }
        
        .amount-highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            font-size: 1.25rem;
            font-weight: bold;
        }
        
        .signature-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 2px solid #dee2e6;
        }
        
        .signature-box {
            border: 1px solid #dee2e6;
            height: 80px;
            border-radius: 8px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- أزرار التحكم -->
    <div class="container-fluid no-print py-3 bg-light">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="transactions.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-right me-1"></i>
                        العودة للمعاملات
                    </a>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary me-2">
                            <i class="fas fa-print me-1"></i>
                            طباعة الفاتورة
                        </button>
                        <button onclick="downloadPDF()" class="btn btn-success">
                            <i class="fas fa-download me-1"></i>
                            تحميل PDF
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الفاتورة -->
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="invoice-container">
                    <!-- رأس الفاتورة -->
                    <div class="invoice-header">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <?php if (!empty($company_settings['company_logo']) && file_exists('assets/uploads/' . $company_settings['company_logo'])): ?>
                                        <img src="assets/uploads/<?php echo htmlspecialchars($company_settings['company_logo']); ?>" 
                                             alt="شعار الشركة" class="company-logo me-3">
                                    <?php endif; ?>
                                    <div>
                                        <h2 class="mb-1"><?php echo htmlspecialchars($company_settings['company_name']); ?></h2>
                                        <?php if (!empty($company_settings['company_phone'])): ?>
                                            <p class="mb-1"><i class="fas fa-phone me-2"></i><?php echo htmlspecialchars($company_settings['company_phone']); ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($company_settings['company_email'])): ?>
                                            <p class="mb-1"><i class="fas fa-envelope me-2"></i><?php echo htmlspecialchars($company_settings['company_email']); ?></p>
                                        <?php endif; ?>
                                        <?php if (!empty($company_settings['company_address'])): ?>
                                            <p class="mb-0"><i class="fas fa-map-marker-alt me-2"></i><?php echo htmlspecialchars($company_settings['company_address']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="invoice-number"><?php echo $invoice_number; ?></div>
                                <p class="mb-0">فاتورة معاملة مالية</p>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل الفاتورة -->
                    <div class="p-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="invoice-details">
                                    <h5 class="text-primary mb-3">
                                        <i class="fas fa-<?php echo $transaction['entity_type'] === 'customer' ? 'user' : 'truck'; ?> me-2"></i>
                                        بيانات <?php echo $transaction['entity_type'] === 'customer' ? 'العميل' : 'المورد'; ?>
                                    </h5>
                                    <p class="mb-2"><strong>الاسم:</strong> <?php echo htmlspecialchars($transaction['entity_name'] ?? 'غير محدد'); ?></p>
                                    <?php if (!empty($transaction['entity_phone'])): ?>
                                        <p class="mb-2"><strong>الهاتف:</strong> <?php echo htmlspecialchars($transaction['entity_phone']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($transaction['entity_email'])): ?>
                                        <p class="mb-2"><strong>البريد:</strong> <?php echo htmlspecialchars($transaction['entity_email']); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($transaction['entity_address'])): ?>
                                        <p class="mb-0"><strong>العنوان:</strong> <?php echo htmlspecialchars($transaction['entity_address']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="invoice-details">
                                    <h5 class="text-success mb-3">
                                        <i class="fas fa-info-circle me-2"></i>
                                        تفاصيل المعاملة
                                    </h5>
                                    <p class="mb-2"><strong>تاريخ المعاملة:</strong> <?php echo date('Y-m-d', strtotime($transaction['transaction_date'])); ?></p>
                                    <p class="mb-2"><strong>وقت الإنشاء:</strong> <?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></p>
                                    <p class="mb-2"><strong>نوع المعاملة:</strong> 
                                        <span class="badge bg-<?php echo $transaction['type'] === 'income' ? 'success' : 'danger'; ?>">
                                            <?php echo $transaction['type'] === 'income' ? 'وارد' : 'صادر'; ?>
                                        </span>
                                    </p>
                                    <p class="mb-2"><strong>الصندوق:</strong> <?php echo htmlspecialchars($transaction['cash_box_name'] ?? 'غير محدد'); ?></p>
                                    <?php if (!empty($transaction['reference_number'])): ?>
                                        <p class="mb-0"><strong>رقم المرجع:</strong> <?php echo htmlspecialchars($transaction['reference_number']); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- جدول المبالغ -->
                        <div class="table-responsive mt-4">
                            <table class="table table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>الوصف</th>
                                        <th class="text-center">العملة</th>
                                        <th class="text-center">سعر الصرف</th>
                                        <th class="text-end">المبلغ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <?php if (!empty($transaction['description'])): ?>
                                                <?php echo htmlspecialchars($transaction['description']); ?>
                                            <?php else: ?>
                                                معاملة <?php echo $transaction['type'] === 'income' ? 'وارد' : 'صادر'; ?> 
                                                - <?php echo $transaction['entity_type'] === 'customer' ? 'عميل' : 'مورد'; ?>: 
                                                <?php echo htmlspecialchars($transaction['entity_name'] ?? 'غير محدد'); ?>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?php echo $transaction['currency']; ?></span>
                                        </td>
                                        <td class="text-center">
                                            <?php echo number_format($transaction['exchange_rate'], 4); ?>
                                        </td>
                                        <td class="text-end">
                                            <strong><?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?></strong>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- المبلغ الإجمالي -->
                        <div class="row">
                            <div class="col-md-6 offset-md-6">
                                <div class="amount-highlight">
                                    <div>المبلغ الإجمالي</div>
                                    <div class="fs-3"><?php echo formatCurrency($transaction['amount'], $transaction['currency']); ?></div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="invoice-details">
                                    <h6 class="text-muted mb-2">معلومات إضافية</h6>
                                    <p class="mb-1"><strong>المستخدم:</strong> <?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير محدد'); ?></p>
                                    <p class="mb-0"><strong>تاريخ الطباعة:</strong> <?php echo date('Y-m-d H:i'); ?></p>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <?php if ($transaction['currency'] === 'USD' && $transaction['exchange_rate'] > 1): ?>
                                <div class="invoice-details">
                                    <h6 class="text-muted mb-2">المبلغ بالدينار العراقي</h6>
                                    <p class="mb-0 fs-5 text-success">
                                        <strong><?php echo formatCurrency($transaction['amount'] * $transaction['exchange_rate'], 'IQD'); ?></strong>
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- التوقيعات -->
                        <div class="signature-section">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="signature-box mb-2"></div>
                                        <p class="mb-0"><strong>توقيع المحاسب</strong></p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="signature-box mb-2"></div>
                                        <p class="mb-0"><strong>توقيع <?php echo $transaction['entity_type'] === 'customer' ? 'العميل' : 'المورد'; ?></strong></p>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-center">
                                        <div class="signature-box mb-2"></div>
                                        <p class="mb-0"><strong>ختم الشركة</strong></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تذييل الفاتورة -->
                        <div class="text-center mt-4 pt-3 border-top">
                            <p class="text-muted mb-0">
                                <small>تم إنشاء هذه الفاتورة تلقائياً بواسطة نظام إدارة الديون</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <script>
        function downloadPDF() {
            const { jsPDF } = window.jspdf;
            const invoice = document.querySelector('.invoice-container');
            
            html2canvas(invoice, {
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                
                let position = 0;
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }
                
                pdf.save('<?php echo $invoice_number; ?>.pdf');
            });
        }
    </script>
</body>
</html>
