<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة تسجيل الحضور
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] == 'check_in') {
            $employeeId = (int)$_POST['employee_id'];
            $attendanceDate = $_POST['attendance_date'] ?? date('Y-m-d');
            $checkInTime = $_POST['check_in_time'] ?? date('H:i:s');

            // التحقق من عدم وجود تسجيل مسبق لنفس اليوم
            $existing = $db->fetchOne("SELECT id FROM attendance WHERE employee_id = ? AND attendance_date = ?", [$employeeId, $attendanceDate]);

            if ($existing) {
                throw new Exception("تم تسجيل الحضور مسبقاً لهذا اليوم");
            }

            $attendanceData = [
                'employee_id' => $employeeId,
                'attendance_date' => $attendanceDate,
                'check_in_time' => $checkInTime,
                'status' => 'present',
                'created_by' => $user['id']
            ];

            $db->insert('attendance', $attendanceData);
            $success = "تم تسجيل الحضور بنجاح";

        } elseif ($_POST['action'] == 'check_out') {
            $attendanceId = (int)$_POST['attendance_id'];
            $checkOutTime = $_POST['check_out_time'] ?? date('H:i:s');
            $breakDuration = (int)($_POST['break_duration'] ?? 0);

            // حساب إجمالي ساعات العمل
            $attendance = $db->fetchOne("SELECT * FROM attendance WHERE id = ?", [$attendanceId]);
            if ($attendance) {
                $checkIn = new DateTime($attendance['attendance_date'] . ' ' . $attendance['check_in_time']);
                $checkOut = new DateTime($attendance['attendance_date'] . ' ' . $checkOutTime);
                $interval = $checkOut->diff($checkIn);
                $totalMinutes = ($interval->h * 60) + $interval->i - $breakDuration;
                $totalHours = $totalMinutes / 60;

                // حساب ساعات العمل الإضافي (أكثر من 8 ساعات)
                $overtimeHours = max(0, $totalHours - 8);

                $updateData = [
                    'check_out_time' => $checkOutTime,
                    'break_duration' => $breakDuration,
                    'total_hours' => round($totalHours, 2),
                    'overtime_hours' => round($overtimeHours, 2)
                ];

                $db->update('attendance', $updateData, 'id = ?', [$attendanceId]);
                $success = "تم تسجيل الانصراف بنجاح";
            }

        } elseif ($_POST['action'] == 'mark_absent') {
            $employeeId = (int)$_POST['employee_id'];
            $attendanceDate = $_POST['attendance_date'];
            $notes = Helper::cleanInput($_POST['notes'] ?? '');

            $attendanceData = [
                'employee_id' => $employeeId,
                'attendance_date' => $attendanceDate,
                'status' => 'absent',
                'notes' => $notes,
                'created_by' => $user['id']
            ];

            $db->insert('attendance', $attendanceData);
            $success = "تم تسجيل الغياب";
        }

    } catch (Exception $e) {
        $error = "خطأ: " . $e->getMessage();
    }
}

// فلاتر البحث
$selectedDate = $_GET['date'] ?? date('Y-m-d');
$departmentFilter = $_GET['department'] ?? '';
$statusFilter = $_GET['status'] ?? '';

// بناء استعلام الحضور
$whereConditions = ["a.attendance_date = ?"];
$params = [$selectedDate];

if ($departmentFilter) {
    $whereConditions[] = "e.department_id = ?";
    $params[] = $departmentFilter;
}

if ($statusFilter) {
    $whereConditions[] = "a.status = ?";
    $params[] = $statusFilter;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب بيانات الحضور
$attendanceRecords = $db->fetchAll("
    SELECT a.*, e.full_name, e.employee_code, d.name as department_name,
           e.hourly_rate, e.overtime_rate
    FROM attendance a
    JOIN employees e ON a.employee_id = e.id
    LEFT JOIN departments d ON e.department_id = d.id
    WHERE $whereClause
    ORDER BY e.full_name
", $params);

// جلب الموظفين الذين لم يسجلوا حضور اليوم
$absentEmployees = $db->fetchAll("
    SELECT e.id, e.full_name, e.employee_code, d.name as department_name
    FROM employees e
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN attendance a ON e.id = a.employee_id AND a.attendance_date = ?
    WHERE e.status = 'active' AND a.id IS NULL
    " . ($departmentFilter ? " AND e.department_id = ?" : "") . "
    ORDER BY e.full_name
", $departmentFilter ? [$selectedDate, $departmentFilter] : [$selectedDate]);

// جلب الأقسام
$departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");

// إحصائيات اليوم
$todayStats = [
    'present' => $db->fetchOne("SELECT COUNT(*) as count FROM attendance WHERE attendance_date = ? AND status = 'present'", [$selectedDate])['count'],
    'absent' => $db->fetchOne("SELECT COUNT(*) as count FROM attendance WHERE attendance_date = ? AND status = 'absent'", [$selectedDate])['count'],
    'late' => $db->fetchOne("SELECT COUNT(*) as count FROM attendance WHERE attendance_date = ? AND status = 'late'", [$selectedDate])['count'],
    'total_employees' => $db->fetchOne("SELECT COUNT(*) as count FROM employees WHERE status = 'active'")['count']
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحضور والانصراف - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stats-card.present { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .stats-card.absent { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); }
        .stats-card.late { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .stats-card.total { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .attendance-row {
            transition: all 0.3s ease;
        }
        .attendance-row:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .time-display {
            font-family: monospace;
            font-size: 1.1rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-clock me-3"></i>الحضور والانصراف</h1>
                    <p class="mb-0">إدارة حضور وانصراف الموظفين</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="employees.php" class="btn btn-light me-2">
                        <i class="fas fa-users me-2"></i>الموظفين
                    </a>
                    <a href="payroll.php" class="btn btn-outline-light">
                        <i class="fas fa-money-bill me-2"></i>الرواتب
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات اليوم -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card present">
                    <h3><?= $todayStats['present'] ?></h3>
                    <p class="mb-0">حاضر</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card absent">
                    <h3><?= $todayStats['absent'] ?></h3>
                    <p class="mb-0">غائب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card late">
                    <h3><?= $todayStats['late'] ?></h3>
                    <p class="mb-0">متأخر</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card total">
                    <h3><?= $todayStats['total_employees'] ?></h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>الفلاتر</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">التاريخ</label>
                                <input type="date" name="date" class="form-control"
                                       value="<?= htmlspecialchars($selectedDate) ?>" onchange="this.form.submit()">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select name="department" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>" <?= $departmentFilter == $dept['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="present" <?= $statusFilter == 'present' ? 'selected' : '' ?>>حاضر</option>
                                    <option value="absent" <?= $statusFilter == 'absent' ? 'selected' : '' ?>>غائب</option>
                                    <option value="late" <?= $statusFilter == 'late' ? 'selected' : '' ?>>متأخر</option>
                                    <option value="half_day" <?= $statusFilter == 'half_day' ? 'selected' : '' ?>>نصف يوم</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- تسجيل حضور سريع -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>تسجيل سريع</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="check_in">
                            <div class="mb-3">
                                <label class="form-label">الموظف</label>
                                <select name="employee_id" class="form-select" required>
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($absentEmployees as $emp): ?>
                                        <option value="<?= $emp['id'] ?>">
                                            <?= htmlspecialchars($emp['employee_code'] . ' - ' . $emp['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">وقت الحضور</label>
                                <input type="time" name="check_in_time" class="form-control"
                                       value="<?= date('H:i') ?>" required>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-clock me-2"></i>تسجيل الحضور
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة الحضور -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>سجل الحضور - <?= date('d/m/Y', strtotime($selectedDate)) ?></h5>
                            <a href="attendance_report.php?date=<?= $selectedDate ?>" class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i>تصدير
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($attendanceRecords)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                                <h5>لا توجد سجلات حضور لهذا التاريخ</h5>
                                <p class="text-muted">ابدأ بتسجيل حضور الموظفين</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الموظف</th>
                                            <th>القسم</th>
                                            <th>وقت الحضور</th>
                                            <th>وقت الانصراف</th>
                                            <th>إجمالي الساعات</th>
                                            <th>ساعات إضافية</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendanceRecords as $record): ?>
                                            <tr class="attendance-row">
                                                <td>
                                                    <strong><?= htmlspecialchars($record['full_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($record['employee_code']) ?></small>
                                                </td>
                                                <td><?= htmlspecialchars($record['department_name']) ?></td>
                                                <td>
                                                    <?php if ($record['check_in_time']): ?>
                                                        <span class="time-display text-success">
                                                            <?= date('H:i', strtotime($record['check_in_time'])) ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['check_out_time']): ?>
                                                        <span class="time-display text-danger">
                                                            <?= date('H:i', strtotime($record['check_out_time'])) ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لم ينصرف</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['total_hours']): ?>
                                                        <span class="badge bg-info"><?= $record['total_hours'] ?> ساعة</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($record['overtime_hours'] > 0): ?>
                                                        <span class="badge bg-warning"><?= $record['overtime_hours'] ?> ساعة</span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'present' => 'success',
                                                        'absent' => 'danger',
                                                        'late' => 'warning',
                                                        'half_day' => 'info',
                                                        'holiday' => 'secondary'
                                                    ];
                                                    $statusNames = [
                                                        'present' => 'حاضر',
                                                        'absent' => 'غائب',
                                                        'late' => 'متأخر',
                                                        'half_day' => 'نصف يوم',
                                                        'holiday' => 'إجازة'
                                                    ];
                                                    ?>
                                                    <span class="status-badge bg-<?= $statusColors[$record['status']] ?>">
                                                        <?= $statusNames[$record['status']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!$record['check_out_time'] && $record['status'] == 'present'): ?>
                                                        <button class="btn btn-sm btn-outline-danger"
                                                                onclick="checkOut(<?= $record['id'] ?>, '<?= $record['full_name'] ?>')">
                                                            <i class="fas fa-sign-out-alt"></i> انصراف
                                                        </button>
                                                    <?php endif; ?>
                                                    <a href="edit_attendance.php?id=<?= $record['id'] ?>"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- الموظفين الغائبين -->
                <?php if (!empty($absentEmployees)): ?>
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>الموظفين الذين لم يسجلوا حضور (<?= count($absentEmployees) ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($absentEmployees as $emp): ?>
                                    <div class="col-md-4 mb-2">
                                        <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                                            <div>
                                                <strong><?= htmlspecialchars($emp['full_name']) ?></strong>
                                                <br><small class="text-muted"><?= htmlspecialchars($emp['employee_code']) ?></small>
                                            </div>
                                            <button class="btn btn-sm btn-outline-danger"
                                                    onclick="markAbsent(<?= $emp['id'] ?>, '<?= htmlspecialchars($emp['full_name']) ?>')">
                                                <i class="fas fa-times"></i> غائب
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal للانصراف -->
    <div class="modal fade" id="checkOutModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل الانصراف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="check_out">
                        <input type="hidden" name="attendance_id" id="attendance_id">

                        <div class="mb-3">
                            <label class="form-label">الموظف</label>
                            <input type="text" id="employee_name" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">وقت الانصراف</label>
                            <input type="time" name="check_out_time" class="form-control"
                                   value="<?= date('H:i') ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">مدة الاستراحة (بالدقائق)</label>
                            <input type="number" name="break_duration" class="form-control"
                                   value="60" min="0" max="480">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">تسجيل الانصراف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal لتسجيل الغياب -->
    <div class="modal fade" id="markAbsentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل الغياب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="mark_absent">
                        <input type="hidden" name="employee_id" id="absent_employee_id">
                        <input type="hidden" name="attendance_date" value="<?= $selectedDate ?>">

                        <div class="mb-3">
                            <label class="form-label">الموظف</label>
                            <input type="text" id="absent_employee_name" class="form-control" readonly>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3"
                                      placeholder="سبب الغياب (اختياري)"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">تسجيل الغياب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function checkOut(attendanceId, employeeName) {
            document.getElementById('attendance_id').value = attendanceId;
            document.getElementById('employee_name').value = employeeName;
            new bootstrap.Modal(document.getElementById('checkOutModal')).show();
        }

        function markAbsent(employeeId, employeeName) {
            document.getElementById('absent_employee_id').value = employeeId;
            document.getElementById('absent_employee_name').value = employeeName;
            new bootstrap.Modal(document.getElementById('markAbsentModal')).show();
        }
    </script>
</body>
</html>
