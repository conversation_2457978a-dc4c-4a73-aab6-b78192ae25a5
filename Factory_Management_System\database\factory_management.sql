-- نظام إدارة المعامل المتكامل - قاعدة البيانات
-- Factory Management System Database

CREATE DATABASE IF NOT EXISTS factory_management 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE factory_management;

-- ===================================
-- 1. جدول العملات
-- ===================================
CREATE TABLE currencies (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(3) NOT NULL UNIQUE,
    name VARCHAR(50) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    is_base BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================
-- 2. جدول المستخدمين والصلاحيات
-- ===================================
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'manager', 'accountant', 'warehouse', 'production', 'sales') NOT NULL,
    permissions JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ===================================
-- 3. جدول الموظفين
-- ===================================
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_code VARCHAR(20) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    department ENUM('production', 'warehouse', 'sales', 'admin', 'maintenance') NOT NULL,
    position VARCHAR(50),
    salary DECIMAL(10,2) DEFAULT 0.00,
    currency_id INT,
    hire_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- ===================================
-- 4. جدول المخازن
-- ===================================
CREATE TABLE warehouses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    manager_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (manager_id) REFERENCES employees(id)
);

-- ===================================
-- 5. جدول فئات الأصناف
-- ===================================
CREATE TABLE item_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES item_categories(id)
);

-- ===================================
-- 6. جدول الأصناف
-- ===================================
CREATE TABLE items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    category_id INT,
    unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
    type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
    cost_price DECIMAL(10,2) DEFAULT 0.00,
    selling_price DECIMAL(10,2) DEFAULT 0.00,
    currency_id INT,
    min_stock_level INT DEFAULT 0,
    max_stock_level INT DEFAULT 0,
    reorder_level INT DEFAULT 0,
    barcode VARCHAR(100),
    image_path VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES item_categories(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- ===================================
-- 7. جدول مخزون الأصناف
-- ===================================
CREATE TABLE item_stock (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    warehouse_id INT NOT NULL,
    quantity DECIMAL(10,3) DEFAULT 0.000,
    reserved_quantity DECIMAL(10,3) DEFAULT 0.000,
    available_quantity DECIMAL(10,3) GENERATED ALWAYS AS (quantity - reserved_quantity) STORED,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    UNIQUE KEY unique_item_warehouse (item_id, warehouse_id)
);

-- ===================================
-- 8. جدول العملاء
-- ===================================
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    tax_number VARCHAR(50),
    credit_limit DECIMAL(12,2) DEFAULT 0.00,
    currency_id INT,
    payment_terms INT DEFAULT 0, -- أيام السداد
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- ===================================
-- 9. جدول الموردين
-- ===================================
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(200) NOT NULL,
    type ENUM('individual', 'company', 'institution') DEFAULT 'company',
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    tax_number VARCHAR(50),
    payment_terms INT DEFAULT 0,
    currency_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- ===================================
-- 10. جدول الصناديق
-- ===================================
CREATE TABLE cash_boxes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    type ENUM('cash', 'bank', 'digital') DEFAULT 'cash',
    currency_id INT NOT NULL,
    initial_balance DECIMAL(12,2) DEFAULT 0.00,
    current_balance DECIMAL(12,2) DEFAULT 0.00,
    bank_name VARCHAR(100),
    account_number VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id)
);

-- ===================================
-- 11. جدول فواتير المبيعات
-- ===================================
CREATE TABLE sales_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    currency_id INT NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    subtotal DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    paid_amount DECIMAL(12,2) DEFAULT 0.00,
    remaining_amount DECIMAL(12,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- 12. جدول تفاصيل فواتير المبيعات
-- ===================================
CREATE TABLE sales_invoice_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    item_id INT NOT NULL,
    warehouse_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED,
    FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
);

-- ===================================
-- 13. جدول فواتير المشتريات
-- ===================================
CREATE TABLE purchase_invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    supplier_id INT NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE,
    currency_id INT NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    subtotal DECIMAL(12,2) DEFAULT 0.00,
    discount_amount DECIMAL(12,2) DEFAULT 0.00,
    tax_amount DECIMAL(12,2) DEFAULT 0.00,
    total_amount DECIMAL(12,2) DEFAULT 0.00,
    paid_amount DECIMAL(12,2) DEFAULT 0.00,
    remaining_amount DECIMAL(12,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    status ENUM('draft', 'confirmed', 'received', 'paid', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- 14. جدول تفاصيل فواتير المشتريات
-- ===================================
CREATE TABLE purchase_invoice_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    item_id INT NOT NULL,
    warehouse_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_cost - discount_amount) STORED,
    FOREIGN KEY (invoice_id) REFERENCES purchase_invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
);

-- ===================================
-- 15. جدول أوامر الإنتاج
-- ===================================
CREATE TABLE production_orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    product_id INT NOT NULL,
    quantity_to_produce DECIMAL(10,3) NOT NULL,
    quantity_produced DECIMAL(10,3) DEFAULT 0.000,
    warehouse_id INT NOT NULL,
    start_date DATE,
    expected_end_date DATE,
    actual_end_date DATE,
    status ENUM('planned', 'in_progress', 'completed', 'cancelled') DEFAULT 'planned',
    total_cost DECIMAL(12,2) DEFAULT 0.00,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (product_id) REFERENCES items(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- 16. جدول مكونات الإنتاج (وصفة الإنتاج)
-- ===================================
CREATE TABLE production_recipes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    product_id INT NOT NULL,
    material_id INT NOT NULL,
    quantity_required DECIMAL(10,3) NOT NULL,
    cost_per_unit DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (product_id) REFERENCES items(id),
    FOREIGN KEY (material_id) REFERENCES items(id),
    UNIQUE KEY unique_product_material (product_id, material_id)
);

-- ===================================
-- 17. جدول تفاصيل أوامر الإنتاج
-- ===================================
CREATE TABLE production_order_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    material_id INT NOT NULL,
    quantity_required DECIMAL(10,3) NOT NULL,
    quantity_consumed DECIMAL(10,3) DEFAULT 0.000,
    unit_cost DECIMAL(10,2) DEFAULT 0.00,
    total_cost DECIMAL(12,2) GENERATED ALWAYS AS (quantity_consumed * unit_cost) STORED,
    FOREIGN KEY (order_id) REFERENCES production_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (material_id) REFERENCES items(id)
);

-- ===================================
-- 18. جدول المصروفات
-- ===================================
CREATE TABLE expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    expense_number VARCHAR(50) NOT NULL UNIQUE,
    category ENUM('operational', 'administrative', 'production', 'maintenance', 'other') NOT NULL,
    description VARCHAR(500) NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    currency_id INT NOT NULL,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    expense_date DATE NOT NULL,
    cash_box_id INT,
    supplier_id INT,
    department ENUM('production', 'warehouse', 'sales', 'admin', 'maintenance'),
    receipt_number VARCHAR(100),
    attachment_path VARCHAR(255),
    is_approved BOOLEAN DEFAULT FALSE,
    approved_by INT,
    approved_at TIMESTAMP NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (currency_id) REFERENCES currencies(id),
    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (approved_by) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- 19. جدول حركات الصندوق
-- ===================================
CREATE TABLE cash_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_number VARCHAR(50) NOT NULL UNIQUE,
    cash_box_id INT NOT NULL,
    type ENUM('income', 'expense', 'transfer_in', 'transfer_out') NOT NULL,
    amount DECIMAL(12,2) NOT NULL,
    description VARCHAR(500),
    reference_type ENUM('sales_invoice', 'purchase_invoice', 'expense', 'salary', 'transfer', 'other'),
    reference_id INT,
    transaction_date DATE NOT NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- 20. جدول حركات المخزون
-- ===================================
CREATE TABLE stock_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_id INT NOT NULL,
    warehouse_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'transfer_in', 'transfer_out', 'adjustment') NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,2) DEFAULT 0.00,
    reference_type ENUM('sales_invoice', 'purchase_invoice', 'production_order', 'adjustment', 'transfer'),
    reference_id INT,
    movement_date DATE NOT NULL,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (item_id) REFERENCES items(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- ===================================
-- إدراج البيانات الأساسية
-- ===================================

-- العملات الأساسية
INSERT INTO currencies (code, name, symbol, exchange_rate, is_base) VALUES
('IQD', 'دينار عراقي', 'د.ع', 1.0000, TRUE),
('USD', 'دولار أمريكي', '$', 1320.0000, FALSE),
('EUR', 'يورو', '€', 1450.0000, FALSE);

-- المستخدم الافتراضي
INSERT INTO users (username, email, password_hash, full_name, role, permissions) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', '{"all": true}');

-- المخزن الرئيسي
INSERT INTO warehouses (name, location) VALUES
('المخزن الرئيسي', 'الطابق الأرضي - المبنى الرئيسي'),
('مخزن المواد الخام', 'الطابق الأول - المبنى الإنتاجي'),
('مخزن المنتجات النهائية', 'الطابق الثاني - المبنى الرئيسي');

-- فئات الأصناف
INSERT INTO item_categories (name, description) VALUES
('مواد خام', 'المواد الأولية المستخدمة في الإنتاج'),
('منتجات نهائية', 'المنتجات الجاهزة للبيع'),
('منتجات نصف مصنعة', 'منتجات في مراحل الإنتاج'),
('مواد استهلاكية', 'مواد مساعدة في العملية الإنتاجية');

-- الصندوق الرئيسي
INSERT INTO cash_boxes (name, type, currency_id, initial_balance, current_balance) VALUES
('الصندوق الرئيسي', 'cash', 1, 1000000.00, 1000000.00),
('حساب البنك - دولار', 'bank', 2, 5000.00, 5000.00);
