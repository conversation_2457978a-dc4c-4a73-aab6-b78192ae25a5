<?php
/**
 * ملف إعداد قاعدة البيانات
 * نظام إدارة الديون
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إعداد نظام إدارة الديون</title>";
echo "<link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>";
echo "<link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>";
echo "</head>";
echo "<body class='bg-light'>";

echo "<div class='container mt-5'>";
echo "<div class='row justify-content-center'>";
echo "<div class='col-md-8'>";

echo "<div class='card shadow'>";
echo "<div class='card-header bg-primary text-white text-center'>";
echo "<h3><i class='fas fa-database me-2'></i>إعداد نظام إدارة الديون</h3>";
echo "</div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-info'>";
    echo "<i class='fas fa-info-circle me-2'></i>";
    echo "جاري إعداد قاعدة البيانات...";
    echo "</div>";

    // إنشاء قاعدة البيانات والجداول
    $result = Database::createDatabase();
    
    if ($result) {
        echo "<div class='alert alert-success'>";
        echo "<i class='fas fa-check-circle me-2'></i>";
        echo "تم إنشاء قاعدة البيانات والجداول بنجاح!";
        echo "</div>";
        
        echo "<div class='alert alert-warning'>";
        echo "<h5><i class='fas fa-key me-2'></i>بيانات تسجيل الدخول الافتراضية:</h5>";
        echo "<p><strong>اسم المستخدم:</strong> admin</p>";
        echo "<p><strong>كلمة المرور:</strong> admin123</p>";
        echo "<p class='mb-0'><small class='text-muted'>يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول</small></p>";
        echo "</div>";
        
        echo "<div class='text-center'>";
        echo "<a href='login.php' class='btn btn-primary btn-lg'>";
        echo "<i class='fas fa-sign-in-alt me-2'></i>";
        echo "الانتقال إلى صفحة تسجيل الدخول";
        echo "</a>";
        echo "</div>";
        
        // إنشاء ملف .htaccess للحماية
        $htaccess_content = "# حماية ملفات النظام\n";
        $htaccess_content .= "<Files \"*.php\">\n";
        $htaccess_content .= "    Order Allow,Deny\n";
        $htaccess_content .= "    Allow from all\n";
        $htaccess_content .= "</Files>\n\n";
        $htaccess_content .= "<Files \"config/*\">\n";
        $htaccess_content .= "    Order Deny,Allow\n";
        $htaccess_content .= "    Deny from all\n";
        $htaccess_content .= "</Files>\n\n";
        $htaccess_content .= "<Files \"logs/*\">\n";
        $htaccess_content .= "    Order Deny,Allow\n";
        $htaccess_content .= "    Deny from all\n";
        $htaccess_content .= "</Files>\n\n";
        $htaccess_content .= "# إعادة كتابة URLs\n";
        $htaccess_content .= "RewriteEngine On\n";
        $htaccess_content .= "RewriteCond %{REQUEST_FILENAME} !-f\n";
        $htaccess_content .= "RewriteCond %{REQUEST_FILENAME} !-d\n";
        
        file_put_contents('.htaccess', $htaccess_content);
        
        echo "<div class='mt-3 alert alert-info'>";
        echo "<i class='fas fa-shield-alt me-2'></i>";
        echo "تم إنشاء ملف الحماية .htaccess";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<i class='fas fa-exclamation-triangle me-2'></i>";
        echo "حدث خطأ أثناء إعداد قاعدة البيانات!";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>";
    echo "<i class='fas fa-exclamation-triangle me-2'></i>";
    echo "خطأ: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    
    echo "<div class='alert alert-info'>";
    echo "<h5>تحقق من الإعدادات التالية:</h5>";
    echo "<ul>";
    echo "<li>تأكد من تشغيل خادم MySQL</li>";
    echo "<li>تحقق من بيانات الاتصال في ملف config/database.php</li>";
    echo "<li>تأكد من وجود صلاحيات إنشاء قواعد البيانات</li>";
    echo "</ul>";
    echo "</div>";
}

echo "</div>"; // card-body
echo "</div>"; // card

echo "<div class='mt-4'>";
echo "<div class='card'>";
echo "<div class='card-header'>";
echo "<h5><i class='fas fa-info-circle me-2'></i>معلومات النظام</h5>";
echo "</div>";
echo "<div class='card-body'>";
echo "<div class='row'>";

echo "<div class='col-md-6'>";
echo "<h6>الميزات الرئيسية:</h6>";
echo "<ul>";
echo "<li>إدارة العملاء والموردين</li>";
echo "<li>تسجيل المعاملات المالية</li>";
echo "<li>إدارة الصناديق المتعددة</li>";
echo "<li>دعم عملتين (دينار ودولار)</li>";
echo "<li>تقارير مفصلة</li>";
echo "<li>نظام صلاحيات المستخدمين</li>";
echo "</ul>";
echo "</div>";

echo "<div class='col-md-6'>";
echo "<h6>المتطلبات التقنية:</h6>";
echo "<ul>";
echo "<li>PHP 7.4 أو أحدث</li>";
echo "<li>MySQL 5.7 أو أحدث</li>";
echo "<li>Apache/Nginx</li>";
echo "<li>PDO Extension</li>";
echo "<li>JSON Extension</li>";
echo "</ul>";
echo "</div>";

echo "</div>"; // row
echo "</div>"; // card-body
echo "</div>"; // card
echo "</div>"; // mt-4

echo "</div>"; // col-md-8
echo "</div>"; // row
echo "</div>"; // container

echo "<footer class='text-center mt-5 py-3 bg-dark text-white'>";
echo "<p class='mb-0'>&copy; 2024 نظام إدارة الديون - جميع الحقوق محفوظة</p>";
echo "</footer>";

echo "</body>";
echo "</html>";
?>
