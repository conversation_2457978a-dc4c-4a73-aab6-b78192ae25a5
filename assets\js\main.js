/**
 * نظام إدارة الديون - ملف JavaScript الرئيسي
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // تفعيل tooltips
    initializeTooltips();
    
    // تفعيل modals
    initializeModals();
    
    // تفعيل التحقق من النماذج
    initializeFormValidation();
    
    // تفعيل الجداول التفاعلية
    initializeTables();
    
    // تفعيل التأثيرات البصرية
    initializeAnimations();
    
    // تفعيل الاختصارات
    initializeKeyboardShortcuts();
}

/**
 * تفعيل tooltips
 */
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل modals
 */
function initializeModals() {
    // إعداد modals للحذف
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.getAttribute('href');
            const itemName = this.getAttribute('data-name') || 'هذا العنصر';
            
            showConfirmDialog(
                'تأكيد الحذف',
                `هل أنت متأكد من حذف ${itemName}؟ لا يمكن التراجع عن هذا الإجراء.`,
                'حذف',
                'إلغاء',
                function() {
                    window.location.href = url;
                }
            );
        });
    });
}

/**
 * تفعيل التحقق من النماذج
 */
function initializeFormValidation() {
    // التحقق من النماذج باستخدام Bootstrap
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
    
    // التحقق المخصص للحقول
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid')) {
                validateField(this);
            }
        });
    });
}

/**
 * التحقق من صحة الحقل
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    
    // إزالة الرسائل السابقة
    field.classList.remove('is-valid', 'is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
    
    let isValid = true;
    let message = '';
    
    // التحقق من الحقول المطلوبة
    if (required && value === '') {
        isValid = false;
        message = 'هذا الحقل مطلوب';
    }
    
    // التحقق من البريد الإلكتروني
    if (type === 'email' && value !== '') {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            isValid = false;
            message = 'البريد الإلكتروني غير صحيح';
        }
    }
    
    // التحقق من الأرقام
    if (type === 'number' && value !== '') {
        if (isNaN(value) || value < 0) {
            isValid = false;
            message = 'يجب إدخال رقم صحيح';
        }
    }
    
    // التحقق من رقم الهاتف
    if (field.name === 'phone' && value !== '') {
        const phoneRegex = /^[0-9+\-\s()]+$/;
        if (!phoneRegex.test(value)) {
            isValid = false;
            message = 'رقم الهاتف غير صحيح';
        }
    }
    
    // عرض النتيجة
    if (isValid) {
        field.classList.add('is-valid');
    } else {
        field.classList.add('is-invalid');
        const feedbackDiv = document.createElement('div');
        feedbackDiv.className = 'invalid-feedback';
        feedbackDiv.textContent = message;
        field.parentNode.appendChild(feedbackDiv);
    }
    
    return isValid;
}

/**
 * تفعيل الجداول التفاعلية
 */
function initializeTables() {
    // البحث في الجداول
    const searchInputs = document.querySelectorAll('.table-search');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const table = document.querySelector(this.getAttribute('data-table'));
            const searchTerm = this.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
    
    // ترتيب الجداول
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            sortTable(this);
        });
    });
}

/**
 * ترتيب الجدول
 */
function sortTable(header) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = !header.classList.contains('sort-asc');
    
    // إزالة فئات الترتيب من جميع الرؤوس
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // إضافة فئة الترتيب للرأس الحالي
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // ترتيب الصفوف
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        // التحقق من كون القيم أرقام
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ? 
                aValue.localeCompare(bValue, 'ar') : 
                bValue.localeCompare(aValue, 'ar');
        }
    });
    
    // إعادة ترتيب الصفوف في الجدول
    rows.forEach(row => tbody.appendChild(row));
}

/**
 * تفعيل التأثيرات البصرية
 */
function initializeAnimations() {
    // تأثير fade-in للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // مراقبة البطاقات والجداول
    document.querySelectorAll('.card, .table').forEach(el => {
        observer.observe(el);
    });
}

/**
 * تفعيل الاختصارات
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('.btn-save, button[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Escape لإغلاق المودال
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    });
}

/**
 * عرض مربع حوار التأكيد
 */
function showConfirmDialog(title, message, confirmText, cancelText, onConfirm) {
    const modalHtml = `
        <div class="modal fade" id="confirmModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                        <button type="button" class="btn btn-danger" id="confirmButton">${confirmText}</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('confirmModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    
    // ربط حدث التأكيد
    document.getElementById('confirmButton').addEventListener('click', function() {
        modal.hide();
        if (onConfirm) {
            onConfirm();
        }
    });
    
    // إزالة المودال بعد الإغلاق
    document.getElementById('confirmModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
    
    modal.show();
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="fas fa-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        const alert = document.querySelector('.alert:last-of-type');
        if (alert) {
            alert.remove();
        }
    }, duration);
}

/**
 * الحصول على أيقونة التنبيه
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number, decimals = 0) {
    return new Intl.NumberFormat('ar-IQ', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'IQD') {
    if (currency === 'USD') {
        return '$' + formatNumber(amount, 2);
    } else {
        return formatNumber(amount, 0) + ' د.ع';
    }
}

/**
 * تحديث الوقت الحالي
 */
function updateCurrentTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = new Date();
    const timeString = now.toLocaleString('ar-IQ');
    
    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);

/**
 * حفظ البيانات في localStorage
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('خطأ في حفظ البيانات:', e);
        return false;
    }
}

/**
 * جلب البيانات من localStorage
 */
function getFromLocalStorage(key, defaultValue = null) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
    } catch (e) {
        console.error('خطأ في جلب البيانات:', e);
        return defaultValue;
    }
}
