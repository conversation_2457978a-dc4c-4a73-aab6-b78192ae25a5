<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // التحقق من عدم وجود كود مكرر
        $existingItem = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$_POST['code']]);
        if ($existingItem) {
            throw new Exception("كود الصنف موجود مسبقاً");
        }
        
        $itemData = [
            'code' => Helper::cleanInput($_POST['code']),
            'name' => Helper::cleanInput($_POST['name']),
            'description' => Helper::cleanInput($_POST['description'] ?? ''),
            'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            'unit' => Helper::cleanInput($_POST['unit']),
            'type' => $_POST['type'],
            'cost_price' => (float)($_POST['cost_price'] ?? 0),
            'selling_price' => (float)($_POST['selling_price'] ?? 0),
            'currency_id' => (int)$_POST['currency_id'],
            'min_stock_level' => (int)($_POST['min_stock_level'] ?? 0),
            'max_stock_level' => (int)($_POST['max_stock_level'] ?? 0),
            'reorder_level' => (int)($_POST['reorder_level'] ?? 0),
            'barcode' => Helper::cleanInput($_POST['barcode'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        $itemId = $db->insert('items', $itemData);
        
        $success = "تم إضافة الصنف بنجاح برقم: " . $itemData['code'];
        
        // إعادة توجيه بعد النجاح
        header("Location: inventory.php?success=" . urlencode($success));
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في إضافة الصنف: " . $e->getMessage();
    }
}

// جلب الفئات
$categories = $db->fetchAll("SELECT id, name FROM item_categories WHERE is_active = 1 ORDER BY name");

// جلب العملات
$currencies = $db->fetchAll("SELECT id, name, symbol FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");

// توليد كود تلقائي
$itemTypes = [
    'raw_material' => 'RAW',
    'finished_product' => 'PROD',
    'semi_finished' => 'SEMI',
    'consumable' => 'CONS'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة صنف جديد - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-secondary {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .info-box {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .type-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .code-preview {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            font-family: monospace;
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>إضافة صنف جديد</h1>
                    <p class="mb-0">إضافة صنف جديد إلى المخزون</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="inventory.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="itemForm">
            <div class="row">
                <div class="col-lg-8">
                    <!-- المعلومات الأساسية -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع الصنف <span class="text-danger">*</span></label>
                                    <select name="type" id="item_type" class="form-select" required onchange="updateCodePreview()">
                                        <option value="">اختر النوع</option>
                                        <option value="raw_material">مادة خام</option>
                                        <option value="finished_product">منتج نهائي</option>
                                        <option value="semi_finished">نصف مصنع</option>
                                        <option value="consumable">مواد استهلاكية</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">كود الصنف <span class="text-danger">*</span></label>
                                    <input type="text" name="code" id="item_code" class="form-control" required 
                                           placeholder="سيتم توليده تلقائياً" onchange="updateCodePreview()">
                                    <small class="text-muted">كود فريد للصنف</small>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <div class="code-preview" id="code_preview">
                                        اختر النوع لتوليد الكود تلقائياً
                                    </div>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">اسم الصنف <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" required 
                                           placeholder="أدخل اسم الصنف">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea name="description" class="form-control" rows="3" 
                                              placeholder="وصف تفصيلي للصنف (اختياري)"></textarea>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>">
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">وحدة القياس <span class="text-danger">*</span></label>
                                    <select name="unit" class="form-select" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="قطعة">قطعة</option>
                                        <option value="كيلو">كيلو</option>
                                        <option value="لتر">لتر</option>
                                        <option value="متر">متر</option>
                                        <option value="علبة">علبة</option>
                                        <option value="كرتون">كرتون</option>
                                        <option value="طن">طن</option>
                                        <option value="جرام">جرام</option>
                                        <option value="مليلتر">مليلتر</option>
                                        <option value="سنتيمتر">سنتيمتر</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الباركود</label>
                                    <input type="text" name="barcode" class="form-control" 
                                           placeholder="رقم الباركود (اختياري)">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            صنف نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأسعار والتكلفة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>الأسعار والتكلفة</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">العملة</label>
                                    <select name="currency_id" class="form-select">
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?= $currency['id'] ?>" <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر التكلفة</label>
                                    <input type="number" name="cost_price" class="form-control" 
                                           step="0.01" min="0" value="0" placeholder="0.00">
                                    <small class="text-muted">سعر الشراء أو التكلفة</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر البيع</label>
                                    <input type="number" name="selling_price" class="form-control" 
                                           step="0.01" min="0" value="0" placeholder="0.00">
                                    <small class="text-muted">سعر البيع للعملاء</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مستويات المخزون -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-warehouse me-2"></i>مستويات المخزون</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأدنى</label>
                                    <input type="number" name="min_stock_level" class="form-control" 
                                           min="0" value="0" placeholder="0">
                                    <small class="text-muted">تنبيه عند الوصول لهذا المستوى</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأقصى</label>
                                    <input type="number" name="max_stock_level" class="form-control" 
                                           min="0" value="0" placeholder="0">
                                    <small class="text-muted">الحد الأقصى للمخزون</small>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">نقطة إعادة الطلب</label>
                                    <input type="number" name="reorder_level" class="form-control" 
                                           min="0" value="0" placeholder="0">
                                    <small class="text-muted">مستوى إعادة الطلب من المورد</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- معلومات مساعدة -->
                    <div class="info-box">
                        <h5><i class="fas fa-lightbulb me-2"></i>نصائح مهمة</h5>
                        <ul class="mb-0">
                            <li>تأكد من اختيار النوع المناسب للصنف</li>
                            <li>الكود يجب أن يكون فريداً</li>
                            <li>حدد وحدة القياس بدقة</li>
                            <li>ضع مستويات المخزون المناسبة</li>
                            <li>تأكد من صحة الأسعار</li>
                        </ul>
                    </div>

                    <!-- أنواع الأصناف -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-tags me-2"></i>أنواع الأصناف</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <span class="type-badge bg-primary">مادة خام</span>
                                <p class="small mt-2 mb-3">المواد الأولية المستخدمة في الإنتاج</p>
                            </div>
                            
                            <div class="mb-3">
                                <span class="type-badge bg-success">منتج نهائي</span>
                                <p class="small mt-2 mb-3">المنتجات الجاهزة للبيع</p>
                            </div>
                            
                            <div class="mb-3">
                                <span class="type-badge bg-warning">نصف مصنع</span>
                                <p class="small mt-2 mb-3">منتجات في مراحل التصنيع</p>
                            </div>
                            
                            <div class="mb-0">
                                <span class="type-badge bg-info">مواد استهلاكية</span>
                                <p class="small mt-2 mb-0">مواد مساعدة ومكتبية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ الصنف
                </button>
                <a href="inventory.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const itemTypes = <?= json_encode($itemTypes) ?>;
        
        function updateCodePreview() {
            const typeSelect = document.getElementById('item_type');
            const codeInput = document.getElementById('item_code');
            const codePreview = document.getElementById('code_preview');
            
            if (typeSelect.value && itemTypes[typeSelect.value]) {
                const prefix = itemTypes[typeSelect.value];
                const timestamp = Date.now().toString().slice(-6);
                const suggestedCode = prefix + timestamp;
                
                if (!codeInput.value) {
                    codeInput.value = suggestedCode;
                }
                
                codePreview.innerHTML = `الكود المقترح: <span style="color: #667eea;">${suggestedCode}</span>`;
            } else {
                codePreview.innerHTML = 'اختر النوع لتوليد الكود تلقائياً';
            }
        }
        
        // تحديث الكود عند تغيير النوع
        document.getElementById('item_type').addEventListener('change', updateCodePreview);
        
        // التحقق من صحة النموذج
        document.getElementById('itemForm').addEventListener('submit', function(e) {
            const costPrice = parseFloat(document.querySelector('input[name="cost_price"]').value) || 0;
            const sellingPrice = parseFloat(document.querySelector('input[name="selling_price"]').value) || 0;
            const minLevel = parseInt(document.querySelector('input[name="min_stock_level"]').value) || 0;
            const maxLevel = parseInt(document.querySelector('input[name="max_stock_level"]').value) || 0;
            const reorderLevel = parseInt(document.querySelector('input[name="reorder_level"]').value) || 0;
            
            // التحقق من منطقية الأسعار
            if (sellingPrice > 0 && costPrice > 0 && sellingPrice < costPrice) {
                if (!confirm('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return;
                }
            }
            
            // التحقق من منطقية مستويات المخزون
            if (maxLevel > 0 && minLevel > maxLevel) {
                alert('الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى');
                e.preventDefault();
                return;
            }
            
            if (reorderLevel > 0 && minLevel > 0 && reorderLevel < minLevel) {
                if (!confirm('نقطة إعادة الطلب أقل من الحد الأدنى. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return;
                }
            }
        });
    </script>
</body>
</html>
