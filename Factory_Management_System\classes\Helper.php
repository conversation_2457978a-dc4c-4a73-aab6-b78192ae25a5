<?php
/**
 * فئة الدوال المساعدة
 * Helper Class
 */
class Helper {
    
    /**
     * تنظيف المدخلات من الأكواد الضارة
     * Clean input from malicious code
     */
    public static function cleanInput($input) {
        if (is_array($input)) {
            return array_map([self::class, 'cleanInput'], $input);
        }
        
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        return $input;
    }
    
    /**
     * تنسيق المبلغ المالي
     * Format money amount
     */
    public static function formatMoney($amount, $currency = 'IQD', $decimals = 2) {
        if (!is_numeric($amount)) {
            $amount = 0;
        }
        
        $formatted = number_format($amount, $decimals, '.', ',');
        
        // رموز العملات
        $currencySymbols = [
            'IQD' => 'د.ع',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'SAR' => 'ر.س',
            'AED' => 'د.إ',
            'KWD' => 'د.ك',
            'QAR' => 'ر.ق',
            'BHD' => 'د.ب',
            'OMR' => 'ر.ع',
            'JOD' => 'د.أ',
            'LBP' => 'ل.ل',
            'EGP' => 'ج.م'
        ];
        
        $symbol = $currencySymbols[$currency] ?? $currency;
        
        return $formatted . ' ' . $symbol;
    }
    
    /**
     * تنسيق التاريخ
     * Format date
     */
    public static function formatDate($date, $format = 'd/m/Y') {
        if (empty($date) || $date == '0000-00-00') {
            return '-';
        }
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $date;
        }
    }
    
    /**
     * تنسيق التاريخ والوقت
     * Format date and time
     */
    public static function formatDateTime($datetime, $format = 'd/m/Y H:i') {
        if (empty($datetime) || $datetime == '0000-00-00 00:00:00') {
            return '-';
        }
        
        try {
            $dateObj = new DateTime($datetime);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return $datetime;
        }
    }
    
    /**
     * تحويل التاريخ من الهجري إلى الميلادي
     * Convert Hijri to Gregorian date
     */
    public static function hijriToGregorian($hijriDate) {
        // هذه دالة مبسطة - يمكن تطويرها لاحقاً
        return $hijriDate;
    }
    
    /**
     * تحويل التاريخ من الميلادي إلى الهجري
     * Convert Gregorian to Hijri date
     */
    public static function gregorianToHijri($gregorianDate) {
        // هذه دالة مبسطة - يمكن تطويرها لاحقاً
        return $gregorianDate;
    }
    
    /**
     * توليد كود عشوائي
     * Generate random code
     */
    public static function generateCode($prefix = '', $length = 6) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $prefix . $code;
    }
    
    /**
     * توليد كلمة مرور عشوائية
     * Generate random password
     */
    public static function generatePassword($length = 8) {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        
        return $password;
    }
    
    /**
     * تشفير كلمة المرور
     * Hash password
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }
    
    /**
     * التحقق من كلمة المرور
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * تحويل الحجم بالبايت إلى وحدة مقروءة
     * Convert bytes to human readable format
     */
    public static function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * تحويل الوقت إلى تنسيق مقروء
     * Convert time to human readable format
     */
    public static function timeAgo($datetime) {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'منذ لحظات';
        if ($time < 3600) return 'منذ ' . floor($time/60) . ' دقيقة';
        if ($time < 86400) return 'منذ ' . floor($time/3600) . ' ساعة';
        if ($time < 2592000) return 'منذ ' . floor($time/86400) . ' يوم';
        if ($time < 31536000) return 'منذ ' . floor($time/2592000) . ' شهر';
        
        return 'منذ ' . floor($time/31536000) . ' سنة';
    }
    
    /**
     * التحقق من صحة البريد الإلكتروني
     * Validate email
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * التحقق من صحة رقم الهاتف
     * Validate phone number
     */
    public static function validatePhone($phone) {
        // إزالة المسافات والرموز
        $phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // التحقق من الطول والتنسيق
        return preg_match('/^(\+964|0)?[0-9]{10}$/', $phone);
    }
    
    /**
     * تحويل الأرقام إلى كلمات (عربي)
     * Convert numbers to words (Arabic)
     */
    public static function numberToWords($number) {
        // هذه دالة مبسطة - يمكن تطويرها لاحقاً
        $ones = [
            '', 'واحد', 'اثنان', 'ثلاثة', 'أربعة', 'خمسة', 'ستة', 'سبعة', 'ثمانية', 'تسعة',
            'عشرة', 'أحد عشر', 'اثنا عشر', 'ثلاثة عشر', 'أربعة عشر', 'خمسة عشر', 'ستة عشر',
            'سبعة عشر', 'ثمانية عشر', 'تسعة عشر'
        ];
        
        $tens = [
            '', '', 'عشرون', 'ثلاثون', 'أربعون', 'خمسون', 'ستون', 'سبعون', 'ثمانون', 'تسعون'
        ];
        
        if ($number < 20) {
            return $ones[$number];
        } elseif ($number < 100) {
            return $tens[intval($number / 10)] . ($number % 10 ? ' ' . $ones[$number % 10] : '');
        }
        
        return (string)$number; // للأرقام الكبيرة
    }
    
    /**
     * ضغط وتحسين الصور
     * Compress and optimize images
     */
    public static function compressImage($source, $destination, $quality = 80) {
        $info = getimagesize($source);
        
        if ($info['mime'] == 'image/jpeg') {
            $image = imagecreatefromjpeg($source);
        } elseif ($info['mime'] == 'image/gif') {
            $image = imagecreatefromgif($source);
        } elseif ($info['mime'] == 'image/png') {
            $image = imagecreatefrompng($source);
        } else {
            return false;
        }
        
        return imagejpeg($image, $destination, $quality);
    }
    
    /**
     * إنشاء رمز QR
     * Generate QR Code
     */
    public static function generateQRCode($text, $size = 200) {
        // يتطلب مكتبة QR Code - هذه دالة مبسطة
        return "https://api.qrserver.com/v1/create-qr-code/?size={$size}x{$size}&data=" . urlencode($text);
    }
    
    /**
     * تسجيل الأحداث في ملف السجل
     * Log events to file
     */
    public static function logEvent($message, $level = 'INFO', $file = 'system.log') {
        $logDir = '../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logFile = $logDir . '/' . $file;
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
        
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * إرسال إشعار
     * Send notification
     */
    public static function sendNotification($title, $message, $type = 'info') {
        // يمكن تطوير هذه الدالة لإرسال إشعارات عبر البريد الإلكتروني أو SMS
        self::logEvent("Notification: $title - $message", 'NOTIFICATION');
        return true;
    }
    
    /**
     * التحقق من الصلاحيات
     * Check permissions
     */
    public static function hasPermission($user, $permission) {
        if (!$user || !isset($user['role'])) {
            return false;
        }
        
        // صلاحيات المدير
        if ($user['role'] === 'admin') {
            return true;
        }
        
        // صلاحيات حسب الدور
        $permissions = [
            'manager' => ['view_reports', 'manage_employees', 'manage_inventory'],
            'accountant' => ['view_financial', 'manage_invoices', 'view_reports'],
            'warehouse' => ['manage_inventory', 'view_stock'],
            'employee' => ['view_profile']
        ];
        
        return in_array($permission, $permissions[$user['role']] ?? []);
    }
    
    /**
     * تحويل النص إلى رابط آمن
     * Convert text to safe URL slug
     */
    public static function createSlug($text) {
        // تحويل النص العربي والإنجليزي إلى رابط آمن
        $text = trim($text);
        $text = preg_replace('/[^a-zA-Z0-9\u0600-\u06FF\s-]/', '', $text);
        $text = preg_replace('/[\s-]+/', '-', $text);
        $text = trim($text, '-');
        
        return strtolower($text);
    }
}

// إضافة الدوال العامة للتوافق مع الكود الموجود
if (!function_exists('cleanInput')) {
    function cleanInput($input) {
        return Helper::cleanInput($input);
    }
}

if (!function_exists('formatMoney')) {
    function formatMoney($amount, $currency = 'IQD', $decimals = 2) {
        return Helper::formatMoney($amount, $currency, $decimals);
    }
}

if (!function_exists('formatDate')) {
    function formatDate($date, $format = 'd/m/Y') {
        return Helper::formatDate($date, $format);
    }
}

if (!function_exists('formatDateTime')) {
    function formatDateTime($datetime, $format = 'd/m/Y H:i') {
        return Helper::formatDateTime($datetime, $format);
    }
}
?>
