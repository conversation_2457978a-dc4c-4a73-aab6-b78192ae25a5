<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// إنشاء جدول الأقسام إذا لم يكن موجوداً
try {
    $db->query("CREATE TABLE IF NOT EXISTS departments (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        code VARCHAR(50) UNIQUE,
        description TEXT,
        manager_id INT,
        parent_id INT,
        budget DECIMAL(15,2) DEFAULT 0.00,
        location VARCHAR(200),
        phone VARCHAR(20),
        email VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL,
        FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )");
} catch (Exception $e) {
    $error = "خطأ في إعداد قاعدة البيانات: " . $e->getMessage();
}

// معالجة إضافة قسم جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_department'])) {
    try {
        $db->beginTransaction();

        // توليد كود القسم
        $prefix = 'DEPT';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM departments");
        $sequence = ($countResult['count'] ?? 0) + 1;
        $deptCode = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);

        $departmentData = [
            'name' => trim($_POST['name']),
            'code' => $deptCode,
            'description' => trim($_POST['description'] ?? ''),
            'manager_id' => !empty($_POST['manager_id']) ? (int)$_POST['manager_id'] : null,
            'parent_id' => !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null,
            'budget' => (float)($_POST['budget'] ?? 0),
            'location' => trim($_POST['location'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'created_by' => $user['id']
        ];

        $db->insert('departments', $departmentData);
        $db->commit();

        $success = "تم إضافة القسم بنجاح";

    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إضافة القسم: " . $e->getMessage();
    }
}

// معالجة تحديث القسم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_department'])) {
    try {
        $deptId = (int)$_POST['dept_id'];

        $updateData = [
            'name' => trim($_POST['name']),
            'description' => trim($_POST['description'] ?? ''),
            'manager_id' => !empty($_POST['manager_id']) ? (int)$_POST['manager_id'] : null,
            'parent_id' => !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null,
            'budget' => (float)($_POST['budget'] ?? 0),
            'location' => trim($_POST['location'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        $db->update('departments', $updateData, ['id' => $deptId]);
        $success = "تم تحديث القسم بنجاح";

    } catch (Exception $e) {
        $error = "خطأ في تحديث القسم: " . $e->getMessage();
    }
}

// معالجة حذف القسم
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_department'])) {
    try {
        $deptId = (int)$_POST['dept_id'];

        // التحقق من وجود موظفين في القسم
        $employeeCount = $db->fetchOne("SELECT COUNT(*) as count FROM employees WHERE department_id = ?", [$deptId]);

        if ($employeeCount['count'] > 0) {
            $error = "لا يمكن حذف القسم لوجود موظفين مرتبطين به";
        } else {
            $db->delete('departments', ['id' => $deptId]);
            $success = "تم حذف القسم بنجاح";
        }

    } catch (Exception $e) {
        $error = "خطأ في حذف القسم: " . $e->getMessage();
    }
}

// جلب الأقسام
try {
    $departments = $db->fetchAll("
        SELECT d.*,
               m.full_name as manager_name,
               p.name as parent_name,
               (SELECT COUNT(*) FROM employees WHERE department_id = d.id AND status = 'active') as employee_count,
               u.full_name as created_by_name
        FROM departments d
        LEFT JOIN employees m ON d.manager_id = m.id
        LEFT JOIN departments p ON d.parent_id = p.id
        LEFT JOIN users u ON d.created_by = u.id
        ORDER BY d.name
    ");

    // جلب الموظفين للمديرين
    $employees = $db->fetchAll("SELECT id, full_name FROM employees WHERE status = 'active' ORDER BY full_name");

    // جلب الأقسام الرئيسية للأقسام الفرعية
    $parentDepartments = $db->fetchAll("SELECT id, name FROM departments WHERE parent_id IS NULL ORDER BY name");

} catch (Exception $e) {
    $departments = [];
    $employees = [];
    $parentDepartments = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }

        .department-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }

        .department-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .department-card.inactive {
            opacity: 0.7;
            border-left-color: #dc3545;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .form-control, .form-select {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1);
            color: white;
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.5);
        }

        .department-hierarchy {
            padding-right: 20px;
            border-right: 3px solid #e9ecef;
        }

        .department-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
        }

        .budget-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
        }

        .employee-count {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .modal-content {
            border-radius: 20px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
        }
    </style>
</head>
<body>
    <div class="main-header animate__animated animate__fadeInDown">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-sitemap fa-2x me-3"></i>إدارة الأقسام</h1>
                    <p class="mb-0 fs-5">تنظيم وإدارة أقسام المعمل والهيكل التنظيمي</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                        <i class="fas fa-plus me-2"></i>إضافة قسم
                    </button>
                    <a href="dashboard.php" class="btn btn-outline-light">
                        <i class="fas fa-home me-2"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success animate__animated animate__fadeInUp">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger animate__animated animate__fadeInUp">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الأقسام -->
        <div class="row animate__animated animate__fadeInUp">
            <?php
            $totalDepartments = count($departments);
            $activeDepartments = count(array_filter($departments, function($d) { return $d['is_active']; }));
            $totalEmployees = array_sum(array_column($departments, 'employee_count'));
            $totalBudget = array_sum(array_column($departments, 'budget'));
            ?>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $totalDepartments ?></div>
                    <div class="text-muted">إجمالي الأقسام</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $activeDepartments ?></div>
                    <div class="text-muted">أقسام نشطة</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $totalEmployees ?></div>
                    <div class="text-muted">إجمالي الموظفين</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= number_format($totalBudget/1000, 0) ?>K</div>
                    <div class="text-muted">إجمالي الميزانية (د.ع)</div>
                </div>
            </div>
        </div>

        <!-- قائمة الأقسام -->
        <div class="card animate__animated animate__fadeInUp">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>قائمة الأقسام</h5>
                    <span class="badge bg-light text-dark"><?= count($departments) ?> قسم</span>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($departments)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-sitemap fa-4x text-muted mb-3"></i>
                        <h5>لا توجد أقسام</h5>
                        <p class="text-muted">ابدأ بإضافة أول قسم في المعمل</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                            <i class="fas fa-plus me-2"></i>إضافة قسم جديد
                        </button>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($departments as $dept): ?>
                            <div class="col-lg-6 col-xl-4">
                                <div class="department-card <?= !$dept['is_active'] ? 'inactive' : '' ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="department-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-0"><?= htmlspecialchars($dept['name']) ?></h6>
                                                <span class="status-badge status-<?= $dept['is_active'] ? 'active' : 'inactive' ?>">
                                                    <?= $dept['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </div>

                                            <p class="text-muted mb-2">
                                                <small><i class="fas fa-code me-1"></i><?= htmlspecialchars($dept['code']) ?></small>
                                            </p>

                                            <?php if ($dept['description']): ?>
                                                <p class="text-muted small mb-2"><?= htmlspecialchars($dept['description']) ?></p>
                                            <?php endif; ?>

                                            <div class="row text-center mb-2">
                                                <div class="col-6">
                                                    <div class="employee-count">
                                                        <i class="fas fa-users me-1"></i>
                                                        <?= $dept['employee_count'] ?> موظف
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="budget-display">
                                                        <strong><?= number_format($dept['budget'], 0) ?></strong>
                                                        <small class="d-block text-muted">د.ع ميزانية</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <?php if ($dept['manager_name']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-user-tie me-1 text-primary"></i>
                                                    <small>المدير: <?= htmlspecialchars($dept['manager_name']) ?></small>
                                                </p>
                                            <?php endif; ?>

                                            <?php if ($dept['parent_name']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-sitemap me-1 text-info"></i>
                                                    <small>تابع لـ: <?= htmlspecialchars($dept['parent_name']) ?></small>
                                                </p>
                                            <?php endif; ?>

                                            <?php if ($dept['location']): ?>
                                                <p class="mb-2">
                                                    <i class="fas fa-map-marker-alt me-1 text-warning"></i>
                                                    <small><?= htmlspecialchars($dept['location']) ?></small>
                                                </p>
                                            <?php endif; ?>

                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?= date('d/m/Y', strtotime($dept['created_at'])) ?>
                                                </small>

                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-outline-primary btn-sm"
                                                            onclick="editDepartment(<?= htmlspecialchars(json_encode($dept)) ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-info btn-sm"
                                                            onclick="viewDepartment(<?= $dept['id'] ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            onclick="deleteDepartment(<?= $dept['id'] ?>, '<?= htmlspecialchars($dept['name']) ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة قسم -->
    <div class="modal fade" id="addDepartmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>إضافة قسم جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم القسم <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">مدير القسم</label>
                                <select name="manager_id" class="form-select">
                                    <option value="">اختر المدير</option>
                                    <?php foreach ($employees as $emp): ?>
                                        <option value="<?= $emp['id'] ?>">
                                            <?= htmlspecialchars($emp['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">القسم الرئيسي</label>
                                <select name="parent_id" class="form-select">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($parentDepartments as $parent): ?>
                                        <option value="<?= $parent['id'] ?>">
                                            <?= htmlspecialchars($parent['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الميزانية (د.ع)</label>
                                <input type="number" name="budget" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع</label>
                                <input type="text" name="location" class="form-control" placeholder="مثال: الطابق الأول">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" name="phone" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control">
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" class="form-control" rows="3"
                                          placeholder="وصف مختصر عن القسم ومهامه"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="submit" name="add_department" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ القسم
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل قسم -->
    <div class="modal fade" id="editDepartmentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>تعديل القسم</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editDepartmentForm">
                    <input type="hidden" name="dept_id" id="edit_dept_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم القسم <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="edit_name" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">مدير القسم</label>
                                <select name="manager_id" id="edit_manager_id" class="form-select">
                                    <option value="">اختر المدير</option>
                                    <?php foreach ($employees as $emp): ?>
                                        <option value="<?= $emp['id'] ?>">
                                            <?= htmlspecialchars($emp['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">القسم الرئيسي</label>
                                <select name="parent_id" id="edit_parent_id" class="form-select">
                                    <option value="">قسم رئيسي</option>
                                    <?php foreach ($parentDepartments as $parent): ?>
                                        <option value="<?= $parent['id'] ?>">
                                            <?= htmlspecialchars($parent['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الميزانية (د.ع)</label>
                                <input type="number" name="budget" id="edit_budget" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموقع</label>
                                <input type="text" name="location" id="edit_location" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" name="phone" id="edit_phone" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" id="edit_email" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                                    <label class="form-check-label">القسم نشط</label>
                                </div>
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="submit" name="update_department" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="deleteDepartmentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-trash me-2"></i>تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                        <h5>هل أنت متأكد من حذف القسم؟</h5>
                        <p class="text-muted">سيتم حذف القسم <strong id="delete_dept_name"></strong> نهائياً</p>
                        <p class="text-danger"><small>تأكد من عدم وجود موظفين مرتبطين بهذا القسم</small></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="dept_id" id="delete_dept_id">
                        <button type="submit" name="delete_department" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>حذف نهائي
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الإجراء العائم -->
    <button class="floating-action" data-bs-toggle="modal" data-bs-target="#addDepartmentModal" title="إضافة قسم جديد">
        <i class="fas fa-plus"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعديل قسم
        function editDepartment(dept) {
            document.getElementById('edit_dept_id').value = dept.id;
            document.getElementById('edit_name').value = dept.name;
            document.getElementById('edit_manager_id').value = dept.manager_id || '';
            document.getElementById('edit_parent_id').value = dept.parent_id || '';
            document.getElementById('edit_budget').value = dept.budget;
            document.getElementById('edit_location').value = dept.location || '';
            document.getElementById('edit_phone').value = dept.phone || '';
            document.getElementById('edit_email').value = dept.email || '';
            document.getElementById('edit_description').value = dept.description || '';
            document.getElementById('edit_is_active').checked = dept.is_active == 1;

            new bootstrap.Modal(document.getElementById('editDepartmentModal')).show();
        }

        // حذف قسم
        function deleteDepartment(deptId, deptName) {
            document.getElementById('delete_dept_id').value = deptId;
            document.getElementById('delete_dept_name').textContent = deptName;

            new bootstrap.Modal(document.getElementById('deleteDepartmentModal')).show();
        }

        // عرض تفاصيل القسم
        function viewDepartment(deptId) {
            // يمكن إضافة صفحة عرض تفاصيل القسم
            window.location.href = 'view_department.php?id=' + deptId;
        }

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك الأرقام في الإحصائيات
            const numbers = document.querySelectorAll('.stats-number');
            numbers.forEach(number => {
                const finalNumber = parseInt(number.textContent.replace('K', ''));
                let currentNumber = 0;
                const increment = finalNumber / 50;

                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        number.textContent = finalNumber + (number.textContent.includes('K') ? 'K' : '');
                        clearInterval(timer);
                    } else {
                        number.textContent = Math.floor(currentNumber) + (number.textContent.includes('K') ? 'K' : '');
                    }
                }, 30);
            });

            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.department-card, .stats-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>