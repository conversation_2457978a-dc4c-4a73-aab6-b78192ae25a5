<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();

        // توليد رقم الفاتورة
        $prefix = 'INV-' . date('Ymd') . '-';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM sales_invoices WHERE invoice_number LIKE ?", [$prefix . '%']);
        $sequence = ($countResult['count'] ?? 0) + 1;
        $invoiceNumber = $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);

        // بيانات الفاتورة الرئيسية
        $invoiceData = [
            'invoice_number' => $invoiceNumber,
            'customer_id' => (int)$_POST['customer_id'],
            'invoice_date' => $_POST['invoice_date'],
            'due_date' => $_POST['due_date'] ?: null,
            'currency_id' => (int)$_POST['currency_id'],
            'exchange_rate' => (float)$_POST['exchange_rate'],
            'discount_amount' => (float)($_POST['discount_amount'] ?? 0),
            'tax_amount' => (float)($_POST['tax_amount'] ?? 0),
            'notes' => Helper::cleanInput($_POST['notes'] ?? ''),
            'status' => 'draft',
            'created_by' => $user['id']
        ];

        // إدراج الفاتورة
        $invoiceId = $db->insert('sales_invoices', $invoiceData);

        // معالجة أصناف الفاتورة
        $subtotal = 0;
        $items = $_POST['items'] ?? [];

        foreach ($items as $item) {
            if (empty($item['item_id']) || empty($item['quantity']) || empty($item['unit_price'])) {
                continue;
            }

            // التحقق من وجود المخزن
            $warehouseId = (int)$item['warehouse_id'];
            if ($warehouseId <= 0) {
                // استخدام المخزن الافتراضي إذا لم يتم تحديد مخزن
                $defaultWarehouse = $db->fetchOne("SELECT id FROM warehouses WHERE is_active = 1 ORDER BY id LIMIT 1");
                $warehouseId = $defaultWarehouse ? $defaultWarehouse['id'] : 1;
            }

            // التحقق من وجود الصنف
            $itemExists = $db->fetchOne("SELECT id FROM items WHERE id = ?", [(int)$item['item_id']]);
            if (!$itemExists) {
                continue; // تخطي الصنف إذا لم يكن موجوداً
            }

            $quantity = (float)$item['quantity'];
            $unitPrice = (float)$item['unit_price'];
            $discountPercentage = (float)($item['discount_percentage'] ?? 0);
            $discountAmount = ($quantity * $unitPrice * $discountPercentage) / 100;
            $lineTotal = ($quantity * $unitPrice) - $discountAmount;

            $itemData = [
                'invoice_id' => $invoiceId,
                'item_id' => (int)$item['item_id'],
                'warehouse_id' => $warehouseId,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'discount_percentage' => $discountPercentage,
                'discount_amount' => $discountAmount
            ];

            $db->insert('sales_invoice_details', $itemData);
            $subtotal += $lineTotal;
        }

        // تحديث إجماليات الفاتورة
        $totalAmount = $subtotal - $invoiceData['discount_amount'] + $invoiceData['tax_amount'];

        $db->update('sales_invoices', [
            'subtotal' => $subtotal,
            'total_amount' => $totalAmount
        ], 'id = ?', [$invoiceId]);

        $db->commit();

        $success = "تم إنشاء فاتورة المبيعات رقم $invoiceNumber بنجاح!";

        // إعادة توجيه بعد النجاح
        header("Location: sales.php?success=" . urlencode($success));
        exit;

    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إنشاء الفاتورة: " . $e->getMessage();
    }
}

// جلب العملاء
$customers = $db->fetchAll("SELECT id, code, name, phone, credit_limit FROM customers WHERE is_active = 1 ORDER BY name");

// جلب العملات
$currencies = $db->fetchAll("SELECT id, code, name, symbol, exchange_rate FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");

// جلب الأصناف
$items = $db->fetchAll("SELECT id, code, name, unit, selling_price, type FROM items WHERE is_active = 1 AND type IN ('finished_product', 'semi_finished') ORDER BY name");

// جلب المخازن
$warehouses = $db->fetchAll("SELECT id, name, location FROM warehouses WHERE is_active = 1 ORDER BY name");

// التحقق من وجود البيانات الأساسية
$hasRequiredData = !empty($customers) && !empty($currencies) && !empty($items) && !empty($warehouses);

// جلب أسعار الصرف الحالية
$exchangeRates = [];
foreach ($currencies as $currency) {
    $exchangeRates[$currency['id']] = $currency['exchange_rate'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات جديدة - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-secondary {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .item-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        .total-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
        }
        .btn-add-item {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .btn-remove-item {
            background: #dc3545;
            border: none;
            border-radius: 8px;
            color: white;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>فاتورة مبيعات جديدة</h1>
                    <p class="mb-0">إنشاء فاتورة مبيعات جديدة للعملاء</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="sales.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <?php if (!$hasRequiredData): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تحذير:</strong> بعض البيانات الأساسية مفقودة:
                <ul class="mb-0 mt-2">
                    <?php if (empty($customers)): ?><li>لا توجد عملاء نشطين</li><?php endif; ?>
                    <?php if (empty($currencies)): ?><li>لا توجد عملات نشطة</li><?php endif; ?>
                    <?php if (empty($items)): ?><li>لا توجد أصناف للبيع</li><?php endif; ?>
                    <?php if (empty($warehouses)): ?><li>لا توجد مخازن نشطة</li><?php endif; ?>
                </ul>
                <div class="mt-3">
                    <a href="fix_sales_constraints.php" class="btn btn-danger">
                        <i class="fas fa-tools me-2"></i>إصلاح المشكلة
                    </a>
                    <a href="setup_sales.php" class="btn btn-warning">
                        <i class="fas fa-cog me-2"></i>إعداد النظام
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <form method="POST" id="salesInvoiceForm">
            <div class="row">
                <div class="col-lg-8">
                    <!-- معلومات الفاتورة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العميل <span class="text-danger">*</span></label>
                                    <select name="customer_id" class="form-select" required>
                                        <option value="">اختر العميل</option>
                                        <?php foreach ($customers as $customer): ?>
                                            <option value="<?= $customer['id'] ?>"
                                                    data-credit="<?= $customer['credit_limit'] ?>">
                                                <?= htmlspecialchars($customer['code'] . ' - ' . $customer['name']) ?>
                                                <?php if ($customer['phone']): ?>
                                                    (<?= htmlspecialchars($customer['phone']) ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العملة <span class="text-danger">*</span></label>
                                    <select name="currency_id" id="currency_id" class="form-select" required>
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?= $currency['id'] ?>"
                                                    data-rate="<?= $currency['exchange_rate'] ?>"
                                                    <?= $currency['code'] == 'IQD' ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                                    <input type="date" name="invoice_date" class="form-control"
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" name="due_date" class="form-control"
                                           value="<?= date('Y-m-d', strtotime('+30 days')) ?>">
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سعر الصرف</label>
                                    <input type="number" name="exchange_rate" id="exchange_rate"
                                           class="form-control" step="0.0001" value="1.0000" readonly>
                                </div>

                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3"
                                              placeholder="أي ملاحظات إضافية حول الفاتورة..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أصناف الفاتورة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-list me-2"></i>أصناف الفاتورة</h5>
                                <button type="button" class="btn btn-add-item btn-sm" onclick="addItemRow()">
                                    <i class="fas fa-plus me-2"></i>إضافة صنف
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-4">
                            <div id="items-container">
                                <!-- سيتم إضافة الأصناف هنا بواسطة JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- ملخص الفاتورة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الفاتورة</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="mb-3">
                                <label class="form-label">خصم إضافي</label>
                                <input type="number" name="discount_amount" id="discount_amount"
                                       class="form-control" step="0.01" value="0" onchange="calculateTotals()">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">ضريبة</label>
                                <input type="number" name="tax_amount" id="tax_amount"
                                       class="form-control" step="0.01" value="0" onchange="calculateTotals()">
                            </div>

                            <div class="total-summary">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>المجموع الفرعي:</span>
                                    <strong id="subtotal-display">0.00</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الخصم:</span>
                                    <strong id="discount-display">0.00</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>الضريبة:</span>
                                    <strong id="tax-display">0.00</strong>
                                </div>
                                <hr class="my-3" style="border-color: rgba(255,255,255,0.3);">
                                <div class="d-flex justify-content-between">
                                    <h5>المجموع الكلي:</h5>
                                    <h5 id="total-display">0.00</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mb-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ الفاتورة
                </button>
                <a href="sales.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // بيانات الأصناف والمخازن
        const items = <?= json_encode($items) ?>;
        const warehouses = <?= json_encode($warehouses) ?>;
        const exchangeRates = <?= json_encode($exchangeRates) ?>;

        let itemRowCounter = 0;

        // تحديث سعر الصرف عند تغيير العملة
        document.getElementById('currency_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const rate = selectedOption.getAttribute('data-rate');
            document.getElementById('exchange_rate').value = rate;
            calculateTotals();
        });

        // إضافة صف صنف جديد
        function addItemRow() {
            itemRowCounter++;
            const container = document.getElementById('items-container');

            const itemRow = document.createElement('div');
            itemRow.className = 'item-row';
            itemRow.id = 'item-row-' + itemRowCounter;

            itemRow.innerHTML = `
                <div class="row align-items-end">
                    <div class="col-md-3 mb-2">
                        <label class="form-label">الصنف</label>
                        <select name="items[${itemRowCounter}][item_id]" class="form-select item-select" onchange="updateItemPrice(${itemRowCounter})">
                            <option value="">اختر الصنف</option>
                            ${items.map(item => `<option value="${item.id}" data-price="${item.selling_price}" data-unit="${item.unit}">${item.code} - ${item.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">المخزن</label>
                        <select name="items[${itemRowCounter}][warehouse_id]" class="form-select">
                            <option value="">اختر المخزن</option>
                            ${warehouses.map(warehouse => `<option value="${warehouse.id}">${warehouse.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">الكمية</label>
                        <input type="number" name="items[${itemRowCounter}][quantity]" class="form-control" step="0.001" min="0.001" onchange="calculateRowTotal(${itemRowCounter})">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">السعر</label>
                        <input type="number" name="items[${itemRowCounter}][unit_price]" class="form-control" step="0.01" min="0" onchange="calculateRowTotal(${itemRowCounter})">
                    </div>
                    <div class="col-md-2 mb-2">
                        <label class="form-label">خصم %</label>
                        <input type="number" name="items[${itemRowCounter}][discount_percentage]" class="form-control" step="0.01" min="0" max="100" value="0" onchange="calculateRowTotal(${itemRowCounter})">
                    </div>
                    <div class="col-md-1 mb-2">
                        <button type="button" class="btn btn-remove-item btn-sm" onclick="removeItemRow(${itemRowCounter})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 text-end">
                        <strong>المجموع: <span id="row-total-${itemRowCounter}">0.00</span></strong>
                    </div>
                </div>
            `;

            container.appendChild(itemRow);
        }

        // حذف صف صنف
        function removeItemRow(rowId) {
            const row = document.getElementById('item-row-' + rowId);
            if (row) {
                row.remove();
                calculateTotals();
            }
        }

        // تحديث سعر الصنف
        function updateItemPrice(rowId) {
            const select = document.querySelector(`select[name="items[${rowId}][item_id]"]`);
            const priceInput = document.querySelector(`input[name="items[${rowId}][unit_price]"]`);

            if (select.selectedIndex > 0) {
                const selectedOption = select.options[select.selectedIndex];
                const price = selectedOption.getAttribute('data-price');
                priceInput.value = price;
                calculateRowTotal(rowId);
            }
        }

        // حساب مجموع الصف
        function calculateRowTotal(rowId) {
            const quantity = parseFloat(document.querySelector(`input[name="items[${rowId}][quantity]"]`).value) || 0;
            const unitPrice = parseFloat(document.querySelector(`input[name="items[${rowId}][unit_price]"]`).value) || 0;
            const discountPercentage = parseFloat(document.querySelector(`input[name="items[${rowId}][discount_percentage]"]`).value) || 0;

            const subtotal = quantity * unitPrice;
            const discountAmount = (subtotal * discountPercentage) / 100;
            const total = subtotal - discountAmount;

            document.getElementById('row-total-' + rowId).textContent = total.toFixed(2);
            calculateTotals();
        }

        // حساب الإجماليات
        function calculateTotals() {
            let subtotal = 0;

            // جمع مجاميع الصفوف
            document.querySelectorAll('[id^="row-total-"]').forEach(element => {
                subtotal += parseFloat(element.textContent) || 0;
            });

            const discountAmount = parseFloat(document.getElementById('discount_amount').value) || 0;
            const taxAmount = parseFloat(document.getElementById('tax_amount').value) || 0;
            const total = subtotal - discountAmount + taxAmount;

            document.getElementById('subtotal-display').textContent = subtotal.toFixed(2);
            document.getElementById('discount-display').textContent = discountAmount.toFixed(2);
            document.getElementById('tax-display').textContent = taxAmount.toFixed(2);
            document.getElementById('total-display').textContent = total.toFixed(2);
        }

        // إضافة صف واحد في البداية
        addItemRow();
    </script>
</body>
</html>
