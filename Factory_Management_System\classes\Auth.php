<?php
require_once '../config/database.php';

/**
 * فئة المصادقة والصلاحيات
 * Authentication and Authorization Class
 */
class Auth {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * تسجيل الدخول
     */
    public function login($username, $password) {
        try {
            // البحث عن المستخدم
            $sql = "SELECT id, username, email, password_hash, full_name, role, permissions, is_active 
                    FROM users WHERE (username = :username OR email = :username) AND is_active = 1";
            
            $user = $this->db->fetchOne($sql, ['username' => $username]);
            
            if (!$user) {
                return ['success' => false, 'message' => 'اسم المستخدم غير موجود'];
            }
            
            // التحقق من كلمة المرور
            if (!Helper::verifyPassword($password, $user['password_hash'])) {
                return ['success' => false, 'message' => 'كلمة المرور غير صحيحة'];
            }
            
            // تحديث آخر تسجيل دخول
            $this->db->update('users', 
                ['last_login' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $user['id']]
            );
            
            // إنشاء الجلسة
            $this->createSession($user);
            
            return [
                'success' => true, 
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $user
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تسجيل الدخول: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء جلسة المستخدم
     */
    private function createSession($user) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['full_name'] = $user['full_name'];
        $_SESSION['role'] = $user['role'];
        $_SESSION['permissions'] = json_decode($user['permissions'], true);
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();
    }
    
    /**
     * تسجيل الخروج
     */
    public function logout() {
        session_unset();
        session_destroy();
        return ['success' => true, 'message' => 'تم تسجيل الخروج بنجاح'];
    }
    
    /**
     * التحقق من تسجيل الدخول
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }
        
        // التحقق من انتهاء الجلسة
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity'] > Config::SESSION_TIMEOUT)) {
            $this->logout();
            return false;
        }
        
        // تحديث آخر نشاط
        $_SESSION['last_activity'] = time();
        return true;
    }
    
    /**
     * الحصول على المستخدم الحالي
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'full_name' => $_SESSION['full_name'],
            'role' => $_SESSION['role'],
            'permissions' => $_SESSION['permissions']
        ];
    }
    
    /**
     * التحقق من الصلاحية
     */
    public function hasPermission($permission) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $user = $this->getCurrentUser();
        
        // المدير له جميع الصلاحيات
        if ($user['role'] === 'admin') {
            return true;
        }
        
        // التحقق من الصلاحيات المخصصة
        $permissions = $user['permissions'];
        
        if (isset($permissions['all']) && $permissions['all']) {
            return true;
        }
        
        return isset($permissions[$permission]) && $permissions[$permission];
    }
    
    /**
     * التحقق من الدور
     */
    public function hasRole($role) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $user = $this->getCurrentUser();
        return $user['role'] === $role;
    }
    
    /**
     * التحقق من عدة أدوار
     */
    public function hasAnyRole($roles) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $user = $this->getCurrentUser();
        return in_array($user['role'], $roles);
    }
    
    /**
     * تغيير كلمة المرور
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        try {
            // التحقق من كلمة المرور الحالية
            $user = $this->db->fetchOne(
                "SELECT password_hash FROM users WHERE id = :id", 
                ['id' => $userId]
            );
            
            if (!$user || !Helper::verifyPassword($currentPassword, $user['password_hash'])) {
                return ['success' => false, 'message' => 'كلمة المرور الحالية غير صحيحة'];
            }
            
            // تحديث كلمة المرور
            $newPasswordHash = Helper::hashPassword($newPassword);
            $this->db->update('users', 
                ['password_hash' => $newPasswordHash, 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $userId]
            );
            
            return ['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تغيير كلمة المرور: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنشاء مستخدم جديد
     */
    public function createUser($data) {
        try {
            // التحقق من عدم وجود المستخدم
            if ($this->db->exists('users', 'username = :username OR email = :email', 
                ['username' => $data['username'], 'email' => $data['email']])) {
                return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'];
            }
            
            // تشفير كلمة المرور
            $data['password_hash'] = Helper::hashPassword($data['password']);
            unset($data['password']);
            
            // تحويل الصلاحيات إلى JSON
            if (isset($data['permissions']) && is_array($data['permissions'])) {
                $data['permissions'] = json_encode($data['permissions']);
            }
            
            $data['created_at'] = date('Y-m-d H:i:s');
            
            $userId = $this->db->insert('users', $data);
            
            return ['success' => true, 'message' => 'تم إنشاء المستخدم بنجاح', 'user_id' => $userId];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في إنشاء المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * تحديث بيانات المستخدم
     */
    public function updateUser($userId, $data) {
        try {
            // التحقق من عدم تكرار اسم المستخدم أو البريد
            if (isset($data['username']) || isset($data['email'])) {
                $checkSql = "SELECT id FROM users WHERE (username = :username OR email = :email) AND id != :id";
                $existing = $this->db->fetchOne($checkSql, [
                    'username' => $data['username'] ?? '',
                    'email' => $data['email'] ?? '',
                    'id' => $userId
                ]);
                
                if ($existing) {
                    return ['success' => false, 'message' => 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'];
                }
            }
            
            // تشفير كلمة المرور إذا تم تغييرها
            if (isset($data['password'])) {
                $data['password_hash'] = Helper::hashPassword($data['password']);
                unset($data['password']);
            }
            
            // تحويل الصلاحيات إلى JSON
            if (isset($data['permissions']) && is_array($data['permissions'])) {
                $data['permissions'] = json_encode($data['permissions']);
            }
            
            $data['updated_at'] = date('Y-m-d H:i:s');
            
            $this->db->update('users', $data, 'id = :id', ['id' => $userId]);
            
            return ['success' => true, 'message' => 'تم تحديث بيانات المستخدم بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في تحديث المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * حذف مستخدم
     */
    public function deleteUser($userId) {
        try {
            // التأكد من عدم حذف المدير الرئيسي
            $user = $this->db->fetchOne("SELECT role FROM users WHERE id = :id", ['id' => $userId]);
            
            if ($user && $user['role'] === 'admin') {
                return ['success' => false, 'message' => 'لا يمكن حذف المدير الرئيسي'];
            }
            
            // إلغاء تفعيل المستخدم بدلاً من الحذف
            $this->db->update('users', 
                ['is_active' => 0, 'updated_at' => date('Y-m-d H:i:s')], 
                'id = :id', 
                ['id' => $userId]
            );
            
            return ['success' => true, 'message' => 'تم إلغاء تفعيل المستخدم بنجاح'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'خطأ في حذف المستخدم: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على قائمة المستخدمين
     */
    public function getUsers($filters = []) {
        try {
            $sql = "SELECT id, username, email, full_name, role, is_active, last_login, created_at 
                    FROM users WHERE 1=1";
            $params = [];
            
            if (isset($filters['role']) && !empty($filters['role'])) {
                $sql .= " AND role = :role";
                $params['role'] = $filters['role'];
            }
            
            if (isset($filters['is_active'])) {
                $sql .= " AND is_active = :is_active";
                $params['is_active'] = $filters['is_active'];
            }
            
            if (isset($filters['search']) && !empty($filters['search'])) {
                $sql .= " AND (username LIKE :search OR email LIKE :search OR full_name LIKE :search)";
                $params['search'] = '%' . $filters['search'] . '%';
            }
            
            $sql .= " ORDER BY created_at DESC";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في جلب المستخدمين: ' . $e->getMessage());
        }
    }
}

/**
 * فئة الصلاحيات المحددة مسبقاً
 */
class Permissions {
    // صلاحيات عامة
    const VIEW_DASHBOARD = 'view_dashboard';
    const MANAGE_USERS = 'manage_users';
    const MANAGE_SETTINGS = 'manage_settings';
    
    // صلاحيات المخزون
    const VIEW_INVENTORY = 'view_inventory';
    const MANAGE_ITEMS = 'manage_items';
    const MANAGE_WAREHOUSES = 'manage_warehouses';
    const VIEW_STOCK_MOVEMENTS = 'view_stock_movements';
    const MANAGE_STOCK_ADJUSTMENTS = 'manage_stock_adjustments';
    
    // صلاحيات المبيعات
    const VIEW_SALES = 'view_sales';
    const CREATE_SALES_INVOICE = 'create_sales_invoice';
    const EDIT_SALES_INVOICE = 'edit_sales_invoice';
    const DELETE_SALES_INVOICE = 'delete_sales_invoice';
    const MANAGE_CUSTOMERS = 'manage_customers';
    
    // صلاحيات المشتريات
    const VIEW_PURCHASES = 'view_purchases';
    const CREATE_PURCHASE_INVOICE = 'create_purchase_invoice';
    const EDIT_PURCHASE_INVOICE = 'edit_purchase_invoice';
    const DELETE_PURCHASE_INVOICE = 'delete_purchase_invoice';
    const MANAGE_SUPPLIERS = 'manage_suppliers';
    
    // صلاحيات الإنتاج
    const VIEW_PRODUCTION = 'view_production';
    const CREATE_PRODUCTION_ORDER = 'create_production_order';
    const MANAGE_PRODUCTION_RECIPES = 'manage_production_recipes';
    const VIEW_PRODUCTION_REPORTS = 'view_production_reports';
    
    // صلاحيات المالية
    const VIEW_FINANCIAL = 'view_financial';
    const MANAGE_CASH_BOXES = 'manage_cash_boxes';
    const MANAGE_EXPENSES = 'manage_expenses';
    const VIEW_FINANCIAL_REPORTS = 'view_financial_reports';
    
    // صلاحيات الموظفين
    const VIEW_EMPLOYEES = 'view_employees';
    const MANAGE_EMPLOYEES = 'manage_employees';
    const MANAGE_SALARIES = 'manage_salaries';
    const VIEW_EMPLOYEE_REPORTS = 'view_employee_reports';
    
    // صلاحيات التقارير
    const VIEW_ALL_REPORTS = 'view_all_reports';
    const EXPORT_REPORTS = 'export_reports';
    const PRINT_REPORTS = 'print_reports';
    
    /**
     * الحصول على جميع الصلاحيات
     */
    public static function getAllPermissions() {
        $reflection = new ReflectionClass(__CLASS__);
        return $reflection->getConstants();
    }
    
    /**
     * الحصول على الصلاحيات حسب الدور
     */
    public static function getPermissionsByRole($role) {
        $permissions = [];
        
        switch($role) {
            case 'admin':
                $permissions = ['all' => true];
                break;
                
            case 'manager':
                $permissions = [
                    self::VIEW_DASHBOARD => true,
                    self::VIEW_INVENTORY => true,
                    self::VIEW_SALES => true,
                    self::VIEW_PURCHASES => true,
                    self::VIEW_PRODUCTION => true,
                    self::VIEW_FINANCIAL => true,
                    self::VIEW_EMPLOYEES => true,
                    self::VIEW_ALL_REPORTS => true,
                    self::EXPORT_REPORTS => true,
                    self::PRINT_REPORTS => true
                ];
                break;
                
            case 'accountant':
                $permissions = [
                    self::VIEW_DASHBOARD => true,
                    self::VIEW_SALES => true,
                    self::CREATE_SALES_INVOICE => true,
                    self::EDIT_SALES_INVOICE => true,
                    self::VIEW_PURCHASES => true,
                    self::CREATE_PURCHASE_INVOICE => true,
                    self::EDIT_PURCHASE_INVOICE => true,
                    self::MANAGE_CUSTOMERS => true,
                    self::MANAGE_SUPPLIERS => true,
                    self::VIEW_FINANCIAL => true,
                    self::MANAGE_CASH_BOXES => true,
                    self::MANAGE_EXPENSES => true,
                    self::VIEW_FINANCIAL_REPORTS => true
                ];
                break;
                
            case 'warehouse':
                $permissions = [
                    self::VIEW_DASHBOARD => true,
                    self::VIEW_INVENTORY => true,
                    self::MANAGE_ITEMS => true,
                    self::VIEW_STOCK_MOVEMENTS => true,
                    self::MANAGE_STOCK_ADJUSTMENTS => true,
                    self::VIEW_PURCHASES => true,
                    self::VIEW_PRODUCTION => true
                ];
                break;
                
            case 'production':
                $permissions = [
                    self::VIEW_DASHBOARD => true,
                    self::VIEW_INVENTORY => true,
                    self::VIEW_PRODUCTION => true,
                    self::CREATE_PRODUCTION_ORDER => true,
                    self::MANAGE_PRODUCTION_RECIPES => true,
                    self::VIEW_PRODUCTION_REPORTS => true
                ];
                break;
                
            case 'sales':
                $permissions = [
                    self::VIEW_DASHBOARD => true,
                    self::VIEW_INVENTORY => true,
                    self::VIEW_SALES => true,
                    self::CREATE_SALES_INVOICE => true,
                    self::EDIT_SALES_INVOICE => true,
                    self::MANAGE_CUSTOMERS => true
                ];
                break;
        }
        
        return $permissions;
    }
}
?>
