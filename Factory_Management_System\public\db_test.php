<?php
/**
 * اختبار الاتصال بقاعدة البيانات
 * Database Connection Test
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // اختبار الاتصال المباشر
    echo "<h3>1. اختبار الاتصال المباشر:</h3>";
    
    $host = 'localhost';
    $username = 'root';
    $password = '';
    
    // محاولة الاتصال بدون قاعدة بيانات
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "✅ الاتصال بـ MySQL ناجح<br>";
    
    // إنشاء قاعدة البيانات إذا لم تكن موجودة
    $dbName = 'factory_management';
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ قاعدة البيانات '$dbName' جاهزة<br>";
    
    // الاتصال بقاعدة البيانات
    $dsn = "mysql:host=$host;dbname=$dbName;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    echo "✅ الاتصال بقاعدة البيانات '$dbName' ناجح<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال المباشر: " . $e->getMessage() . "<br>";
}

echo "<hr>";

try {
    // اختبار فئة Database
    echo "<h3>2. اختبار فئة Database:</h3>";
    
    require_once '../config/database.php';
    
    $db = new Database();
    echo "✅ إنشاء كائن Database ناجح<br>";
    
    $connection = $db->getConnection();
    if ($connection) {
        echo "✅ الحصول على الاتصال ناجح<br>";
        
        // اختبار استعلام بسيط
        $result = $db->query("SELECT 1 as test");
        $row = $result->fetch();
        if ($row && $row['test'] == 1) {
            echo "✅ تنفيذ الاستعلام ناجح<br>";
        }
        
        // اختبار fetchOne
        $result = $db->fetchOne("SELECT 'Hello World' as message");
        if ($result && $result['message'] == 'Hello World') {
            echo "✅ دالة fetchOne تعمل بشكل صحيح<br>";
        }
        
    } else {
        echo "❌ فشل في الحصول على الاتصال<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في فئة Database: " . $e->getMessage() . "<br>";
}

echo "<hr>";

try {
    // اختبار إنشاء جدول
    echo "<h3>3. اختبار إنشاء جدول:</h3>";
    
    $db = new Database();
    
    // إنشاء جدول اختبار
    $db->query("CREATE TABLE IF NOT EXISTS test_table (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "✅ إنشاء جدول الاختبار ناجح<br>";
    
    // إدراج بيانات اختبار
    $db->query("INSERT INTO test_table (name) VALUES (?)", ['اختبار']);
    echo "✅ إدراج بيانات الاختبار ناجح<br>";
    
    // قراءة البيانات
    $result = $db->fetchOne("SELECT * FROM test_table ORDER BY id DESC LIMIT 1");
    if ($result && $result['name'] == 'اختبار') {
        echo "✅ قراءة البيانات ناجحة<br>";
    }
    
    // حذف جدول الاختبار
    $db->query("DROP TABLE test_table");
    echo "✅ حذف جدول الاختبار ناجح<br>";
    
} catch (Exception $e) {
    echo "❌ خطأ في اختبار الجدول: " . $e->getMessage() . "<br>";
}

echo "<hr>";

// معلومات قاعدة البيانات
try {
    echo "<h3>4. معلومات قاعدة البيانات:</h3>";
    
    $db = new Database();
    
    // إصدار MySQL
    $version = $db->fetchOne("SELECT VERSION() as version");
    echo "إصدار MySQL: " . $version['version'] . "<br>";
    
    // قائمة الجداول
    $tables = $db->fetchAll("SHOW TABLES");
    echo "عدد الجداول: " . count($tables) . "<br>";
    
    if (!empty($tables)) {
        echo "الجداول الموجودة:<br>";
        foreach ($tables as $table) {
            $tableName = array_values($table)[0];
            echo "- $tableName<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في جلب معلومات قاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>النتيجة:</h3>";
echo "<p>إذا ظهرت جميع العلامات الخضراء ✅ أعلاه، فإن قاعدة البيانات تعمل بشكل صحيح.</p>";
echo "<p><a href='quick_setup.php'>الذهاب للإعداد السريع</a> | <a href='login.php'>تسجيل الدخول</a></p>";
?>
