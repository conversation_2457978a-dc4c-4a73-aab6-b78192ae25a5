<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إنشاء كشف راتب
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] == 'generate_payroll') {
            $employeeId = (int)$_POST['employee_id'];
            $periodStart = $_POST['pay_period_start'];
            $periodEnd = $_POST['pay_period_end'];

            // التحقق من عدم وجود كشف راتب مسبق لنفس الفترة
            $existing = $db->fetchOne("SELECT id FROM payroll WHERE employee_id = ? AND pay_period_start = ? AND pay_period_end = ?",
                [$employeeId, $periodStart, $periodEnd]);

            if ($existing) {
                throw new Exception("يوجد كشف راتب مسبق لهذه الفترة");
            }

            // جلب بيانات الموظف
            $employee = $db->fetchOne("SELECT * FROM employees WHERE id = ?", [$employeeId]);
            if (!$employee) {
                throw new Exception("الموظف غير موجود");
            }

            // حساب ساعات العمل والإضافي من سجل الحضور
            $attendanceStats = $db->fetchOne("
                SELECT
                    SUM(total_hours) as total_hours,
                    SUM(overtime_hours) as overtime_hours,
                    COUNT(*) as working_days
                FROM attendance
                WHERE employee_id = ? AND attendance_date BETWEEN ? AND ? AND status = 'present'
            ", [$employeeId, $periodStart, $periodEnd]);

            $totalHours = $attendanceStats['total_hours'] ?? 0;
            $overtimeHours = $attendanceStats['overtime_hours'] ?? 0;
            $workingDays = $attendanceStats['working_days'] ?? 0;

            // حساب الراتب الأساسي
            $basicSalary = (float)$employee['salary'];

            // حساب أجر الساعات الإضافية
            $overtimeAmount = $overtimeHours * (float)$employee['overtime_rate'];

            // حساب البدلات والمكافآت (يمكن تخصيصها حسب الحاجة)
            $bonus = (float)($_POST['bonus'] ?? 0);
            $allowances = (float)($_POST['allowances'] ?? 0);

            // حساب الخصومات
            $deductions = (float)($_POST['deductions'] ?? 0);

            // حساب الراتب الإجمالي
            $grossSalary = $basicSalary + $overtimeAmount + $bonus + $allowances;

            // حساب الضريبة (مثال: 5% للرواتب أكثر من مليون)
            $taxAmount = 0;
            if ($grossSalary > 1000000) {
                $taxAmount = ($grossSalary - 1000000) * 0.05;
            }

            // حساب الراتب الصافي
            $netSalary = $grossSalary - $taxAmount - $deductions;

            $payrollData = [
                'employee_id' => $employeeId,
                'pay_period_start' => $periodStart,
                'pay_period_end' => $periodEnd,
                'basic_salary' => $basicSalary,
                'overtime_amount' => $overtimeAmount,
                'bonus' => $bonus,
                'allowances' => $allowances,
                'deductions' => $deductions,
                'gross_salary' => $grossSalary,
                'tax_amount' => $taxAmount,
                'net_salary' => $netSalary,
                'created_by' => $user['id']
            ];

            $payrollId = $db->insert('payroll', $payrollData);
            $success = "تم إنشاء كشف الراتب بنجاح";

        } elseif ($_POST['action'] == 'approve_payroll') {
            $payrollId = (int)$_POST['payroll_id'];

            $updateData = [
                'status' => 'approved',
                'approved_by' => $user['id'],
                'approved_at' => date('Y-m-d H:i:s')
            ];

            $db->update('payroll', $updateData, 'id = ?', [$payrollId]);
            $success = "تم اعتماد كشف الراتب";

        } elseif ($_POST['action'] == 'mark_paid') {
            $payrollId = (int)$_POST['payroll_id'];

            $updateData = [
                'status' => 'paid',
                'payment_date' => date('Y-m-d')
            ];

            $db->update('payroll', $updateData, 'id = ?', [$payrollId]);
            $success = "تم تسجيل دفع الراتب";
        }

    } catch (Exception $e) {
        $error = "خطأ: " . $e->getMessage();
    }
}

// فلاتر البحث
$selectedMonth = $_GET['month'] ?? date('Y-m');
$departmentFilter = $_GET['department'] ?? '';
$statusFilter = $_GET['status'] ?? '';

// بناء استعلام الرواتب
$whereConditions = ["DATE_FORMAT(p.pay_period_start, '%Y-%m') = ?"];
$params = [$selectedMonth];

if ($departmentFilter) {
    $whereConditions[] = "e.department_id = ?";
    $params[] = $departmentFilter;
}

if ($statusFilter) {
    $whereConditions[] = "p.status = ?";
    $params[] = $statusFilter;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب بيانات الرواتب
$payrollRecords = $db->fetchAll("
    SELECT p.*, e.full_name, e.employee_code, d.name as department_name,
           u.full_name as created_by_name
    FROM payroll p
    JOIN employees e ON p.employee_id = e.id
    LEFT JOIN departments d ON e.department_id = d.id
    LEFT JOIN users u ON p.created_by = u.id
    WHERE $whereClause
    ORDER BY e.full_name
", $params);

// جلب الموظفين النشطين
$employees = $db->fetchAll("
    SELECT e.id, e.full_name, e.employee_code, e.salary, d.name as department_name
    FROM employees e
    LEFT JOIN departments d ON e.department_id = d.id
    WHERE e.status = 'active'
    ORDER BY e.full_name
");

// جلب الأقسام
$departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");

// إحصائيات الشهر
$monthStats = $db->fetchOne("
    SELECT
        COUNT(*) as total_payrolls,
        SUM(gross_salary) as total_gross,
        SUM(net_salary) as total_net,
        SUM(tax_amount) as total_tax,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count
    FROM payroll p
    WHERE DATE_FORMAT(p.pay_period_start, '%Y-%m') = ?
", [$selectedMonth]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الرواتب - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stats-card.gross { background: linear-gradient(135deg, #17a2b8 0%, #**********%); }
        .stats-card.net { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .stats-card.tax { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .stats-card.paid { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); }
        .payroll-row {
            transition: all 0.3s ease;
        }
        .payroll-row:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .salary-amount {
            font-family: monospace;
            font-size: 1.1rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-money-check-alt me-3"></i>إدارة الرواتب</h1>
                    <p class="mb-0">إدارة رواتب الموظفين وكشوف الدفع</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="attendance.php" class="btn btn-light me-2">
                        <i class="fas fa-clock me-2"></i>الحضور
                    </a>
                    <a href="employees.php" class="btn btn-outline-light">
                        <i class="fas fa-users me-2"></i>الموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الشهر -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card gross">
                    <h4><?= number_format($monthStats['total_gross'] ?? 0, 0) ?></h4>
                    <p class="mb-0">إجمالي الرواتب</p>
                    <small><?= $monthStats['total_payrolls'] ?? 0 ?> كشف راتب</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card net">
                    <h4><?= number_format($monthStats['total_net'] ?? 0, 0) ?></h4>
                    <p class="mb-0">صافي الرواتب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card tax">
                    <h4><?= number_format($monthStats['total_tax'] ?? 0, 0) ?></h4>
                    <p class="mb-0">إجمالي الضرائب</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card paid">
                    <h4><?= $monthStats['paid_count'] ?? 0 ?></h4>
                    <p class="mb-0">رواتب مدفوعة</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>الفلاتر</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">الشهر</label>
                                <input type="month" name="month" class="form-control"
                                       value="<?= htmlspecialchars($selectedMonth) ?>" onchange="this.form.submit()">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select name="department" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>" <?= $departmentFilter == $dept['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select" onchange="this.form.submit()">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" <?= $statusFilter == 'draft' ? 'selected' : '' ?>>مسودة</option>
                                    <option value="approved" <?= $statusFilter == 'approved' ? 'selected' : '' ?>>معتمد</option>
                                    <option value="paid" <?= $statusFilter == 'paid' ? 'selected' : '' ?>>مدفوع</option>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- إنشاء كشف راتب جديد -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>كشف راتب جديد</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-primary w-100"
                                data-bs-toggle="modal" data-bs-target="#generatePayrollModal">
                            <i class="fas fa-calculator me-2"></i>إنشاء كشف راتب
                        </button>
                    </div>
                </div>
            </div>

            <!-- قائمة الرواتب -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>كشوف الرواتب - <?= date('m/Y', strtotime($selectedMonth . '-01')) ?>
                                (<?= count($payrollRecords) ?>)
                            </h5>
                            <a href="payroll_report.php?month=<?= $selectedMonth ?>" class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i>تصدير
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($payrollRecords)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-check-alt fa-4x text-muted mb-3"></i>
                                <h5>لا توجد كشوف رواتب لهذا الشهر</h5>
                                <p class="text-muted">ابدأ بإنشاء كشوف رواتب للموظفين</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الموظف</th>
                                            <th>القسم</th>
                                            <th>الفترة</th>
                                            <th>الراتب الأساسي</th>
                                            <th>الإضافي</th>
                                            <th>الإجمالي</th>
                                            <th>الصافي</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payrollRecords as $record): ?>
                                            <tr class="payroll-row">
                                                <td>
                                                    <strong><?= htmlspecialchars($record['full_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($record['employee_code']) ?></small>
                                                </td>
                                                <td><?= htmlspecialchars($record['department_name']) ?></td>
                                                <td>
                                                    <small>
                                                        <?= date('d/m', strtotime($record['pay_period_start'])) ?> -
                                                        <?= date('d/m/Y', strtotime($record['pay_period_end'])) ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <span class="salary-amount text-primary">
                                                        <?= number_format($record['basic_salary'], 0) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="salary-amount text-warning">
                                                        <?= number_format($record['overtime_amount'], 0) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="salary-amount text-info">
                                                        <?= number_format($record['gross_salary'], 0) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="salary-amount text-success">
                                                        <?= number_format($record['net_salary'], 0) ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'draft' => 'secondary',
                                                        'approved' => 'success',
                                                        'paid' => 'primary'
                                                    ];
                                                    $statusNames = [
                                                        'draft' => 'مسودة',
                                                        'approved' => 'معتمد',
                                                        'paid' => 'مدفوع'
                                                    ];
                                                    ?>
                                                    <span class="status-badge bg-<?= $statusColors[$record['status']] ?>">
                                                        <?= $statusNames[$record['status']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view_payroll.php?id=<?= $record['id'] ?>"
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($record['status'] == 'draft' && $user['role'] == 'admin'): ?>
                                                            <button class="btn btn-sm btn-outline-success"
                                                                    onclick="approvePayroll(<?= $record['id'] ?>)">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <?php if ($record['status'] == 'approved' && $user['role'] == 'admin'): ?>
                                                            <button class="btn btn-sm btn-outline-warning"
                                                                    onclick="markPaid(<?= $record['id'] ?>)">
                                                                <i class="fas fa-money-bill"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <a href="print_payroll.php?id=<?= $record['id'] ?>"
                                                           class="btn btn-sm btn-outline-info" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إنشاء كشف راتب -->
    <div class="modal fade" id="generatePayrollModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء كشف راتب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="generate_payroll">

                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label">الموظف <span class="text-danger">*</span></label>
                                <select name="employee_id" class="form-select" required onchange="loadEmployeeData(this.value)">
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($employees as $emp): ?>
                                        <option value="<?= $emp['id'] ?>"
                                                data-salary="<?= $emp['salary'] ?>"
                                                data-department="<?= htmlspecialchars($emp['department_name']) ?>">
                                            <?= htmlspecialchars($emp['employee_code'] . ' - ' . $emp['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">من تاريخ <span class="text-danger">*</span></label>
                                <input type="date" name="pay_period_start" class="form-control"
                                       value="<?= date('Y-m-01') ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">إلى تاريخ <span class="text-danger">*</span></label>
                                <input type="date" name="pay_period_end" class="form-control"
                                       value="<?= date('Y-m-t') ?>" required>
                            </div>

                            <div class="col-12 mb-3">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">معلومات الموظف</h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <strong>القسم:</strong> <span id="employee_department">-</span>
                                            </div>
                                            <div class="col-md-6">
                                                <strong>الراتب الأساسي:</strong> <span id="employee_salary">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">مكافآت</label>
                                <input type="number" name="bonus" class="form-control"
                                       step="0.01" min="0" value="0" placeholder="0.00">
                                <small class="text-muted">مكافآت إضافية</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">بدلات</label>
                                <input type="number" name="allowances" class="form-control"
                                       step="0.01" min="0" value="0" placeholder="0.00">
                                <small class="text-muted">بدل نقل، سكن، إلخ</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">خصومات</label>
                                <input type="number" name="deductions" class="form-control"
                                       step="0.01" min="0" value="0" placeholder="0.00">
                                <small class="text-muted">خصومات، غرامات، إلخ</small>
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="mt-4">
                                    <button type="button" class="btn btn-info w-100" onclick="calculateAttendance()">
                                        <i class="fas fa-calculator me-2"></i>حساب الحضور
                                    </button>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6 class="card-title">ملخص الحضور</h6>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <strong>أيام العمل:</strong> <span id="working_days">-</span>
                                            </div>
                                            <div class="col-md-4">
                                                <strong>إجمالي الساعات:</strong> <span id="total_hours">-</span>
                                            </div>
                                            <div class="col-md-4">
                                                <strong>ساعات إضافية:</strong> <span id="overtime_hours">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إنشاء كشف الراتب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function loadEmployeeData(employeeId) {
            const select = document.querySelector('select[name="employee_id"]');
            const selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value) {
                document.getElementById('employee_department').textContent = selectedOption.dataset.department || '-';
                document.getElementById('employee_salary').textContent =
                    new Intl.NumberFormat('ar-IQ').format(selectedOption.dataset.salary) + ' د.ع';
            } else {
                document.getElementById('employee_department').textContent = '-';
                document.getElementById('employee_salary').textContent = '-';
            }

            // إعادة تعيين بيانات الحضور
            document.getElementById('working_days').textContent = '-';
            document.getElementById('total_hours').textContent = '-';
            document.getElementById('overtime_hours').textContent = '-';
        }

        function calculateAttendance() {
            const employeeId = document.querySelector('select[name="employee_id"]').value;
            const periodStart = document.querySelector('input[name="pay_period_start"]').value;
            const periodEnd = document.querySelector('input[name="pay_period_end"]').value;

            if (!employeeId || !periodStart || !periodEnd) {
                alert('يرجى اختيار الموظف وتحديد فترة الراتب');
                return;
            }

            // إرسال طلب AJAX لحساب الحضور
            fetch('calculate_attendance.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    employee_id: employeeId,
                    period_start: periodStart,
                    period_end: periodEnd
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('working_days').textContent = data.working_days;
                    document.getElementById('total_hours').textContent = data.total_hours + ' ساعة';
                    document.getElementById('overtime_hours').textContent = data.overtime_hours + ' ساعة';
                } else {
                    alert('خطأ في حساب الحضور: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        function approvePayroll(payrollId) {
            if (confirm('هل أنت متأكد من اعتماد كشف الراتب؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="approve_payroll">
                    <input type="hidden" name="payroll_id" value="${payrollId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        function markPaid(payrollId) {
            if (confirm('هل أنت متأكد من تسجيل دفع الراتب؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="mark_paid">
                    <input type="hidden" name="payroll_id" value="${payrollId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
