﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.CompressionLevel">
      <summary>Especifica valores que indican si una operación de compresión hace hincapié en la velocidad o en el tamaño de la compresión.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Fastest">
      <summary>La operación de compresión debe completarse lo antes posible, aunque el archivo resultante no esté comprimido de forma óptima.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.NoCompression">
      <summary>No debe comprimirse el archivo.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Optimal">
      <summary>La operación de compresión se debe comprimir de forma óptima, incluso aunque la operación tarde más tiempo en completarse.</summary>
    </member>
    <member name="T:System.IO.Compression.CompressionMode">
      <summary> Especifica si se comprime o descomprime la secuencia subyacente.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Compress">
      <summary>Comprime la secuencia subyacente.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Decompress">
      <summary>Descomprime la secuencia subyacente.</summary>
    </member>
    <member name="T:System.IO.Compression.DeflateStream">
      <summary>Proporciona métodos y propiedades para comprimir y descomprimir secuencias usando el algoritmo Deflate.</summary>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.DeflateStream" /> usando la secuencia y nivel de compresión especificados.</summary>
      <param name="stream">Secuencia que se va a comprimir.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al comprimir la secuencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">La secuencia no admite operaciones de escritura como compresión.(El <see cref="P:System.IO.Stream.CanWrite" /> es propiedad del objeto de secuencia false.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.DeflateStream" /> usando la secuencia y nivel de compresión especificados y, opcionalmente, deja la secuencia abierta.</summary>
      <param name="stream">Secuencia que se va a comprimir.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al comprimir la secuencia.</param>
      <param name="leaveOpen">true para mantener el objeto de secuencia abierto después de desechar el objeto <see cref="T:System.IO.Compression.DeflateStream" />; en caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">La secuencia no admite operaciones de escritura como compresión.(El <see cref="P:System.IO.Stream.CanWrite" /> es propiedad del objeto de secuencia false.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.DeflateStream" /> usando la secuencia y modo de compresión especificados.</summary>
      <param name="stream">Secuencia que se va a comprimir o descomprimir.</param>
      <param name="mode">Uno de los valores de la enumeración que indica si se debe comprimir o descomprimir la secuencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es válida <see cref="T:System.IO.Compression.CompressionMode" /> valor.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Compress" />  y <see cref="P:System.IO.Stream.CanWrite" /> es false.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  y <see cref="P:System.IO.Stream.CanRead" /> es false.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.DeflateStream" /> usando la secuencia y modo de compresión especificados y, opcionalmente, deja la secuencia abierta.</summary>
      <param name="stream">Secuencia que se va a comprimir o descomprimir.</param>
      <param name="mode">Uno de los valores de la enumeración que indica si se debe comprimir o descomprimir la secuencia.</param>
      <param name="leaveOpen">true para mantener la secuencia abierta después de desechar el objeto <see cref="T:System.IO.Compression.DeflateStream" />; en caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es válida <see cref="T:System.IO.Compression.CompressionMode" /> valor.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Compress" />  y <see cref="P:System.IO.Stream.CanWrite" /> es false.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  y <see cref="P:System.IO.Stream.CanRead" /> es false.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.BaseStream">
      <summary>Obtiene una referencia a la secuencia subyacente.</summary>
      <returns>Un objeto de secuencia que representa la secuencia subyacente.</returns>
      <exception cref="T:System.ObjectDisposedException">La secuencia subyacente está cerrada.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanRead">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de lectura mientras se descomprime un archivo.</summary>
      <returns>Es true si el valor <see cref="T:System.IO.Compression.CompressionMode" /> es Decompress, y la secuencia subyacente está abierta y admite operaciones de lectura; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanSeek">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de búsqueda.</summary>
      <returns>false en todos los casos.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanWrite">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de escritura.</summary>
      <returns>true si el valor de <see cref="T:System.IO.Compression.CompressionMode" /> es Compress y la secuencia subyacente admite operaciones de escritura y no está cerrada; en caso contrario, false.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.IO.Compression.DeflateStream" /> y libera los recursos administrados de forma opcional.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Flush">
      <summary>La implementación actual de este método no tiene funcionalidad.</summary>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Length">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Position">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Introduce varios bytes descomprimidos leídos en la matriz de bytes especificada.</summary>
      <returns>Número de bytes leídos de la matriz de bytes.</returns>
      <param name="array">Matriz para almacenar los bytes descomprimidos.</param>
      <param name="offset">Desplazamiento de bytes en <paramref name="array" /> donde se colocarán los bytes leídos.</param>
      <param name="count">Número máximo de bytes descomprimidos que se van a leer.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.IO.Compression.CompressionMode" /> valor era Compress cuando se creó el objeto.o bien La secuencia subyacente no admite operaciones de lectura.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es menor que cero.o bien<paramref name="array" /> longitud menos el punto inicial del índice es menor que <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">El formato de los datos no es válido.</exception>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Esta operación no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <param name="offset">Ubicación en la secuencia.</param>
      <param name="origin">Uno de los valores de <see cref="T:System.IO.SeekOrigin" />.</param>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.SetLength(System.Int64)">
      <summary>Esta operación no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">Longitud de la secuencia.</param>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Escribe los bytes comprimidos en la secuencia subyacente de la matriz de bytes especificada.</summary>
      <param name="array">Búfer que contiene los datos que se van a comprimir.</param>
      <param name="offset">Desplazamiento de bytes en <paramref name="array" /> donde se leerán los bytes.</param>
      <param name="count">Número máximo de bytes que se pueden escribir.</param>
    </member>
    <member name="T:System.IO.Compression.GZipStream">
      <summary>Proporciona los métodos y propiedades que permiten comprimir y descomprimir secuencias.</summary>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.GZipStream" /> usando la secuencia y nivel de compresión especificados.</summary>
      <param name="stream">Secuencia en la que se van a escribir los datos comprimidos.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al comprimir la secuencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">La secuencia no admite operaciones de escritura como compresión.(El <see cref="P:System.IO.Stream.CanWrite" /> es propiedad del objeto de secuencia false.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.GZipStream" /> usando la secuencia y nivel de compresión especificados y, opcionalmente, deja la secuencia abierta.</summary>
      <param name="stream">Secuencia en la que se van a escribir los datos comprimidos.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al comprimir la secuencia.</param>
      <param name="leaveOpen">true para mantener el objeto de secuencia abierto después de desechar el objeto <see cref="T:System.IO.Compression.GZipStream" />; en caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">La secuencia no admite operaciones de escritura como compresión.(El <see cref="P:System.IO.Stream.CanWrite" /> es propiedad del objeto de secuencia false.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.GZipStream" /> usando la secuencia y modo de compresión especificados.</summary>
      <param name="stream">Secuencia en la que se escriben los datos comprimidos o descomprimidos.</param>
      <param name="mode">Uno de los valores de la enumeración que indica si se debe comprimir o descomprimir la secuencia.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es válida <see cref="T:System.IO.Compression.CompressionMode" /> valor de enumeración.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Compress" />  y <see cref="P:System.IO.Stream.CanWrite" /> es false.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  y <see cref="P:System.IO.Stream.CanRead" /> es false.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.GZipStream" /> usando la secuencia y modo de compresión especificados y, opcionalmente, deja la secuencia abierta.</summary>
      <param name="stream">Secuencia en la que se escriben los datos comprimidos o descomprimidos.</param>
      <param name="mode">Uno de los valores de la enumeración que indica si se debe comprimir o descomprimir la secuencia.</param>
      <param name="leaveOpen">true para mantener la secuencia abierta después de desechar el objeto <see cref="T:System.IO.Compression.GZipStream" />; en caso contrario, false.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="stream" /> es null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> no es válida <see cref="T:System.IO.Compression.CompressionMode" /> valor.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Compress" />  y <see cref="P:System.IO.Stream.CanWrite" /> es false.o bien<see cref="T:System.IO.Compression.CompressionMode" /> es <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  y <see cref="P:System.IO.Stream.CanRead" /> es false.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.BaseStream">
      <summary>Obtiene una referencia a la secuencia subyacente.</summary>
      <returns>Un objeto de secuencia que representa la secuencia subyacente.</returns>
      <exception cref="T:System.ObjectDisposedException">La secuencia subyacente está cerrada.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanRead">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de lectura mientras se descomprime un archivo.</summary>
      <returns>true si el valor de <see cref="T:System.IO.Compression.CompressionMode" /> es Decompress, y la secuencia subyacente admite operaciones de lectura y no está cerrada; en caso contrario, false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanSeek">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de búsqueda.</summary>
      <returns>false en todos los casos.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanWrite">
      <summary>Obtiene un valor que indica si la secuencia admite operaciones de escritura.</summary>
      <returns>true si el valor de <see cref="T:System.IO.Compression.CompressionMode" /> es Compress y la secuencia subyacente admite operaciones de escritura y no está cerrada; en caso contrario, false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.IO.Compression.GZipStream" /> y libera los recursos administrados de forma opcional.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Flush">
      <summary>La implementación actual de este método no tiene funcionalidad.</summary>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Length">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Position">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Introduce varios bytes descomprimidos leídos en la matriz de bytes especificada.</summary>
      <returns>El número de bytes que se han descomprimido en la matriz de bytes.Si se ha llegado al final de la secuencia, se devuelve cero o el número de bytes leídos.</returns>
      <param name="array">Matriz que se utiliza para almacenar los bytes descomprimidos.</param>
      <param name="offset">Desplazamiento de bytes en <paramref name="array" /> donde se colocarán los bytes leídos.</param>
      <param name="count">Número máximo de bytes descomprimidos que se van a leer.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="array" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">El <see cref="T:System.IO.Compression.CompressionMode" /> valor era Compress cuando se creó el objeto.o bienLa secuencia subyacente no admite operaciones de lectura.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> o <paramref name="count" /> es menor que cero.o bien<paramref name="array" /> longitud menos el punto inicial del índice es menor que <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">El formato de los datos no es válido.</exception>
      <exception cref="T:System.ObjectDisposedException">La secuencia está cerrada.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <returns>Un valor largo.</returns>
      <param name="offset">Ubicación en la secuencia.</param>
      <param name="origin">Uno de los valores de <see cref="T:System.IO.SeekOrigin" />.</param>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.SetLength(System.Int64)">
      <summary>Esta propiedad no se admite y siempre produce una excepción <see cref="T:System.NotSupportedException" />.</summary>
      <param name="value">Longitud de la secuencia.</param>
      <exception cref="T:System.NotSupportedException">Esta propiedad no se admite en esta secuencia.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Escribe los bytes comprimidos en la secuencia subyacente de la matriz de bytes especificada.</summary>
      <param name="array">Búfer que contiene los datos que se van a comprimir.</param>
      <param name="offset">Desplazamiento de bytes en <paramref name="array" /> donde se leerán los bytes.</param>
      <param name="count">Número máximo de bytes que se pueden escribir.</param>
      <exception cref="T:System.ObjectDisposedException">La operación de escritura no se puede realizar porque la secuencia está cerrada.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipArchive">
      <summary>Representa un paquete de archivos comprimidos en formato de archivo zip.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.ZipArchive" /> a partir de la secuencia especificada.</summary>
      <param name="stream">Secuencia que contiene el archivo que se va a leer.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed or does not support reading.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream are not in the zip archive format.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.ZipArchive" /> a partir de la secuencia especificada y con el modo especificado.</summary>
      <param name="stream">Flujo de entrada o de salida.</param>
      <param name="mode">Uno de los valores de enumeración que indica si el archivo zip se usa para leer, crear o actualizar entradas.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.ZipArchive" /> en el flujo especificado para el modo especificado y, opcionalmente, deja el flujo abierto.</summary>
      <param name="stream">Flujo de entrada o de salida.</param>
      <param name="mode">Uno de los valores de enumeración que indica si el archivo zip se usa para leer, crear o actualizar entradas.</param>
      <param name="leaveOpen">true para dejar el flujo abierto después de desechar el objeto <see cref="T:System.IO.Compression.ZipArchive" />; de lo contrario, false.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.IO.Compression.ZipArchive" /> en el flujo especificado para el modo especificado, usa la codificación especificada para los nombres de entrada y opcionalmente deja el flujo abierto.</summary>
      <param name="stream">Flujo de entrada o de salida.</param>
      <param name="mode">Uno de los valores de enumeración que indica si el archivo zip se usa para leer, crear o actualizar entradas.</param>
      <param name="leaveOpen">true para dejar el flujo abierto después de desechar el objeto <see cref="T:System.IO.Compression.ZipArchive" />; de lo contrario, false.</param>
      <param name="entryNameEncoding">Codificación que se va a usar al leer o escribir nombres de entrada en este archivo.Especifique un valor para este parámetro únicamente cuando se necesite una codificación para la interoperabilidad con herramientas y bibliotecas de archivos zip que no admiten la codificación UTF-8 para los nombres de entrada.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String)">
      <summary>Crea una entrada vacía en el archivo zip con la ruta de acceso y el nombre de entrada especificados.</summary>
      <returns>Una entrada vacía en el archivo zip.</returns>
      <param name="entryName">Ruta de acceso, relativa a la raíz del archivo, que especifica el nombre de la entrada que se va a crear.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String,System.IO.Compression.CompressionLevel)">
      <summary>Crea una entrada vacía en el archivo zip con el nombre de entrada y el nivel de compresión especificados.</summary>
      <returns>Una entrada vacía en el archivo zip.</returns>
      <param name="entryName">Ruta de acceso, relativa a la raíz del archivo, que especifica el nombre de la entrada que se va a crear.</param>
      <param name="compressionLevel">Uno de los valores de enumeración que indica si se va a hacer hincapié en la eficacia de velocidad o de compresión al crear la entrada.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose">
      <summary>Libera los recursos utilizados por la instancia actual de la clase <see cref="T:System.IO.Compression.ZipArchive" />.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose(System.Boolean)">
      <summary>Llamado por los métodos <see cref="M:System.IO.Compression.ZipArchive.Dispose" /> y <see cref="M:System.Object.Finalize" /> para liberar los recursos no administrados utilizados por la instancia actual de la clase de <see cref="T:System.IO.Compression.ZipArchive" />, y termina opcionalmente de escribir el archivo y libera los recursos administrados.</summary>
      <param name="disposing">true para terminar de escribir el archivo y liberar recursos no administrados y administrados; false para liberar solo recursos no administrados.</param>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Entries">
      <summary>Obtiene la colección de entradas que están actualmente en el archivo zip.</summary>
      <returns>La colección de entradas que están actualmente en el archivo zip.</returns>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.GetEntry(System.String)">
      <summary>Recupera un contenedor para la entrada especificada en el archivo zip.</summary>
      <returns>Un contenedor para la entrada especificada en el archivo; null si la entrada no existe en el archivo.</returns>
      <param name="entryName">Ruta de acceso, relativa a la raíz del archivo, que identifica la entrada a recuperar.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Mode">
      <summary>Obtiene un valor que describe el tipo de acción que el archivo zip puede realizar en las entradas.</summary>
      <returns>Uno de los valores de enumeración que describe el tipo de acción (lectura, creación o actualización) que puede realizar el archivo zip en las entradas.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveEntry">
      <summary>Representa un archivo comprimido incluido en un archivo zip.</summary>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Archive">
      <summary>Obtiene el archivo zip al que pertenece la entrada.</summary>
      <returns>El archivo zip al que pertenece la entrada o null si se ha eliminado la entrada.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.CompressedLength">
      <summary>Obtiene el tamaño comprimido de la entrada en el archivo zip.</summary>
      <returns>Tamaño comprimido de la entrada en el archivo zip.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Delete">
      <summary>Elimina la entrada del archivo zip.</summary>
      <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.FullName">
      <summary>Obtiene la ruta de acceso relativa de la entrada en el archivo zip.</summary>
      <returns>Ruta de acceso relativa de la entrada en el archivo zip.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.LastWriteTime">
      <summary>Obtiene o establece la última vez que se cambió la entrada en el archivo zip.</summary>
      <returns>Última vez que la entrada del archivo zip se cambió.</returns>
      <exception cref="T:System.NotSupportedException">The attempt to set this property failed, because the zip archive for the entry is in <see cref="F:System.IO.Compression.ZipArchiveMode.Read" /> mode.</exception>
      <exception cref="T:System.IO.IOException">The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />.- or -The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and the entry has been opened.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt was made to set this property to a value that is either earlier than 1980 January 1 0:00:00 (midnight) or later than 2107 December 31 23:59:58 (one second before midnight).</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Length">
      <summary>Obtiene el tamaño descomprimido de la entrada en el archivo zip.</summary>
      <returns>Tamaño descomprimido de la entrada en el archivo zip.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Name">
      <summary>Obtiene el nombre de archivo de la entrada en el archivo zip.</summary>
      <returns>Nombre de archivo de la entrada en el archivo zip.</returns>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Open">
      <summary>Abre la entrada desde el archivo zip.</summary>
      <returns>La secuencia que representa el contenido de la entrada.</returns>
      <exception cref="T:System.IO.IOException">The entry is already currently open for writing.-or-The entry has been deleted from the archive.-or-The archive for this entry was opened with the <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> mode, and this entry has already been written to. </exception>
      <exception cref="T:System.IO.InvalidDataException">The entry is either missing from the archive or is corrupt and cannot be read. -or-The entry has been compressed by using a compression method that is not supported.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.ToString">
      <summary>Recupera la ruta de acceso relativa de la entrada en el archivo zip.</summary>
      <returns>La ruta de acceso relativa de la entrada, que es el valor almacenado en la propiedad <see cref="P:System.IO.Compression.ZipArchiveEntry.FullName" />.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveMode">
      <summary>Especifica los valores para interactuar con entradas de archivo zip.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Create">
      <summary>Solo se permite crear nuevas entradas de archivo.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Read">
      <summary>Solo se permite leer entradas del archivo.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Update">
      <summary>Ambas operaciones de lectura y escritura se permiten para las entradas del archivo.</summary>
    </member>
  </members>
</doc>