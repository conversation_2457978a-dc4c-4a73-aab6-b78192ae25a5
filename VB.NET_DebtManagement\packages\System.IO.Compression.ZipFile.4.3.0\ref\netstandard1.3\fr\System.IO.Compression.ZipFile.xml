﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression.ZipFile</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.ZipFile">
      <summary>Fournit des méthodes statiques pour la création, l'extraction et l'ouverture des archives zip. </summary>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String)">
      <summary>Crée une archive zip qui contient les fichiers et les répertoires à partir du répertoire spécifié.</summary>
      <param name="sourceDirectoryName">Chemin d'accès au répertoire à archiver, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="destinationArchiveFileName">Chemin d'accès de l'archive à créer, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Crée une archive zip qui contient les fichiers et les répertoires du répertoire spécifié, utilise le niveau de compression spécifié et inclut éventuellement le répertoire de base.</summary>
      <param name="sourceDirectoryName">Chemin d'accès au répertoire à archiver, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="destinationArchiveFileName">Chemin d'accès de l'archive à créer, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="compressionLevel">Une des valeurs d'énumération qui indique s'il faut privilégier la rapidité ou l'efficacité de la compression lors de la création de l'entrée.</param>
      <param name="includeBaseDirectory">true pour inclure le nom de répertoire de <paramref name="sourceDirectoryName" /> à la racine de l'archive ; false pour inclure uniquement le contenu du répertoire.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean,System.Text.Encoding)">
      <summary>Crée une archive zip qui contient les fichiers et les répertoires du répertoire spécifié, utilise le niveau de compression et l'encodage de caractères spécifiés pour les noms d'entrée, et inclut éventuellement le répertoire de base.</summary>
      <param name="sourceDirectoryName">Chemin d'accès au répertoire à archiver, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="destinationArchiveFileName">Chemin d'accès de l'archive à créer, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="compressionLevel">Une des valeurs d'énumération qui indique s'il faut privilégier la rapidité ou l'efficacité de la compression lors de la création de l'entrée.</param>
      <param name="includeBaseDirectory">true pour inclure le nom de répertoire de <paramref name="sourceDirectoryName" /> à la racine de l'archive ; false pour inclure uniquement le contenu du répertoire.</param>
      <param name="entryNameEncoding">Encodage à utiliser lors de la lecture ou de l'écriture des noms d'entrée dans cette archive.Spécifie une valeur pour ce paramètre seulement quand un encodage est obligatoire pour l'interopérabilité avec les outils et les bibliothèques d'archivage zip qui ne prennent pas en charge l'encodage UTF-8 pour les noms d'entrée.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String)">
      <summary>Extrait tous les fichiers de l'archive zip spécifiée vers un répertoire sur le système de fichiers.</summary>
      <param name="sourceArchiveFileName">Chemin d'accès à l'archive qui doit être extraite.</param>
      <param name="destinationDirectoryName">Chemin d'accès au répertoire où placer les fichiers extraits, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String,System.Text.Encoding)">
      <summary>Extrait tous les fichiers de l'archive zip spécifiée vers un répertoire sur le système de fichiers et utilise l'encodage de caractères spécifié pour les noms d'entrée.</summary>
      <param name="sourceArchiveFileName">Chemin d'accès à l'archive qui doit être extraite.</param>
      <param name="destinationDirectoryName">Chemin d'accès au répertoire où placer les fichiers extraits, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="entryNameEncoding">Encodage à utiliser lors de la lecture ou de l'écriture des noms d'entrée dans cette archive.Spécifie une valeur pour ce paramètre seulement quand un encodage est obligatoire pour l'interopérabilité avec les outils et les bibliothèques d'archivage zip qui ne prennent pas en charge l'encodage UTF-8 pour les noms d'entrée.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode)">
      <summary>Ouvre une archive zip dans le chemin d'accès et dans le mode spécifiés.</summary>
      <returns>Archive zip ouverte.</returns>
      <param name="archiveFileName">Chemin d'accès de l'archive à ouvrir, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="mode">Une des valeurs d'énumération spécifiant les actions qui sont autorisées sur les entrées de l'archive ouverte.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode,System.Text.Encoding)">
      <summary>Ouvre une archive zip dans le chemin d'accès spécifié, dans le mode spécifié et avec un encodage de caractères spécifié pour les noms d'entrée.</summary>
      <returns>Archive zip ouverte.</returns>
      <param name="archiveFileName">Chemin d'accès de l'archive à ouvrir, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="mode">Une des valeurs d'énumération spécifiant les actions qui sont autorisées sur les entrées de l'archive ouverte.</param>
      <param name="entryNameEncoding">Encodage à utiliser lors de la lecture ou de l'écriture des noms d'entrée dans cette archive.Spécifie une valeur pour ce paramètre seulement quand un encodage est obligatoire pour l'interopérabilité avec les outils et les bibliothèques d'archivage zip qui ne prennent pas en charge l'encodage UTF-8 pour les noms d'entrée.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.OpenRead(System.String)">
      <summary>Ouvre une archive zip pour la lecture au chemin d'accès spécifié.</summary>
      <returns>Archive zip ouverte.</returns>
      <param name="archiveFileName">Chemin d'accès de l'archive à ouvrir, spécifié sous forme de chemin d'accès relatif ou absolu.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipFileExtensions">
      <summary>Fournit les méthodes d'extension pour les classes <see cref="T:System.IO.Compression.ZipArchive" /> et <see cref="T:System.IO.Compression.ZipArchiveEntry" /> .</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String)">
      <summary>Archive un fichier en le compressant et en l'ajoutant à l'archive ZIP.</summary>
      <returns>Wrapper pour la nouvelle entrée dans l'archive ZIP.</returns>
      <param name="destination">Archive zip à ajouter au fichier.</param>
      <param name="sourceFileName">Chemin d'accès du fichier à archiver.Spécification possible d'un chemin d'accès absolu ou relatif.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="entryName">Nom de l'entrée à créer dans l'archive ZIP.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> est <see cref="F:System.String.Empty" />, ne contient qu'un espace blanc ou contient au moins un caractère non valide.ou<paramref name="entryName" /> a la valeur <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> ou <paramref name="entryName" /> est null.</exception>
      <exception cref="T:System.IO.PathTooLongException">Dans <paramref name="sourceFileName" />, le chemin d'accès spécifié, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.IOException">Le fichier spécifié par <paramref name="sourceFileName" /> ne peut pas être ouvert.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> spécifie un répertoire.ouL'appelant n'a pas l'autorisation requise pour accéder au fichier spécifié par <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="sourceFileName" /> est introuvable.</exception>
      <exception cref="T:System.NotSupportedException">Le format du paramètre <paramref name="sourceFileName" /> n'est pas valide.ouL'archive zip ne prend pas en charge l'écriture.</exception>
      <exception cref="T:System.ObjectDisposedException">L'archive zip de a été supprimée.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String,System.IO.Compression.CompressionLevel)">
      <summary>Archive un fichier en le compressant à l'aide du niveau de compression spécifié et en l'ajoutant à l'archive ZIP.</summary>
      <returns>Wrapper pour la nouvelle entrée dans l'archive ZIP.</returns>
      <param name="destination">Archive zip à ajouter au fichier.</param>
      <param name="sourceFileName">Chemin d'accès du fichier à archiver.Spécification possible d'un chemin d'accès absolu ou relatif.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="entryName">Nom de l'entrée à créer dans l'archive ZIP.</param>
      <param name="compressionLevel">L'une des valeurs d'énumération qui indique s'il faut mettre l'accent sur rapidité ou la compression en créant l'entrée.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> est <see cref="F:System.String.Empty" />, ne contient qu'un espace blanc ou contient au moins un caractère non valide.ou<paramref name="entryName" /> a la valeur <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> ou <paramref name="entryName" /> est null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> n'est pas valide (par exemple, il se trouve sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.PathTooLongException">Dans <paramref name="sourceFileName" />, le chemin d'accès spécifié, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.IOException">Le fichier spécifié par <paramref name="sourceFileName" /> ne peut pas être ouvert.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> spécifie un répertoire.ouL'appelant n'a pas l'autorisation requise pour accéder au fichier spécifié par <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Le fichier spécifié par <paramref name="sourceFileName" /> est introuvable.</exception>
      <exception cref="T:System.NotSupportedException">Le format du paramètre <paramref name="sourceFileName" /> n'est pas valide.ouL'archive zip ne prend pas en charge l'écriture.</exception>
      <exception cref="T:System.ObjectDisposedException">L'archive zip de a été supprimée.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToDirectory(System.IO.Compression.ZipArchive,System.String)">
      <summary>Extrait tous les fichiers dans l'archive zip d'un répertoire sur le système de fichiers.</summary>
      <param name="source">Archive ZIP depuis laquelle extraire les fichiers.</param>
      <param name="destinationDirectoryName">Chemin d'accès au répertoire dans lequel placer les fichiers extraits.Spécification possible d'un chemin d'accès absolu ou relatif.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> est <see cref="F:System.String.Empty" />, ne contient qu'un espace blanc ou contient au moins un caractère non valide.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> a la valeur null.</exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès spécifié dépasse la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé).</exception>
      <exception cref="T:System.IO.IOException">Le répertoire spécifié par <paramref name="destinationDirectoryName" /> existe déjà.ouLe nom d'une entrée dans l'archive est <see cref="F:System.String.Empty" />, il contient uniquement des espaces blancs ou il contient au moins un caractère non valide.ouL'extraction d'une entrée de l'archive pourrait créer un fichier qui se trouve en dehors du répertoire spécifié par <paramref name="destinationDirectoryName" />. (Par exemple, cela peut se produire si le nom d'entrée contient des accesseurs de répertoire parent.) ouPlusieurs entrées de l'archive portent le même nom.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise pour écrire dans le répertoire de destination.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> contient un format non valide.</exception>
      <exception cref="T:System.IO.InvalidDataException">Entrée d'archive introuvable ou endommagée.ouL'entrée d'archive a été compressée à l'aide d'une méthode de compression non prise en charge.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String)">
      <summary>Extrait une entrée de l'archive zip dans un fichier.</summary>
      <param name="source">Entrée d'archive ZIP depuis laquelle extraire un fichier.</param>
      <param name="destinationFileName">Chemin d'accès du fichier à créer à partir du contenu de l'entrée.Spécification possible d'un chemin d'accès absolu ou relatif.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou<paramref name="destinationFileName" /> spécifie un répertoire.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> existe déjà.ou Une erreur d'E/S s'est produite.ouL'entrée est actuellement ouverte en écriture.ouL'entrée a été supprimée de l'archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise pour créer le fichier.</exception>
      <exception cref="T:System.IO.InvalidDataException">L'entrée est manquante dans l'archive, ou est endommagée et ne peut pas être lue.ouL'entrée a été compressée à l'aide d'une méthode de compression non prise en charge.</exception>
      <exception cref="T:System.ObjectDisposedException">L'archive ZIP à laquelle appartient cette entrée a été supprimée.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="destinationFileName" /> n'est pas valide. ouL'archive ZIP de cette entrée a été ouverte en mode <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, qui ne permet pas la récupération des entrées.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String,System.Boolean)">
      <summary>Extrait une entrée dans l'archive zip d'un fichier et remplace éventuellement un fichier existant qui porte le même nom.</summary>
      <param name="source">Entrée d'archive ZIP depuis laquelle extraire un fichier.</param>
      <param name="destinationFileName">Chemin d'accès du fichier à créer à partir du contenu de l'entrée.Spécification possible d'un chemin d'accès absolu ou relatif.Un chemin d'accès relatif est interprété comme étant relatif au répertoire de travail actif.</param>
      <param name="overwrite">true pour remplacer un fichier existant portant le même nom que le fichier de destination ; sinon, false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> est une chaîne de longueur nulle, ne contient que des espaces blancs ou contient un ou plusieurs caractères non valides comme défini par <see cref="F:System.IO.Path.InvalidPathChars" />.ou<paramref name="destinationFileName" /> spécifie un répertoire.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> a la valeur null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Le chemin d'accès, le nom de fichier spécifié ou les deux dépassent la longueur maximale définie par le système.Par exemple, sur les plateformes Windows, les chemins et les noms de fichiers ne doivent pas dépasser, respectivement, 248 et 260 caractères.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Le chemin d'accès spécifié n'est pas valide (il se trouve, par exemple, sur un lecteur non mappé). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> existe déjà et <paramref name="overwrite" /> a la valeur false.ou Une erreur d'E/S s'est produite.ouL'entrée est actuellement ouverte en écriture.ouL'entrée a été supprimée de l'archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">L'appelant n'a pas l'autorisation requise pour créer le fichier.</exception>
      <exception cref="T:System.IO.InvalidDataException">L'entrée est manquante dans l'archive, ou est endommagée et ne peut pas être lue.ouL'entrée a été compressée à l'aide d'une méthode de compression non prise en charge.</exception>
      <exception cref="T:System.ObjectDisposedException">L'archive ZIP à laquelle appartient cette entrée a été supprimée.</exception>
      <exception cref="T:System.NotSupportedException">Le format de <paramref name="destinationFileName" /> n'est pas valide. ouL'archive ZIP de cette entrée a été ouverte en mode <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, qui ne permet pas la récupération des entrées.</exception>
    </member>
  </members>
</doc>