<?php
require_once '../config/database.php';

/**
 * فئة إدارة الإنتاج
 * Production Management Class
 */
class Production {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * إنشاء أمر إنتاج جديد
     */
    public function createProductionOrder($data) {
        try {
            $this->db->beginTransaction();
            
            // توليد رقم أمر الإنتاج
            $data['order_number'] = $this->generateOrderNumber();
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['status'] = 'planned';
            
            // إدراج أمر الإنتاج
            $orderId = $this->db->insert('production_orders', $data);
            
            // الحصول على وصفة الإنتاج
            $recipe = $this->getProductionRecipe($data['product_id']);
            
            if (empty($recipe)) {
                throw new Exception('لا توجد وصفة إنتاج لهذا المنتج');
            }
            
            // إدراج تفاصيل أمر الإنتاج
            foreach ($recipe as $material) {
                $requiredQuantity = $material['quantity_required'] * $data['quantity_to_produce'];
                
                $detailData = [
                    'order_id' => $orderId,
                    'material_id' => $material['material_id'],
                    'quantity_required' => $requiredQuantity,
                    'unit_cost' => $material['cost_per_unit']
                ];
                
                $this->db->insert('production_order_details', $detailData);
            }
            
            // حساب التكلفة الإجمالية
            $totalCost = $this->calculateProductionCost($orderId);
            $this->db->update('production_orders', 
                ['total_cost' => $totalCost], 
                'id = :id', 
                ['id' => $orderId]
            );
            
            $this->db->commit();
            
            return [
                'success' => true, 
                'message' => 'تم إنشاء أمر الإنتاج بنجاح',
                'order_id' => $orderId,
                'order_number' => $data['order_number']
            ];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'خطأ في إنشاء أمر الإنتاج: ' . $e->getMessage()];
        }
    }
    
    /**
     * بدء تنفيذ أمر الإنتاج
     */
    public function startProductionOrder($orderId) {
        try {
            $this->db->beginTransaction();
            
            // التحقق من توفر المواد الخام
            $availability = $this->checkMaterialAvailability($orderId);
            
            if (!$availability['available']) {
                return [
                    'success' => false, 
                    'message' => 'المواد الخام غير متوفرة بالكمية المطلوبة',
                    'missing_materials' => $availability['missing_materials']
                ];
            }
            
            // حجز المواد الخام
            $this->reserveMaterials($orderId);
            
            // تحديث حالة أمر الإنتاج
            $this->db->update('production_orders', [
                'status' => 'in_progress',
                'start_date' => date('Y-m-d'),
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $orderId]);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم بدء تنفيذ أمر الإنتاج بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'خطأ في بدء أمر الإنتاج: ' . $e->getMessage()];
        }
    }
    
    /**
     * إنهاء أمر الإنتاج
     */
    public function completeProductionOrder($orderId, $actualQuantity, $warehouseId) {
        try {
            $this->db->beginTransaction();
            
            // الحصول على تفاصيل أمر الإنتاج
            $order = $this->getProductionOrder($orderId);
            
            if (!$order || $order['status'] !== 'in_progress') {
                throw new Exception('أمر الإنتاج غير صالح أو غير قيد التنفيذ');
            }
            
            // استهلاك المواد الخام
            $this->consumeMaterials($orderId, $actualQuantity, $order['quantity_to_produce']);
            
            // إضافة المنتج النهائي للمخزون
            $this->addFinishedProduct($order['product_id'], $actualQuantity, $warehouseId, $orderId);
            
            // تحديث أمر الإنتاج
            $this->db->update('production_orders', [
                'status' => 'completed',
                'quantity_produced' => $actualQuantity,
                'actual_end_date' => date('Y-m-d'),
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $orderId]);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إنهاء أمر الإنتاج بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'خطأ في إنهاء أمر الإنتاج: ' . $e->getMessage()];
        }
    }
    
    /**
     * إلغاء أمر الإنتاج
     */
    public function cancelProductionOrder($orderId, $reason = '') {
        try {
            $this->db->beginTransaction();
            
            // إلغاء حجز المواد الخام
            $this->unreserveMaterials($orderId);
            
            // تحديث حالة أمر الإنتاج
            $this->db->update('production_orders', [
                'status' => 'cancelled',
                'notes' => $reason,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = :id', ['id' => $orderId]);
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم إلغاء أمر الإنتاج بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'خطأ في إلغاء أمر الإنتاج: ' . $e->getMessage()];
        }
    }
    
    /**
     * إدارة وصفة الإنتاج
     */
    public function manageProductionRecipe($productId, $materials) {
        try {
            $this->db->beginTransaction();
            
            // حذف الوصفة الحالية
            $this->db->delete('production_recipes', 'product_id = :product_id', ['product_id' => $productId]);
            
            // إضافة المواد الجديدة
            foreach ($materials as $material) {
                $recipeData = [
                    'product_id' => $productId,
                    'material_id' => $material['material_id'],
                    'quantity_required' => $material['quantity_required'],
                    'cost_per_unit' => $material['cost_per_unit'] ?? 0,
                    'is_active' => true
                ];
                
                $this->db->insert('production_recipes', $recipeData);
            }
            
            $this->db->commit();
            
            return ['success' => true, 'message' => 'تم حفظ وصفة الإنتاج بنجاح'];
            
        } catch (Exception $e) {
            $this->db->rollback();
            return ['success' => false, 'message' => 'خطأ في حفظ وصفة الإنتاج: ' . $e->getMessage()];
        }
    }
    
    /**
     * الحصول على وصفة الإنتاج
     */
    public function getProductionRecipe($productId) {
        $sql = "SELECT pr.*, i.name as material_name, i.unit 
                FROM production_recipes pr
                JOIN items i ON pr.material_id = i.id
                WHERE pr.product_id = :product_id AND pr.is_active = 1
                ORDER BY i.name";
        
        return $this->db->fetchAll($sql, ['product_id' => $productId]);
    }
    
    /**
     * التحقق من توفر المواد الخام
     */
    private function checkMaterialAvailability($orderId) {
        $sql = "SELECT pod.material_id, pod.quantity_required, i.name as material_name,
                       COALESCE(SUM(ist.available_quantity), 0) as available_quantity
                FROM production_order_details pod
                JOIN items i ON pod.material_id = i.id
                LEFT JOIN item_stock ist ON pod.material_id = ist.item_id
                WHERE pod.order_id = :order_id
                GROUP BY pod.material_id, pod.quantity_required, i.name";
        
        $materials = $this->db->fetchAll($sql, ['order_id' => $orderId]);
        $missingMaterials = [];
        $available = true;
        
        foreach ($materials as $material) {
            if ($material['available_quantity'] < $material['quantity_required']) {
                $available = false;
                $missingMaterials[] = [
                    'material_name' => $material['material_name'],
                    'required' => $material['quantity_required'],
                    'available' => $material['available_quantity'],
                    'shortage' => $material['quantity_required'] - $material['available_quantity']
                ];
            }
        }
        
        return [
            'available' => $available,
            'missing_materials' => $missingMaterials
        ];
    }
    
    /**
     * حجز المواد الخام
     */
    private function reserveMaterials($orderId) {
        $materials = $this->db->fetchAll(
            "SELECT material_id, quantity_required FROM production_order_details WHERE order_id = :order_id",
            ['order_id' => $orderId]
        );
        
        foreach ($materials as $material) {
            // تحديث الكمية المحجوزة في المخزون
            $sql = "UPDATE item_stock 
                    SET reserved_quantity = reserved_quantity + :quantity
                    WHERE item_id = :item_id AND quantity >= reserved_quantity + :quantity";
            
            $this->db->query($sql, [
                'quantity' => $material['quantity_required'],
                'item_id' => $material['material_id']
            ]);
        }
    }
    
    /**
     * إلغاء حجز المواد الخام
     */
    private function unreserveMaterials($orderId) {
        $materials = $this->db->fetchAll(
            "SELECT material_id, quantity_required FROM production_order_details WHERE order_id = :order_id",
            ['order_id' => $orderId]
        );
        
        foreach ($materials as $material) {
            $sql = "UPDATE item_stock 
                    SET reserved_quantity = GREATEST(0, reserved_quantity - :quantity)
                    WHERE item_id = :item_id";
            
            $this->db->query($sql, [
                'quantity' => $material['quantity_required'],
                'item_id' => $material['material_id']
            ]);
        }
    }
    
    /**
     * استهلاك المواد الخام
     */
    private function consumeMaterials($orderId, $actualQuantity, $plannedQuantity) {
        $materials = $this->db->fetchAll(
            "SELECT material_id, quantity_required, unit_cost FROM production_order_details WHERE order_id = :order_id",
            ['order_id' => $orderId]
        );
        
        foreach ($materials as $material) {
            $consumedQuantity = ($material['quantity_required'] / $plannedQuantity) * $actualQuantity;
            
            // تحديث الكمية المستهلكة في تفاصيل أمر الإنتاج
            $this->db->update('production_order_details', 
                ['quantity_consumed' => $consumedQuantity], 
                'order_id = :order_id AND material_id = :material_id', 
                ['order_id' => $orderId, 'material_id' => $material['material_id']]
            );
            
            // تسجيل حركة المخزون (صادر)
            $this->recordStockMovement(
                $material['material_id'],
                1, // المخزن الافتراضي
                'out',
                $consumedQuantity,
                $material['unit_cost'],
                'production_order',
                $orderId
            );
            
            // تحديث المخزون
            $this->updateStock($material['material_id'], 1, -$consumedQuantity, -$consumedQuantity);
        }
    }
    
    /**
     * إضافة المنتج النهائي للمخزون
     */
    private function addFinishedProduct($productId, $quantity, $warehouseId, $orderId) {
        // حساب تكلفة الوحدة
        $totalCost = $this->calculateProductionCost($orderId);
        $unitCost = $quantity > 0 ? $totalCost / $quantity : 0;
        
        // تسجيل حركة المخزون (وارد)
        $this->recordStockMovement(
            $productId,
            $warehouseId,
            'in',
            $quantity,
            $unitCost,
            'production_order',
            $orderId
        );
        
        // تحديث المخزون
        $this->updateStock($productId, $warehouseId, $quantity, 0);
        
        // تحديث تكلفة الصنف
        $this->db->update('items', 
            ['cost_price' => $unitCost], 
            'id = :id', 
            ['id' => $productId]
        );
    }
    
    /**
     * تسجيل حركة المخزون
     */
    private function recordStockMovement($itemId, $warehouseId, $type, $quantity, $unitCost, $referenceType, $referenceId) {
        $movementData = [
            'item_id' => $itemId,
            'warehouse_id' => $warehouseId,
            'movement_type' => $type,
            'quantity' => $quantity,
            'unit_cost' => $unitCost,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'movement_date' => date('Y-m-d'),
            'created_by' => $_SESSION['user_id'] ?? 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('stock_movements', $movementData);
    }
    
    /**
     * تحديث المخزون
     */
    private function updateStock($itemId, $warehouseId, $quantityChange, $reservedChange) {
        // التحقق من وجود السجل
        $exists = $this->db->exists('item_stock', 
            'item_id = :item_id AND warehouse_id = :warehouse_id',
            ['item_id' => $itemId, 'warehouse_id' => $warehouseId]
        );
        
        if ($exists) {
            $sql = "UPDATE item_stock 
                    SET quantity = quantity + :quantity_change,
                        reserved_quantity = GREATEST(0, reserved_quantity + :reserved_change),
                        last_updated = CURRENT_TIMESTAMP
                    WHERE item_id = :item_id AND warehouse_id = :warehouse_id";
        } else {
            $sql = "INSERT INTO item_stock (item_id, warehouse_id, quantity, reserved_quantity, last_updated)
                    VALUES (:item_id, :warehouse_id, :quantity_change, GREATEST(0, :reserved_change), CURRENT_TIMESTAMP)";
        }
        
        $this->db->query($sql, [
            'item_id' => $itemId,
            'warehouse_id' => $warehouseId,
            'quantity_change' => $quantityChange,
            'reserved_change' => $reservedChange
        ]);
    }
    
    /**
     * حساب تكلفة الإنتاج
     */
    private function calculateProductionCost($orderId) {
        $sql = "SELECT SUM(quantity_consumed * unit_cost) as total_cost 
                FROM production_order_details 
                WHERE order_id = :order_id";
        
        $result = $this->db->fetchOne($sql, ['order_id' => $orderId]);
        return $result['total_cost'] ?? 0;
    }
    
    /**
     * توليد رقم أمر الإنتاج
     */
    private function generateOrderNumber() {
        $prefix = 'PRD-' . date('Ymd') . '-';
        $sql = "SELECT COUNT(*) as count FROM production_orders WHERE order_number LIKE :prefix";
        $result = $this->db->fetchOne($sql, ['prefix' => $prefix . '%']);
        $sequence = ($result['count'] ?? 0) + 1;
        
        return $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * الحصول على أمر إنتاج
     */
    public function getProductionOrder($orderId) {
        $sql = "SELECT po.*, i.name as product_name, i.unit, w.name as warehouse_name
                FROM production_orders po
                JOIN items i ON po.product_id = i.id
                JOIN warehouses w ON po.warehouse_id = w.id
                WHERE po.id = :id";
        
        return $this->db->fetchOne($sql, ['id' => $orderId]);
    }
    
    /**
     * الحصول على قائمة أوامر الإنتاج
     */
    public function getProductionOrders($filters = []) {
        $sql = "SELECT po.*, i.name as product_name, i.unit, w.name as warehouse_name,
                       u.full_name as created_by_name
                FROM production_orders po
                JOIN items i ON po.product_id = i.id
                JOIN warehouses w ON po.warehouse_id = w.id
                LEFT JOIN users u ON po.created_by = u.id
                WHERE 1=1";
        
        $params = [];
        
        if (isset($filters['status']) && !empty($filters['status'])) {
            $sql .= " AND po.status = :status";
            $params['status'] = $filters['status'];
        }
        
        if (isset($filters['product_id']) && !empty($filters['product_id'])) {
            $sql .= " AND po.product_id = :product_id";
            $params['product_id'] = $filters['product_id'];
        }
        
        if (isset($filters['date_from']) && !empty($filters['date_from'])) {
            $sql .= " AND po.start_date >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (isset($filters['date_to']) && !empty($filters['date_to'])) {
            $sql .= " AND po.start_date <= :date_to";
            $params['date_to'] = $filters['date_to'];
        }
        
        $sql .= " ORDER BY po.created_at DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * تقرير الإنتاج
     */
    public function getProductionReport($dateFrom, $dateTo) {
        $sql = "SELECT 
                    i.name as product_name,
                    COUNT(po.id) as orders_count,
                    SUM(po.quantity_to_produce) as planned_quantity,
                    SUM(po.quantity_produced) as actual_quantity,
                    SUM(po.total_cost) as total_cost,
                    AVG(DATEDIFF(po.actual_end_date, po.start_date)) as avg_production_days
                FROM production_orders po
                JOIN items i ON po.product_id = i.id
                WHERE po.start_date BETWEEN :date_from AND :date_to
                AND po.status = 'completed'
                GROUP BY po.product_id, i.name
                ORDER BY total_cost DESC";
        
        return $this->db->fetchAll($sql, [
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ]);
    }
}
?>
