<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة إضافة معاملة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $validation_rules = [
        'type' => ['required' => true, 'message' => 'نوع المعاملة مطلوب'],
        'entity_type' => ['required' => true, 'message' => 'نوع الطرف مطلوب'],
        'entity_id' => ['required' => true, 'message' => 'اختيار الطرف مطلوب'],
        'cash_box_id' => ['required' => true, 'message' => 'اختيار الصندوق مطلوب'],
        'amount' => ['required' => true, 'type' => 'numeric', 'message' => 'المبلغ مطلوب'],
        'currency' => ['required' => true, 'message' => 'العملة مطلوبة'],
        'transaction_date' => ['required' => true, 'message' => 'تاريخ المعاملة مطلوب']
    ];

    $errors = validateInput($_POST, $validation_rules);

    if (empty($errors)) {
        $transaction_data = [
            'type' => $_POST['type'],
            'entity_type' => $_POST['entity_type'],
            'entity_id' => intval($_POST['entity_id']),
            'cash_box_id' => intval($_POST['cash_box_id']),
            'amount' => floatval($_POST['amount']),
            'currency' => $_POST['currency'],
            'exchange_rate' => floatval($_POST['exchange_rate'] ?? 1),
            'description' => trim($_POST['description']) ?: null,
            'reference_number' => trim($_POST['reference_number']) ?: null,
            'transaction_date' => $_POST['transaction_date']
        ];

        $transaction_id = addTransaction($transaction_data);

        if ($transaction_id) {
            $message = 'تم إضافة المعاملة بنجاح';
            $message_type = 'success';
            logActivity('إضافة معاملة', "تم إضافة معاملة بمبلغ {$transaction_data['amount']} {$transaction_data['currency']}");
        } else {
            $message = 'حدث خطأ أثناء إضافة المعاملة';
            $message_type = 'danger';
        }
    } else {
        $message = 'يرجى تصحيح الأخطاء في النموذج';
        $message_type = 'danger';
    }
}

// جلب قائمة المعاملات
try {
    $stmt = $pdo->query("
        SELECT t.*,
               CASE
                   WHEN t.entity_type = 'customer' THEN c.name
                   WHEN t.entity_type = 'supplier' THEN s.name
               END as entity_name,
               cb.name as cash_box_name,
               u.username as created_by_name
        FROM transactions t
        LEFT JOIN customers c ON t.entity_type = 'customer' AND t.entity_id = c.id
        LEFT JOIN suppliers s ON t.entity_type = 'supplier' AND t.entity_id = s.id
        LEFT JOIN cash_boxes cb ON t.cash_box_id = cb.id
        LEFT JOIN users u ON t.created_by = u.id
        ORDER BY t.created_at DESC
        LIMIT 100
    ");
    $transactions = $stmt->fetchAll();
} catch (PDOException $e) {
    $transactions = [];
    error_log("خطأ جلب المعاملات: " . $e->getMessage());
}

// جلب قائمة العملاء
try {
    $stmt = $pdo->query("SELECT id, name FROM customers WHERE is_active = 1 ORDER BY name ASC");
    $customers = $stmt->fetchAll();
} catch (PDOException $e) {
    $customers = [];
}

// جلب قائمة الموردين
try {
    $stmt = $pdo->query("SELECT id, name FROM suppliers WHERE is_active = 1 ORDER BY name ASC");
    $suppliers = $stmt->fetchAll();
} catch (PDOException $e) {
    $suppliers = [];
}

// جلب قائمة الصناديق
try {
    $stmt = $pdo->query("SELECT id, name, currency_id FROM cash_boxes WHERE is_active = 1 ORDER BY name ASC");
    $cash_boxes = $stmt->fetchAll();
} catch (PDOException $e) {
    $cash_boxes = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المعاملات المالية - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calculator me-2"></i>
                نظام إدارة الديون
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">المعاملات المالية</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                            <i class="fas fa-plus me-1"></i>
                            إضافة معاملة جديدة
                        </button>
                    </div>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول المعاملات -->
                <div class="card shadow">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="m-0 font-weight-bold text-primary">قائمة المعاملات</h6>
                            </div>
                            <div class="col-auto">
                                <input type="text" class="form-control table-search" placeholder="البحث..." data-table="#transactionsTable">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="transactionsTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الطرف</th>
                                        <th>المبلغ</th>
                                        <th>العملة</th>
                                        <th>الصندوق</th>
                                        <th>الوصف</th>
                                        <th>المستخدم</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($transaction['transaction_date'])); ?></td>
                                        <td>
                                            <span class="badge <?php echo $transaction['type'] == 'income' ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $transaction['type'] == 'income' ? 'وارد' : 'صادر'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($transaction['entity_name'] ?? 'غير محدد'); ?></strong>
                                            <br><small class="text-muted">
                                                <?php echo $transaction['entity_type'] == 'customer' ? 'عميل' : 'مورد'; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($transaction['amount'], 2); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $transaction['currency']; ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['cash_box_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <?php if ($transaction['description']): ?>
                                                <?php echo htmlspecialchars($transaction['description']); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($transaction['created_by_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="invoice.php?id=<?php echo $transaction['id']; ?>" class="btn btn-sm btn-success" title="طباعة الفاتورة" target="_blank">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                <a href="transaction_details.php?id=<?php echo $transaction['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_transaction.php?id=<?php echo $transaction['id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- مودال إضافة معاملة -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة معاملة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="type" class="form-label">نوع المعاملة *</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">اختر نوع المعاملة</option>
                                    <option value="income">وارد</option>
                                    <option value="expense">صادر</option>
                                </select>
                                <div class="invalid-feedback">نوع المعاملة مطلوب</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="entity_type" class="form-label">نوع الطرف *</label>
                                <select class="form-select" id="entity_type" name="entity_type" required onchange="loadEntities()">
                                    <option value="">اختر نوع الطرف</option>
                                    <option value="customer">عميل</option>
                                    <option value="supplier">مورد</option>
                                </select>
                                <div class="invalid-feedback">نوع الطرف مطلوب</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="entity_id" class="form-label">اختيار الطرف *</label>
                                <select class="form-select" id="entity_id" name="entity_id" required>
                                    <option value="">اختر الطرف</option>
                                </select>
                                <div class="invalid-feedback">اختيار الطرف مطلوب</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="cash_box_id" class="form-label">الصندوق *</label>
                                <select class="form-select" id="cash_box_id" name="cash_box_id" required>
                                    <option value="">اختر الصندوق</option>
                                    <?php foreach ($cash_boxes as $cash_box): ?>
                                        <option value="<?php echo $cash_box['id']; ?>">
                                            <?php echo htmlspecialchars($cash_box['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">اختيار الصندوق مطلوب</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="amount" class="form-label">المبلغ *</label>
                                <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0" required>
                                <div class="invalid-feedback">المبلغ مطلوب</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="currency" class="form-label">العملة *</label>
                                <select class="form-select" id="currency" name="currency" required>
                                    <option value="">اختر العملة</option>
                                    <option value="IQD">دينار عراقي</option>
                                    <option value="USD">دولار أمريكي</option>
                                </select>
                                <div class="invalid-feedback">العملة مطلوبة</div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="transaction_date" class="form-label">تاريخ المعاملة *</label>
                                <input type="date" class="form-control" id="transaction_date" name="transaction_date" value="<?php echo date('Y-m-d'); ?>" required>
                                <div class="invalid-feedback">تاريخ المعاملة مطلوب</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="reference_number" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="exchange_rate" class="form-label">سعر الصرف</label>
                                <input type="number" class="form-control" id="exchange_rate" name="exchange_rate" value="1" step="0.0001" min="0">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ المعاملة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // بيانات العملاء والموردين
        const customers = <?php echo json_encode($customers); ?>;
        const suppliers = <?php echo json_encode($suppliers); ?>;

        function loadEntities() {
            const entityType = document.getElementById('entity_type').value;
            const entitySelect = document.getElementById('entity_id');

            // مسح الخيارات الحالية
            entitySelect.innerHTML = '<option value="">اختر الطرف</option>';

            let entities = [];
            if (entityType === 'customer') {
                entities = customers;
            } else if (entityType === 'supplier') {
                entities = suppliers;
            }

            // إضافة الخيارات الجديدة
            entities.forEach(entity => {
                const option = document.createElement('option');
                option.value = entity.id;
                option.textContent = entity.name;
                entitySelect.appendChild(option);
            });
        }

        // تحديث سعر الصرف عند تغيير العملة
        document.getElementById('currency').addEventListener('change', function() {
            const exchangeRateField = document.getElementById('exchange_rate');
            if (this.value === 'USD') {
                exchangeRateField.value = '<?php echo EXCHANGE_RATE_USD_TO_IQD; ?>';
            } else {
                exchangeRateField.value = '1';
            }
        });
    </script>
</body>
</html>
