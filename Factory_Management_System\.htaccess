# Factory Management System - Apache Configuration
# نظام إدارة المعامل - إعدادات أباتشي

# تفعيل إعادة الكتابة
RewriteEngine On

# منع الوصول للملفات الحساسة
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "database.php">
    Order Deny,Allow
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
RedirectMatch 404 /\.git
RedirectMatch 404 /config/
RedirectMatch 404 /classes/
RedirectMatch 404 /logs/
RedirectMatch 404 /backups/

# إعدادات الأمان
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;"
</IfModule>

# ضغط الملفات
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تخزين مؤقت للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/icon "access plus 1 year"
    ExpiresByType text/x-icon "access plus 1 year"
    ExpiresByType application/x-icon "access plus 1 year"
    ExpiresByType font/truetype "access plus 1 year"
    ExpiresByType font/opentype "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
</IfModule>

# منع عرض قائمة الملفات
Options -Indexes

# صفحات الأخطاء المخصصة
ErrorDocument 404 /Factory_Management_System/public/errors/404.php
ErrorDocument 403 /Factory_Management_System/public/errors/403.php
ErrorDocument 500 /Factory_Management_System/public/errors/500.php

# حد حجم الملف المرفوع
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_time 300

# إعدادات الجلسة
php_value session.cookie_httponly 1
php_value session.cookie_secure 0
php_value session.use_only_cookies 1

# منع الوصول للملفات الاحتياطية
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# توجيه الصفحة الرئيسية
DirectoryIndex index.php index.html

# إعدادات MIME
AddType application/x-font-woff .woff
AddType application/x-font-woff2 .woff2
