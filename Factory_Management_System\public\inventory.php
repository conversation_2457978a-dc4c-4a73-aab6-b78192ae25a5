<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$category = $_GET['category'] ?? '';
$type = $_GET['type'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$sql = "SELECT i.*, ic.name as category_name, c.symbol as currency_symbol,
               COALESCE(SUM(ist.quantity), 0) as total_stock,
               COALESCE(SUM(ist.available_quantity), 0) as available_stock
        FROM items i
        LEFT JOIN item_categories ic ON i.category_id = ic.id
        LEFT JOIN currencies c ON i.currency_id = c.id
        LEFT JOIN item_stock ist ON i.id = ist.item_id
        WHERE i.is_active = 1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (i.name LIKE :search OR i.code LIKE :search OR i.description LIKE :search)";
    $params['search'] = "%$search%";
}

if (!empty($category)) {
    $sql .= " AND i.category_id = :category";
    $params['category'] = $category;
}

if (!empty($type)) {
    $sql .= " AND i.type = :type";
    $params['type'] = $type;
}

$sql .= " GROUP BY i.id ORDER BY i.name LIMIT :limit OFFSET :offset";
$params['limit'] = $limit;
$params['offset'] = $offset;

try {
    $items = $db->fetchAll($sql, $params);

    // عدد النتائج الإجمالي
    $countSql = str_replace("SELECT i.*, ic.name as category_name, c.symbol as currency_symbol,
               COALESCE(SUM(ist.quantity), 0) as total_stock,
               COALESCE(SUM(ist.available_quantity), 0) as available_stock", "SELECT COUNT(DISTINCT i.id) as count", $sql);
    $countSql = str_replace("GROUP BY i.id ORDER BY i.name LIMIT :limit OFFSET :offset", "", $countSql);
    unset($params['limit'], $params['offset']);
    $totalItems = $db->fetchOne($countSql, $params)['count'] ?? 0;
    $totalPages = ceil($totalItems / $limit);

    // الفئات للفلترة
    $categories = $db->fetchAll("SELECT * FROM item_categories WHERE is_active = 1 ORDER BY name");

} catch (Exception $e) {
    $items = [];
    $totalItems = 0;
    $totalPages = 0;
    $categories = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .table th { background-color: #f8f9fa; }
        .badge-low-stock { background-color: #dc3545; }
        .badge-normal-stock { background-color: #28a745; }
        .badge-warning-stock { background-color: #ffc107; color: #000; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>إدارة المخزون</h5>
                        <div>
                            <a href="add_item.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة صنف جديد
                            </a>
                            <a href="stock_movements.php" class="btn btn-info">
                                <i class="fas fa-exchange-alt me-2"></i>حركات المخزون
                            </a>
                            <a href="setup_inventory.php" class="btn btn-warning">
                                <i class="fas fa-cog me-2"></i>إعداد النظام
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- فلاتر البحث -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-4">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="اسم الصنف أو الكود...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">الفئة</label>
                                <select class="form-select" name="category">
                                    <option value="">جميع الفئات</option>
                                    <?php foreach ($categories as $cat): ?>
                                        <option value="<?= $cat['id'] ?>" <?= $category == $cat['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($cat['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">النوع</label>
                                <select class="form-select" name="type">
                                    <option value="">جميع الأنواع</option>
                                    <option value="raw_material" <?= $type == 'raw_material' ? 'selected' : '' ?>>مادة خام</option>
                                    <option value="finished_product" <?= $type == 'finished_product' ? 'selected' : '' ?>>منتج نهائي</option>
                                    <option value="semi_finished" <?= $type == 'semi_finished' ? 'selected' : '' ?>>نصف مصنع</option>
                                    <option value="consumable" <?= $type == 'consumable' ? 'selected' : '' ?>>مواد استهلاكية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="inventory.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </div>
                        </form>

                        <!-- جدول الأصناف -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الكود</th>
                                        <th>اسم الصنف</th>
                                        <th>الفئة</th>
                                        <th>النوع</th>
                                        <th>الوحدة</th>
                                        <th>المخزون الحالي</th>
                                        <th>المخزون المتاح</th>
                                        <th>سعر التكلفة</th>
                                        <th>سعر البيع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($items)): ?>
                                        <tr>
                                            <td colspan="11" class="text-center text-muted py-4">
                                                <i class="fas fa-box-open fa-3x mb-3"></i><br>
                                                لا توجد أصناف مطابقة للبحث
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($items as $item): ?>
                                            <tr>
                                                <td><code><?= htmlspecialchars($item['code']) ?></code></td>
                                                <td>
                                                    <strong><?= htmlspecialchars($item['name']) ?></strong>
                                                    <?php if ($item['description']): ?>
                                                        <br><small class="text-muted"><?= htmlspecialchars($item['description']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= htmlspecialchars($item['category_name'] ?? 'غير محدد') ?></td>
                                                <td>
                                                    <?php
                                                    $typeLabels = [
                                                        'raw_material' => 'مادة خام',
                                                        'finished_product' => 'منتج نهائي',
                                                        'semi_finished' => 'نصف مصنع',
                                                        'consumable' => 'استهلاكية'
                                                    ];
                                                    echo $typeLabels[$item['type']] ?? $item['type'];
                                                    ?>
                                                </td>
                                                <td><?= htmlspecialchars($item['unit']) ?></td>
                                                <td><?= number_format($item['total_stock'], 2) ?></td>
                                                <td>
                                                    <?= number_format($item['available_stock'], 2) ?>
                                                    <?php
                                                    $stockStatus = '';
                                                    if ($item['available_stock'] <= 0) {
                                                        $stockStatus = '<span class="badge badge-low-stock">نفد المخزون</span>';
                                                    } elseif ($item['available_stock'] <= $item['reorder_level']) {
                                                        $stockStatus = '<span class="badge badge-warning-stock">تحت الحد الأدنى</span>';
                                                    } else {
                                                        $stockStatus = '<span class="badge badge-normal-stock">متوفر</span>';
                                                    }
                                                    echo $stockStatus;
                                                    ?>
                                                </td>
                                                <td><?= formatMoney($item['cost_price'], $item['currency_symbol'] ?? 'IQD') ?></td>
                                                <td><?= formatMoney($item['selling_price'], $item['currency_symbol'] ?? 'IQD') ?></td>
                                                <td>
                                                    <span class="badge bg-success">نشط</span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="edit_item.php?id=<?= $item['id'] ?>" class="btn btn-outline-primary" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="item_details.php?id=<?= $item['id'] ?>" class="btn btn-outline-info" title="التفاصيل">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="stock_adjustment.php?id=<?= $item['id'] ?>" class="btn btn-outline-warning" title="تسوية المخزون">
                                                            <i class="fas fa-balance-scale"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&type=<?= urlencode($type) ?>">السابق</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&type=<?= urlencode($type) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&category=<?= urlencode($category) ?>&type=<?= urlencode($type) ?>">التالي</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>

                        <!-- معلومات إضافية -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    إجمالي الأصناف: <strong><?= $totalItems ?></strong>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    الأصناف تحت الحد الأدنى: <strong><?= count(array_filter($items, function($item) { return $item['available_stock'] <= $item['reorder_level']; })) ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
