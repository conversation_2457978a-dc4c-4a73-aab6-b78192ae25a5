<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$saleId = (int)($_GET['id'] ?? 0);
if (!$saleId) {
    header('Location: sales.php');
    exit;
}

// جلب بيانات فاتورة المبيعات
try {
    $sale = $db->fetchOne("
        SELECT s.*, c.name as customer_name, c.phone as customer_phone, 
               c.address as customer_address, u.full_name as created_by_name,
               cur.symbol as currency_symbol
        FROM sales s
        LEFT JOIN customers c ON s.customer_id = c.id
        LEFT JOIN users u ON s.created_by = u.id
        LEFT JOIN currencies cur ON s.currency_id = cur.id
        WHERE s.id = ?
    ", [$saleId]);
    
    if (!$sale) {
        header('Location: sales.php');
        exit;
    }
    
    // جلب تفاصيل الفاتورة
    $items = $db->fetchAll("
        SELECT sd.*, i.name as item_name, i.unit as item_unit, i.code as item_code
        FROM sale_details sd
        LEFT JOIN items i ON sd.item_id = i.id
        WHERE sd.sale_id = ?
        ORDER BY sd.id
    ", [$saleId]);
    
} catch (Exception $e) {
    header('Location: sales.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة مبيعات - <?= htmlspecialchars($sale['invoice_number']) ?></title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 20px;
            background: white;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #333;
            padding: 20px;
        }
        
        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        
        .company-info {
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 14px;
            color: #666;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #28a745;
            margin: 15px 0;
        }
        
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .info-section {
            flex: 1;
        }
        
        .info-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .info-item {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 10px;
            text-align: center;
        }
        
        .items-table th {
            background: #333;
            color: white;
            font-weight: bold;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .totals-section {
            float: left;
            width: 300px;
            border: 2px solid #333;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .total-row.final {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 18px;
            color: #28a745;
        }
        
        .footer {
            clear: both;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        
        .print-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .print-btn:hover {
            background: #218838;
        }
        
        .payment-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-paid { background: #d4edda; color: #155724; }
        .status-partial { background: #fff3cd; color: #856404; }
        .status-unpaid { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="window.print()" class="print-btn">
            <i class="fas fa-print"></i> طباعة الفاتورة
        </button>
        <button onclick="window.close()" class="print-btn" style="background: #dc3545;">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-name">شركة المعامل المتقدمة</div>
                <div class="company-details">
                    العراق - بغداد | هاتف: 07901234567 | البريد الإلكتروني: <EMAIL>
                </div>
            </div>
            <div class="invoice-title">فاتورة مبيعات</div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="invoice-info">
            <div class="info-section">
                <div class="info-title">معلومات الفاتورة</div>
                <div class="info-item"><strong>رقم الفاتورة:</strong> <?= htmlspecialchars($sale['invoice_number']) ?></div>
                <div class="info-item"><strong>تاريخ الفاتورة:</strong> <?= date('d/m/Y', strtotime($sale['sale_date'])) ?></div>
                <div class="info-item"><strong>تاريخ الاستحقاق:</strong> <?= $sale['due_date'] ? date('d/m/Y', strtotime($sale['due_date'])) : 'فوري' ?></div>
                <div class="info-item"><strong>أنشأ بواسطة:</strong> <?= htmlspecialchars($sale['created_by_name']) ?></div>
                <div class="info-item">
                    <strong>حالة الدفع:</strong> 
                    <?php
                    $statusClass = '';
                    $statusText = '';
                    switch($sale['payment_status']) {
                        case 'paid':
                            $statusClass = 'status-paid';
                            $statusText = 'مدفوع';
                            break;
                        case 'partial':
                            $statusClass = 'status-partial';
                            $statusText = 'مدفوع جزئياً';
                            break;
                        default:
                            $statusClass = 'status-unpaid';
                            $statusText = 'غير مدفوع';
                    }
                    ?>
                    <span class="payment-status <?= $statusClass ?>"><?= $statusText ?></span>
                </div>
            </div>
            
            <div class="info-section">
                <div class="info-title">معلومات العميل</div>
                <div class="info-item"><strong>اسم العميل:</strong> <?= htmlspecialchars($sale['customer_name'] ?: 'عميل نقدي') ?></div>
                <?php if ($sale['customer_phone']): ?>
                <div class="info-item"><strong>الهاتف:</strong> <?= htmlspecialchars($sale['customer_phone']) ?></div>
                <?php endif; ?>
                <?php if ($sale['customer_address']): ?>
                <div class="info-item"><strong>العنوان:</strong> <?= htmlspecialchars($sale['customer_address']) ?></div>
                <?php endif; ?>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 15%;">كود الصنف</th>
                    <th style="width: 30%;">اسم الصنف</th>
                    <th style="width: 10%;">الوحدة</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">سعر الوحدة</th>
                    <th style="width: 15%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php $counter = 1; ?>
                <?php foreach ($items as $item): ?>
                    <tr>
                        <td><?= $counter++ ?></td>
                        <td><?= htmlspecialchars($item['item_code']) ?></td>
                        <td style="text-align: right;"><?= htmlspecialchars($item['item_name']) ?></td>
                        <td><?= htmlspecialchars($item['item_unit']) ?></td>
                        <td><?= number_format($item['quantity'], 3) ?></td>
                        <td><?= number_format($item['unit_price'], 2) ?></td>
                        <td><?= number_format($item['total_price'], 2) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?= number_format($sale['subtotal'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            
            <?php if ($sale['tax_amount'] > 0): ?>
            <div class="total-row">
                <span>الضريبة (<?= $sale['tax_rate'] ?>%):</span>
                <span><?= number_format($sale['tax_amount'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($sale['discount_amount'] > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span><?= number_format($sale['discount_amount'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            <?php endif; ?>
            
            <div class="total-row final">
                <span>المجموع النهائي:</span>
                <span><?= number_format($sale['total_amount'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            
            <?php if ($sale['paid_amount'] > 0): ?>
            <div class="total-row">
                <span>المبلغ المدفوع:</span>
                <span><?= number_format($sale['paid_amount'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            
            <div class="total-row">
                <span>المبلغ المتبقي:</span>
                <span><?= number_format($sale['total_amount'] - $sale['paid_amount'], 2) ?> <?= $sale['currency_symbol'] ?></span>
            </div>
            <?php endif; ?>
        </div>

        <div style="clear: both;"></div>

        <?php if ($sale['notes']): ?>
        <div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9;">
            <strong>ملاحظات:</strong><br>
            <?= nl2br(htmlspecialchars($sale['notes'])) ?>
        </div>
        <?php endif; ?>

        <!-- تذييل الفاتورة -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المعامل - <?= date('d/m/Y H:i') ?></p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
