<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$db = new Database();

// تشخيص سريع للمشاكل
$diagnosis = [];

try {
    // فحص الجداول الأساسية
    $tables = ['users', 'customers', 'items', 'warehouses', 'currencies', 'sales_invoices', 'sales_invoice_details'];
    foreach ($tables as $table) {
        try {
            $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
            $diagnosis['tables'][$table] = !empty($result);
        } catch (Exception $e) {
            $diagnosis['tables'][$table] = false;
        }
    }
    
    // فحص البيانات الأساسية
    $diagnosis['data']['customers'] = $db->fetchOne("SELECT COUNT(*) as count FROM customers WHERE is_active = 1")['count'] ?? 0;
    $diagnosis['data']['items'] = $db->fetchOne("SELECT COUNT(*) as count FROM items WHERE is_active = 1 AND type IN ('finished_product', 'semi_finished')")['count'] ?? 0;
    $diagnosis['data']['warehouses'] = $db->fetchOne("SELECT COUNT(*) as count FROM warehouses WHERE is_active = 1")['count'] ?? 0;
    $diagnosis['data']['currencies'] = $db->fetchOne("SELECT COUNT(*) as count FROM currencies WHERE is_active = 1")['count'] ?? 0;
    
    // فحص القيود المرجعية
    try {
        $constraints = $db->fetchAll("
            SELECT 
                CONSTRAINT_NAME,
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE CONSTRAINT_SCHEMA = 'factory_management' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
            AND TABLE_NAME = 'sales_invoice_details'
        ");
        $diagnosis['constraints'] = $constraints;
    } catch (Exception $e) {
        $diagnosis['constraints'] = [];
    }
    
    // اختبار إنشاء فاتورة تجريبية
    try {
        $testCustomer = $db->fetchOne("SELECT id FROM customers WHERE is_active = 1 LIMIT 1");
        $testItem = $db->fetchOne("SELECT id FROM items WHERE is_active = 1 AND type IN ('finished_product', 'semi_finished') LIMIT 1");
        $testWarehouse = $db->fetchOne("SELECT id FROM warehouses WHERE is_active = 1 LIMIT 1");
        $testCurrency = $db->fetchOne("SELECT id FROM currencies WHERE is_active = 1 LIMIT 1");
        
        $diagnosis['test_data'] = [
            'customer' => $testCustomer ? $testCustomer['id'] : null,
            'item' => $testItem ? $testItem['id'] : null,
            'warehouse' => $testWarehouse ? $testWarehouse['id'] : null,
            'currency' => $testCurrency ? $testCurrency['id'] : null
        ];
        
        $diagnosis['can_create_invoice'] = $testCustomer && $testItem && $testWarehouse && $testCurrency;
        
    } catch (Exception $e) {
        $diagnosis['can_create_invoice'] = false;
        $diagnosis['test_error'] = $e->getMessage();
    }
    
} catch (Exception $e) {
    $diagnosis['error'] = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص سريع - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .diagnosis-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .diagnosis-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .diagnosis-body {
            padding: 40px;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .diagnostic-card {
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="diagnosis-container">
        <div class="diagnosis-header">
            <h1><i class="fas fa-stethoscope fa-2x mb-3"></i><br>تشخيص سريع للنظام</h1>
            <p class="mb-0">فحص شامل لحالة النظام والمشاكل المحتملة</p>
        </div>
        
        <div class="diagnosis-body">
            <!-- حالة الجداول -->
            <div class="card diagnostic-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة الجداول</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($diagnosis['tables'] ?? [] as $table => $exists): ?>
                            <div class="col-md-3 mb-2">
                                <i class="fas fa-<?= $exists ? 'check-circle status-good' : 'times-circle status-bad' ?> me-2"></i>
                                <code><?= $table ?></code>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- حالة البيانات -->
            <div class="card diagnostic-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>حالة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>البيانات</th>
                                    <th>العدد</th>
                                    <th>الحالة</th>
                                    <th>التوصية</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $dataItems = [
                                    'customers' => ['العملاء', 'إضافة عملاء جدد'],
                                    'items' => ['الأصناف', 'إضافة أصناف للبيع'],
                                    'warehouses' => ['المخازن', 'إضافة مخازن'],
                                    'currencies' => ['العملات', 'إضافة عملات']
                                ];
                                
                                foreach ($dataItems as $key => $info):
                                    $count = $diagnosis['data'][$key] ?? 0;
                                    $status = $count > 0 ? 'good' : 'bad';
                                ?>
                                    <tr>
                                        <td><strong><?= $info[0] ?></strong></td>
                                        <td><?= $count ?></td>
                                        <td>
                                            <i class="fas fa-<?= $status == 'good' ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                            <?= $status == 'good' ? 'جيد' : 'مطلوب' ?>
                                        </td>
                                        <td><?= $status == 'bad' ? $info[1] : 'لا يوجد' ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- اختبار إنشاء الفاتورة -->
            <div class="card diagnostic-card">
                <div class="card-header <?= ($diagnosis['can_create_invoice'] ?? false) ? 'bg-success' : 'bg-danger' ?> text-white">
                    <h5 class="mb-0"><i class="fas fa-file-invoice me-2"></i>اختبار إنشاء الفاتورة</h5>
                </div>
                <div class="card-body">
                    <?php if ($diagnosis['can_create_invoice'] ?? false): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>ممتاز!</strong> يمكن إنشاء فواتير المبيعات بدون مشاكل.
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>عميل تجريبي:</strong> ID <?= $diagnosis['test_data']['customer'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>صنف تجريبي:</strong> ID <?= $diagnosis['test_data']['item'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>مخزن تجريبي:</strong> ID <?= $diagnosis['test_data']['warehouse'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>عملة تجريبية:</strong> ID <?= $diagnosis['test_data']['currency'] ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-times-circle me-2"></i>
                            <strong>مشكلة!</strong> لا يمكن إنشاء فواتير المبيعات.
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>عميل:</strong> 
                                <span class="<?= $diagnosis['test_data']['customer'] ? 'status-good' : 'status-bad' ?>">
                                    <?= $diagnosis['test_data']['customer'] ? 'متوفر' : 'مفقود' ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>صنف:</strong> 
                                <span class="<?= $diagnosis['test_data']['item'] ? 'status-good' : 'status-bad' ?>">
                                    <?= $diagnosis['test_data']['item'] ? 'متوفر' : 'مفقود' ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>مخزن:</strong> 
                                <span class="<?= $diagnosis['test_data']['warehouse'] ? 'status-good' : 'status-bad' ?>">
                                    <?= $diagnosis['test_data']['warehouse'] ? 'متوفر' : 'مفقود' ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>عملة:</strong> 
                                <span class="<?= $diagnosis['test_data']['currency'] ? 'status-good' : 'status-bad' ?>">
                                    <?= $diagnosis['test_data']['currency'] ? 'متوفر' : 'مفقود' ?>
                                </span>
                            </div>
                        </div>
                        <?php if (isset($diagnosis['test_error'])): ?>
                            <div class="mt-3">
                                <strong>تفاصيل الخطأ:</strong>
                                <code><?= htmlspecialchars($diagnosis['test_error']) ?></code>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- القيود المرجعية -->
            <?php if (!empty($diagnosis['constraints'])): ?>
                <div class="card diagnostic-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0"><i class="fas fa-link me-2"></i>القيود المرجعية</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>اسم القيد</th>
                                        <th>الجدول</th>
                                        <th>العمود</th>
                                        <th>الجدول المرجعي</th>
                                        <th>العمود المرجعي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($diagnosis['constraints'] as $constraint): ?>
                                        <tr>
                                            <td><code><?= htmlspecialchars($constraint['CONSTRAINT_NAME']) ?></code></td>
                                            <td><?= htmlspecialchars($constraint['TABLE_NAME']) ?></td>
                                            <td><?= htmlspecialchars($constraint['COLUMN_NAME']) ?></td>
                                            <td><?= htmlspecialchars($constraint['REFERENCED_TABLE_NAME']) ?></td>
                                            <td><?= htmlspecialchars($constraint['REFERENCED_COLUMN_NAME']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!($diagnosis['can_create_invoice'] ?? false)): ?>
                    <a href="fix_sales_constraints.php" class="btn btn-danger btn-lg me-3">
                        <i class="fas fa-tools me-2"></i>إصلاح المشاكل
                    </a>
                <?php endif; ?>
                
                <a href="setup_sales.php" class="btn btn-warning btn-lg me-3">
                    <i class="fas fa-cog me-2"></i>إعداد النظام
                </a>
                
                <a href="add_sale.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>فاتورة جديدة
                </a>
                
                <a href="sales.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>المبيعات
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
