<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // توليد رقم أمر الإنتاج
        $prefix = 'PRD-' . date('Ymd') . '-';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM production_orders WHERE order_number LIKE ?", [$prefix . '%']);
        $sequence = ($countResult['count'] ?? 0) + 1;
        $orderNumber = $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);
        
        // بيانات أمر الإنتاج
        $orderData = [
            'order_number' => $orderNumber,
            'product_id' => (int)$_POST['product_id'],
            'quantity_to_produce' => (float)$_POST['quantity_to_produce'],
            'warehouse_id' => (int)$_POST['warehouse_id'],
            'start_date' => $_POST['start_date'],
            'expected_end_date' => $_POST['expected_end_date'],
            'status' => 'planned',
            'notes' => Helper::cleanInput($_POST['notes'] ?? ''),
            'created_by' => $user['id']
        ];
        
        // إدراج أمر الإنتاج
        $orderId = $db->insert('production_orders', $orderData);
        
        // الحصول على وصفة الإنتاج
        $recipe = $db->fetchAll("SELECT * FROM production_recipes WHERE product_id = ? AND is_active = 1", [$orderData['product_id']]);
        
        $totalCost = 0;
        
        // إدراج تفاصيل أمر الإنتاج
        foreach ($recipe as $material) {
            $requiredQuantity = $material['quantity_required'] * $orderData['quantity_to_produce'];
            $materialCost = $requiredQuantity * $material['cost_per_unit'];
            $totalCost += $materialCost;
            
            $detailData = [
                'order_id' => $orderId,
                'material_id' => $material['material_id'],
                'quantity_required' => $requiredQuantity,
                'unit_cost' => $material['cost_per_unit']
            ];
            
            $db->insert('production_order_details', $detailData);
        }
        
        // تحديث التكلفة الإجمالية
        $db->update('production_orders', ['total_cost' => $totalCost], 'id = ?', [$orderId]);
        
        $db->commit();
        
        $success = "تم إنشاء أمر الإنتاج رقم $orderNumber بنجاح!";
        
        // إعادة توجيه بعد النجاح
        header("Location: production.php?success=" . urlencode($success));
        exit;
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إنشاء أمر الإنتاج: " . $e->getMessage();
    }
}

// جلب المنتجات النهائية
$products = $db->fetchAll("SELECT id, code, name, unit FROM items WHERE type = 'finished_product' AND is_active = 1 ORDER BY name");

// جلب المخازن
$warehouses = $db->fetchAll("SELECT id, name, location FROM warehouses WHERE is_active = 1 ORDER BY name");

// جلب وصفات الإنتاج للمنتجات
$recipes = [];
$recipeData = $db->fetchAll("
    SELECT pr.product_id, pr.material_id, pr.quantity_required, pr.cost_per_unit,
           i.name as material_name, i.unit as material_unit
    FROM production_recipes pr
    JOIN items i ON pr.material_id = i.id
    WHERE pr.is_active = 1
    ORDER BY pr.product_id, i.name
");

foreach ($recipeData as $recipe) {
    $recipes[$recipe['product_id']][] = $recipe;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر إنتاج جديد - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-secondary {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .recipe-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
            display: none;
        }
        .material-item {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border: 1px solid #dee2e6;
        }
        .cost-summary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>أمر إنتاج جديد</h1>
                    <p class="mb-0">إنشاء أمر إنتاج جديد للمنتجات</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="production.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة لأوامر الإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="productionOrderForm">
            <div class="row">
                <div class="col-lg-8">
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات أمر الإنتاج</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المنتج <span class="text-danger">*</span></label>
                                    <select name="product_id" id="product_id" class="form-select" required>
                                        <option value="">اختر المنتج</option>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?= $product['id'] ?>" data-unit="<?= $product['unit'] ?>">
                                                <?= htmlspecialchars($product['code'] . ' - ' . $product['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكمية المطلوب إنتاجها <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" name="quantity_to_produce" id="quantity_to_produce" 
                                               class="form-control" step="0.001" min="0.001" required>
                                        <span class="input-group-text" id="unit-display">وحدة</span>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المخزن <span class="text-danger">*</span></label>
                                    <select name="warehouse_id" class="form-select" required>
                                        <option value="">اختر المخزن</option>
                                        <?php foreach ($warehouses as $warehouse): ?>
                                            <option value="<?= $warehouse['id'] ?>">
                                                <?= htmlspecialchars($warehouse['name']) ?>
                                                <?php if ($warehouse['location']): ?>
                                                    - <?= htmlspecialchars($warehouse['location']) ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                                    <input type="date" name="start_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الانتهاء المتوقع</label>
                                    <input type="date" name="expected_end_date" class="form-control" 
                                           value="<?= date('Y-m-d', strtotime('+7 days')) ?>">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="أي ملاحظات إضافية حول أمر الإنتاج..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>وصفة الإنتاج</h5>
                        </div>
                        <div class="card-body p-4">
                            <div id="recipe-container">
                                <div class="text-center text-muted py-4">
                                    <i class="fas fa-flask fa-3x mb-3"></i>
                                    <p>اختر المنتج لعرض وصفة الإنتاج</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>إنشاء أمر الإنتاج
                </button>
                <a href="production.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // بيانات الوصفات
        const recipes = <?= json_encode($recipes) ?>;
        
        // عند تغيير المنتج
        document.getElementById('product_id').addEventListener('change', function() {
            const productId = this.value;
            const selectedOption = this.options[this.selectedIndex];
            const unit = selectedOption.getAttribute('data-unit') || 'وحدة';
            
            // تحديث وحدة القياس
            document.getElementById('unit-display').textContent = unit;
            
            // عرض وصفة الإنتاج
            updateRecipeDisplay(productId);
        });
        
        // عند تغيير الكمية
        document.getElementById('quantity_to_produce').addEventListener('input', function() {
            const productId = document.getElementById('product_id').value;
            if (productId) {
                updateRecipeDisplay(productId);
            }
        });
        
        function updateRecipeDisplay(productId) {
            const container = document.getElementById('recipe-container');
            const quantity = parseFloat(document.getElementById('quantity_to_produce').value) || 0;
            
            if (!productId || !recipes[productId]) {
                container.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                        <p>لا توجد وصفة إنتاج لهذا المنتج</p>
                    </div>
                `;
                return;
            }
            
            const recipe = recipes[productId];
            let totalCost = 0;
            let html = '<div class="recipe-details">';
            
            recipe.forEach(material => {
                const requiredQty = material.quantity_required * quantity;
                const materialCost = requiredQty * material.cost_per_unit;
                totalCost += materialCost;
                
                html += `
                    <div class="material-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${material.material_name}</strong>
                                <br><small class="text-muted">
                                    ${requiredQty.toFixed(3)} ${material.material_unit}
                                </small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">
                                    ${materialCost.toFixed(2)} د.ع
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += `
                <div class="cost-summary">
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>التكلفة الإجمالية:</strong>
                        <strong>${totalCost.toFixed(2)} د.ع</strong>
                    </div>
                </div>
            `;
            
            html += '</div>';
            container.innerHTML = html;
        }
    </script>
</body>
</html>
