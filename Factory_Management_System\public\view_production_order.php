<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$orderId = $_GET['id'] ?? 0;
if (!$orderId) {
    header('Location: production.php');
    exit;
}

// جلب بيانات أمر الإنتاج
$order = $db->fetchOne("
    SELECT po.*, i.name as product_name, i.code as product_code, i.unit as product_unit,
           w.name as warehouse_name, w.location as warehouse_location,
           u.full_name as created_by_name
    FROM production_orders po
    JOIN items i ON po.product_id = i.id
    JOIN warehouses w ON po.warehouse_id = w.id
    LEFT JOIN users u ON po.created_by = u.id
    WHERE po.id = ?
", [$orderId]);

if (!$order) {
    header('Location: production.php?error=' . urlencode('أمر الإنتاج غير موجود'));
    exit;
}

// جلب تفاصيل أمر الإنتاج (المواد المطلوبة)
$orderDetails = $db->fetchAll("
    SELECT pod.*, i.name as material_name, i.code as material_code, i.unit as material_unit
    FROM production_order_details pod
    JOIN items i ON pod.material_id = i.id
    WHERE pod.order_id = ?
    ORDER BY i.name
", [$orderId]);

// حساب النسب المئوية
$progressPercentage = 0;
if ($order['quantity_to_produce'] > 0) {
    $progressPercentage = ($order['quantity_produced'] / $order['quantity_to_produce']) * 100;
}

// ألوان الحالات
$statusColors = [
    'planned' => 'secondary',
    'in_progress' => 'primary',
    'completed' => 'success',
    'cancelled' => 'danger'
];

$statusNames = [
    'planned' => 'مخطط',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل أمر الإنتاج <?= htmlspecialchars($order['order_number']) ?> - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .info-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .info-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .progress-custom {
            height: 20px;
            border-radius: 10px;
        }
        .material-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
        }
        .status-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        .cost-highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
        }
        .btn-action {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            margin: 0 5px;
        }
        @media print {
            .no-print { display: none !important; }
            .main-header { background: #667eea !important; }
        }
    </style>
</head>
<body>
    <div class="main-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-eye me-3"></i>تفاصيل أمر الإنتاج</h1>
                    <p class="mb-0">رقم الأمر: <?= htmlspecialchars($order['order_number']) ?></p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="production.php" class="btn btn-light me-2">
                        <i class="fas fa-arrow-right me-2"></i>العودة
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-light">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- معلومات أمر الإنتاج -->
            <div class="col-lg-8">
                <div class="card info-card">
                    <div class="info-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات أمر الإنتاج</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">رقم الأمر</label>
                                <div class="fw-bold"><?= htmlspecialchars($order['order_number']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الحالة</label>
                                <div>
                                    <span class="badge bg-<?= $statusColors[$order['status']] ?> status-badge">
                                        <?= $statusNames[$order['status']] ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">المنتج</label>
                                <div class="fw-bold"><?= htmlspecialchars($order['product_code'] . ' - ' . $order['product_name']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">المخزن</label>
                                <div class="fw-bold"><?= htmlspecialchars($order['warehouse_name']) ?></div>
                                <?php if ($order['warehouse_location']): ?>
                                    <small class="text-muted"><?= htmlspecialchars($order['warehouse_location']) ?></small>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الكمية المطلوبة</label>
                                <div class="fw-bold"><?= number_format($order['quantity_to_produce'], 3) ?> <?= htmlspecialchars($order['product_unit']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الكمية المنتجة</label>
                                <div class="fw-bold"><?= number_format($order['quantity_produced'], 3) ?> <?= htmlspecialchars($order['product_unit']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ البدء</label>
                                <div class="fw-bold"><?= $order['start_date'] ? date('d/m/Y', strtotime($order['start_date'])) : 'غير محدد' ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ الانتهاء المتوقع</label>
                                <div class="fw-bold"><?= $order['expected_end_date'] ? date('d/m/Y', strtotime($order['expected_end_date'])) : 'غير محدد' ?></div>
                            </div>
                            <?php if ($order['actual_end_date']): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">تاريخ الانتهاء الفعلي</label>
                                    <div class="fw-bold"><?= date('d/m/Y', strtotime($order['actual_end_date'])) ?></div>
                                </div>
                            <?php endif; ?>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">أنشأ بواسطة</label>
                                <div class="fw-bold"><?= htmlspecialchars($order['created_by_name'] ?? 'غير معروف') ?></div>
                            </div>
                        </div>

                        <!-- شريط التقدم -->
                        <div class="mt-4">
                            <label class="form-label text-muted">نسبة الإنجاز</label>
                            <div class="progress progress-custom">
                                <div class="progress-bar bg-success" role="progressbar" 
                                     style="width: <?= $progressPercentage ?>%" 
                                     aria-valuenow="<?= $progressPercentage ?>" 
                                     aria-valuemin="0" aria-valuemax="100">
                                    <?= number_format($progressPercentage, 1) ?>%
                                </div>
                            </div>
                        </div>

                        <?php if ($order['notes']): ?>
                            <div class="mt-4">
                                <label class="form-label text-muted">ملاحظات</label>
                                <div class="p-3 bg-light rounded"><?= nl2br(htmlspecialchars($order['notes'])) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- التكلفة والملخص -->
            <div class="col-lg-4">
                <div class="card info-card">
                    <div class="info-header">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص التكلفة</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="cost-highlight">
                            <h4 class="mb-1"><?= number_format($order['total_cost'], 2) ?> د.ع</h4>
                            <p class="mb-0">التكلفة الإجمالية</p>
                        </div>
                        
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>تكلفة الوحدة:</span>
                                <strong>
                                    <?= $order['quantity_to_produce'] > 0 ? number_format($order['total_cost'] / $order['quantity_to_produce'], 2) : '0.00' ?> د.ع
                                </strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>تاريخ الإنشاء:</span>
                                <strong><?= date('d/m/Y', strtotime($order['created_at'])) ?></strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>آخر تحديث:</span>
                                <strong><?= date('d/m/Y H:i', strtotime($order['updated_at'])) ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="card info-card no-print">
                    <div class="info-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>الإجراءات</h5>
                    </div>
                    <div class="card-body p-4 text-center">
                        <?php if ($order['status'] == 'planned'): ?>
                            <a href="start_production.php?id=<?= $order['id'] ?>" class="btn btn-primary btn-action">
                                <i class="fas fa-play me-2"></i>بدء الإنتاج
                            </a>
                        <?php elseif ($order['status'] == 'in_progress'): ?>
                            <a href="update_production.php?id=<?= $order['id'] ?>" class="btn btn-warning btn-action">
                                <i class="fas fa-edit me-2"></i>تحديث الإنتاج
                            </a>
                            <a href="complete_production.php?id=<?= $order['id'] ?>" class="btn btn-success btn-action">
                                <i class="fas fa-check me-2"></i>إكمال الإنتاج
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($order['status'] != 'completed' && $order['status'] != 'cancelled'): ?>
                            <a href="cancel_production.php?id=<?= $order['id'] ?>" class="btn btn-danger btn-action"
                               onclick="return confirm('هل أنت متأكد من إلغاء أمر الإنتاج؟')">
                                <i class="fas fa-times me-2"></i>إلغاء الأمر
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل المواد المطلوبة -->
        <?php if (!empty($orderDetails)): ?>
            <div class="card info-card">
                <div class="info-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>المواد المطلوبة</h5>
                </div>
                <div class="card-body p-4">
                    <?php foreach ($orderDetails as $detail): ?>
                        <div class="material-item">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h6 class="mb-1"><?= htmlspecialchars($detail['material_name']) ?></h6>
                                    <small class="text-muted"><?= htmlspecialchars($detail['material_code']) ?></small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong><?= number_format($detail['quantity_required'], 3) ?></strong>
                                    <br><small class="text-muted"><?= htmlspecialchars($detail['material_unit']) ?></small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong><?= number_format($detail['quantity_consumed'], 3) ?></strong>
                                    <br><small class="text-muted">مستهلك</small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong><?= number_format($detail['unit_cost'], 2) ?></strong>
                                    <br><small class="text-muted">د.ع/وحدة</small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <span class="badge bg-success">
                                        <?= number_format($detail['quantity_consumed'] * $detail['unit_cost'], 2) ?> د.ع
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
