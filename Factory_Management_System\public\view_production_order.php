<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$orderId = (int)($_GET['id'] ?? 0);
if (!$orderId) {
    header('Location: production_orders.php');
    exit;
}

// جلب بيانات أمر الإنتاج
try {
    $order = $db->fetchOne("
        SELECT po.*, i.name as product_name, i.unit as product_unit, i.code as product_code,
               d.name as department_name, e.full_name as supervisor_name,
               u.full_name as created_by_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN departments d ON po.department_id = d.id
        LEFT JOIN employees e ON po.supervisor_id = e.id
        LEFT JOIN users u ON po.created_by = u.id
        WHERE po.id = ?
    ", [$orderId]);
    
    if (!$order) {
        header('Location: production_orders.php');
        exit;
    }
    
    // جلب المواد المطلوبة
    $materials = $db->fetchAll("
        SELECT pom.*, i.name as material_name, i.unit as material_unit, i.code as material_code,
               w.name as warehouse_name
        FROM production_order_materials pom
        LEFT JOIN items i ON pom.material_id = i.id
        LEFT JOIN warehouses w ON pom.warehouse_id = w.id
        WHERE pom.production_order_id = ?
        ORDER BY i.name
    ", [$orderId]);
    
} catch (Exception $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
    $order = null;
    $materials = [];
}

// حساب النسب المئوية
$progressPercentage = $order && $order['quantity_required'] > 0 ? 
    ($order['quantity_produced'] / $order['quantity_required']) * 100 : 0;

$costVariance = $order && $order['estimated_cost'] > 0 ? 
    (($order['actual_cost'] - $order['estimated_cost']) / $order['estimated_cost']) * 100 : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض أمر الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 1rem;
            font-weight: 600;
        }
        .priority-urgent { color: #dc3545; }
        .priority-high { color: #fd7e14; }
        .priority-normal { color: #28a745; }
        .priority-low { color: #6c757d; }
        .info-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .progress-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body>
    <?php if (!$order): ?>
        <div class="container mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                أمر الإنتاج غير موجود أو حدث خطأ في جلب البيانات
                <br><a href="production_orders.php" class="btn btn-sm btn-primary mt-2">العودة إلى قائمة الأوامر</a>
            </div>
        </div>
    <?php else: ?>
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1><i class="fas fa-eye me-3"></i>أمر الإنتاج: <?= htmlspecialchars($order['order_number']) ?></h1>
                        <p class="mb-0"><?= htmlspecialchars($order['product_name']) ?></p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="edit_production_order.php?id=<?= $order['id'] ?>" class="btn btn-light me-2">
                            <i class="fas fa-edit me-2"></i>تعديل
                        </a>
                        <a href="production_orders.php" class="btn btn-outline-light">
                            <i class="fas fa-list me-2"></i>قائمة الأوامر
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <!-- معلومات أمر الإنتاج -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات أمر الإنتاج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>رقم الأمر:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['order_number']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>المنتج:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['product_name']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>كود المنتج:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['product_code']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الكمية المطلوبة:</strong>
                                        <span class="float-end"><?= number_format($order['quantity_required'], 3) ?> <?= htmlspecialchars($order['product_unit']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الكمية المنتجة:</strong>
                                        <span class="float-end text-success"><?= number_format($order['quantity_produced'], 3) ?> <?= htmlspecialchars($order['product_unit']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الكمية المتبقية:</strong>
                                        <span class="float-end text-warning"><?= number_format($order['quantity_remaining'], 3) ?> <?= htmlspecialchars($order['product_unit']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>تاريخ البدء:</strong>
                                        <span class="float-end"><?= date('d/m/Y', strtotime($order['start_date'])) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>تاريخ الاستحقاق:</strong>
                                        <span class="float-end"><?= date('d/m/Y', strtotime($order['due_date'])) ?></span>
                                    </div>
                                    <?php if ($order['completion_date']): ?>
                                    <div class="info-item">
                                        <strong>تاريخ الإنجاز:</strong>
                                        <span class="float-end"><?= date('d/m/Y', strtotime($order['completion_date'])) ?></span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="info-item">
                                        <strong>القسم المسؤول:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['department_name']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>المشرف:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['supervisor_name']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>أنشأ بواسطة:</strong>
                                        <span class="float-end"><?= htmlspecialchars($order['created_by_name']) ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($order['notes']): ?>
                            <div class="mt-3">
                                <strong>ملاحظات:</strong>
                                <div class="bg-light p-3 rounded mt-2">
                                    <?= nl2br(htmlspecialchars($order['notes'])) ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- المواد المطلوبة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>المواد المطلوبة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($materials)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد مواد مطلوبة لهذا الأمر</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>كود المادة</th>
                                                <th>اسم المادة</th>
                                                <th>الكمية المطلوبة</th>
                                                <th>الكمية المستخدمة</th>
                                                <th>سعر الوحدة</th>
                                                <th>التكلفة الإجمالية</th>
                                                <th>المخزن</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($materials as $material): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($material['material_code']) ?></td>
                                                    <td><?= htmlspecialchars($material['material_name']) ?></td>
                                                    <td><?= number_format($material['quantity_required'], 3) ?> <?= htmlspecialchars($material['material_unit']) ?></td>
                                                    <td><?= number_format($material['quantity_used'], 3) ?> <?= htmlspecialchars($material['material_unit']) ?></td>
                                                    <td><?= number_format($material['unit_cost'], 2) ?> د.ع</td>
                                                    <td><?= number_format($material['total_cost'], 2) ?> د.ع</td>
                                                    <td><?= htmlspecialchars($material['warehouse_name']) ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- الحالة والإحصائيات -->
                <div class="col-lg-4">
                    <!-- حالة الأمر -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>حالة الأمر</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php
                            $statusColors = [
                                'pending' => 'secondary',
                                'in_progress' => 'info',
                                'completed' => 'success',
                                'cancelled' => 'danger',
                                'on_hold' => 'warning'
                            ];
                            $statusNames = [
                                'pending' => 'في الانتظار',
                                'in_progress' => 'قيد التنفيذ',
                                'completed' => 'مكتمل',
                                'cancelled' => 'ملغي',
                                'on_hold' => 'معلق'
                            ];
                            ?>
                            <span class="status-badge bg-<?= $statusColors[$order['status']] ?> mb-3 d-inline-block">
                                <?= $statusNames[$order['status']] ?>
                            </span>
                            
                            <?php
                            $priorityNames = [
                                'low' => 'منخفضة',
                                'normal' => 'عادية',
                                'high' => 'عالية',
                                'urgent' => 'عاجلة'
                            ];
                            ?>
                            <div class="priority-<?= $order['priority'] ?> mb-3">
                                <i class="fas fa-flag"></i> الأولوية: <?= $priorityNames[$order['priority']] ?>
                            </div>
                        </div>
                    </div>

                    <!-- تقدم الإنتاج -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>تقدم الإنتاج</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="progress-circle bg-<?= $progressPercentage >= 100 ? 'success' : ($progressPercentage >= 50 ? 'warning' : 'danger') ?> mb-3">
                                <?= number_format($progressPercentage, 1) ?>%
                            </div>
                            <p class="mb-0">نسبة الإنجاز</p>
                        </div>
                    </div>

                    <!-- التكلفة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>التكلفة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between mb-2">
                                <span>التكلفة المقدرة:</span>
                                <strong><?= number_format($order['estimated_cost'], 2) ?> د.ع</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>التكلفة الفعلية:</span>
                                <strong><?= number_format($order['actual_cost'], 2) ?> د.ع</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>الانحراف:</span>
                                <strong class="<?= $costVariance > 0 ? 'text-danger' : 'text-success' ?>">
                                    <?= $costVariance > 0 ? '+' : '' ?><?= number_format($costVariance, 1) ?>%
                                </strong>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="edit_production_order.php?id=<?= $order['id'] ?>" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>تعديل الأمر
                                </a>
                                <a href="print_production_order.php?id=<?= $order['id'] ?>" class="btn btn-info" target="_blank">
                                    <i class="fas fa-print me-2"></i>طباعة الأمر
                                </a>
                                <a href="production_orders.php" class="btn btn-secondary">
                                    <i class="fas fa-list me-2"></i>قائمة الأوامر
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
