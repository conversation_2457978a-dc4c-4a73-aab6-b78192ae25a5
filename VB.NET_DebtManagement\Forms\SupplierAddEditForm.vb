Imports System.Windows.Forms
Imports System.Drawing

''' <summary>
''' نموذج إضافة/تعديل المورد - Supplier Add/Edit Form
''' </summary>
Public Class SupplierAddEditForm
    Inherits Form
    
#Region "Fields"
    
    Private _context As DebtContext
    Private _currentUser As User
    Private _supplier As Supplier
    Private _isEditMode As Boolean
    
    ' عناصر التحكم
    Private _txtName As TextBox
    Private _txtPhone As TextBox
    Private _txtEmail As TextBox
    Private _txtAddress As TextBox
    Private _cmbSupplierType As ComboBox
    Private _txtTaxNumber As TextBox
    Private _txtBalanceIQD As TextBox
    Private _txtBalanceUSD As TextBox
    Private _txtNotes As TextBox
    Private _btnSave As Button
    Private _btnCancel As Button
    
    ' تسميات الحقول
    Private _lblName As Label
    Private _lblPhone As Label
    Private _lblEmail As Label
    Private _lblAddress As Label
    Private _lblSupplierType As Label
    Private _lblTaxNumber As Label
    Private _lblBalanceIQD As Label
    Private _lblBalanceUSD As Label
    Private _lblNotes As Label
    
    ' لوحات التجميع
    Private _panelMain As Panel
    Private _panelButtons As Panel
    
#End Region

#Region "Constructors"
    
    ''' <summary>
    ''' منشئ لإضافة مورد جديد
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        _supplier = New Supplier()
        _isEditMode = False
        
        InitializeComponent()
        SetupForm()
        SetupControls()
        Me.Text = "إضافة مورد جديد"
    End Sub
    
    ''' <summary>
    ''' منشئ لتعديل مورد موجود
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User, supplier As Supplier)
        _context = context
        _currentUser = currentUser
        _supplier = supplier
        _isEditMode = True
        
        InitializeComponent()
        SetupForm()
        SetupControls()
        LoadSupplierData()
        Me.Text = $"تعديل المورد: {supplier.Name}"
    End Sub
    
#End Region

#Region "Form Setup"
    
    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Size = New Size(550, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.SupplierIcon
    End Sub
    
    ''' <summary>
    ''' إعداد عناصر التحكم
    ''' </summary>
    Private Sub SetupControls()
        ' اللوحة الرئيسية
        _panelMain = New Panel With {
            .Size = New Size(510, 580),
            .Location = New Point(20, 20),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .Padding = New Padding(20)
        }
        
        ' إنشاء الحقول
        CreateFormFields()
        
        ' لوحة الأزرار
        _panelButtons = New Panel With {
            .Size = New Size(510, 60),
            .Location = New Point(20, 620),
            .BackColor = Color.Transparent
        }
        
        ' أزرار الحفظ والإلغاء
        CreateButtons()
        
        Me.Controls.AddRange({_panelMain, _panelButtons})
    End Sub
    
    ''' <summary>
    ''' إنشاء حقول النموذج
    ''' </summary>
    Private Sub CreateFormFields()
        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 55
        
        ' اسم المورد
        _lblName = CreateLabel("اسم المورد *:", yPosition)
        _txtName = CreateTextBox(yPosition + 25, True)
        yPosition += fieldHeight
        
        ' نوع المورد
        _lblSupplierType = CreateLabel("نوع المورد:", yPosition)
        _cmbSupplierType = CreateComboBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' رقم الهاتف
        _lblPhone = CreateLabel("رقم الهاتف:", yPosition)
        _txtPhone = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' البريد الإلكتروني
        _lblEmail = CreateLabel("البريد الإلكتروني:", yPosition)
        _txtEmail = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' العنوان
        _lblAddress = CreateLabel("العنوان:", yPosition)
        _txtAddress = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' الرقم الضريبي
        _lblTaxNumber = CreateLabel("الرقم الضريبي:", yPosition)
        _txtTaxNumber = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' الرصيد الافتتاحي بالدينار
        _lblBalanceIQD = CreateLabel("الرصيد الافتتاحي (دينار):", yPosition)
        _txtBalanceIQD = CreateNumericTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' الرصيد الافتتاحي بالدولار
        _lblBalanceUSD = CreateLabel("الرصيد الافتتاحي (دولار):", yPosition)
        _txtBalanceUSD = CreateNumericTextBox(yPosition + 25)
        yPosition += fieldHeight
        
        ' الملاحظات
        _lblNotes = CreateLabel("ملاحظات:", yPosition)
        _txtNotes = New TextBox With {
            .Size = New Size(450, 80),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .TextAlign = HorizontalAlignment.Right
        }
        
        ' إضافة جميع العناصر للوحة الرئيسية
        _panelMain.Controls.AddRange({
            _lblName, _txtName,
            _lblSupplierType, _cmbSupplierType,
            _lblPhone, _txtPhone,
            _lblEmail, _txtEmail,
            _lblAddress, _txtAddress,
            _lblTaxNumber, _txtTaxNumber,
            _lblBalanceIQD, _txtBalanceIQD,
            _lblBalanceUSD, _txtBalanceUSD,
            _lblNotes, _txtNotes
        })
    End Sub
    
    ''' <summary>
    ''' إنشاء تسمية
    ''' </summary>
    Private Function CreateLabel(text As String, y As Integer) As Label
        Return New Label With {
            .Text = text,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = Color.FromArgb(73, 80, 87),
            .Size = New Size(450, 20),
            .Location = New Point(20, y),
            .TextAlign = ContentAlignment.MiddleRight
        }
    End Function
    
    ''' <summary>
    ''' إنشاء مربع نص
    ''' </summary>
    Private Function CreateTextBox(y As Integer, Optional isRequired As Boolean = False) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(450, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Right
        }
        
        If isRequired Then
            textBox.BackColor = Color.FromArgb(255, 248, 220)
        End If
        
        Return textBox
    End Function
    
    ''' <summary>
    ''' إنشاء قائمة منسدلة
    ''' </summary>
    Private Function CreateComboBox(y As Integer) As ComboBox
        Dim comboBox As New ComboBox With {
            .Size = New Size(450, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        
        comboBox.Items.AddRange({"شركة", "فرد", "مؤسسة", "أخرى"})
        comboBox.SelectedIndex = 0
        
        Return comboBox
    End Function
    
    ''' <summary>
    ''' إنشاء مربع نص رقمي
    ''' </summary>
    Private Function CreateNumericTextBox(y As Integer) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(450, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Center,
            .Text = "0"
        }
        
        AddHandler textBox.KeyPress, AddressOf NumericTextBox_KeyPress
        
        Return textBox
    End Function
    
    ''' <summary>
    ''' إنشاء الأزرار
    ''' </summary>
    Private Sub CreateButtons()
        _btnSave = New Button With {
            .Text = If(_isEditMode, "حفظ التغييرات", "إضافة المورد"),
            .Size = New Size(130, 40),
            .Location = New Point(360, 10),
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnSave.FlatAppearance.BorderSize = 0
        
        _btnCancel = New Button With {
            .Text = "إلغاء",
            .Size = New Size(100, 40),
            .Location = New Point(250, 10),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnCancel.FlatAppearance.BorderSize = 0
        
        ' ربط الأحداث
        AddHandler _btnSave.Click, AddressOf BtnSave_Click
        AddHandler _btnCancel.Click, AddressOf BtnCancel_Click
        
        ' تأثيرات الماوس
        AddHandler _btnSave.MouseEnter, Sub() _btnSave.BackColor = Color.FromArgb(33, 136, 56)
        AddHandler _btnSave.MouseLeave, Sub() _btnSave.BackColor = Color.FromArgb(40, 167, 69)
        AddHandler _btnCancel.MouseEnter, Sub() _btnCancel.BackColor = Color.FromArgb(90, 98, 104)
        AddHandler _btnCancel.MouseLeave, Sub() _btnCancel.BackColor = Color.FromArgb(108, 117, 125)
        
        _panelButtons.Controls.AddRange({_btnSave, _btnCancel})
    End Sub
    
#End Region

#Region "Data Methods"
    
    ''' <summary>
    ''' تحميل بيانات المورد للتعديل
    ''' </summary>
    Private Sub LoadSupplierData()
        If _supplier IsNot Nothing Then
            _txtName.Text = _supplier.Name
            _txtPhone.Text = _supplier.Phone
            _txtEmail.Text = _supplier.Email
            _txtAddress.Text = _supplier.Address
            _txtTaxNumber.Text = _supplier.TaxNumber
            _txtBalanceIQD.Text = _supplier.InitialBalanceIQD.ToString("F2")
            _txtBalanceUSD.Text = _supplier.InitialBalanceUSD.ToString("F2")
            _txtNotes.Text = _supplier.Notes
            
            If Not String.IsNullOrEmpty(_supplier.SupplierType) Then
                _cmbSupplierType.Text = _supplier.SupplierType
            End If
        End If
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    Private Function ValidateData() As Boolean
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(_txtName.Text) Then
            errors.Add("اسم المورد مطلوب")
        End If
        
        If Not String.IsNullOrWhiteSpace(_txtEmail.Text) Then
            Try
                Dim addr As New System.Net.Mail.MailAddress(_txtEmail.Text)
            Catch
                errors.Add("البريد الإلكتروني غير صحيح")
            End Try
        End If
        
        Dim balanceIQD As Decimal
        If Not Decimal.TryParse(_txtBalanceIQD.Text, balanceIQD) Then
            errors.Add("الرصيد بالدينار يجب أن يكون رقم صحيح")
        End If
        
        Dim balanceUSD As Decimal
        If Not Decimal.TryParse(_txtBalanceUSD.Text, balanceUSD) Then
            errors.Add("الرصيد بالدولار يجب أن يكون رقم صحيح")
        End If
        
        If errors.Count > 0 Then
            MessageBox.Show(String.Join(Environment.NewLine, errors), "أخطاء في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If
        
        Return True
    End Function
    
    ''' <summary>
    ''' حفظ بيانات المورد
    ''' </summary>
    Private Function SaveSupplier() As Boolean
        Try
            _supplier.Name = _txtName.Text.Trim()
            _supplier.Phone = If(String.IsNullOrWhiteSpace(_txtPhone.Text), Nothing, _txtPhone.Text.Trim())
            _supplier.Email = If(String.IsNullOrWhiteSpace(_txtEmail.Text), Nothing, _txtEmail.Text.Trim())
            _supplier.Address = If(String.IsNullOrWhiteSpace(_txtAddress.Text), Nothing, _txtAddress.Text.Trim())
            _supplier.SupplierType = _cmbSupplierType.Text
            _supplier.TaxNumber = If(String.IsNullOrWhiteSpace(_txtTaxNumber.Text), Nothing, _txtTaxNumber.Text.Trim())
            _supplier.Notes = If(String.IsNullOrWhiteSpace(_txtNotes.Text), Nothing, _txtNotes.Text.Trim())
            
            Dim balanceIQD As Decimal = Decimal.Parse(_txtBalanceIQD.Text)
            Dim balanceUSD As Decimal = Decimal.Parse(_txtBalanceUSD.Text)
            
            If _isEditMode Then
                _supplier.UpdatedAt = DateTime.Now
                _supplier.UpdatedBy = _currentUser.Id
                
                If _supplier.InitialBalanceIQD <> balanceIQD Then
                    Dim difference = balanceIQD - _supplier.InitialBalanceIQD
                    _supplier.CurrentBalanceIQD += difference
                    _supplier.InitialBalanceIQD = balanceIQD
                End If
                
                If _supplier.InitialBalanceUSD <> balanceUSD Then
                    Dim difference = balanceUSD - _supplier.InitialBalanceUSD
                    _supplier.CurrentBalanceUSD += difference
                    _supplier.InitialBalanceUSD = balanceUSD
                End If
            Else
                _supplier.InitialBalanceIQD = balanceIQD
                _supplier.InitialBalanceUSD = balanceUSD
                _supplier.CurrentBalanceIQD = balanceIQD
                _supplier.CurrentBalanceUSD = balanceUSD
                _supplier.CreatedAt = DateTime.Now
                _supplier.CreatedBy = _currentUser.Id
                _supplier.IsActive = True
                
                _context.Suppliers.Add(_supplier)
            End If
            
            _context.SaveChanges()
            Return True
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function
    
#End Region

#Region "Event Handlers"
    
    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateData() Then
            If SaveSupplier() Then
                Me.DialogResult = DialogResult.OK
                Me.Close()
            End If
        End If
    End Sub
    
    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub
    
    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso e.KeyChar <> "-"c AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If
        
        Dim textBox = DirectCast(sender, TextBox)
        If e.KeyChar = "."c AndAlso textBox.Text.Contains(".") Then
            e.Handled = True
        End If
        
        If e.KeyChar = "-"c AndAlso (textBox.SelectionStart <> 0 OrElse textBox.Text.Contains("-")) Then
            e.Handled = True
        End If
    End Sub
    
    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If Me.DialogResult = DialogResult.None Then
            If MessageBox.Show("هل تريد إغلاق النموذج بدون حفظ؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                e.Cancel = True
                Return
            End If
        End If
        
        MyBase.OnFormClosing(e)
    End Sub
    
#End Region

End Class
