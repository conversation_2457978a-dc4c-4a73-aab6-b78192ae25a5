<?php
/**
 * ملف اختبار سريع للتأكد من عمل النظام
 * Quick Test File
 */

// تضمين الملفات المطلوبة
require_once '../config/database.php';

echo "<h1>🔧 اختبار نظام إدارة المعامل</h1>";

// اختبار الاتصال بقاعدة البيانات
echo "<h2>1. اختبار قاعدة البيانات:</h2>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    
    if ($conn) {
        echo "✅ تم الاتصال بقاعدة البيانات بنجاح<br>";
        
        // اختبار وجود الجداول
        $tables = [
            'users', 'currencies', 'warehouses', 'item_categories', 
            'items', 'customers', 'suppliers', 'cash_boxes'
        ];
        
        echo "<h3>الجداول الموجودة:</h3>";
        foreach ($tables as $table) {
            try {
                $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
                if ($result) {
                    echo "✅ $table<br>";
                } else {
                    echo "❌ $table (غير موجود)<br>";
                }
            } catch (Exception $e) {
                echo "❌ $table (خطأ: " . $e->getMessage() . ")<br>";
            }
        }
        
        // اختبار المستخدمين
        echo "<h3>المستخدمين:</h3>";
        try {
            $users = $db->fetchAll("SELECT username, role FROM users LIMIT 5");
            if ($users) {
                foreach ($users as $user) {
                    echo "👤 {$user['username']} ({$user['role']})<br>";
                }
            } else {
                echo "⚠️ لا يوجد مستخدمين<br>";
            }
        } catch (Exception $e) {
            echo "❌ خطأ في جلب المستخدمين: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ فشل الاتصال بقاعدة البيانات<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
}

// اختبار فئة Config
echo "<h2>2. اختبار الإعدادات:</h2>";
try {
    if (class_exists('Config')) {
        echo "✅ فئة Config موجودة<br>";
        echo "📱 اسم التطبيق: " . Config::APP_NAME . "<br>";
        echo "🔢 الإصدار: " . Config::APP_VERSION . "<br>";
        echo "⏰ مهلة الجلسة: " . Config::SESSION_TIMEOUT . " ثانية<br>";
        echo "💰 العملة الافتراضية: " . Config::DEFAULT_CURRENCY . "<br>";
    } else {
        echo "❌ فئة Config غير موجودة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الإعدادات: " . $e->getMessage() . "<br>";
}

// اختبار الدوال المساعدة
echo "<h2>3. اختبار الدوال المساعدة:</h2>";
try {
    if (function_exists('formatMoney')) {
        echo "✅ formatMoney: " . formatMoney(1000, 'IQD') . "<br>";
        echo "✅ formatMoney: " . formatMoney(100, 'USD') . "<br>";
    } else {
        echo "❌ دالة formatMoney غير موجودة<br>";
    }
    
    if (function_exists('formatDate')) {
        echo "✅ formatDate: " . formatDate(date('Y-m-d')) . "<br>";
    } else {
        echo "❌ دالة formatDate غير موجودة<br>";
    }
    
    if (function_exists('cleanInput')) {
        echo "✅ cleanInput: " . cleanInput('<script>alert("test")</script>') . "<br>";
    } else {
        echo "❌ دالة cleanInput غير موجودة<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في الدوال المساعدة: " . $e->getMessage() . "<br>";
}

// اختبار فئة Auth
echo "<h2>4. اختبار نظام المصادقة:</h2>";
try {
    require_once '../classes/Auth.php';
    $auth = new Auth();
    echo "✅ فئة Auth تم تحميلها بنجاح<br>";
    
    // اختبار تسجيل دخول تجريبي
    $result = $auth->login('admin', 'admin123');
    if ($result['success']) {
        echo "✅ تسجيل الدخول نجح: " . $result['message'] . "<br>";
        
        $user = $auth->getCurrentUser();
        if ($user) {
            echo "👤 المستخدم الحالي: " . $user['full_name'] . " (" . $user['role'] . ")<br>";
        }
        
        $auth->logout();
        echo "✅ تم تسجيل الخروج<br>";
    } else {
        echo "❌ فشل تسجيل الدخول: " . $result['message'] . "<br>";
    }
} catch (Exception $e) {
    echo "❌ خطأ في نظام المصادقة: " . $e->getMessage() . "<br>";
}

// اختبار فئة Financial
echo "<h2>5. اختبار النظام المالي:</h2>";
try {
    require_once '../classes/Financial.php';
    $financial = new Financial();
    echo "✅ فئة Financial تم تحميلها بنجاح<br>";
    
    $kpis = $financial->getKPIs(date('Y-m-d'), date('Y-m-d'));
    echo "✅ مؤشرات الأداء:<br>";
    echo "💰 إجمالي المبيعات: " . formatMoney($kpis['total_sales']) . "<br>";
    echo "🛒 إجمالي المشتريات: " . formatMoney($kpis['total_purchases']) . "<br>";
    echo "📈 صافي الربح: " . formatMoney($kpis['net_profit']) . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ في النظام المالي: " . $e->getMessage() . "<br>";
}

// معلومات النظام
echo "<h2>6. معلومات النظام:</h2>";
echo "🐘 إصدار PHP: " . PHP_VERSION . "<br>";
echo "🗄️ إصدار MySQL: ";
try {
    $version = $db->fetchOne("SELECT VERSION() as version");
    echo $version['version'] . "<br>";
} catch (Exception $e) {
    echo "غير معروف<br>";
}
echo "🌐 الخادم: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف') . "<br>";
echo "📁 مجلد المشروع: " . __DIR__ . "<br>";

// روابط مفيدة
echo "<h2>7. روابط مفيدة:</h2>";
echo '<a href="setup.php" style="display:inline-block; padding:10px 20px; background:#007bff; color:white; text-decoration:none; border-radius:5px; margin:5px;">🔧 إعداد النظام</a><br>';
echo '<a href="login.php" style="display:inline-block; padding:10px 20px; background:#28a745; color:white; text-decoration:none; border-radius:5px; margin:5px;">🔐 تسجيل الدخول</a><br>';
echo '<a href="dashboard.php" style="display:inline-block; padding:10px 20px; background:#17a2b8; color:white; text-decoration:none; border-radius:5px; margin:5px;">📊 لوحة التحكم</a><br>';

echo "<hr>";
echo "<p style='color: #666; font-size: 14px;'>تم إنشاء هذا التقرير في: " . date('Y-m-d H:i:s') . "</p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
    margin: 20px;
    background: #f8f9fa;
}
h1, h2, h3 {
    color: #333;
}
h1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}
h2 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
    margin-top: 30px;
}
</style>
