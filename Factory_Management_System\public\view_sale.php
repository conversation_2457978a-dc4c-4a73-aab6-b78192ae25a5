<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$invoiceId = $_GET['id'] ?? 0;
if (!$invoiceId) {
    header('Location: sales.php');
    exit;
}

// جلب بيانات الفاتورة
$invoice = $db->fetchOne("
    SELECT si.*, c.name as customer_name, c.code as customer_code, c.phone as customer_phone,
           c.address as customer_address, c.email as customer_email,
           cur.name as currency_name, cur.symbol as currency_symbol,
           u.full_name as created_by_name
    FROM sales_invoices si
    JOIN customers c ON si.customer_id = c.id
    JOIN currencies cur ON si.currency_id = cur.id
    LEFT JOIN users u ON si.created_by = u.id
    WHERE si.id = ?
", [$invoiceId]);

if (!$invoice) {
    header('Location: sales.php?error=' . urlencode('الفاتورة غير موجودة'));
    exit;
}

// جلب تفاصيل الفاتورة
$invoiceDetails = $db->fetchAll("
    SELECT sid.*, i.name as item_name, i.code as item_code, i.unit as item_unit,
           w.name as warehouse_name
    FROM sales_invoice_details sid
    JOIN items i ON sid.item_id = i.id
    JOIN warehouses w ON sid.warehouse_id = w.id
    WHERE sid.invoice_id = ?
    ORDER BY i.name
", [$invoiceId]);

// ألوان الحالات
$statusColors = [
    'draft' => 'secondary',
    'confirmed' => 'primary',
    'shipped' => 'warning',
    'paid' => 'success',
    'cancelled' => 'danger'
];

$statusNames = [
    'draft' => 'مسودة',
    'confirmed' => 'مؤكدة',
    'shipped' => 'مشحونة',
    'paid' => 'مدفوعة',
    'cancelled' => 'ملغية'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مبيعات <?= htmlspecialchars($invoice['invoice_number']) ?> - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .invoice-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .invoice-title {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .status-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        .amount-highlight {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
        }
        .item-row {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border: 1px solid #dee2e6;
        }
        .company-header {
            text-align: center;
            margin-bottom: 2rem;
            padding: 1rem;
            border-bottom: 2px solid #667eea;
        }
        @media print {
            .no-print { display: none !important; }
            .invoice-header { background: #667eea !important; }
            body { background: white !important; }
        }
    </style>
</head>
<body>
    <div class="invoice-header no-print">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-file-invoice me-3"></i>فاتورة مبيعات</h1>
                    <p class="mb-0">رقم الفاتورة: <?= htmlspecialchars($invoice['invoice_number']) ?></p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="sales.php" class="btn btn-light me-2">
                        <i class="fas fa-arrow-right me-2"></i>العودة
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-light">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- رأس الشركة -->
        <div class="company-header">
            <h2>شركة المعامل المتقدمة</h2>
            <p class="mb-1">بغداد - العراق | هاتف: +964 1 234 5678</p>
            <p class="mb-0">البريد الإلكتروني: <EMAIL></p>
        </div>

        <div class="row">
            <!-- معلومات الفاتورة -->
            <div class="col-lg-8">
                <div class="card invoice-card">
                    <div class="invoice-title">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">رقم الفاتورة</label>
                                <div class="fw-bold"><?= htmlspecialchars($invoice['invoice_number']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">الحالة</label>
                                <div>
                                    <span class="badge bg-<?= $statusColors[$invoice['status']] ?> status-badge">
                                        <?= $statusNames[$invoice['status']] ?>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ الفاتورة</label>
                                <div class="fw-bold"><?= date('d/m/Y', strtotime($invoice['invoice_date'])) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ الاستحقاق</label>
                                <div class="fw-bold">
                                    <?= $invoice['due_date'] ? date('d/m/Y', strtotime($invoice['due_date'])) : 'غير محدد' ?>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">العملة</label>
                                <div class="fw-bold"><?= htmlspecialchars($invoice['currency_name'] . ' (' . $invoice['currency_symbol'] . ')') ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">سعر الصرف</label>
                                <div class="fw-bold"><?= number_format($invoice['exchange_rate'], 4) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">أنشأ بواسطة</label>
                                <div class="fw-bold"><?= htmlspecialchars($invoice['created_by_name'] ?? 'غير معروف') ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">تاريخ الإنشاء</label>
                                <div class="fw-bold"><?= date('d/m/Y H:i', strtotime($invoice['created_at'])) ?></div>
                            </div>
                        </div>

                        <?php if ($invoice['notes']): ?>
                            <div class="mt-4">
                                <label class="form-label text-muted">ملاحظات</label>
                                <div class="p-3 bg-light rounded"><?= nl2br(htmlspecialchars($invoice['notes'])) ?></div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="card invoice-card">
                    <div class="invoice-title">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>معلومات العميل</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">كود العميل</label>
                                <div class="fw-bold"><?= htmlspecialchars($invoice['customer_code']) ?></div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label text-muted">اسم العميل</label>
                                <div class="fw-bold"><?= htmlspecialchars($invoice['customer_name']) ?></div>
                            </div>
                            <?php if ($invoice['customer_phone']): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الهاتف</label>
                                    <div class="fw-bold"><?= htmlspecialchars($invoice['customer_phone']) ?></div>
                                </div>
                            <?php endif; ?>
                            <?php if ($invoice['customer_email']): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">البريد الإلكتروني</label>
                                    <div class="fw-bold"><?= htmlspecialchars($invoice['customer_email']) ?></div>
                                </div>
                            <?php endif; ?>
                            <?php if ($invoice['customer_address']): ?>
                                <div class="col-12 mb-3">
                                    <label class="form-label text-muted">العنوان</label>
                                    <div class="fw-bold"><?= htmlspecialchars($invoice['customer_address']) ?></div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملخص المبالغ -->
            <div class="col-lg-4">
                <div class="card invoice-card">
                    <div class="invoice-title">
                        <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص المبالغ</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between mb-3">
                            <span>المجموع الفرعي:</span>
                            <strong><?= number_format($invoice['subtotal'], 2) ?> <?= $invoice['currency_symbol'] ?></strong>
                        </div>
                        
                        <?php if ($invoice['discount_amount'] > 0): ?>
                            <div class="d-flex justify-content-between mb-3">
                                <span>الخصم:</span>
                                <strong class="text-danger">-<?= number_format($invoice['discount_amount'], 2) ?> <?= $invoice['currency_symbol'] ?></strong>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($invoice['tax_amount'] > 0): ?>
                            <div class="d-flex justify-content-between mb-3">
                                <span>الضريبة:</span>
                                <strong><?= number_format($invoice['tax_amount'], 2) ?> <?= $invoice['currency_symbol'] ?></strong>
                            </div>
                        <?php endif; ?>
                        
                        <hr>
                        
                        <div class="amount-highlight">
                            <h4 class="mb-1"><?= number_format($invoice['total_amount'], 2) ?> <?= $invoice['currency_symbol'] ?></h4>
                            <p class="mb-0">المجموع الكلي</p>
                        </div>
                        
                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>المبلغ المدفوع:</span>
                                <strong class="text-success"><?= number_format($invoice['paid_amount'], 2) ?> <?= $invoice['currency_symbol'] ?></strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>المبلغ المتبقي:</span>
                                <strong class="text-warning"><?= number_format($invoice['remaining_amount'], 2) ?> <?= $invoice['currency_symbol'] ?></strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="card invoice-card no-print">
                    <div class="invoice-title">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>الإجراءات</h5>
                    </div>
                    <div class="card-body p-4 text-center">
                        <?php if ($invoice['status'] == 'draft'): ?>
                            <a href="confirm_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-primary btn-sm mb-2">
                                <i class="fas fa-check me-2"></i>تأكيد الفاتورة
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($invoice['status'] == 'confirmed'): ?>
                            <a href="ship_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-warning btn-sm mb-2">
                                <i class="fas fa-truck me-2"></i>شحن الفاتورة
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($invoice['remaining_amount'] > 0 && $invoice['status'] != 'cancelled'): ?>
                            <a href="pay_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-success btn-sm mb-2">
                                <i class="fas fa-money-bill me-2"></i>تسجيل دفعة
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($invoice['status'] != 'paid' && $invoice['status'] != 'cancelled'): ?>
                            <a href="cancel_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-danger btn-sm mb-2"
                               onclick="return confirm('هل أنت متأكد من إلغاء الفاتورة؟')">
                                <i class="fas fa-times me-2"></i>إلغاء الفاتورة
                            </a>
                        <?php endif; ?>
                        
                        <a href="edit_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-secondary btn-sm mb-2">
                            <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- تفاصيل الأصناف -->
        <?php if (!empty($invoiceDetails)): ?>
            <div class="card invoice-card">
                <div class="invoice-title">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>تفاصيل الأصناف</h5>
                </div>
                <div class="card-body p-4">
                    <?php foreach ($invoiceDetails as $detail): ?>
                        <div class="item-row">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h6 class="mb-1"><?= htmlspecialchars($detail['item_name']) ?></h6>
                                    <small class="text-muted"><?= htmlspecialchars($detail['item_code']) ?></small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong><?= number_format($detail['quantity'], 3) ?></strong>
                                    <br><small class="text-muted"><?= htmlspecialchars($detail['item_unit']) ?></small>
                                </div>
                                <div class="col-md-2 text-center">
                                    <strong><?= number_format($detail['unit_price'], 2) ?></strong>
                                    <br><small class="text-muted"><?= $invoice['currency_symbol'] ?>/وحدة</small>
                                </div>
                                <?php if ($detail['discount_percentage'] > 0): ?>
                                    <div class="col-md-2 text-center">
                                        <strong><?= number_format($detail['discount_percentage'], 2) ?>%</strong>
                                        <br><small class="text-muted">خصم</small>
                                    </div>
                                <?php endif; ?>
                                <div class="col-md-2 text-center">
                                    <span class="badge bg-success fs-6">
                                        <?= number_format($detail['line_total'], 2) ?> <?= $invoice['currency_symbol'] ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
