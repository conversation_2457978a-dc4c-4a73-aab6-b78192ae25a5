# 🔧 دليل الإصلاح السريع - نظام إدارة المعامل

## ❌ **المشاكل التي تم حلها:**

### 1. **خطأ: "Undefined array key 'permissions'"**
✅ **تم الحل:** إضافة فحص للمتغير قبل الاستخدام
```php
$permissions = $user['permissions'] ?? [];
```

### 2. **خطأ: "Table 'sales_invoices' doesn't exist"**
✅ **تم الحل:** إضافة فحص وجود الجداول قبل الاستعلام
```php
if (!$this->tableExists('sales_invoices')) {
    return $this->getEmptyKPIs();
}
```

---

## 🚀 **خطوات التشغيل السريع:**

### **الطريقة الأولى: استخدام صفحة الإعداد**
1. اذهب إلى: `http://localhost/Factory_Management_System/public/setup.php`
2. اتبع الخطوات المعروضة
3. أدخل بيانات قاعدة البيانات
4. انقر "إنشاء قاعدة البيانات"
5. سجل دخول بالبيانات المعروضة

### **الطريقة الثانية: يدوياً**

#### **1. إنشاء قاعدة البيانات:**
```sql
-- في phpMyAdmin أو MySQL Command Line
CREATE DATABASE factory_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### **2. استيراد الهيكل:**
```sql
-- استيراد ملف database/factory_management.sql
-- أو نسخ ولصق محتوى الملف في phpMyAdmin
```

#### **3. تشغيل سكريبت الإعداد:**
```
http://localhost/Factory_Management_System/setup/install.php
```

---

## 🔑 **بيانات الدخول الافتراضية:**

| الدور | اسم المستخدم | كلمة المرور | الوصف |
|-------|--------------|-------------|--------|
| **مدير النظام** | `admin` | `admin123` | جميع الصلاحيات |
| **مدير عام** | `manager` | `manager123` | عرض التقارير |
| **محاسب** | `accountant` | `acc123` | المالية والمبيعات |
| **أمين مخزن** | `warehouse` | `wh123` | إدارة المخزون |
| **مدير إنتاج** | `production` | `prod123` | إدارة الإنتاج |
| **مدير مبيعات** | `sales` | `sales123` | المبيعات والعملاء |

---

## 🛠️ **حل المشاكل الشائعة:**

### **❌ خطأ: "Access denied for user"**
**الحل:**
```php
// في config/database.php
private $username = 'root';        // اسم المستخدم الصحيح
private $password = '';            // كلمة المرور الصحيحة
```

### **❌ خطأ: "Unknown database"**
**الحل:**
```sql
-- إنشاء قاعدة البيانات أولاً
CREATE DATABASE factory_management;
```

### **❌ خطأ: "Table doesn't exist"**
**الحل:**
```
1. تأكد من استيراد ملف database/factory_management.sql
2. أو شغل setup.php لإنشاء الجداول تلقائياً
```

### **❌ صفحة بيضاء**
**الحل:**
```php
// أضف في بداية الملف لعرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

### **❌ خطأ في الصلاحيات**
**الحل:**
```bash
# إعطاء صلاحيات للمجلدات
chmod 755 Factory_Management_System/
chmod 644 Factory_Management_System/config/database.php
```

---

## 📊 **التحقق من التثبيت:**

### **1. فحص قاعدة البيانات:**
```sql
-- تحقق من وجود الجداول
SHOW TABLES FROM factory_management;

-- يجب أن ترى 20 جدول تقريباً
```

### **2. فحص المستخدمين:**
```sql
-- تحقق من المستخدمين
SELECT username, role FROM users;
```

### **3. فحص البيانات الأساسية:**
```sql
-- تحقق من العملات
SELECT * FROM currencies;

-- تحقق من المخازن
SELECT * FROM warehouses;
```

---

## 🎯 **اختبار النظام:**

### **1. تسجيل الدخول:**
- اذهب إلى: `http://localhost/Factory_Management_System/public/`
- سجل دخول بحساب admin
- تأكد من ظهور لوحة التحكم

### **2. اختبار الوظائف:**
- ✅ عرض لوحة التحكم
- ✅ الانتقال بين القوائم
- ✅ عرض الإحصائيات
- ✅ تسجيل الخروج

---

## 📞 **الدعم الفني:**

### **إذا استمرت المشاكل:**

1. **تحقق من:**
   - إصدار PHP (7.4+)
   - إصدار MySQL (5.7+)
   - تفعيل PDO extension

2. **ملفات السجل:**
   - تحقق من error_log في Apache/Nginx
   - تحقق من MySQL error log

3. **إعادة التثبيت:**
   ```sql
   -- حذف قاعدة البيانات
   DROP DATABASE IF EXISTS factory_management;
   
   -- إعادة التثبيت
   -- ثم شغل setup.php مرة أخرى
   ```

---

## ✅ **التأكد من نجاح التثبيت:**

عند نجاح التثبيت يجب أن ترى:
- ✅ صفحة تسجيل دخول جميلة
- ✅ لوحة تحكم تفاعلية
- ✅ قوائم جانبية تعمل
- ✅ إحصائيات (حتى لو كانت صفر)
- ✅ عدم وجود أخطاء PHP

---

## 🎉 **مبروك!**

إذا وصلت هنا بدون أخطاء، فقد تم تثبيت النظام بنجاح!

**الخطوات التالية:**
1. غير كلمات المرور الافتراضية
2. أضف البيانات الأساسية (عملاء، موردين، أصناف)
3. ابدأ في استخدام النظام

**🚀 استمتع بنظام إدارة المعامل المتكامل!**
