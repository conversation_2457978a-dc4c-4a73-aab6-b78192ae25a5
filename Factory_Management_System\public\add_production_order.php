<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة أمر إنتاج جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_order'])) {
    try {
        $db->beginTransaction();
        
        // توليد رقم الأمر
        $prefix = 'PO';
        $year = date('Y');
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM production_orders WHERE YEAR(created_at) = ?", [$year]);
        $sequence = ($countResult['count'] ?? 0) + 1;
        $orderNumber = $prefix . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
        
        // إدراج أمر الإنتاج
        $orderData = [
            'order_number' => $orderNumber,
            'product_id' => (int)$_POST['product_id'],
            'quantity_required' => (float)$_POST['quantity_required'],
            'start_date' => $_POST['start_date'],
            'due_date' => $_POST['due_date'],
            'priority' => $_POST['priority'],
            'department_id' => !empty($_POST['department_id']) ? (int)$_POST['department_id'] : null,
            'supervisor_id' => !empty($_POST['supervisor_id']) ? (int)$_POST['supervisor_id'] : null,
            'estimated_cost' => (float)($_POST['estimated_cost'] ?? 0),
            'notes' => trim($_POST['notes'] ?? ''),
            'created_by' => $user['id']
        ];
        
        $orderId = $db->insert('production_orders', $orderData);
        
        // إضافة المواد المطلوبة للإنتاج
        if (!empty($_POST['materials'])) {
            foreach ($_POST['materials'] as $material) {
                if (!empty($material['material_id']) && !empty($material['quantity'])) {
                    $materialData = [
                        'production_order_id' => $orderId,
                        'material_id' => (int)$material['material_id'],
                        'quantity_required' => (float)$material['quantity'],
                        'unit_cost' => (float)($material['unit_cost'] ?? 0),
                        'warehouse_id' => !empty($material['warehouse_id']) ? (int)$material['warehouse_id'] : null
                    ];
                    
                    $db->insert('production_materials', $materialData);
                }
            }
        }
        
        // إضافة مراحل الإنتاج
        if (!empty($_POST['stages'])) {
            foreach ($_POST['stages'] as $index => $stage) {
                if (!empty($stage['stage_name'])) {
                    $stageData = [
                        'production_order_id' => $orderId,
                        'stage_name' => trim($stage['stage_name']),
                        'stage_order' => $index + 1,
                        'responsible_employee_id' => !empty($stage['responsible_employee_id']) ? (int)$stage['responsible_employee_id'] : null,
                        'notes' => trim($stage['notes'] ?? '')
                    ];
                    
                    $db->insert('production_stages', $stageData);
                }
            }
        }
        
        $db->commit();
        $success = "تم إضافة أمر الإنتاج بنجاح برقم: $orderNumber";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إضافة أمر الإنتاج: " . $e->getMessage();
    }
}

// جلب البيانات المطلوبة
try {
    $products = $db->fetchAll("SELECT id, name, code, unit FROM items WHERE type = 'product' ORDER BY name");
    $materials = $db->fetchAll("SELECT id, name, code, unit FROM items WHERE type = 'raw_material' ORDER BY name");
    $departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
    $supervisors = $db->fetchAll("SELECT id, full_name FROM employees WHERE status = 'active' ORDER BY full_name");
    $warehouses = $db->fetchAll("SELECT id, name FROM warehouses WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $products = [];
    $materials = [];
    $departments = [];
    $supervisors = [];
    $warehouses = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة أمر إنتاج جديد - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
        }
        
        .material-row, .stage-row {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 5px solid #667eea;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>إضافة أمر إنتاج جديد</h1>
                    <p class="mb-0">إنشاء أمر إنتاج متكامل مع إدارة المواد والمراحل</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="production.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="row">
                <!-- معلومات أساسية -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">المنتج <span class="text-danger">*</span></label>
                                <select name="product_id" class="form-select" required>
                                    <option value="">اختر المنتج</option>
                                    <?php foreach ($products as $product): ?>
                                        <option value="<?= $product['id'] ?>">
                                            <?= htmlspecialchars($product['name']) ?> (<?= htmlspecialchars($product['code']) ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الكمية المطلوبة <span class="text-danger">*</span></label>
                                <input type="number" name="quantity_required" class="form-control" 
                                       step="0.001" min="0.001" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                                    <input type="date" name="start_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                    <input type="date" name="due_date" class="form-control" 
                                           value="<?= date('Y-m-d', strtotime('+7 days')) ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-select">
                                    <option value="low">منخفضة</option>
                                    <option value="normal" selected>عادية</option>
                                    <option value="high">عالية</option>
                                    <option value="urgent">عاجلة</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">التكلفة المقدرة (د.ع)</label>
                                <input type="number" name="estimated_cost" class="form-control" 
                                       step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات إدارية -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>المعلومات الإدارية</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">القسم المسؤول</label>
                                <select name="department_id" class="form-select">
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>">
                                            <?= htmlspecialchars($dept['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">المشرف</label>
                                <select name="supervisor_id" class="form-select">
                                    <option value="">اختر المشرف</option>
                                    <?php foreach ($supervisors as $supervisor): ?>
                                        <option value="<?= $supervisor['id'] ?>">
                                            <?= htmlspecialchars($supervisor['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="4" 
                                          placeholder="أي ملاحظات أو تعليمات خاصة"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواد المطلوبة -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>المواد المطلوبة</h5>
                        <button type="button" class="btn btn-success" onclick="addMaterial()">
                            <i class="fas fa-plus me-2"></i>إضافة مادة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="materials-container">
                        <div class="material-row">
                            <div class="row align-items-center">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">المادة</label>
                                    <select name="materials[0][material_id]" class="form-select">
                                        <option value="">اختر المادة</option>
                                        <?php foreach ($materials as $material): ?>
                                            <option value="<?= $material['id'] ?>">
                                                <?= htmlspecialchars($material['name']) ?> (<?= htmlspecialchars($material['code']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">الكمية</label>
                                    <input type="number" name="materials[0][quantity]" class="form-control" 
                                           step="0.001" min="0">
                                </div>
                                
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">سعر الوحدة</label>
                                    <input type="number" name="materials[0][unit_cost]" class="form-control" 
                                           step="0.01" min="0">
                                </div>
                                
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">المخزن</label>
                                    <select name="materials[0][warehouse_id]" class="form-select">
                                        <option value="">اختر المخزن</option>
                                        <?php foreach ($warehouses as $warehouse): ?>
                                            <option value="<?= $warehouse['id'] ?>">
                                                <?= htmlspecialchars($warehouse['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-1 mb-3">
                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeMaterial(this)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" name="add_order" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>إنشاء أمر الإنتاج
                </button>
                <a href="production.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let materialIndex = 1;
        
        // إضافة مادة جديدة
        function addMaterial() {
            const container = document.getElementById('materials-container');
            const materialRow = document.createElement('div');
            materialRow.className = 'material-row';
            materialRow.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">المادة</label>
                        <select name="materials[${materialIndex}][material_id]" class="form-select">
                            <option value="">اختر المادة</option>
                            <?php foreach ($materials as $material): ?>
                                <option value="<?= $material['id'] ?>">
                                    <?= htmlspecialchars($material['name']) ?> (<?= htmlspecialchars($material['code']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">الكمية</label>
                        <input type="number" name="materials[${materialIndex}][quantity]" class="form-control" 
                               step="0.001" min="0">
                    </div>
                    
                    <div class="col-md-2 mb-3">
                        <label class="form-label">سعر الوحدة</label>
                        <input type="number" name="materials[${materialIndex}][unit_cost]" class="form-control" 
                               step="0.01" min="0">
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label class="form-label">المخزن</label>
                        <select name="materials[${materialIndex}][warehouse_id]" class="form-select">
                            <option value="">اختر المخزن</option>
                            <?php foreach ($warehouses as $warehouse): ?>
                                <option value="<?= $warehouse['id'] ?>">
                                    <?= htmlspecialchars($warehouse['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-1 mb-3">
                        <button type="button" class="btn btn-danger btn-sm" onclick="removeMaterial(this)">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(materialRow);
            materialIndex++;
        }
        
        // حذف مادة
        function removeMaterial(button) {
            const materialRow = button.closest('.material-row');
            materialRow.remove();
        }
    </script>
</body>
</html>
