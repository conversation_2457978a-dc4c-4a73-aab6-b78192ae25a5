<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة أمر إنتاج جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول أوامر الإنتاج إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS production_orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_number VARCHAR(20) NOT NULL UNIQUE,
            product_id INT NOT NULL,
            quantity_required DECIMAL(10,3) NOT NULL,
            quantity_produced DECIMAL(10,3) DEFAULT 0.000,
            quantity_remaining DECIMAL(10,3) GENERATED ALWAYS AS (quantity_required - quantity_produced) STORED,
            start_date DATE NOT NULL,
            due_date DATE NOT NULL,
            completion_date DATE NULL,
            priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
            status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
            department_id INT,
            supervisor_id INT,
            estimated_cost DECIMAL(12,2) DEFAULT 0.00,
            actual_cost DECIMAL(12,2) DEFAULT 0.00,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول مكونات أوامر الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_order_materials (
            id INT PRIMARY KEY AUTO_INCREMENT,
            production_order_id INT NOT NULL,
            material_id INT NOT NULL,
            quantity_required DECIMAL(10,3) NOT NULL,
            quantity_used DECIMAL(10,3) DEFAULT 0.000,
            unit_cost DECIMAL(10,2) DEFAULT 0.00,
            total_cost DECIMAL(12,2) GENERATED ALWAYS AS (quantity_used * unit_cost) STORED,
            warehouse_id INT,
            allocated_date TIMESTAMP NULL,
            notes TEXT
        )");
        
        // توليد رقم أمر الإنتاج
        $prefix = 'PRO';
        $year = date('Y');
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM production_orders WHERE YEAR(created_at) = ?", [$year]);
        $sequence = ($countResult['count'] ?? 0) + 1;
        $orderNumber = $prefix . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
        
        // بيانات أمر الإنتاج
        $orderData = [
            'order_number' => $orderNumber,
            'product_id' => (int)$_POST['product_id'],
            'quantity_required' => (float)$_POST['quantity_required'],
            'start_date' => $_POST['start_date'],
            'due_date' => $_POST['due_date'],
            'priority' => $_POST['priority'],
            'department_id' => (int)$_POST['department_id'],
            'supervisor_id' => (int)($_POST['supervisor_id'] ?? 0),
            'estimated_cost' => (float)($_POST['estimated_cost'] ?? 0),
            'notes' => $_POST['notes'] ?? '',
            'created_by' => $user['id']
        ];
        
        // إدراج أمر الإنتاج
        $orderId = $db->insert('production_orders', $orderData);
        
        // معالجة المواد المطلوبة
        $materials = $_POST['materials'] ?? [];
        foreach ($materials as $material) {
            if (empty($material['material_id']) || empty($material['quantity_required'])) {
                continue;
            }
            
            $materialData = [
                'production_order_id' => $orderId,
                'material_id' => (int)$material['material_id'],
                'quantity_required' => (float)$material['quantity_required'],
                'unit_cost' => (float)($material['unit_cost'] ?? 0),
                'warehouse_id' => (int)($material['warehouse_id'] ?? 1),
                'notes' => $material['notes'] ?? ''
            ];
            
            $db->insert('production_order_materials', $materialData);
        }
        
        $db->commit();
        $success = "تم إنشاء أمر الإنتاج بنجاح برقم: $orderNumber";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إنشاء أمر الإنتاج: " . $e->getMessage();
    }
}

// جلب البيانات المطلوبة
try {
    // المنتجات النهائية
    $products = $db->fetchAll("SELECT id, code, name, unit FROM items WHERE type = 'finished_product' AND is_active = 1 ORDER BY name");
    
    // المواد الخام
    $materials = $db->fetchAll("SELECT id, code, name, unit, cost_price FROM items WHERE type IN ('raw_material', 'semi_finished') AND is_active = 1 ORDER BY name");
    
    // الأقسام
    $departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
    
    // المشرفين (الموظفين)
    $supervisors = $db->fetchAll("SELECT id, full_name, position FROM employees WHERE status = 'active' ORDER BY full_name");
    
    // المخازن
    $warehouses = $db->fetchAll("SELECT id, name FROM warehouses WHERE is_active = 1 ORDER BY name");
    
} catch (Exception $e) {
    $products = [];
    $materials = [];
    $departments = [];
    $supervisors = [];
    $warehouses = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أمر إنتاج جديد - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .material-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .priority-urgent { color: #dc3545; }
        .priority-high { color: #fd7e14; }
        .priority-normal { color: #28a745; }
        .priority-low { color: #6c757d; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>أمر إنتاج جديد</h1>
                    <p class="mb-0">إضافة أمر إنتاج جديد للمصنع</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="production_orders.php" class="btn btn-light">
                        <i class="fas fa-list me-2"></i>أوامر الإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <div class="mt-2">
                    <a href="production_orders.php" class="btn btn-sm btn-primary">عرض أوامر الإنتاج</a>
                    <a href="add_production_order.php" class="btn btn-sm btn-outline-primary">أمر جديد</a>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <?php if (strpos($error, 'Table') !== false): ?>
                    <br><a href="complete_fix.php" class="btn btn-sm btn-primary mt-2">إعداد النظام</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="productionOrderForm">
            <div class="row">
                <!-- معلومات أمر الإنتاج -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات أمر الإنتاج</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المنتج <span class="text-danger">*</span></label>
                                    <select name="product_id" class="form-select" required>
                                        <option value="">اختر المنتج</option>
                                        <?php foreach ($products as $product): ?>
                                            <option value="<?= $product['id'] ?>">
                                                <?= htmlspecialchars($product['name']) ?> (<?= htmlspecialchars($product['unit']) ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الكمية المطلوبة <span class="text-danger">*</span></label>
                                    <input type="number" name="quantity_required" class="form-control" 
                                           step="0.001" min="0.001" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ البدء <span class="text-danger">*</span></label>
                                    <input type="date" name="start_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الاستحقاق <span class="text-danger">*</span></label>
                                    <input type="date" name="due_date" class="form-control" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الأولوية</label>
                                    <select name="priority" class="form-select">
                                        <option value="low" class="priority-low">منخفضة</option>
                                        <option value="normal" class="priority-normal" selected>عادية</option>
                                        <option value="high" class="priority-high">عالية</option>
                                        <option value="urgent" class="priority-urgent">عاجلة</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">القسم المسؤول</label>
                                    <select name="department_id" class="form-select">
                                        <option value="">اختر القسم</option>
                                        <?php foreach ($departments as $dept): ?>
                                            <option value="<?= $dept['id'] ?>" <?= $dept['name'] == 'الإنتاج' ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($dept['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المشرف</label>
                                    <select name="supervisor_id" class="form-select">
                                        <option value="">اختر المشرف</option>
                                        <?php foreach ($supervisors as $supervisor): ?>
                                            <option value="<?= $supervisor['id'] ?>">
                                                <?= htmlspecialchars($supervisor['full_name']) ?>
                                                <?php if ($supervisor['position']): ?>
                                                    - <?= htmlspecialchars($supervisor['position']) ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">التكلفة المقدرة</label>
                                    <input type="number" name="estimated_cost" class="form-control" 
                                           step="0.01" min="0" placeholder="0.00">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية حول أمر الإنتاج"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص أمر الإنتاج -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص أمر الإنتاج</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center p-3 bg-light rounded mb-3">
                                <h4 class="text-primary" id="totalCost">0.00</h4>
                                <p class="mb-0">التكلفة الإجمالية المقدرة</p>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>عدد المواد:</span>
                                    <span id="materialCount">0</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>الكمية المطلوبة:</span>
                                    <span id="requiredQuantity">0</span>
                                </div>
                            </div>
                            
                            <div class="alert alert-info">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    سيتم حساب التكلفة الفعلية أثناء الإنتاج
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواد المطلوبة -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>المواد المطلوبة</h5>
                        <button type="button" class="btn btn-success" onclick="addMaterial()">
                            <i class="fas fa-plus me-2"></i>إضافة مادة
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="materialsContainer">
                        <!-- سيتم إضافة المواد هنا -->
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ أمر الإنتاج
                </button>
                <a href="production_orders.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let materialCounter = 0;
        const materials = <?= json_encode($materials) ?>;
        const warehouses = <?= json_encode($warehouses) ?>;

        function addMaterial() {
            materialCounter++;
            const container = document.getElementById('materialsContainer');
            
            const materialHtml = `
                <div class="material-row" id="material-${materialCounter}">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">المادة</label>
                            <select name="materials[${materialCounter}][material_id]" class="form-select" required onchange="updateMaterialCost(${materialCounter})">
                                <option value="">اختر المادة</option>
                                ${materials.map(material => `<option value="${material.id}" data-cost="${material.cost_price}">${material.name} (${material.unit})</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">الكمية</label>
                            <input type="number" name="materials[${materialCounter}][quantity_required]" class="form-control" 
                                   step="0.001" min="0.001" required onchange="calculateMaterialCost(${materialCounter})">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="number" name="materials[${materialCounter}][unit_cost]" class="form-control" 
                                   step="0.01" min="0" onchange="calculateMaterialCost(${materialCounter})">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">المخزن</label>
                            <select name="materials[${materialCounter}][warehouse_id]" class="form-select">
                                ${warehouses.map(warehouse => `<option value="${warehouse.id}">${warehouse.name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger w-100" onclick="removeMaterial(${materialCounter})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        <div class="col-12">
                            <input type="text" name="materials[${materialCounter}][notes]" class="form-control" 
                                   placeholder="ملاحظات حول هذه المادة">
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', materialHtml);
            updateSummary();
        }

        function removeMaterial(id) {
            document.getElementById(`material-${id}`).remove();
            updateSummary();
        }

        function updateMaterialCost(id) {
            const select = document.querySelector(`select[name="materials[${id}][material_id]"]`);
            const costInput = document.querySelector(`input[name="materials[${id}][unit_cost]"]`);
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.dataset.cost) {
                costInput.value = selectedOption.dataset.cost;
                calculateMaterialCost(id);
            }
        }

        function calculateMaterialCost(id) {
            updateSummary();
        }

        function updateSummary() {
            let totalCost = 0;
            let materialCount = 0;
            
            document.querySelectorAll('.material-row').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity_required]"]').value) || 0;
                const unitCost = parseFloat(row.querySelector('input[name*="[unit_cost]"]').value) || 0;
                
                if (quantity > 0 && unitCost > 0) {
                    totalCost += quantity * unitCost;
                    materialCount++;
                }
            });
            
            const requiredQuantity = parseFloat(document.querySelector('input[name="quantity_required"]').value) || 0;
            
            document.getElementById('totalCost').textContent = totalCost.toFixed(2);
            document.getElementById('materialCount').textContent = materialCount;
            document.getElementById('requiredQuantity').textContent = requiredQuantity;
            
            // تحديث التكلفة المقدرة
            document.querySelector('input[name="estimated_cost"]').value = totalCost.toFixed(2);
        }

        // إضافة مادة واحدة افتراضياً
        addMaterial();

        // تحديث الملخص عند تغيير الكمية المطلوبة
        document.querySelector('input[name="quantity_required"]').addEventListener('input', updateSummary);
    </script>
</body>
</html>
