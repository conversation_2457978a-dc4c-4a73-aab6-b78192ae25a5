<?php
/**
 * إعدادات قاعدة البيانات - نظام إدارة المعامل
 * Database Configuration - Factory Management System
 */

// تضمين ملف الإعدادات العامة
require_once __DIR__ . '/config.php';

class Database {
    private $host = 'localhost';
    private $db_name = 'factory_management';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);

        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }

        return $this->conn;
    }

    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * بدء المعاملة
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على سجل واحد
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * الحصول على عدة سجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * إدراج سجل جديد
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);

        return $this->conn->lastInsertId();
    }

    /**
     * تحديث سجل
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);

        return $this->query($sql, $params);
    }

    /**
     * حذف سجل
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    /**
     * عدد السجلات
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetchOne($sql, $params);
        return $result['count'];
    }

    /**
     * فحص وجود سجل
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
}

/**
 * إعدادات النظام العامة
 */
class Config {
    // إعدادات التطبيق
    const APP_NAME = 'نظام إدارة المعامل';
    const APP_VERSION = '1.0.0';
    const APP_URL = 'http://localhost/Factory_Management_System';

    // إعدادات الأمان
    const SESSION_TIMEOUT = 3600; // ساعة واحدة
    const PASSWORD_MIN_LENGTH = 6;
    const MAX_LOGIN_ATTEMPTS = 3;

    // إعدادات الملفات
    const UPLOAD_PATH = 'uploads/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];

    // إعدادات التقارير
    const REPORTS_PATH = 'reports/';
    const BACKUP_PATH = 'backups/';

    // إعدادات العملة الافتراضية
    const DEFAULT_CURRENCY = 'IQD';
    const DECIMAL_PLACES = 2;

    // إعدادات التاريخ والوقت
    const TIMEZONE = 'Asia/Baghdad';
    const DATE_FORMAT = 'Y-m-d';
    const DATETIME_FORMAT = 'Y-m-d H:i:s';
    const DISPLAY_DATE_FORMAT = 'd/m/Y';
    const DISPLAY_DATETIME_FORMAT = 'd/m/Y H:i';

    /**
     * الحصول على إعداد
     */
    public static function get($key, $default = null) {
        return defined("self::{$key}") ? constant("self::{$key}") : $default;
    }
}

/**
 * فئة المساعدات العامة
 */
class Helper {
    /**
     * تنسيق المبلغ
     */
    public static function formatMoney($amount, $currency = 'IQD', $decimals = 2) {
        $formatted = number_format($amount, $decimals);

        switch($currency) {
            case 'IQD':
                return $formatted . ' د.ع';
            case 'USD':
                return '$' . $formatted;
            case 'EUR':
                return '€' . $formatted;
            default:
                return $formatted . ' ' . $currency;
        }
    }

    /**
     * تنسيق التاريخ
     */
    public static function formatDate($date, $format = null) {
        if(!$format) $format = Config::DISPLAY_DATE_FORMAT;
        return date($format, strtotime($date));
    }

    /**
     * تنسيق التاريخ والوقت
     */
    public static function formatDateTime($datetime, $format = null) {
        if(!$format) $format = Config::DISPLAY_DATETIME_FORMAT;
        return date($format, strtotime($datetime));
    }

    /**
     * توليد رقم عشوائي
     */
    public static function generateNumber($prefix = '', $length = 6) {
        $number = str_pad(mt_rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
        return $prefix . $number;
    }

    /**
     * تنظيف النص
     */
    public static function cleanInput($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }

    /**
     * تشفير كلمة المرور
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * التحقق من كلمة المرور
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }

    /**
     * إنشاء رمز مميز
     */
    public static function generateToken($length = 32) {
        return bin2hex(random_bytes($length));
    }

    /**
     * تحويل العملة
     */
    public static function convertCurrency($amount, $fromRate, $toRate) {
        return ($amount / $fromRate) * $toRate;
    }

    /**
     * حساب النسبة المئوية
     */
    public static function calculatePercentage($value, $total) {
        return $total > 0 ? ($value / $total) * 100 : 0;
    }

    /**
     * حساب الخصم
     */
    public static function calculateDiscount($amount, $percentage) {
        return $amount * ($percentage / 100);
    }

    /**
     * حساب الضريبة
     */
    public static function calculateTax($amount, $rate) {
        return $amount * ($rate / 100);
    }
}

// تعيين المنطقة الزمنية
date_default_timezone_set(Config::TIMEZONE);

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
