<?php
/**
 * إعدادات قاعدة البيانات - نظام إدارة المعامل
 * Database Configuration - Factory Management System
 */

// تضمين ملف الإعدادات العامة
require_once __DIR__ . '/config.php';

class Database {
    private $host = 'localhost';
    private $db_name = 'factory_management';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    /**
     * Constructor - إنشاء الاتصال تلقائياً
     */
    public function __construct() {
        $this->connect();
    }

    /**
     * الاتصال بقاعدة البيانات
     */
    private function connect() {
        try {
            // محاولة الاتصال بقاعدة البيانات
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);

        } catch(PDOException $exception) {
            // إذا فشل الاتصال، محاولة إنشاء قاعدة البيانات
            try {
                $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
                $tempConn = new PDO($dsn, $this->username, $this->password, $options);
                $tempConn->exec("CREATE DATABASE IF NOT EXISTS `" . $this->db_name . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

                // إعادة محاولة الاتصال
                $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
                $this->conn = new PDO($dsn, $this->username, $this->password, $options);

            } catch(PDOException $e) {
                throw new Exception("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
            }
        }
    }

    /**
     * الحصول على الاتصال
     */
    public function getConnection() {
        if ($this->conn === null) {
            $this->connect();
        }
        return $this->conn;
    }

    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * بدء المعاملة
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->conn->rollback();
    }

    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            // التأكد من وجود الاتصال
            if ($this->conn === null) {
                $this->connect();
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }

    /**
     * الحصول على سجل واحد
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * الحصول على عدة سجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * إدراج سجل جديد
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);

        return $this->conn->lastInsertId();
    }

    /**
     * تحديث سجل
     */
    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);

        return $this->query($sql, $params);
    }

    /**
     * حذف سجل
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    /**
     * عدد السجلات
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetchOne($sql, $params);
        return $result['count'];
    }

    /**
     * فحص وجود سجل
     */
    public function exists($table, $where, $params = []) {
        return $this->count($table, $where, $params) > 0;
    }
}

// تم نقل فئة Config إلى ملف config/config.php

// تم نقل فئة Helper والدوال المساعدة إلى ملف config/config.php

// تم نقل إعدادات الجلسة والمنطقة الزمنية إلى ملف config/config.php
?>
