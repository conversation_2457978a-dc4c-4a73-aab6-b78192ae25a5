<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$sql = "SELECT po.*, i.name as product_name, i.unit, w.name as warehouse_name,
               u.full_name as created_by_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN warehouses w ON po.warehouse_id = w.id
        LEFT JOIN users u ON po.created_by = u.id
        WHERE 1=1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (po.order_number LIKE :search OR i.name LIKE :search)";
    $params['search'] = "%$search%";
}

if (!empty($status)) {
    $sql .= " AND po.status = :status";
    $params['status'] = $status;
}

if (!empty($date_from)) {
    $sql .= " AND po.start_date >= :date_from";
    $params['date_from'] = $date_from;
}

if (!empty($date_to)) {
    $sql .= " AND po.start_date <= :date_to";
    $params['date_to'] = $date_to;
}

$sql .= " ORDER BY po.created_at DESC LIMIT :limit OFFSET :offset";
$params['limit'] = $limit;
$params['offset'] = $offset;

try {
    // التحقق من وجود الجداول
    if (!$db->fetchOne("SHOW TABLES LIKE 'production_orders'")) {
        $orders = [];
        $totalOrders = 0;
        $totalPages = 0;
        $totalCost = 0;
        $error = "جداول الإنتاج غير موجودة. يرجى إعداد قاعدة البيانات أولاً.";
    } else {
        $orders = $db->fetchAll($sql, $params);

        // عدد النتائج الإجمالي
        $countSql = str_replace("SELECT po.*, i.name as product_name, i.unit, w.name as warehouse_name,
               u.full_name as created_by_name", "SELECT COUNT(*) as count", $sql);
        $countSql = str_replace("ORDER BY po.created_at DESC LIMIT :limit OFFSET :offset", "", $countSql);
        unset($params['limit'], $params['offset']);
        $totalOrders = $db->fetchOne($countSql, $params)['count'] ?? 0;
        $totalPages = ceil($totalOrders / $limit);

        // إجمالي التكلفة
        $totalCostSql = str_replace("SELECT po.*, i.name as product_name, i.unit, w.name as warehouse_name,
               u.full_name as created_by_name", "SELECT SUM(po.total_cost) as total", $sql);
        $totalCostSql = str_replace("ORDER BY po.created_at DESC LIMIT :limit OFFSET :offset", "", $totalCostSql);
        $totalCost = $db->fetchOne($totalCostSql, $params)['total'] ?? 0;
    }
} catch (Exception $e) {
    $orders = [];
    $totalOrders = 0;
    $totalPages = 0;
    $totalCost = 0;
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .table th { background-color: #f8f9fa; }
        .status-planned { background-color: #6c757d; }
        .status-in_progress { background-color: #0d6efd; }
        .status-completed { background-color: #198754; }
        .status-cancelled { background-color: #dc3545; }
        .progress-bar-custom { height: 8px; border-radius: 4px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>إدارة الإنتاج</h5>
                        <div>
                            <a href="new_production_order.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>أمر إنتاج جديد
                            </a>
                            <a href="production_recipes.php" class="btn btn-info">
                                <i class="fas fa-flask me-2"></i>وصفات الإنتاج
                            </a>
                            <a href="setup_production.php" class="btn btn-warning">
                                <i class="fas fa-cog me-2"></i>إعداد النظام
                            </a>
                            <a href="production_reports.php" class="btn btn-success">
                                <i class="fas fa-chart-bar me-2"></i>تقارير الإنتاج
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                                <br><a href="setup.php" class="btn btn-sm btn-primary mt-2">إعداد قاعدة البيانات</a>
                            </div>
                        <?php endif; ?>

                        <!-- فلاتر البحث -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="رقم الأمر أو اسم المنتج...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="planned" <?= $status == 'planned' ? 'selected' : '' ?>>مخطط</option>
                                    <option value="in_progress" <?= $status == 'in_progress' ? 'selected' : '' ?>>قيد التنفيذ</option>
                                    <option value="completed" <?= $status == 'completed' ? 'selected' : '' ?>>مكتمل</option>
                                    <option value="cancelled" <?= $status == 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="production.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </form>

                        <!-- إحصائيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6>إجمالي الأوامر</h6>
                                        <h4><?= number_format($totalOrders) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h6>قيد التنفيذ</h6>
                                        <h4><?= count(array_filter($orders, function($order) { return $order['status'] == 'in_progress'; })) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6>مكتملة</h6>
                                        <h4><?= count(array_filter($orders, function($order) { return $order['status'] == 'completed'; })) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6>إجمالي التكلفة</h6>
                                        <h4><?= formatMoney($totalCost) ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أوامر الإنتاج -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الأمر</th>
                                        <th>المنتج</th>
                                        <th>الكمية المطلوبة</th>
                                        <th>الكمية المنتجة</th>
                                        <th>التقدم</th>
                                        <th>المخزن</th>
                                        <th>تاريخ البدء</th>
                                        <th>التاريخ المتوقع</th>
                                        <th>التكلفة</th>
                                        <th>الحالة</th>
                                        <th>أنشأ بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($orders)): ?>
                                        <tr>
                                            <td colspan="12" class="text-center text-muted py-4">
                                                <i class="fas fa-cogs fa-3x mb-3"></i><br>
                                                لا توجد أوامر إنتاج
                                                <?php if (!isset($error)): ?>
                                                    <br><a href="add_production_order.php" class="btn btn-primary mt-2">إنشاء أول أمر إنتاج</a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($orders as $order): ?>
                                            <?php
                                            $progress = $order['quantity_to_produce'] > 0 ?
                                                       ($order['quantity_produced'] / $order['quantity_to_produce']) * 100 : 0;
                                            $progress = min(100, max(0, $progress));
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($order['order_number']) ?></strong>
                                                </td>
                                                <td>
                                                    <?= htmlspecialchars($order['product_name'] ?? 'منتج محذوف') ?>
                                                    <br><small class="text-muted"><?= htmlspecialchars($order['unit'] ?? '') ?></small>
                                                </td>
                                                <td><?= number_format($order['quantity_to_produce'], 2) ?></td>
                                                <td><?= number_format($order['quantity_produced'], 2) ?></td>
                                                <td>
                                                    <div class="progress progress-bar-custom">
                                                        <div class="progress-bar
                                                            <?= $progress == 100 ? 'bg-success' : ($progress > 0 ? 'bg-warning' : 'bg-secondary') ?>"
                                                            style="width: <?= $progress ?>%"></div>
                                                    </div>
                                                    <small><?= number_format($progress, 1) ?>%</small>
                                                </td>
                                                <td><?= htmlspecialchars($order['warehouse_name'] ?? 'مخزن محذوف') ?></td>
                                                <td><?= $order['start_date'] ? formatDate($order['start_date']) : '-' ?></td>
                                                <td>
                                                    <?= $order['expected_end_date'] ? formatDate($order['expected_end_date']) : '-' ?>
                                                    <?php if ($order['expected_end_date'] && $order['expected_end_date'] < date('Y-m-d') && $order['status'] != 'completed'): ?>
                                                        <br><small class="text-danger">متأخر</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= formatMoney($order['total_cost']) ?></td>
                                                <td>
                                                    <?php
                                                    $statusLabels = [
                                                        'planned' => ['مخطط', 'status-planned'],
                                                        'in_progress' => ['قيد التنفيذ', 'status-in_progress'],
                                                        'completed' => ['مكتمل', 'status-completed'],
                                                        'cancelled' => ['ملغي', 'status-cancelled']
                                                    ];
                                                    $statusInfo = $statusLabels[$order['status']] ?? [$order['status'], 'bg-secondary'];
                                                    ?>
                                                    <span class="badge <?= $statusInfo[1] ?>"><?= $statusInfo[0] ?></span>
                                                </td>
                                                <td>
                                                    <small><?= htmlspecialchars($order['created_by_name'] ?? 'غير معروف') ?></small>
                                                    <br><small class="text-muted"><?= formatDateTime($order['created_at']) ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="view_production_order.php?id=<?= $order['id'] ?>" class="btn btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($order['status'] == 'planned'): ?>
                                                            <a href="start_production.php?id=<?= $order['id'] ?>" class="btn btn-outline-success" title="بدء الإنتاج">
                                                                <i class="fas fa-play"></i>
                                                            </a>
                                                            <a href="edit_production_order.php?id=<?= $order['id'] ?>" class="btn btn-outline-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if ($order['status'] == 'in_progress'): ?>
                                                            <a href="complete_production.php?id=<?= $order['id'] ?>" class="btn btn-outline-success" title="إنهاء الإنتاج">
                                                                <i class="fas fa-check"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="print_production_order.php?id=<?= $order['id'] ?>" class="btn btn-outline-secondary" title="طباعة" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">السابق</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">التالي</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
