<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// إنشاء الجداول المطلوبة
try {
    // جدول أوامر الإنتاج
    $db->query("CREATE TABLE IF NOT EXISTS production_orders (
        id INT PRIMARY KEY AUTO_INCREMENT,
        order_number VARCHAR(50) NOT NULL UNIQUE,
        product_id INT NOT NULL,
        quantity_required DECIMAL(15,3) NOT NULL,
        quantity_produced DECIMAL(15,3) DEFAULT 0,
        quantity_remaining DECIMAL(15,3) GENERATED ALWAYS AS (quantity_required - quantity_produced) STORED,
        start_date DATE NOT NULL,
        due_date DATE NOT NULL,
        completion_date DATE NULL,
        status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'on_hold') DEFAULT 'pending',
        priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
        department_id INT,
        supervisor_id INT,
        estimated_cost DECIMAL(15,2) DEFAULT 0,
        actual_cost DECIMAL(15,2) DEFAULT 0,
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // جدول مواد الإنتاج
    $db->query("CREATE TABLE IF NOT EXISTS production_materials (
        id INT PRIMARY KEY AUTO_INCREMENT,
        production_order_id INT NOT NULL,
        material_id INT NOT NULL,
        quantity_required DECIMAL(15,3) NOT NULL,
        quantity_used DECIMAL(15,3) DEFAULT 0,
        unit_cost DECIMAL(10,2) DEFAULT 0,
        total_cost DECIMAL(15,2) GENERATED ALWAYS AS (quantity_used * unit_cost) STORED,
        warehouse_id INT,
        status ENUM('pending', 'allocated', 'consumed') DEFAULT 'pending',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (production_order_id) REFERENCES production_orders(id) ON DELETE CASCADE
    )");
    
    // جدول حركات المخزون للإنتاج
    $db->query("CREATE TABLE IF NOT EXISTS production_inventory_movements (
        id INT PRIMARY KEY AUTO_INCREMENT,
        production_order_id INT NOT NULL,
        item_id INT NOT NULL,
        movement_type ENUM('raw_material_out', 'finished_product_in', 'waste_out', 'return_in') NOT NULL,
        quantity DECIMAL(15,3) NOT NULL,
        unit_cost DECIMAL(10,2) DEFAULT 0,
        total_cost DECIMAL(15,2) GENERATED ALWAYS AS (quantity * unit_cost) STORED,
        warehouse_id INT,
        reference_number VARCHAR(100),
        notes TEXT,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    
    // جدول مراحل الإنتاج
    $db->query("CREATE TABLE IF NOT EXISTS production_stages (
        id INT PRIMARY KEY AUTO_INCREMENT,
        production_order_id INT NOT NULL,
        stage_name VARCHAR(200) NOT NULL,
        stage_order INT NOT NULL,
        start_date DATETIME,
        end_date DATETIME,
        status ENUM('pending', 'in_progress', 'completed', 'skipped') DEFAULT 'pending',
        responsible_employee_id INT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (production_order_id) REFERENCES production_orders(id) ON DELETE CASCADE
    )");
    
} catch (Exception $e) {
    $error = "خطأ في إعداد قاعدة البيانات: " . $e->getMessage();
}

// جلب إحصائيات الإنتاج
try {
    $productionStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN priority = 'urgent' THEN 1 END) as urgent_orders,
            SUM(estimated_cost) as total_estimated_cost,
            SUM(actual_cost) as total_actual_cost
        FROM production_orders
        WHERE start_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ");
    
    $recentOrders = $db->fetchAll("
        SELECT po.*, i.name as product_name, i.unit as product_unit, i.code as product_code,
               d.name as department_name, e.full_name as supervisor_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN departments d ON po.department_id = d.id
        LEFT JOIN employees e ON po.supervisor_id = e.id
        ORDER BY po.created_at DESC
        LIMIT 10
    ");
    
    // إحصائيات المخزون
    $inventoryStats = $db->fetchOne("
        SELECT 
            COUNT(DISTINCT item_id) as total_items,
            SUM(CASE WHEN quantity > 0 THEN 1 ELSE 0 END) as available_items,
            SUM(CASE WHEN quantity <= reorder_level THEN 1 ELSE 0 END) as low_stock_items
        FROM inventory
    ");
    
} catch (Exception $e) {
    $productionStats = [
        'total_orders' => 0, 'pending_orders' => 0, 'in_progress_orders' => 0,
        'completed_orders' => 0, 'urgent_orders' => 0, 'total_estimated_cost' => 0,
        'total_actual_cost' => 0
    ];
    $recentOrders = [];
    $inventoryStats = ['total_items' => 0, 'available_items' => 0, 'low_stock_items' => 0];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .stats-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #667eea;
        }
        
        .stats-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .stats-number {
            font-size: 3rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 600;
            margin-top: 0.5rem;
        }
        
        .quick-action {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 2rem;
            text-decoration: none;
            display: block;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        
        .quick-action:hover {
            transform: translateY(-5px);
            color: white;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .production-flow {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .flow-step {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .flow-step:hover {
            transform: translateX(-10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .flow-arrow {
            text-align: center;
            color: #667eea;
            font-size: 2rem;
            margin: 1rem 0;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .priority-urgent { color: #dc3545; }
        .priority-high { color: #fd7e14; }
        .priority-normal { color: #28a745; }
        .priority-low { color: #6c757d; }
        
        .floating-action {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .floating-action:hover {
            transform: scale(1.1);
            color: white;
            box-shadow: 0 15px 35px rgba(40, 167, 69, 0.5);
        }
        
        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 0 auto;
        }
        
        .progress-ring circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }
        
        .progress-ring .background {
            stroke: #e9ecef;
        }
        
        .progress-ring .progress {
            stroke: url(#gradient);
            stroke-dasharray: 283;
            stroke-dashoffset: 283;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
            animation: progress 2s ease-in-out forwards;
        }
        
        @keyframes progress {
            to {
                stroke-dashoffset: 70;
            }
        }
    </style>
</head>
<body>
    <div class="main-header animate__animated animate__fadeInDown">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-industry fa-2x me-3"></i>نظام الإنتاج المتطور</h1>
                    <p class="mb-0 fs-5">إدارة شاملة ومتكاملة لعمليات الإنتاج والتصنيع</p>
                </div>
                <div class="col-md-6 text-end">
                    <div class="progress-ring">
                        <svg width="120" height="120">
                            <defs>
                                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#667eea"/>
                                    <stop offset="100%" style="stop-color:#764ba2"/>
                                </linearGradient>
                            </defs>
                            <circle class="background" cx="60" cy="60" r="45"/>
                            <circle class="progress" cx="60" cy="60" r="45"/>
                            <text x="60" y="65" text-anchor="middle" fill="white" font-size="16" font-weight="bold">75%</text>
                            <text x="60" y="80" text-anchor="middle" fill="white" font-size="10">كفاءة الإنتاج</text>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- إحصائيات الإنتاج -->
        <div class="row animate__animated animate__fadeInUp">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $productionStats['total_orders'] ?></div>
                    <div class="stats-label">إجمالي الأوامر</div>
                    <small class="text-muted">(30 يوم)</small>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $productionStats['pending_orders'] ?></div>
                    <div class="stats-label">في الانتظار</div>
                    <i class="fas fa-clock text-warning"></i>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $productionStats['in_progress_orders'] ?></div>
                    <div class="stats-label">قيد التنفيذ</div>
                    <i class="fas fa-cogs text-info"></i>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $productionStats['completed_orders'] ?></div>
                    <div class="stats-label">مكتملة</div>
                    <i class="fas fa-check-circle text-success"></i>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $productionStats['urgent_orders'] ?></div>
                    <div class="stats-label">عاجلة</div>
                    <i class="fas fa-exclamation-triangle text-danger"></i>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card">
                    <div class="stats-number"><?= number_format($productionStats['total_estimated_cost']/1000, 0) ?>K</div>
                    <div class="stats-label">التكلفة المقدرة</div>
                    <small class="text-muted">د.ع</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الإجراءات السريعة -->
            <div class="col-lg-3">
                <div class="animate__animated animate__fadeInLeft">
                    <h4 class="text-white mb-4"><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h4>
                    
                    <a href="add_production_order.php" class="quick-action">
                        <i class="fas fa-plus fa-2x mb-2"></i>
                        <h5>أمر إنتاج جديد</h5>
                        <p class="mb-0">إنشاء أمر إنتاج جديد</p>
                    </a>
                    
                    <a href="production_orders.php" class="quick-action">
                        <i class="fas fa-list fa-2x mb-2"></i>
                        <h5>جميع الأوامر</h5>
                        <p class="mb-0">عرض وإدارة الأوامر</p>
                    </a>
                    
                    <a href="production_materials.php" class="quick-action">
                        <i class="fas fa-boxes fa-2x mb-2"></i>
                        <h5>إدارة المواد</h5>
                        <p class="mb-0">مواد الإنتاج والمخزون</p>
                    </a>
                    
                    <a href="production_reports.php" class="quick-action">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h5>تقارير الإنتاج</h5>
                        <p class="mb-0">تحليلات وإحصائيات</p>
                    </a>
                </div>
            </div>

            <!-- سير العمل -->
            <div class="col-lg-6">
                <div class="production-flow animate__animated animate__fadeInUp">
                    <h4 class="mb-4"><i class="fas fa-project-diagram me-2"></i>سير عملية الإنتاج</h4>
                    
                    <div class="flow-step">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-file-plus fa-2x text-primary"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">1. إنشاء أمر الإنتاج</h6>
                                <small class="text-muted">تحديد المنتج والكمية والمواصفات</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-boxes fa-2x text-warning"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">2. صرف المواد الأولية</h6>
                                <small class="text-muted">سحب المواد المطلوبة من المخزن</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-cogs fa-2x text-info"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">3. عملية التصنيع</h6>
                                <small class="text-muted">تنفيذ مراحل الإنتاج المختلفة</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    
                    <div class="flow-step">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-warehouse fa-2x text-success"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">4. إدخال المنتج النهائي</h6>
                                <small class="text-muted">إضافة المنتج المكتمل للمخزن</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أوامر الإنتاج الحديثة -->
            <div class="col-lg-3">
                <div class="card animate__animated animate__fadeInRight">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>أوامر حديثة</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recentOrders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-industry fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد أوامر إنتاج</p>
                                <a href="add_production_order.php" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus me-2"></i>إضافة أمر
                                </a>
                            </div>
                        <?php else: ?>
                            <?php foreach (array_slice($recentOrders, 0, 5) as $order): ?>
                                <div class="border-bottom pb-2 mb-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1"><?= htmlspecialchars($order['order_number']) ?></h6>
                                            <small class="text-muted"><?= htmlspecialchars($order['product_name']) ?></small>
                                        </div>
                                        <div>
                                            <?php
                                            $statusColors = [
                                                'pending' => 'secondary',
                                                'in_progress' => 'info',
                                                'completed' => 'success',
                                                'cancelled' => 'danger',
                                                'on_hold' => 'warning'
                                            ];
                                            $statusNames = [
                                                'pending' => 'انتظار',
                                                'in_progress' => 'تنفيذ',
                                                'completed' => 'مكتمل',
                                                'cancelled' => 'ملغي',
                                                'on_hold' => 'معلق'
                                            ];
                                            ?>
                                            <span class="badge bg-<?= $statusColors[$order['status']] ?> status-badge">
                                                <?= $statusNames[$order['status']] ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="mt-1">
                                        <small class="priority-<?= $order['priority'] ?>">
                                            <i class="fas fa-flag me-1"></i>
                                            <?php
                                            $priorityNames = ['low' => 'منخفضة', 'normal' => 'عادية', 'high' => 'عالية', 'urgent' => 'عاجلة'];
                                            echo $priorityNames[$order['priority']];
                                            ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            <div class="text-center mt-3">
                                <a href="production_orders.php" class="btn btn-outline-primary btn-sm">
                                    عرض الكل
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إحصائيات المخزون -->
                <div class="card animate__animated animate__fadeInRight" style="animation-delay: 0.2s;">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-warehouse me-2"></i>حالة المخزون</h5>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-4">
                                <h4 class="text-primary"><?= $inventoryStats['total_items'] ?></h4>
                                <small>إجمالي الأصناف</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-success"><?= $inventoryStats['available_items'] ?></h4>
                                <small>متوفر</small>
                            </div>
                            <div class="col-4">
                                <h4 class="text-danger"><?= $inventoryStats['low_stock_items'] ?></h4>
                                <small>مخزون منخفض</small>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="inventory.php" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-boxes me-2"></i>إدارة المخزون
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الإجراء العائم -->
    <a href="add_production_order.php" class="floating-action animate__animated animate__bounceIn" title="أمر إنتاج جديد">
        <i class="fas fa-plus"></i>
    </a>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك الأرقام
            const numbers = document.querySelectorAll('.stats-number');
            numbers.forEach(number => {
                const finalNumber = parseInt(number.textContent);
                let currentNumber = 0;
                const increment = finalNumber / 50;
                
                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        number.textContent = finalNumber;
                        clearInterval(timer);
                    } else {
                        number.textContent = Math.floor(currentNumber);
                    }
                }, 30);
            });
            
            // تأثير hover للبطاقات
            const cards = document.querySelectorAll('.stats-card, .quick-action');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>
