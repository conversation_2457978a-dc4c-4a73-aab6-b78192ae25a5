<?php
session_start();

// تفعيل عرض الأخطاء للتطوير
error_reporting(E_ALL);
ini_set('display_errors', 1);

$success = '';
$error = '';

// التحقق من وجود الملفات المطلوبة
$requiredFiles = [
    '../config/database.php',
    '../config/config.php',
    '../classes/Helper.php'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        $error = "الملف المطلوب غير موجود: $file";
        break;
    }
}

if (!$error) {
    try {
        require_once '../config/database.php';
        require_once '../config/config.php';
        require_once '../classes/Helper.php';
    } catch (Exception $e) {
        $error = "خطأ في تحميل الملفات: " . $e->getMessage();
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && !$error) {
    try {
        // اختبار الاتصال أولاً
        $testConnection = new PDO("mysql:host=localhost;charset=utf8mb4", "root", "", [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);

        // إنشاء قاعدة البيانات إذا لم تكن موجودة
        $testConnection->exec("CREATE DATABASE IF NOT EXISTS `factory_management` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

        // إنشاء كائن Database
        $db = new Database();

        // إنشاء جدول المستخدمين
        $db->query("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'manager', 'accountant', 'warehouse', 'employee') DEFAULT 'employee',
            permissions TEXT,
            is_active TINYINT(1) DEFAULT 1,
            last_login DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الإعدادات
        $db->query("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE,
            setting_value TEXT,
            setting_group VARCHAR(50) DEFAULT 'general',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول العملات
        $db->query("CREATE TABLE IF NOT EXISTS currencies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base TINYINT(1) DEFAULT 0,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إنشاء المستخدمين الافتراضيين
        $users = [
            ['admin', '<EMAIL>', 'admin123', 'مدير النظام', 'admin'],
            ['accountant', '<EMAIL>', 'acc123', 'المحاسب', 'accountant'],
            ['warehouse', '<EMAIL>', 'wh123', 'أمين المخزن', 'warehouse']
        ];

        foreach ($users as $userData) {
            $passwordHash = password_hash($userData[2], PASSWORD_DEFAULT);
            $db->query("INSERT IGNORE INTO users (username, email, password_hash, full_name, role, permissions) VALUES (?, ?, ?, ?, ?, ?)", [
                $userData[0], $userData[1], $passwordHash, $userData[3], $userData[4], json_encode([])
            ]);
        }

        // إنشاء العملات الافتراضية
        $db->query("INSERT IGNORE INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, ?)", [
            'الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1, 1
        ]);
        $db->query("INSERT IGNORE INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, ?)", [
            'الدولار الأمريكي', '$', 'USD', 0.00068, 0, 1
        ]);

        $success = 'تم إعداد قاعدة البيانات بنجاح! يمكنك الآن تسجيل الدخول.';
    } catch (Exception $e) {
        $error = 'خطأ في إعداد قاعدة البيانات: ' . $e->getMessage();
    }
}

// فحص حالة قاعدة البيانات
$dbReady = false;
$dbStatus = [];

if (!$error) {
    try {
        $db = new Database();

        // فحص وجود الجداول
        $tables = ['users', 'settings', 'currencies'];
        foreach ($tables as $table) {
            try {
                $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
                $dbStatus[$table] = !empty($result);
            } catch (Exception $e) {
                $dbStatus[$table] = false;
            }
        }

        // فحص وجود مستخدمين
        if ($dbStatus['users']) {
            $userCount = $db->fetchOne("SELECT COUNT(*) as count FROM users");
            $dbReady = $userCount['count'] > 0;
        }

    } catch (Exception $e) {
        $dbReady = false;
        $dbStatus = ['users' => false, 'settings' => false, 'currencies' => false];
    }
} else {
    $dbStatus = ['users' => false, 'settings' => false, 'currencies' => false];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد سريع - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 30px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="setup-card">
        <div class="setup-header">
            <h2><i class="fas fa-cogs fa-2x mb-3"></i><br>إعداد سريع</h2>
            <p class="mb-0">نظام إدارة المعامل</p>
        </div>

        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                <div class="text-center">
                    <a href="login.php" class="btn btn-success btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            <?php elseif ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
                <div class="text-center">
                    <a href="db_test.php" class="btn btn-info me-2">
                        <i class="fas fa-database me-2"></i>اختبار قاعدة البيانات
                    </a>
                    <button onclick="location.reload()" class="btn btn-warning">
                        <i class="fas fa-redo me-2"></i>إعادة المحاولة
                    </button>
                </div>
            <?php elseif ($dbReady): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    قاعدة البيانات مُعدة بالفعل. يمكنك تسجيل الدخول مباشرة.
                </div>
                <div class="text-center">
                    <a href="login.php" class="btn btn-success btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
            <?php else: ?>
                <div class="text-center mb-4">
                    <p>اضغط على الزر أدناه لإعداد قاعدة البيانات والبدء في استخدام النظام:</p>
                </div>

                <form method="POST">
                    <button type="submit" class="btn btn-setup btn-lg">
                        <i class="fas fa-play me-2"></i>بدء الإعداد
                    </button>
                </form>

                <div class="mt-4">
                    <h6>المستخدمين الافتراضيين:</h6>
                    <ul class="list-unstyled">
                        <li><strong>admin</strong> / admin123 (مدير النظام)</li>
                        <li><strong>accountant</strong> / acc123 (محاسب)</li>
                        <li><strong>warehouse</strong> / wh123 (أمين مخزن)</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
