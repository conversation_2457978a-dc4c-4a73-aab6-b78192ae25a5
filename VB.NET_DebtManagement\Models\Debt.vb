Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج الدين - Debt Model
''' </summary>
<Table("Debts")>
Public Class Debt
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف الدين الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' نوع الطرف (عميل/مورد)
    ''' </summary>
    <Required(ErrorMessage:="نوع الطرف مطلوب")>
    <StringLength(10, ErrorMessage:="نوع الطرف يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property EntityType As String ' Customer, Supplier
    
    ''' <summary>
    ''' معرف الطرف (العميل أو المورد)
    ''' </summary>
    <Required(ErrorMessage:="معرف الطرف مطلوب")>
    Public Property EntityId As Integer
    
    ''' <summary>
    ''' نوع الدين (مدين/دائن)
    ''' </summary>
    <Required(ErrorMessage:="نوع الدين مطلوب")>
    <StringLength(10, ErrorMessage:="نوع الدين يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property DebtType As String ' Debit, Credit
    
    ''' <summary>
    ''' المبلغ الأصلي للدين
    ''' </summary>
    <Required(ErrorMessage:="مبلغ الدين مطلوب")>
    <Range(0.01, Double.MaxValue, ErrorMessage:="مبلغ الدين يجب أن يكون أكبر من صفر")>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property OriginalAmount As Decimal
    
    ''' <summary>
    ''' المبلغ المتبقي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property RemainingAmount As Decimal
    
    ''' <summary>
    ''' المبلغ المدفوع
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property PaidAmount As Decimal = 0
    
    ''' <summary>
    ''' العملة
    ''' </summary>
    <Required(ErrorMessage:="العملة مطلوبة")>
    <StringLength(3, ErrorMessage:="رمز العملة يجب أن يكون 3 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Currency As String ' IQD, USD
    
    ''' <summary>
    ''' سعر الصرف وقت إنشاء الدين
    ''' </summary>
    <Column(TypeName:="DECIMAL(10,4)")>
    Public Property ExchangeRate As Decimal = 1
    
    ''' <summary>
    ''' تاريخ استحقاق الدين
    ''' </summary>
    <Column(TypeName:="DATE")>
    Public Property DueDate As Date?
    
    ''' <summary>
    ''' وصف الدين
    ''' </summary>
    <StringLength(500, ErrorMessage:="وصف الدين يجب أن يكون أقل من 500 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Description As String
    
    ''' <summary>
    ''' رقم المرجع
    ''' </summary>
    <StringLength(50, ErrorMessage:="رقم المرجع يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property ReferenceNumber As String
    
    ''' <summary>
    ''' حالة الدين
    ''' </summary>
    <StringLength(20, ErrorMessage:="حالة الدين يجب أن تكون أقل من 20 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Status As String = "Active" ' Active, Paid, Overdue, Cancelled
    
    ''' <summary>
    ''' أولوية الدين
    ''' </summary>
    <StringLength(10, ErrorMessage:="أولوية الدين يجب أن تكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Priority As String = "Normal" ' High, Normal, Low
    
    ''' <summary>
    ''' ملاحظات إضافية
    ''' </summary>
    <StringLength(1000, ErrorMessage:="الملاحظات يجب أن تكون أقل من 1000 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Notes As String
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
    ''' <summary>
    ''' معرف المستخدم الذي أنشأ السجل
    ''' </summary>
    Public Property CreatedBy As Integer?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' المستخدم الذي أنشأ السجل
    ''' </summary>
    <ForeignKey("CreatedBy")>
    Public Overridable Property Creator As User
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' اسم الطرف (محسوب)
    ''' </summary>
    <NotMapped>
    Public Property EntityName As String
    
    ''' <summary>
    ''' نوع الطرف بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property EntityTypeInArabic As String
        Get
            Select Case EntityType.ToLower()
                Case "customer"
                    Return "عميل"
                Case "supplier"
                    Return "مورد"
                Case Else
                    Return EntityType
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' نوع الدين بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property DebtTypeInArabic As String
        Get
            Select Case DebtType.ToLower()
                Case "debit"
                    Return "مدين"
                Case "credit"
                    Return "دائن"
                Case Else
                    Return DebtType
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة الدين بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property StatusInArabic As String
        Get
            Select Case Status.ToLower()
                Case "active"
                    Return "نشط"
                Case "paid"
                    Return "مدفوع"
                Case "overdue"
                    Return "متأخر"
                Case "cancelled"
                    Return "ملغي"
                Case Else
                    Return Status
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' أولوية الدين بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property PriorityInArabic As String
        Get
            Select Case Priority.ToLower()
                Case "high"
                    Return "عالية"
                Case "normal"
                    Return "عادية"
                Case "low"
                    Return "منخفضة"
                Case Else
                    Return Priority
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' نسبة المدفوع (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property PaymentPercentage As Decimal
        Get
            If OriginalAmount > 0 Then
                Return (PaidAmount / OriginalAmount) * 100
            Else
                Return 0
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' هل الدين مستحق (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property IsOverdue As Boolean
        Get
            Return DueDate.HasValue AndAlso DueDate.Value < Date.Today AndAlso RemainingAmount > 0
        End Get
    End Property
    
    ''' <summary>
    ''' عدد الأيام المتبقية للاستحقاق (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property DaysUntilDue As Integer?
        Get
            If DueDate.HasValue Then
                Return (DueDate.Value - Date.Today).Days
            Else
                Return Nothing
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' هل الدين مكتمل الدفع (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property IsFullyPaid As Boolean
        Get
            Return RemainingAmount <= 0
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تسجيل دفعة
    ''' </summary>
    ''' <param name="amount">مبلغ الدفعة</param>
    Public Sub RecordPayment(amount As Decimal)
        If amount > 0 AndAlso amount <= RemainingAmount Then
            PaidAmount += amount
            RemainingAmount -= amount
            UpdatedAt = DateTime.Now
            
            If RemainingAmount <= 0 Then
                Status = "Paid"
            End If
        End If
    End Sub
    
    ''' <summary>
    ''' إلغاء دفعة
    ''' </summary>
    ''' <param name="amount">مبلغ الدفعة المراد إلغاؤها</param>
    Public Sub CancelPayment(amount As Decimal)
        If amount > 0 AndAlso amount <= PaidAmount Then
            PaidAmount -= amount
            RemainingAmount += amount
            UpdatedAt = DateTime.Now
            
            If Status = "Paid" AndAlso RemainingAmount > 0 Then
                Status = "Active"
            End If
        End If
    End Sub
    
    ''' <summary>
    ''' تحديث حالة الدين
    ''' </summary>
    Public Sub UpdateStatus()
        If RemainingAmount <= 0 Then
            Status = "Paid"
        ElseIf IsOverdue Then
            Status = "Overdue"
        Else
            Status = "Active"
        End If
        
        UpdatedAt = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' تنسيق المبلغ الأصلي للعرض
    ''' </summary>
    ''' <returns>المبلغ الأصلي منسق</returns>
    Public Function GetFormattedOriginalAmount() As String
        If Currency.ToUpper() = "IQD" Then
            Return String.Format("{0:N0} د.ع", OriginalAmount)
        ElseIf Currency.ToUpper() = "USD" Then
            Return String.Format("${0:N2}", OriginalAmount)
        Else
            Return OriginalAmount.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' تنسيق المبلغ المتبقي للعرض
    ''' </summary>
    ''' <returns>المبلغ المتبقي منسق</returns>
    Public Function GetFormattedRemainingAmount() As String
        If Currency.ToUpper() = "IQD" Then
            Return String.Format("{0:N0} د.ع", RemainingAmount)
        ElseIf Currency.ToUpper() = "USD" Then
            Return String.Format("${0:N2}", RemainingAmount)
        Else
            Return RemainingAmount.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' تنسيق المبلغ المدفوع للعرض
    ''' </summary>
    ''' <returns>المبلغ المدفوع منسق</returns>
    Public Function GetFormattedPaidAmount() As String
        If Currency.ToUpper() = "IQD" Then
            Return String.Format("{0:N0} د.ع", PaidAmount)
        ElseIf Currency.ToUpper() = "USD" Then
            Return String.Format("${0:N2}", PaidAmount)
        Else
            Return PaidAmount.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(EntityType) Then
            errors.Add("نوع الطرف مطلوب")
        ElseIf Not {"Customer", "Supplier"}.Contains(EntityType) Then
            errors.Add("نوع الطرف غير صحيح")
        End If
        
        If EntityId <= 0 Then
            errors.Add("معرف الطرف غير صحيح")
        End If
        
        If String.IsNullOrWhiteSpace(DebtType) Then
            errors.Add("نوع الدين مطلوب")
        ElseIf Not {"Debit", "Credit"}.Contains(DebtType) Then
            errors.Add("نوع الدين غير صحيح")
        End If
        
        If OriginalAmount <= 0 Then
            errors.Add("مبلغ الدين يجب أن يكون أكبر من صفر")
        End If
        
        If PaidAmount < 0 Then
            errors.Add("المبلغ المدفوع لا يمكن أن يكون سالب")
        End If
        
        If PaidAmount > OriginalAmount Then
            errors.Add("المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الأصلي")
        End If
        
        If String.IsNullOrWhiteSpace(Currency) Then
            errors.Add("العملة مطلوبة")
        ElseIf Not {"IQD", "USD"}.Contains(Currency.ToUpper()) Then
            errors.Add("العملة غير مدعومة")
        End If
        
        If ExchangeRate <= 0 Then
            errors.Add("سعر الصرف يجب أن يكون أكبر من صفر")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للدين
    ''' </summary>
    ''' <returns>وصف الدين</returns>
    Public Overrides Function ToString() As String
        Return String.Format("{0} - {1} - {2}", EntityTypeInArabic, DebtTypeInArabic, GetFormattedOriginalAmount())
    End Function
    
#End Region

End Class
