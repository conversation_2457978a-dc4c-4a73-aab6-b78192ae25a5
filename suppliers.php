<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $validation_rules = [
        'name' => ['required' => true, 'message' => 'اسم المورد مطلوب'],
        'phone' => ['type' => 'phone'],
        'email' => ['type' => 'email'],
        'initial_balance_iqd' => ['type' => 'numeric'],
        'initial_balance_usd' => ['type' => 'numeric']
    ];
    
    $errors = validateInput($_POST, $validation_rules);
    
    if (empty($errors)) {
        $supplier_data = [
            'name' => trim($_POST['name']),
            'phone' => trim($_POST['phone']) ?: null,
            'email' => trim($_POST['email']) ?: null,
            'address' => trim($_POST['address']) ?: null,
            'initial_balance_iqd' => floatval($_POST['initial_balance_iqd'] ?? 0),
            'initial_balance_usd' => floatval($_POST['initial_balance_usd'] ?? 0),
            'notes' => trim($_POST['notes']) ?: null
        ];
        
        $supplier_id = addSupplier($supplier_data);
        
        if ($supplier_id) {
            $message = 'تم إضافة المورد بنجاح';
            $message_type = 'success';
            logActivity('إضافة مورد', "تم إضافة المورد: {$supplier_data['name']}");
        } else {
            $message = 'حدث خطأ أثناء إضافة المورد';
            $message_type = 'danger';
        }
    } else {
        $message = 'يرجى تصحيح الأخطاء في النموذج';
        $message_type = 'danger';
    }
}

// معالجة حذف المورد
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $supplier_id = intval($_GET['delete']);
    
    try {
        // التحقق من وجود معاملات للمورد
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM transactions WHERE entity_type = 'supplier' AND entity_id = ?");
        $stmt->execute([$supplier_id]);
        $transaction_count = $stmt->fetch()['count'];
        
        if ($transaction_count > 0) {
            $message = 'لا يمكن حذف المورد لوجود معاملات مرتبطة به';
            $message_type = 'warning';
        } else {
            // حذف المورد
            $stmt = $pdo->prepare("DELETE FROM suppliers WHERE id = ?");
            if ($stmt->execute([$supplier_id])) {
                // حذف الديون المرتبطة
                $stmt = $pdo->prepare("DELETE FROM debts WHERE entity_type = 'supplier' AND entity_id = ?");
                $stmt->execute([$supplier_id]);
                
                $message = 'تم حذف المورد بنجاح';
                $message_type = 'success';
                logActivity('حذف مورد', "تم حذف المورد رقم: {$supplier_id}");
            } else {
                $message = 'حدث خطأ أثناء حذف المورد';
                $message_type = 'danger';
            }
        }
    } catch (PDOException $e) {
        $message = 'حدث خطأ في النظام';
        $message_type = 'danger';
        error_log("خطأ حذف المورد: " . $e->getMessage());
    }
}

// جلب قائمة الموردين
try {
    $stmt = $pdo->query("
        SELECT s.*, 
               (SELECT COUNT(*) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id) as transaction_count
        FROM suppliers s 
        WHERE s.is_active = 1 
        ORDER BY s.name ASC
    ");
    $suppliers = $stmt->fetchAll();
} catch (PDOException $e) {
    $suppliers = [];
    error_log("خطأ جلب الموردين: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموردين - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calculator me-2"></i>
                نظام إدارة الديون
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الموردين</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                            <i class="fas fa-plus me-1"></i>
                            إضافة مورد جديد
                        </button>
                    </div>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- جدول الموردين -->
                <div class="card shadow">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="m-0 font-weight-bold text-primary">قائمة الموردين</h6>
                            </div>
                            <div class="col-auto">
                                <input type="text" class="form-control table-search" placeholder="البحث..." data-table="#suppliersTable">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="suppliersTable">
                                <thead>
                                    <tr>
                                        <th class="sortable">الاسم</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th class="sortable">الرصيد (دينار)</th>
                                        <th class="sortable">الرصيد (دولار)</th>
                                        <th>عدد المعاملات</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($suppliers as $supplier): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($supplier['name']); ?></strong>
                                            <?php if ($supplier['notes']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($supplier['notes']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($supplier['phone'] ?: '-'); ?></td>
                                        <td><?php echo htmlspecialchars($supplier['email'] ?: '-'); ?></td>
                                        <td>
                                            <span class="<?php echo $supplier['current_balance_iqd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo formatCurrency($supplier['current_balance_iqd'], 'IQD'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $supplier['current_balance_usd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo formatCurrency($supplier['current_balance_usd'], 'USD'); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $supplier['transaction_count']; ?></span>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($supplier['created_at'])); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="supplier_details.php?id=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_supplier.php?id=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($supplier['transaction_count'] == 0): ?>
                                                <a href="?delete=<?php echo $supplier['id']; ?>" class="btn btn-sm btn-danger btn-delete" title="حذف" data-name="<?php echo htmlspecialchars($supplier['name']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- مودال إضافة مورد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المورد *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback">اسم المورد مطلوب</div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <input type="text" class="form-control" id="address" name="address">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="initial_balance_iqd" class="form-label">الرصيد الافتتاحي (دينار)</label>
                                <input type="number" class="form-control" id="initial_balance_iqd" name="initial_balance_iqd" value="0" step="0.01">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="initial_balance_usd" class="form-label">الرصيد الافتتاحي (دولار)</label>
                                <input type="number" class="form-control" id="initial_balance_usd" name="initial_balance_usd" value="0" step="0.01">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ المورد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
