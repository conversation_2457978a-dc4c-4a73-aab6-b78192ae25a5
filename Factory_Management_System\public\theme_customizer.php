<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/SystemSettings.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$settings = new SystemSettings();

$success = '';
$error = '';

// معالجة حفظ إعدادات المظهر
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_theme'])) {
    try {
        $settings->set('theme_color', $_POST['theme_color']);
        $settings->set('secondary_color', $_POST['secondary_color']);
        $settings->set('sidebar_color', $_POST['sidebar_color']);
        $settings->set('enable_dark_mode', isset($_POST['enable_dark_mode']) ? '1' : '0');
        
        $success = "تم حفظ إعدادات المظهر بنجاح";
        
    } catch (Exception $e) {
        $error = "خطأ في حفظ إعدادات المظهر: " . $e->getMessage();
    }
}

// الحصول على الإعدادات الحالية
$currentTheme = $settings->get('theme_color', '#6f42c1');
$currentSecondary = $settings->get('secondary_color', '#e83e8c');
$currentSidebar = $settings->get('sidebar_color', '#343a40');
$darkMode = $settings->isFeatureEnabled('enable_dark_mode');

// قوالب الألوان المحددة مسبقاً
$colorThemes = [
    'default' => ['primary' => '#6f42c1', 'secondary' => '#e83e8c', 'name' => 'البنفسجي الافتراضي'],
    'blue' => ['primary' => '#007bff', 'secondary' => '#17a2b8', 'name' => 'الأزرق الكلاسيكي'],
    'green' => ['primary' => '#28a745', 'secondary' => '#20c997', 'name' => 'الأخضر الطبيعي'],
    'red' => ['primary' => '#dc3545', 'secondary' => '#fd7e14', 'name' => 'الأحمر القوي'],
    'orange' => ['primary' => '#fd7e14', 'secondary' => '#ffc107', 'name' => 'البرتقالي المشرق'],
    'purple' => ['primary' => '#6610f2', 'secondary' => '#e83e8c', 'name' => 'البنفسجي الملكي'],
    'teal' => ['primary' => '#20c997', 'secondary' => '#17a2b8', 'name' => 'الفيروزي العصري'],
    'dark' => ['primary' => '#343a40', 'secondary' => '#6c757d', 'name' => 'الرمادي الداكن']
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تخصيص المظهر - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        <?= $settings->getThemeCSS() ?>
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .main-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .theme-preview {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .theme-preview:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .theme-preview.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }
        
        .color-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: inline-block;
            margin: 5px;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .preview-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .preview-header {
            padding: 10px 15px;
            border-radius: 8px;
            color: white;
            margin-bottom: 10px;
        }
        
        .preview-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            color: white;
            margin: 5px;
        }
        
        .live-preview {
            position: sticky;
            top: 20px;
        }
        
        .color-input-group {
            position: relative;
        }
        
        .color-preview-circle {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        
        .form-control[type="color"] {
            padding-left: 50px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-header">
            <h1><i class="fas fa-palette fa-2x mb-3"></i><br>تخصيص المظهر</h1>
            <p class="mb-0">اختر الألوان والمظهر الذي يناسب معملك</p>
        </div>

        <div class="container-fluid p-4">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- قوالب الألوان المحددة مسبقاً -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-swatchbook me-2"></i>قوالب الألوان</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($colorThemes as $key => $theme): ?>
                                    <div class="col-md-6 col-lg-4">
                                        <div class="theme-preview" onclick="applyTheme('<?= $theme['primary'] ?>', '<?= $theme['secondary'] ?>')">
                                            <div class="text-center">
                                                <div class="color-circle" style="background: <?= $theme['primary'] ?>"></div>
                                                <div class="color-circle" style="background: <?= $theme['secondary'] ?>"></div>
                                            </div>
                                            <h6 class="text-center mt-2"><?= $theme['name'] ?></h6>
                                            <div class="preview-header" style="background: linear-gradient(135deg, <?= $theme['primary'] ?> 0%, <?= $theme['secondary'] ?> 100%)">
                                                عنوان تجريبي
                                            </div>
                                            <div class="text-center">
                                                <button class="preview-button" style="background: <?= $theme['primary'] ?>">زر أساسي</button>
                                                <button class="preview-button" style="background: <?= $theme['secondary'] ?>">زر ثانوي</button>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- تخصيص الألوان يدوياً -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-paint-brush me-2"></i>تخصيص الألوان يدوياً</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" id="themeForm">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">اللون الأساسي</label>
                                        <div class="color-input-group">
                                            <input type="color" name="theme_color" id="theme_color" 
                                                   class="form-control" value="<?= $currentTheme ?>">
                                            <div class="color-preview-circle" id="theme_preview" 
                                                 style="background-color: <?= $currentTheme ?>"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">اللون الثانوي</label>
                                        <div class="color-input-group">
                                            <input type="color" name="secondary_color" id="secondary_color" 
                                                   class="form-control" value="<?= $currentSecondary ?>">
                                            <div class="color-preview-circle" id="secondary_preview" 
                                                 style="background-color: <?= $currentSecondary ?>"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">لون الشريط الجانبي</label>
                                        <div class="color-input-group">
                                            <input type="color" name="sidebar_color" id="sidebar_color" 
                                                   class="form-control" value="<?= $currentSidebar ?>">
                                            <div class="color-preview-circle" id="sidebar_preview" 
                                                 style="background-color: <?= $currentSidebar ?>"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            <input type="checkbox" name="enable_dark_mode" 
                                                   class="form-check-input" <?= $darkMode ? 'checked' : '' ?>>
                                            <label class="form-check-label">تفعيل الوضع الليلي</label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <button type="submit" name="save_theme" class="btn btn-primary btn-lg me-3">
                                        <i class="fas fa-save me-2"></i>حفظ المظهر
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-lg" onclick="resetToDefault()">
                                        <i class="fas fa-undo me-2"></i>إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- المعاينة المباشرة -->
                <div class="col-lg-4">
                    <div class="live-preview">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-eye me-2"></i>المعاينة المباشرة</h5>
                            </div>
                            <div class="card-body">
                                <div id="livePreview">
                                    <div class="preview-card">
                                        <div class="preview-header" id="previewHeader" 
                                             style="background: linear-gradient(135deg, <?= $currentTheme ?> 0%, <?= $currentSecondary ?> 100%)">
                                            <i class="fas fa-home me-2"></i>لوحة التحكم
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6>الأزرار:</h6>
                                            <button class="preview-button" id="previewBtn1" 
                                                    style="background: <?= $currentTheme ?>">زر أساسي</button>
                                            <button class="preview-button" id="previewBtn2" 
                                                    style="background: <?= $currentSecondary ?>">زر ثانوي</button>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <h6>الشريط الجانبي:</h6>
                                            <div style="background: <?= $currentSidebar ?>; color: white; padding: 10px; border-radius: 5px;" 
                                                 id="previewSidebar">
                                                <i class="fas fa-users me-2"></i>الموظفين
                                                <br>
                                                <i class="fas fa-shopping-cart me-2"></i>المبيعات
                                            </div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            هذه معاينة لكيفية ظهور الألوان في النظام
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-link me-2"></i>روابط سريعة</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="system_setup.php" class="btn btn-outline-primary">
                                        <i class="fas fa-cogs me-2"></i>إعدادات النظام
                                    </a>
                                    <a href="backup_manager.php" class="btn btn-outline-success">
                                        <i class="fas fa-database me-2"></i>النسخ الاحتياطي
                                    </a>
                                    <a href="dashboard.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-home me-2"></i>لوحة التحكم
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تطبيق قالب ألوان محدد
        function applyTheme(primary, secondary) {
            document.getElementById('theme_color').value = primary;
            document.getElementById('secondary_color').value = secondary;
            updatePreview();
        }
        
        // إعادة تعيين إلى الافتراضي
        function resetToDefault() {
            document.getElementById('theme_color').value = '#6f42c1';
            document.getElementById('secondary_color').value = '#e83e8c';
            document.getElementById('sidebar_color').value = '#343a40';
            updatePreview();
        }
        
        // تحديث المعاينة المباشرة
        function updatePreview() {
            const primary = document.getElementById('theme_color').value;
            const secondary = document.getElementById('secondary_color').value;
            const sidebar = document.getElementById('sidebar_color').value;
            
            // تحديث معاينة الألوان
            document.getElementById('theme_preview').style.backgroundColor = primary;
            document.getElementById('secondary_preview').style.backgroundColor = secondary;
            document.getElementById('sidebar_preview').style.backgroundColor = sidebar;
            
            // تحديث المعاينة المباشرة
            document.getElementById('previewHeader').style.background = `linear-gradient(135deg, ${primary} 0%, ${secondary} 100%)`;
            document.getElementById('previewBtn1').style.backgroundColor = primary;
            document.getElementById('previewBtn2').style.backgroundColor = secondary;
            document.getElementById('previewSidebar').style.backgroundColor = sidebar;
        }
        
        // مراقبة تغيير الألوان
        document.getElementById('theme_color').addEventListener('input', updatePreview);
        document.getElementById('secondary_color').addEventListener('input', updatePreview);
        document.getElementById('sidebar_color').addEventListener('input', updatePreview);
        
        // تحديد القالب النشط
        document.addEventListener('DOMContentLoaded', function() {
            const currentPrimary = document.getElementById('theme_color').value;
            const themes = document.querySelectorAll('.theme-preview');
            
            themes.forEach(theme => {
                theme.addEventListener('click', function() {
                    themes.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
