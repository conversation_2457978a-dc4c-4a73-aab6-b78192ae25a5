# 🚀 تشغيل نظام إدارة الديون

## خطوات التشغيل السريع

### 1. تشغيل الخادم المحلي
```bash
php -S localhost:8000
```

### 2. إعد<PERSON> النظام لأول مرة
افتح المتصفح واذهب إلى:
```
http://localhost:8000/setup.php
```

### 3. تسجيل الدخول
بعد إتمام الإعداد، اذهب إلى:
```
http://localhost:8000/login.php
```

**بيانات تسجيل الدخول الافتراضية:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### 4. الصفحة الرئيسية
```
http://localhost:8000/index.php
```

## 📋 الميزات المتاحة

### ✅ تم تطويرها
- ✅ نظام تسجيل الدخول الآمن
- ✅ لوحة التحكم الرئيسية
- ✅ إدارة العملاء (إضافة، عرض، حذف)
- ✅ إدارة الموردين (إضافة، عرض، حذف)
- ✅ إدارة المعاملات المالية
- ✅ إدارة الصناديق المتعددة
- ✅ إدارة الديون مع الإحصائيات
- ✅ التقارير المفصلة (معاملات، عملاء، موردين، صناديق)
- ✅ دعم العملتين (دينار عراقي ودولار أمريكي)
- ✅ واجهة عصرية متجاوبة
- ✅ نظام البحث والفلترة
- ✅ تصدير التقارير

### 🔄 قيد التطوير
- 🔄 تفاصيل العملاء والموردين
- 🔄 تعديل البيانات
- 🔄 النسخ الاحتياطي
- 🔄 إعدادات النظام
- 🔄 صلاحيات المستخدمين

## 🗂️ هيكل النظام

```
نظام إدارة الديون/
├── 📁 config/          # إعدادات قاعدة البيانات
├── 📁 includes/        # الوظائف الأساسية
├── 📁 assets/          # ملفات CSS و JavaScript
├── 📁 logs/            # ملفات السجلات
├── 📄 index.php        # الصفحة الرئيسية
├── 📄 login.php        # تسجيل الدخول
├── 📄 customers.php    # إدارة العملاء
├── 📄 suppliers.php    # إدارة الموردين
├── 📄 transactions.php # المعاملات المالية
├── 📄 cash_boxes.php   # إدارة الصناديق
├── 📄 debts.php        # إدارة الديون
├── 📄 reports.php      # التقارير
└── 📄 setup.php        # إعداد النظام
```

## 🎯 الاستخدام

### إضافة عميل جديد
1. اذهب إلى "إدارة العملاء"
2. انقر "إضافة عميل جديد"
3. املأ البيانات المطلوبة
4. احفظ

### تسجيل معاملة مالية
1. اذهب إلى "المعاملات المالية"
2. انقر "إضافة معاملة جديدة"
3. اختر نوع المعاملة (وارد/صادر)
4. حدد الطرف والصندوق
5. أدخل المبلغ والتفاصيل
6. احفظ

### عرض التقارير
1. اذهب إلى "التقارير"
2. اختر نوع التقرير
3. حدد الفترة الزمنية
4. اعرض أو صدّر التقرير

## 🔧 المتطلبات التقنية

- PHP 7.4+
- MySQL 5.7+
- متصفح حديث

## 🆘 المساعدة

إذا واجهت أي مشاكل:
1. تأكد من تشغيل PHP و MySQL
2. تحقق من إعدادات قاعدة البيانات في `config/database.php`
3. راجع ملفات السجلات في مجلد `logs/`

## 📞 الدعم

للدعم التقني أو الاستفسارات، يرجى مراجعة ملف README.md للتفاصيل الكاملة.

---
**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان العاليين.**
