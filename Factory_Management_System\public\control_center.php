<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز التحكم - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .control-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .control-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .control-body {
            padding: 40px;
        }
        .control-card {
            border-radius: 15px;
            border: none;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .control-card:hover {
            transform: translateY(-5px);
        }
        .control-card .card-header {
            border-radius: 15px 15px 0 0;
            padding: 20px;
            font-weight: 600;
        }
        .control-card .card-body {
            padding: 20px;
        }
        .btn-control {
            border-radius: 10px;
            padding: 12px 20px;
            font-weight: 600;
            margin: 5px;
            width: 100%;
        }
        .setup-card { border-left: 5px solid #28a745; }
        .setup-card .card-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; }
        
        .diagnosis-card { border-left: 5px solid #17a2b8; }
        .diagnosis-card .card-header { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); color: white; }
        
        .fix-card { border-left: 5px solid #dc3545; }
        .fix-card .card-header { background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%); color: white; }
        
        .system-card { border-left: 5px solid #6f42c1; }
        .system-card .card-header { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); color: white; }
    </style>
</head>
<body>
    <div class="control-container">
        <div class="control-header">
            <h1><i class="fas fa-cogs fa-2x mb-3"></i><br>مركز التحكم الرئيسي</h1>
            <p class="mb-0">إدارة وإعداد جميع مكونات النظام</p>
        </div>
        
        <div class="control-body">
            <div class="row">
                <!-- أدوات الإعداد -->
                <div class="col-lg-6 mb-4">
                    <div class="card control-card setup-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-rocket me-2"></i>أدوات الإعداد</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">إعداد وتهيئة أنظمة المعمل</p>
                            <div class="d-grid gap-2">
                                <a href="complete_setup.php" class="btn btn-success btn-control">
                                    <i class="fas fa-rocket me-2"></i>الإعداد الكامل للنظام
                                </a>
                                <a href="setup_employees.php" class="btn btn-outline-success btn-control">
                                    <i class="fas fa-users me-2"></i>إعداد نظام الموظفين
                                </a>
                                <a href="setup_inventory.php" class="btn btn-outline-success btn-control">
                                    <i class="fas fa-warehouse me-2"></i>إعداد نظام المخزون
                                </a>
                                <a href="setup_sales.php" class="btn btn-outline-success btn-control">
                                    <i class="fas fa-shopping-cart me-2"></i>إعداد نظام المبيعات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات التشخيص -->
                <div class="col-lg-6 mb-4">
                    <div class="card control-card diagnosis-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-stethoscope me-2"></i>أدوات التشخيص</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">فحص وتشخيص حالة النظام</p>
                            <div class="d-grid gap-2">
                                <a href="system_diagnosis.php" class="btn btn-info btn-control">
                                    <i class="fas fa-stethoscope me-2"></i>تشخيص شامل للنظام
                                </a>
                                <a href="quick_diagnosis.php" class="btn btn-outline-info btn-control">
                                    <i class="fas fa-search me-2"></i>تشخيص سريع
                                </a>
                                <a href="database_check.php" class="btn btn-outline-info btn-control">
                                    <i class="fas fa-database me-2"></i>فحص قاعدة البيانات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات الإصلاح -->
                <div class="col-lg-6 mb-4">
                    <div class="card control-card fix-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>أدوات الإصلاح</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">إصلاح المشاكل والأخطاء</p>
                            <div class="d-grid gap-2">
                                <a href="fix_sales_constraints.php" class="btn btn-danger btn-control">
                                    <i class="fas fa-tools me-2"></i>إصلاح مشاكل المبيعات
                                </a>
                                <a href="create_missing_files.php" class="btn btn-outline-danger btn-control">
                                    <i class="fas fa-file-medical me-2"></i>إنشاء الملفات المفقودة
                                </a>
                                <a href="emergency_setup.php" class="btn btn-outline-danger btn-control">
                                    <i class="fas fa-first-aid me-2"></i>الإعداد الطارئ
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدارة النظام -->
                <div class="col-lg-6 mb-4">
                    <div class="card control-card system-card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>إدارة النظام</h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">إدارة وتكوين النظام</p>
                            <div class="d-grid gap-2">
                                <a href="dashboard.php" class="btn btn-primary btn-control">
                                    <i class="fas fa-home me-2"></i>لوحة التحكم الرئيسية
                                </a>
                                <a href="settings.php" class="btn btn-outline-primary btn-control">
                                    <i class="fas fa-cog me-2"></i>إعدادات النظام
                                </a>
                                <a href="backup.php" class="btn btn-outline-primary btn-control">
                                    <i class="fas fa-download me-2"></i>نسخ احتياطي
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الأنظمة الفرعية -->
            <div class="card control-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-th-large me-2"></i>الأنظمة الفرعية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="employees.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-users me-2"></i>الموظفين
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="attendance.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-clock me-2"></i>الحضور
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="payroll.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-money-check-alt me-2"></i>الرواتب
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="expenses.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-money-bill-wave me-2"></i>المصاريف
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="inventory.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-warehouse me-2"></i>المخزون
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="sales.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-shopping-cart me-2"></i>المبيعات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="customers.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-users me-2"></i>العملاء
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="production.php" class="btn btn-outline-secondary btn-control">
                                <i class="fas fa-industry me-2"></i>الإنتاج
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="alert alert-info mt-4">
                <h5><i class="fas fa-info-circle me-2"></i>معلومات مهمة</h5>
                <ul class="mb-0">
                    <li><strong>ابدأ بالإعداد الكامل:</strong> إذا كان النظام جديداً، استخدم "الإعداد الكامل للنظام"</li>
                    <li><strong>تشخيص المشاكل:</strong> استخدم "تشخيص شامل للنظام" لفحص حالة جميع المكونات</li>
                    <li><strong>إصلاح الأخطاء:</strong> استخدم أدوات الإصلاح المناسبة حسب نوع المشكلة</li>
                    <li><strong>النسخ الاحتياطي:</strong> قم بعمل نسخة احتياطية بانتظام</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
