<?php
session_start();
require_once '../config/database.php';

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول المستخدمين
        $db->query("CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(200) NOT NULL,
            role ENUM('admin', 'manager', 'employee', 'accountant') DEFAULT 'employee',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول العملات
        $db->query("CREATE TABLE IF NOT EXISTS currencies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الأقسام
        $db->query("CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول العملاء
        $db->query("CREATE TABLE IF NOT EXISTS customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الموردين
        $db->query("CREATE TABLE IF NOT EXISTS suppliers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'company',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول فئات الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول المخازن
        $db->query("CREATE TABLE IF NOT EXISTS warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول فواتير المبيعات
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول فواتير المشتريات
        $db->query("CREATE TABLE IF NOT EXISTS purchase_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            supplier_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'received', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إدراج البيانات الأساسية
        
        // المستخدم الافتراضي
        $userExists = $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
        if ($userExists['count'] == 0) {
            $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
            $userData = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => $hashedPassword,
                'full_name' => 'مدير النظام',
                'role' => 'admin',
                'is_active' => 1
            ];
            $db->insert('users', $userData);
        }
        
        // العملات
        $currencyExists = $db->fetchOne("SELECT COUNT(*) as count FROM currencies");
        if ($currencyExists['count'] == 0) {
            $currencies = [
                ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
                ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
                ['اليورو', '€', 'EUR', 0.00061, 0]
            ];
            
            foreach ($currencies as $currency) {
                $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
            }
        }
        
        // الأقسام
        $deptExists = $db->fetchOne("SELECT COUNT(*) as count FROM departments");
        if ($deptExists['count'] == 0) {
            $departments = [
                ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
                ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
                ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
                ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
                ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
                ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
                ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
                ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
            ];
            
            foreach ($departments as $dept) {
                $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
            }
        }
        
        $db->commit();
        $success = 'تم الإصلاح الشامل بنجاح! النظام جاهز للاستخدام.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في الإصلاح: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإصلاح الشامل - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .fix-body {
            padding: 40px;
        }
        .btn-fix {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .emergency {
            animation: shake 0.5s infinite;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1 class="emergency"><i class="fas fa-wrench fa-2x mb-3"></i><br>الإصلاح الشامل</h1>
            <p class="mb-0">حل جميع مشاكل النظام دفعة واحدة</p>
        </div>
        
        <div class="fix-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">بيانات تسجيل الدخول</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>اسم المستخدم:</strong>
                                <div class="bg-light p-3 rounded mt-2">
                                    <h4 class="text-primary mb-0"><code>admin</code></h4>
                                </div>
                            </div>
                            <div class="col-6">
                                <strong>كلمة المرور:</strong>
                                <div class="bg-light p-3 rounded mt-2">
                                    <h4 class="text-danger mb-0"><code>123456</code></h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <a href="login.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول الآن
                    </a>
                    <a href="employees.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-users me-2"></i>نظام الموظفين
                    </a>
                    <a href="purchases.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-shopping-bag me-2"></i>نظام المشتريات
                    </a>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="alert alert-danger">
                    <h4><i class="fas fa-exclamation-triangle me-2"></i>مشاكل النظام</h4>
                    <ul class="mb-0">
                        <li>مشكلة في تسجيل الدخول</li>
                        <li>جداول قاعدة البيانات مفقودة</li>
                        <li>المستخدم الافتراضي غير موجود</li>
                        <li>البيانات الأساسية مفقودة</li>
                    </ul>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ما سيتم إصلاحه:</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الجداول:</h6>
                                <ul>
                                    <li>المستخدمين</li>
                                    <li>العملات</li>
                                    <li>الأقسام</li>
                                    <li>الموظفين</li>
                                    <li>العملاء والموردين</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>البيانات:</h6>
                                <ul>
                                    <li>مستخدم افتراضي (admin/123456)</li>
                                    <li>3 عملات أساسية</li>
                                    <li>8 أقسام متنوعة</li>
                                    <li>جداول المبيعات والمشتريات</li>
                                    <li>جداول الأصناف والمخازن</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-fix btn-lg emergency">
                            <i class="fas fa-wrench me-2"></i>إصلاح جميع المشاكل الآن
                        </button>
                    </form>
                </div>
            <?php endif; ?>

            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>معلومات مهمة:</h6>
                <ul class="mb-0">
                    <li>هذا الإصلاح آمن ولن يحذف البيانات الموجودة</li>
                    <li>سيتم إنشاء الجداول المفقودة فقط</li>
                    <li>بعد الإصلاح، يمكنك تسجيل الدخول فوراً</li>
                    <li>يمكنك تغيير كلمة المرور لاحقاً من الإعدادات</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
