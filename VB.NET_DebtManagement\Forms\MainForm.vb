Imports System.Windows.Forms
Imports System.Drawing

''' <summary>
''' النموذج الرئيسي للتطبيق - Main Form
''' </summary>
Public Class MainForm
    Inherits Form
    
#Region "Fields"
    
    Private _currentUser As User
    Private _context As DebtContext
    Private _menuStrip As MenuStrip
    Private _toolStrip As ToolStrip
    Private _statusStrip As StatusStrip
    Private _mainPanel As Panel
    Private _sidePanel As Panel
    Private _contentPanel As Panel
    
    ' أزرار الشريط الجانبي
    Private _btnDashboard As Button
    Private _btnCustomers As Button
    Private _btnSuppliers As Button
    Private _btnTransactions As Button
    Private _btnCashBoxes As Button
    Private _btnDebts As Button
    Private _btnReports As Button
    Private _btnSettings As Button
    
    ' عناصر شريط الحالة
    Private _lblUser As ToolStripStatusLabel
    Private _lblDateTime As ToolStripStatusLabel
    Private _lblStatus As ToolStripStatusLabel
    
    ' مؤقت لتحديث الوقت
    Private _timer As Timer
    
#End Region

#Region "Constructor"
    
    ''' <summary>
    ''' منشئ النموذج الرئيسي
    ''' </summary>
    ''' <param name="user">المستخدم الحالي</param>
    Public Sub New(user As User)
        _currentUser = user
        _context = New DebtContext()
        
        InitializeComponent()
        SetupForm()
        SetupMenu()
        SetupToolbar()
        SetupSidePanel()
        SetupStatusBar()
        SetupTimer()
        
        ' تحميل لوحة التحكم كصفحة افتراضية
        LoadDashboard()
    End Sub
    
#End Region

#Region "Form Setup"
    
    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        ' إعدادات النموذج
        Me.Text = "نظام إدارة الديون - " & _currentUser.FullName
        Me.Size = New Size(1400, 900)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.WindowState = FormWindowState.Maximized
        Me.Icon = My.Resources.AppIcon ' يجب إضافة أيقونة التطبيق
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        
        ' إعداد الخط العربي
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        
        ' إعداد الألوان
        Me.BackColor = Color.FromArgb(240, 242, 247)
        
        ' إعداد اللوحة الرئيسية
        _mainPanel = New Panel With {
            .Dock = DockStyle.Fill,
            .BackColor = Color.Transparent
        }
        Me.Controls.Add(_mainPanel)
    End Sub
    
    ''' <summary>
    ''' إعداد القائمة الرئيسية
    ''' </summary>
    Private Sub SetupMenu()
        _menuStrip = New MenuStrip With {
            .BackColor = Color.FromArgb(54, 84, 134),
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }
        
        ' قائمة الملف
        Dim fileMenu As New ToolStripMenuItem("ملف")
        fileMenu.DropDownItems.AddRange({
            New ToolStripMenuItem("جديد", My.Resources.NewIcon, AddressOf NewFile_Click),
            New ToolStripSeparator(),
            New ToolStripMenuItem("نسخ احتياطي", My.Resources.BackupIcon, AddressOf Backup_Click),
            New ToolStripMenuItem("استعادة", My.Resources.RestoreIcon, AddressOf Restore_Click),
            New ToolStripSeparator(),
            New ToolStripMenuItem("خروج", My.Resources.ExitIcon, AddressOf Exit_Click)
        })
        
        ' قائمة البيانات
        Dim dataMenu As New ToolStripMenuItem("البيانات")
        dataMenu.DropDownItems.AddRange({
            New ToolStripMenuItem("العملاء", My.Resources.CustomersIcon, AddressOf Customers_Click),
            New ToolStripMenuItem("الموردين", My.Resources.SuppliersIcon, AddressOf Suppliers_Click),
            New ToolStripMenuItem("المعاملات", My.Resources.TransactionsIcon, AddressOf Transactions_Click),
            New ToolStripMenuItem("الصناديق", My.Resources.CashBoxIcon, AddressOf CashBoxes_Click),
            New ToolStripMenuItem("الديون", My.Resources.DebtsIcon, AddressOf Debts_Click)
        })
        
        ' قائمة التقارير
        Dim reportsMenu As New ToolStripMenuItem("التقارير")
        reportsMenu.DropDownItems.AddRange({
            New ToolStripMenuItem("تقرير العملاء", My.Resources.ReportIcon, AddressOf CustomersReport_Click),
            New ToolStripMenuItem("تقرير الموردين", My.Resources.ReportIcon, AddressOf SuppliersReport_Click),
            New ToolStripMenuItem("تقرير المعاملات", My.Resources.ReportIcon, AddressOf TransactionsReport_Click),
            New ToolStripMenuItem("تقرير الديون", My.Resources.ReportIcon, AddressOf DebtsReport_Click),
            New ToolStripSeparator(),
            New ToolStripMenuItem("كشف حساب عميل", My.Resources.StatementIcon, AddressOf CustomerStatement_Click),
            New ToolStripMenuItem("كشف حساب مورد", My.Resources.StatementIcon, AddressOf SupplierStatement_Click)
        })
        
        ' قائمة الأدوات
        Dim toolsMenu As New ToolStripMenuItem("أدوات")
        toolsMenu.DropDownItems.AddRange({
            New ToolStripMenuItem("الإعدادات", My.Resources.SettingsIcon, AddressOf Settings_Click),
            New ToolStripMenuItem("إدارة المستخدمين", My.Resources.UsersIcon, AddressOf Users_Click),
            New ToolStripSeparator(),
            New ToolStripMenuItem("حاسبة", My.Resources.CalculatorIcon, AddressOf Calculator_Click),
            New ToolStripMenuItem("التقويم", My.Resources.CalendarIcon, AddressOf Calendar_Click)
        })
        
        ' قائمة المساعدة
        Dim helpMenu As New ToolStripMenuItem("مساعدة")
        helpMenu.DropDownItems.AddRange({
            New ToolStripMenuItem("دليل المستخدم", My.Resources.HelpIcon, AddressOf Help_Click),
            New ToolStripMenuItem("حول البرنامج", My.Resources.AboutIcon, AddressOf About_Click)
        })
        
        _menuStrip.Items.AddRange({fileMenu, dataMenu, reportsMenu, toolsMenu, helpMenu})
        Me.MainMenuStrip = _menuStrip
        Me.Controls.Add(_menuStrip)
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolbar()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }
        
        _toolStrip.Items.AddRange({
            New ToolStripButton("جديد", My.Resources.NewIcon, AddressOf NewFile_Click),
            New ToolStripSeparator(),
            New ToolStripButton("العملاء", My.Resources.CustomersIcon, AddressOf Customers_Click),
            New ToolStripButton("الموردين", My.Resources.SuppliersIcon, AddressOf Suppliers_Click),
            New ToolStripButton("المعاملات", My.Resources.TransactionsIcon, AddressOf Transactions_Click),
            New ToolStripSeparator(),
            New ToolStripButton("التقارير", My.Resources.ReportIcon, AddressOf Reports_Click),
            New ToolStripButton("الإعدادات", My.Resources.SettingsIcon, AddressOf Settings_Click),
            New ToolStripSeparator(),
            New ToolStripButton("نسخ احتياطي", My.Resources.BackupIcon, AddressOf Backup_Click)
        })
        
        Me.Controls.Add(_toolStrip)
    End Sub
    
    ''' <summary>
    ''' إعداد الشريط الجانبي
    ''' </summary>
    Private Sub SetupSidePanel()
        _sidePanel = New Panel With {
            .Width = 250,
            .Dock = DockStyle.Right,
            .BackColor = Color.FromArgb(54, 84, 134),
            .Padding = New Padding(10)
        }
        
        ' إنشاء الأزرار
        CreateSideButton(_btnDashboard, "لوحة التحكم", My.Resources.DashboardIcon, 10, AddressOf Dashboard_Click)
        CreateSideButton(_btnCustomers, "العملاء", My.Resources.CustomersIcon, 60, AddressOf Customers_Click)
        CreateSideButton(_btnSuppliers, "الموردين", My.Resources.SuppliersIcon, 110, AddressOf Suppliers_Click)
        CreateSideButton(_btnTransactions, "المعاملات", My.Resources.TransactionsIcon, 160, AddressOf Transactions_Click)
        CreateSideButton(_btnCashBoxes, "الصناديق", My.Resources.CashBoxIcon, 210, AddressOf CashBoxes_Click)
        CreateSideButton(_btnDebts, "الديون", My.Resources.DebtsIcon, 260, AddressOf Debts_Click)
        CreateSideButton(_btnReports, "التقارير", My.Resources.ReportIcon, 310, AddressOf Reports_Click)
        CreateSideButton(_btnSettings, "الإعدادات", My.Resources.SettingsIcon, 360, AddressOf Settings_Click)
        
        ' تحديد الزر النشط
        SetActiveButton(_btnDashboard)
        
        _mainPanel.Controls.Add(_sidePanel)
        
        ' إعداد لوحة المحتوى
        _contentPanel = New Panel With {
            .Dock = DockStyle.Fill,
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }
        _mainPanel.Controls.Add(_contentPanel)
    End Sub
    
    ''' <summary>
    ''' إنشاء زر في الشريط الجانبي
    ''' </summary>
    Private Sub CreateSideButton(ByRef button As Button, text As String, icon As Image, top As Integer, handler As EventHandler)
        button = New Button With {
            .Text = text,
            .Image = icon,
            .ImageAlign = ContentAlignment.MiddleLeft,
            .TextAlign = ContentAlignment.MiddleLeft,
            .TextImageRelation = TextImageRelation.ImageBeforeText,
            .Size = New Size(230, 40),
            .Location = New Point(10, top),
            .FlatStyle = FlatStyle.Flat,
            .BackColor = Color.Transparent,
            .ForeColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Cursor = Cursors.Hand
        }
        
        button.FlatAppearance.BorderSize = 0
        button.FlatAppearance.MouseOverBackColor = Color.FromArgb(70, 100, 150)
        
        AddHandler button.Click, handler
        _sidePanel.Controls.Add(button)
    End Sub
    
    ''' <summary>
    ''' تعيين الزر النشط
    ''' </summary>
    Private Sub SetActiveButton(activeButton As Button)
        ' إعادة تعيين جميع الأزرار
        For Each ctrl As Control In _sidePanel.Controls
            If TypeOf ctrl Is Button Then
                Dim btn = DirectCast(ctrl, Button)
                btn.BackColor = Color.Transparent
                btn.ForeColor = Color.White
            End If
        Next
        
        ' تعيين الزر النشط
        activeButton.BackColor = Color.FromArgb(70, 100, 150)
        activeButton.ForeColor = Color.Yellow
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الحالة
    ''' </summary>
    Private Sub SetupStatusBar()
        _statusStrip = New StatusStrip With {
            .BackColor = Color.FromArgb(240, 242, 247),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }
        
        _lblUser = New ToolStripStatusLabel With {
            .Text = "المستخدم: " & _currentUser.FullName,
            .Spring = False
        }
        
        _lblDateTime = New ToolStripStatusLabel With {
            .Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss"),
            .Spring = False
        }
        
        _lblStatus = New ToolStripStatusLabel With {
            .Text = "جاهز",
            .Spring = True,
            .TextAlign = ContentAlignment.MiddleLeft
        }
        
        _statusStrip.Items.AddRange({_lblUser, New ToolStripSeparator(), _lblDateTime, New ToolStripSeparator(), _lblStatus})
        Me.Controls.Add(_statusStrip)
    End Sub
    
    ''' <summary>
    ''' إعداد المؤقت
    ''' </summary>
    Private Sub SetupTimer()
        _timer = New Timer With {
            .Interval = 1000,
            .Enabled = True
        }
        AddHandler _timer.Tick, AddressOf Timer_Tick
    End Sub
    
#End Region

#Region "Event Handlers"
    
    ''' <summary>
    ''' تحديث الوقت
    ''' </summary>
    Private Sub Timer_Tick(sender As Object, e As EventArgs)
        _lblDateTime.Text = DateTime.Now.ToString("yyyy/MM/dd - HH:mm:ss")
    End Sub
    
    ''' <summary>
    ''' تحميل لوحة التحكم
    ''' </summary>
    Private Sub Dashboard_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnDashboard)
        LoadDashboard()
    End Sub
    
    ''' <summary>
    ''' فتح نموذج العملاء
    ''' </summary>
    Private Sub Customers_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnCustomers)
        LoadForm(New CustomersForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج الموردين
    ''' </summary>
    Private Sub Suppliers_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnSuppliers)
        LoadForm(New SuppliersForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج المعاملات
    ''' </summary>
    Private Sub Transactions_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnTransactions)
        LoadForm(New TransactionsForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج الصناديق
    ''' </summary>
    Private Sub CashBoxes_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnCashBoxes)
        LoadForm(New CashBoxesForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج الديون
    ''' </summary>
    Private Sub Debts_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnDebts)
        LoadForm(New DebtsForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج التقارير
    ''' </summary>
    Private Sub Reports_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnReports)
        LoadForm(New ReportsForm(_context, _currentUser))
    End Sub
    
    ''' <summary>
    ''' فتح نموذج الإعدادات
    ''' </summary>
    Private Sub Settings_Click(sender As Object, e As EventArgs)
        SetActiveButton(_btnSettings)
        LoadForm(New SettingsForm(_context, _currentUser))
    End Sub
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تحميل لوحة التحكم
    ''' </summary>
    Private Sub LoadDashboard()
        _contentPanel.Controls.Clear()
        
        Dim dashboardControl As New DashboardControl(_context, _currentUser) With {
            .Dock = DockStyle.Fill
        }
        
        _contentPanel.Controls.Add(dashboardControl)
        _lblStatus.Text = "لوحة التحكم"
    End Sub
    
    ''' <summary>
    ''' تحميل نموذج في لوحة المحتوى
    ''' </summary>
    Private Sub LoadForm(form As Form)
        _contentPanel.Controls.Clear()
        
        form.TopLevel = False
        form.FormBorderStyle = FormBorderStyle.None
        form.Dock = DockStyle.Fill
        
        _contentPanel.Controls.Add(form)
        form.Show()
        
        _lblStatus.Text = form.Text
    End Sub
    
#End Region

#Region "Menu Event Handlers"
    
    Private Sub NewFile_Click(sender As Object, e As EventArgs)
        ' تنفيذ إنشاء ملف جديد
    End Sub
    
    Private Sub Backup_Click(sender As Object, e As EventArgs)
        ' تنفيذ النسخ الاحتياطي
    End Sub
    
    Private Sub Restore_Click(sender As Object, e As EventArgs)
        ' تنفيذ الاستعادة
    End Sub
    
    Private Sub Exit_Click(sender As Object, e As EventArgs)
        Me.Close()
    End Sub
    
    Private Sub CustomersReport_Click(sender As Object, e As EventArgs)
        ' فتح تقرير العملاء
    End Sub
    
    Private Sub SuppliersReport_Click(sender As Object, e As EventArgs)
        ' فتح تقرير الموردين
    End Sub
    
    Private Sub TransactionsReport_Click(sender As Object, e As EventArgs)
        ' فتح تقرير المعاملات
    End Sub
    
    Private Sub DebtsReport_Click(sender As Object, e As EventArgs)
        ' فتح تقرير الديون
    End Sub
    
    Private Sub CustomerStatement_Click(sender As Object, e As EventArgs)
        ' فتح كشف حساب عميل
    End Sub
    
    Private Sub SupplierStatement_Click(sender As Object, e As EventArgs)
        ' فتح كشف حساب مورد
    End Sub
    
    Private Sub Users_Click(sender As Object, e As EventArgs)
        ' فتح إدارة المستخدمين
    End Sub
    
    Private Sub Calculator_Click(sender As Object, e As EventArgs)
        Process.Start("calc.exe")
    End Sub
    
    Private Sub Calendar_Click(sender As Object, e As EventArgs)
        ' فتح التقويم
    End Sub
    
    Private Sub Help_Click(sender As Object, e As EventArgs)
        ' فتح دليل المستخدم
    End Sub
    
    Private Sub About_Click(sender As Object, e As EventArgs)
        ' فتح حول البرنامج
    End Sub
    
#End Region

#Region "Form Events"
    
    ''' <summary>
    ''' عند إغلاق النموذج
    ''' </summary>
    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
            e.Cancel = True
            Return
        End If
        
        _timer?.Stop()
        _context?.Dispose()
        
        MyBase.OnFormClosing(e)
    End Sub
    
#End Region

End Class
