Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج إعدادات النظام - SystemSetting Model
''' </summary>
<Table("SystemSettings")>
Public Class SystemSetting
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف الإعداد الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' مفتاح الإعداد
    ''' </summary>
    <Required(ErrorMessage:="مفتاح الإعداد مطلوب")>
    <StringLength(100, ErrorMessage:="مفتاح الإعداد يجب أن يكون أقل من 100 حرف")>
    <Column(TypeName:="NVARCHAR")>
    <Index(IsUnique:=True)>
    Public Property SettingKey As String
    
    ''' <summary>
    ''' قيمة الإعداد
    ''' </summary>
    <Column(TypeName:="NVARCHAR(MAX)")>
    Public Property SettingValue As String
    
    ''' <summary>
    ''' وصف الإعداد
    ''' </summary>
    <StringLength(500, ErrorMessage:="وصف الإعداد يجب أن يكون أقل من 500 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Description As String
    
    ''' <summary>
    ''' نوع البيانات
    ''' </summary>
    <StringLength(20, ErrorMessage:="نوع البيانات يجب أن يكون أقل من 20 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property DataType As String = "String" ' String, Integer, Decimal, Boolean, Date
    
    ''' <summary>
    ''' فئة الإعداد
    ''' </summary>
    <StringLength(50, ErrorMessage:="فئة الإعداد يجب أن تكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Category As String = "General"
    
    ''' <summary>
    ''' هل الإعداد قابل للتعديل
    ''' </summary>
    Public Property IsEditable As Boolean = True
    
    ''' <summary>
    ''' هل الإعداد مرئي للمستخدمين
    ''' </summary>
    Public Property IsVisible As Boolean = True
    
    ''' <summary>
    ''' ترتيب العرض
    ''' </summary>
    Public Property DisplayOrder As Integer = 0
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
    ''' <summary>
    ''' معرف المستخدم الذي حدث السجل
    ''' </summary>
    Public Property UpdatedBy As Integer?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' المستخدم الذي حدث السجل
    ''' </summary>
    <ForeignKey("UpdatedBy")>
    Public Overridable Property Updater As User
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' نوع البيانات بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property DataTypeInArabic As String
        Get
            Select Case DataType.ToLower()
                Case "string"
                    Return "نص"
                Case "integer"
                    Return "رقم صحيح"
                Case "decimal"
                    Return "رقم عشري"
                Case "boolean"
                    Return "صحيح/خطأ"
                Case "date"
                    Return "تاريخ"
                Case Else
                    Return DataType
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' فئة الإعداد بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property CategoryInArabic As String
        Get
            Select Case Category.ToLower()
                Case "general"
                    Return "عام"
                Case "company"
                    Return "الشركة"
                Case "currency"
                    Return "العملة"
                Case "security"
                    Return "الأمان"
                Case "backup"
                    Return "النسخ الاحتياطي"
                Case "reports"
                    Return "التقارير"
                Case "notifications"
                    Return "الإشعارات"
                Case Else
                    Return Category
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة القابلية للتعديل بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property EditableStatusInArabic As String
        Get
            Return If(IsEditable, "قابل للتعديل", "غير قابل للتعديل")
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' الحصول على القيمة كنص
    ''' </summary>
    ''' <returns>القيمة كنص</returns>
    Public Function GetStringValue() As String
        Return If(SettingValue, String.Empty)
    End Function
    
    ''' <summary>
    ''' الحصول على القيمة كرقم صحيح
    ''' </summary>
    ''' <param name="defaultValue">القيمة الافتراضية</param>
    ''' <returns>القيمة كرقم صحيح</returns>
    Public Function GetIntegerValue(Optional defaultValue As Integer = 0) As Integer
        Dim result As Integer
        If Integer.TryParse(SettingValue, result) Then
            Return result
        Else
            Return defaultValue
        End If
    End Function
    
    ''' <summary>
    ''' الحصول على القيمة كرقم عشري
    ''' </summary>
    ''' <param name="defaultValue">القيمة الافتراضية</param>
    ''' <returns>القيمة كرقم عشري</returns>
    Public Function GetDecimalValue(Optional defaultValue As Decimal = 0) As Decimal
        Dim result As Decimal
        If Decimal.TryParse(SettingValue, result) Then
            Return result
        Else
            Return defaultValue
        End If
    End Function
    
    ''' <summary>
    ''' الحصول على القيمة كقيمة منطقية
    ''' </summary>
    ''' <param name="defaultValue">القيمة الافتراضية</param>
    ''' <returns>القيمة كقيمة منطقية</returns>
    Public Function GetBooleanValue(Optional defaultValue As Boolean = False) As Boolean
        Dim result As Boolean
        If Boolean.TryParse(SettingValue, result) Then
            Return result
        Else
            Return defaultValue
        End If
    End Function
    
    ''' <summary>
    ''' الحصول على القيمة كتاريخ
    ''' </summary>
    ''' <param name="defaultValue">القيمة الافتراضية</param>
    ''' <returns>القيمة كتاريخ</returns>
    Public Function GetDateValue(Optional defaultValue As DateTime? = Nothing) As DateTime?
        Dim result As DateTime
        If DateTime.TryParse(SettingValue, result) Then
            Return result
        Else
            Return defaultValue
        End If
    End Function
    
    ''' <summary>
    ''' تحديث القيمة
    ''' </summary>
    ''' <param name="newValue">القيمة الجديدة</param>
    ''' <param name="userId">معرف المستخدم</param>
    Public Sub UpdateValue(newValue As String, Optional userId As Integer? = Nothing)
        SettingValue = newValue
        UpdatedAt = DateTime.Now
        UpdatedBy = userId
    End Sub
    
    ''' <summary>
    ''' تحديث القيمة (رقم صحيح)
    ''' </summary>
    ''' <param name="newValue">القيمة الجديدة</param>
    ''' <param name="userId">معرف المستخدم</param>
    Public Sub UpdateValue(newValue As Integer, Optional userId As Integer? = Nothing)
        UpdateValue(newValue.ToString(), userId)
    End Sub
    
    ''' <summary>
    ''' تحديث القيمة (رقم عشري)
    ''' </summary>
    ''' <param name="newValue">القيمة الجديدة</param>
    ''' <param name="userId">معرف المستخدم</param>
    Public Sub UpdateValue(newValue As Decimal, Optional userId As Integer? = Nothing)
        UpdateValue(newValue.ToString(), userId)
    End Sub
    
    ''' <summary>
    ''' تحديث القيمة (قيمة منطقية)
    ''' </summary>
    ''' <param name="newValue">القيمة الجديدة</param>
    ''' <param name="userId">معرف المستخدم</param>
    Public Sub UpdateValue(newValue As Boolean, Optional userId As Integer? = Nothing)
        UpdateValue(newValue.ToString(), userId)
    End Sub
    
    ''' <summary>
    ''' تحديث القيمة (تاريخ)
    ''' </summary>
    ''' <param name="newValue">القيمة الجديدة</param>
    ''' <param name="userId">معرف المستخدم</param>
    Public Sub UpdateValue(newValue As DateTime, Optional userId As Integer? = Nothing)
        UpdateValue(newValue.ToString("yyyy-MM-dd HH:mm:ss"), userId)
    End Sub
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(SettingKey) Then
            errors.Add("مفتاح الإعداد مطلوب")
        End If
        
        If Not String.IsNullOrWhiteSpace(DataType) AndAlso Not {"String", "Integer", "Decimal", "Boolean", "Date"}.Contains(DataType) Then
            errors.Add("نوع البيانات غير صحيح")
        End If
        
        ' التحقق من صحة القيمة حسب نوع البيانات
        If Not String.IsNullOrWhiteSpace(SettingValue) AndAlso Not String.IsNullOrWhiteSpace(DataType) Then
            Select Case DataType.ToLower()
                Case "integer"
                    Dim intValue As Integer
                    If Not Integer.TryParse(SettingValue, intValue) Then
                        errors.Add("قيمة الإعداد يجب أن تكون رقم صحيح")
                    End If
                Case "decimal"
                    Dim decValue As Decimal
                    If Not Decimal.TryParse(SettingValue, decValue) Then
                        errors.Add("قيمة الإعداد يجب أن تكون رقم عشري")
                    End If
                Case "boolean"
                    Dim boolValue As Boolean
                    If Not Boolean.TryParse(SettingValue, boolValue) Then
                        errors.Add("قيمة الإعداد يجب أن تكون صحيح أو خطأ")
                    End If
                Case "date"
                    Dim dateValue As DateTime
                    If Not DateTime.TryParse(SettingValue, dateValue) Then
                        errors.Add("قيمة الإعداد يجب أن تكون تاريخ صحيح")
                    End If
            End Select
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للإعداد
    ''' </summary>
    ''' <returns>مفتاح الإعداد</returns>
    Public Overrides Function ToString() As String
        Return SettingKey
    End Function
    
#End Region

End Class
