Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema
Imports System.Security.Cryptography
Imports System.Text

''' <summary>
''' نموذج المستخدم - User Model
''' </summary>
<Table("Users")>
Public Class User
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف المستخدم الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' اسم المستخدم
    ''' </summary>
    <Required(ErrorMessage:="اسم المستخدم مطلوب")>
    <StringLength(50, MinimumLength:=3, ErrorMessage:="اسم المستخدم يجب أن يكون بين 3 و 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    <Index(IsUnique:=True)>
    Public Property Username As String
    
    ''' <summary>
    ''' كلمة المرور المشفرة
    ''' </summary>
    <Required(ErrorMessage:="كلمة المرور مطلوبة")>
    <StringLength(255, ErrorMessage:="كلمة المرور طويلة جداً")>
    <Column(TypeName:="NVARCHAR")>
    Public Property PasswordHash As String
    
    ''' <summary>
    ''' الاسم الكامل
    ''' </summary>
    <Required(ErrorMessage:="الاسم الكامل مطلوب")>
    <StringLength(100, ErrorMessage:="الاسم الكامل يجب أن يكون أقل من 100 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property FullName As String
    
    ''' <summary>
    ''' البريد الإلكتروني
    ''' </summary>
    <StringLength(100, ErrorMessage:="البريد الإلكتروني يجب أن يكون أقل من 100 حرف")>
    <EmailAddress(ErrorMessage:="البريد الإلكتروني غير صحيح")>
    <Column(TypeName:="NVARCHAR")>
    <Index(IsUnique:=True)>
    Public Property Email As String
    
    ''' <summary>
    ''' رقم الهاتف
    ''' </summary>
    <StringLength(20, ErrorMessage:="رقم الهاتف يجب أن يكون أقل من 20 رقم")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Phone As String
    
    ''' <summary>
    ''' دور المستخدم
    ''' </summary>
    <Required(ErrorMessage:="دور المستخدم مطلوب")>
    <StringLength(20, ErrorMessage:="دور المستخدم يجب أن يكون أقل من 20 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Role As String = "User" ' Admin, Manager, User
    
    ''' <summary>
    ''' حالة المستخدم (نشط/غير نشط)
    ''' </summary>
    Public Property IsActive As Boolean = True
    
    ''' <summary>
    ''' آخر تسجيل دخول
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property LastLoginAt As DateTime?
    
    ''' <summary>
    ''' تاريخ إنشاء الحساب
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
    ''' <summary>
    ''' معرف المستخدم الذي أنشأ الحساب
    ''' </summary>
    Public Property CreatedBy As Integer?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' العملاء الذين أنشأهم المستخدم
    ''' </summary>
    <InverseProperty("Creator")>
    Public Overridable Property CreatedCustomers As ICollection(Of Customer) = New HashSet(Of Customer)
    
    ''' <summary>
    ''' الموردين الذين أنشأهم المستخدم
    ''' </summary>
    <InverseProperty("Creator")>
    Public Overridable Property CreatedSuppliers As ICollection(Of Supplier) = New HashSet(Of Supplier)
    
    ''' <summary>
    ''' المعاملات التي أنشأها المستخدم
    ''' </summary>
    <InverseProperty("Creator")>
    Public Overridable Property CreatedTransactions As ICollection(Of Transaction) = New HashSet(Of Transaction)
    
    ''' <summary>
    ''' الصناديق التي أنشأها المستخدم
    ''' </summary>
    <InverseProperty("Creator")>
    Public Overridable Property CreatedCashBoxes As ICollection(Of CashBox) = New HashSet(Of CashBox)
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' دور المستخدم بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property RoleInArabic As String
        Get
            Select Case Role.ToLower()
                Case "admin"
                    Return "مدير النظام"
                Case "manager"
                    Return "مدير"
                Case "user"
                    Return "مستخدم"
                Case "accountant"
                    Return "محاسب"
                Case "cashier"
                    Return "أمين صندوق"
                Case Else
                    Return Role
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة المستخدم بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property StatusInArabic As String
        Get
            Return If(IsActive, "نشط", "غير نشط")
        End Get
    End Property
    
    ''' <summary>
    ''' عدد المعاملات التي أنشأها المستخدم (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TransactionCount As Integer
        Get
            Return If(CreatedTransactions?.Count, 0)
        End Get
    End Property
    
    ''' <summary>
    ''' عدد العملاء الذين أنشأهم المستخدم (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property CustomerCount As Integer
        Get
            Return If(CreatedCustomers?.Count, 0)
        End Get
    End Property
    
    ''' <summary>
    ''' عدد الموردين الذين أنشأهم المستخدم (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property SupplierCount As Integer
        Get
            Return If(CreatedSuppliers?.Count, 0)
        End Get
    End Property
    
    ''' <summary>
    ''' الأحرف الأولى من الاسم (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property Initials As String
        Get
            If String.IsNullOrWhiteSpace(FullName) Then
                Return "?"
            End If
            
            Dim parts = FullName.Split(" "c)
            If parts.Length >= 2 Then
                Return (parts(0).Substring(0, 1) + parts(1).Substring(0, 1)).ToUpper()
            Else
                Return parts(0).Substring(0, Math.Min(2, parts(0).Length)).ToUpper()
            End If
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تشفير كلمة المرور
    ''' </summary>
    ''' <param name="password">كلمة المرور</param>
    ''' <returns>كلمة المرور المشفرة</returns>
    Public Shared Function HashPassword(password As String) As String
        Using sha256 As SHA256 = SHA256.Create()
            Dim salt As String = "DebtManagementSystem2024"
            Dim saltedPassword As String = password + salt
            Dim hashedBytes As Byte() = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword))
            Return Convert.ToBase64String(hashedBytes)
        End Using
    End Function
    
    ''' <summary>
    ''' التحقق من كلمة المرور
    ''' </summary>
    ''' <param name="password">كلمة المرور</param>
    ''' <returns>صحيح إذا كانت كلمة المرور صحيحة</returns>
    Public Function VerifyPassword(password As String) As Boolean
        Return PasswordHash = HashPassword(password)
    End Function
    
    ''' <summary>
    ''' تحديث كلمة المرور
    ''' </summary>
    ''' <param name="newPassword">كلمة المرور الجديدة</param>
    Public Sub UpdatePassword(newPassword As String)
        PasswordHash = HashPassword(newPassword)
        UpdatedAt = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' تحديث آخر تسجيل دخول
    ''' </summary>
    Public Sub UpdateLastLogin()
        LastLoginAt = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' التحقق من الصلاحيات
    ''' </summary>
    ''' <param name="requiredRole">الدور المطلوب</param>
    ''' <returns>صحيح إذا كان المستخدم لديه الصلاحية</returns>
    Public Function HasPermission(requiredRole As String) As Boolean
        If Not IsActive Then
            Return False
        End If
        
        Select Case Role.ToLower()
            Case "admin"
                Return True ' المدير له جميع الصلاحيات
            Case "manager"
                Return {"manager", "user", "accountant", "cashier"}.Contains(requiredRole.ToLower())
            Case "accountant"
                Return {"accountant", "user"}.Contains(requiredRole.ToLower())
            Case "cashier"
                Return {"cashier", "user"}.Contains(requiredRole.ToLower())
            Case "user"
                Return requiredRole.ToLower() = "user"
            Case Else
                Return False
        End Select
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(Username) Then
            errors.Add("اسم المستخدم مطلوب")
        ElseIf Username.Length < 3 Then
            errors.Add("اسم المستخدم يجب أن يكون 3 أحرف على الأقل")
        End If
        
        If String.IsNullOrWhiteSpace(FullName) Then
            errors.Add("الاسم الكامل مطلوب")
        End If
        
        If Not String.IsNullOrWhiteSpace(Email) AndAlso Not IsValidEmail(Email) Then
            errors.Add("البريد الإلكتروني غير صحيح")
        End If
        
        If String.IsNullOrWhiteSpace(Role) Then
            errors.Add("دور المستخدم مطلوب")
        ElseIf Not {"Admin", "Manager", "User", "Accountant", "Cashier"}.Contains(Role) Then
            errors.Add("دور المستخدم غير صحيح")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البريد الإلكتروني
    ''' </summary>
    ''' <param name="email">البريد الإلكتروني</param>
    ''' <returns>صحيح إذا كان البريد صحيح</returns>
    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للمستخدم
    ''' </summary>
    ''' <returns>الاسم الكامل</returns>
    Public Overrides Function ToString() As String
        Return FullName
    End Function
    
#End Region

End Class
