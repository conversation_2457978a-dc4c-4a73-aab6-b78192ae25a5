Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج الإعدادات - Settings Form
''' </summary>
Public Class SettingsForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User

    ' عناصر التحكم الرئيسية
    Private _tabControl As TabControl

    ' تبويب الإعدادات العامة
    Private _tabGeneral As TabPage
    Private _txtCompanyName As TextBox
    Private _txtCompanyPhone As TextBox
    Private _txtCompanyEmail As TextBox
    Private _txtCompanyAddress As TextBox
    Private _txtExchangeRate As TextBox
    Private _cmbDefaultCurrency As ComboBox
    Private _chkAutoBackup As CheckBox
    Private _numBackupDays As NumericUpDown

    ' تبويب إعدادات النظام
    Private _tabSystem As TabPage
    Private _chkEnableAuditLog As CheckBox
    Private _chkRequirePasswordChange As CheckBox
    Private _numPasswordExpireDays As NumericUpDown
    Private _numSessionTimeout As NumericUpDown
    Private _chkEnableNotifications As CheckBox

    ' تبويب إعدادات التقارير
    Private _tabReports As TabPage
    Private _txtReportHeader As TextBox
    Private _txtReportFooter As TextBox
    Private _chkShowLogo As CheckBox
    Private _txtLogoPath As TextBox
    Private _btnBrowseLogo As Button

    ' تبويب إعدادات البريد الإلكتروني
    Private _tabEmail As TabPage
    Private _txtSmtpServer As TextBox
    Private _numSmtpPort As NumericUpDown
    Private _txtEmailUsername As TextBox
    Private _txtEmailPassword As TextBox
    Private _chkEnableSSL As CheckBox
    Private _btnTestEmail As Button

    ' أزرار التحكم
    Private _btnSave As Button
    Private _btnCancel As Button
    Private _btnReset As Button

#End Region

#Region "Constructor"

    ''' <summary>
    ''' منشئ نموذج الإعدادات
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser

        InitializeComponent()
        SetupForm()
        SetupTabControl()
        SetupGeneralTab()
        SetupSystemTab()
        SetupReportsTab()
        SetupEmailTab()
        SetupButtons()
        LoadSettings()
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "إعدادات النظام"
        Me.Size = New Size(700, 600)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.SettingsIcon
    End Sub

    ''' <summary>
    ''' إعداد التحكم بالتبويبات
    ''' </summary>
    Private Sub SetupTabControl()
        _tabControl = New TabControl With {
            .Size = New Size(660, 480),
            .Location = New Point(20, 20),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes,
            .RightToLeftLayout = True
        }

        Me.Controls.Add(_tabControl)
    End Sub

    ''' <summary>
    ''' إعداد تبويب الإعدادات العامة
    ''' </summary>
    Private Sub SetupGeneralTab()
        _tabGeneral = New TabPage("الإعدادات العامة") With {
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }

        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 50

        ' اسم الشركة
        Dim lblCompanyName As New Label With {
            .Text = "اسم الشركة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtCompanyName = New TextBox With {
            .Size = New Size(600, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += fieldHeight

        ' هاتف الشركة
        Dim lblCompanyPhone As New Label With {
            .Text = "هاتف الشركة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtCompanyPhone = New TextBox With {
            .Size = New Size(600, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += fieldHeight

        ' بريد الشركة الإلكتروني
        Dim lblCompanyEmail As New Label With {
            .Text = "البريد الإلكتروني:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtCompanyEmail = New TextBox With {
            .Size = New Size(600, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += fieldHeight

        ' عنوان الشركة
        Dim lblCompanyAddress As New Label With {
            .Text = "عنوان الشركة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtCompanyAddress = New TextBox With {
            .Size = New Size(600, 60),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += 90

        ' سعر الصرف
        Dim lblExchangeRate As New Label With {
            .Text = "سعر صرف الدولار (بالدينار):",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(300, 20),
            .Location = New Point(320, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtExchangeRate = New TextBox With {
            .Size = New Size(150, 25),
            .Location = New Point(160, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .TextAlign = HorizontalAlignment.Center,
            .Text = "1320"
        }

        ' العملة الافتراضية
        Dim lblDefaultCurrency As New Label With {
            .Text = "العملة الافتراضية:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(300, 20),
            .Location = New Point(320, yPosition + 50),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbDefaultCurrency = New ComboBox With {
            .Size = New Size(150, 25),
            .Location = New Point(160, yPosition + 75),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbDefaultCurrency.Items.AddRange({"دينار عراقي", "دولار أمريكي"})
        _cmbDefaultCurrency.SelectedIndex = 0
        yPosition += 110

        ' النسخ الاحتياطي التلقائي
        _chkAutoBackup = New CheckBox With {
            .Text = "تفعيل النسخ الاحتياطي التلقائي",
            .Size = New Size(300, 25),
            .Location = New Point(320, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }

        Dim lblBackupDays As New Label With {
            .Text = "كل (أيام):",
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Size = New Size(80, 20),
            .Location = New Point(230, yPosition + 30),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _numBackupDays = New NumericUpDown With {
            .Size = New Size(80, 25),
            .Location = New Point(140, yPosition + 30),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Minimum = 1,
            .Maximum = 365,
            .Value = 7
        }

        ' إضافة العناصر للتبويب
        _tabGeneral.Controls.AddRange({
            lblCompanyName, _txtCompanyName,
            lblCompanyPhone, _txtCompanyPhone,
            lblCompanyEmail, _txtCompanyEmail,
            lblCompanyAddress, _txtCompanyAddress,
            lblExchangeRate, _txtExchangeRate,
            lblDefaultCurrency, _cmbDefaultCurrency,
            _chkAutoBackup, lblBackupDays, _numBackupDays
        })

        ' ربط الأحداث
        AddHandler _txtExchangeRate.KeyPress, AddressOf NumericTextBox_KeyPress

        _tabControl.TabPages.Add(_tabGeneral)
    End Sub

    ''' <summary>
    ''' إعداد تبويب إعدادات النظام
    ''' </summary>
    Private Sub SetupSystemTab()
        _tabSystem = New TabPage("إعدادات النظام") With {
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }

        Dim yPosition As Integer = 30
        Dim fieldHeight As Integer = 40

        ' تفعيل سجل المراجعة
        _chkEnableAuditLog = New CheckBox With {
            .Text = "تفعيل سجل المراجعة (Audit Log)",
            .Size = New Size(400, 25),
            .Location = New Point(220, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Checked = True
        }
        yPosition += fieldHeight

        ' طلب تغيير كلمة المرور
        _chkRequirePasswordChange = New CheckBox With {
            .Text = "طلب تغيير كلمة المرور دورياً",
            .Size = New Size(400, 25),
            .Location = New Point(220, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }

        Dim lblPasswordExpire As New Label With {
            .Text = "كل (أيام):",
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Size = New Size(80, 20),
            .Location = New Point(130, yPosition + 30),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _numPasswordExpireDays = New NumericUpDown With {
            .Size = New Size(80, 25),
            .Location = New Point(40, yPosition + 30),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Minimum = 30,
            .Maximum = 365,
            .Value = 90
        }
        yPosition += 70

        ' مهلة انتهاء الجلسة
        Dim lblSessionTimeout As New Label With {
            .Text = "مهلة انتهاء الجلسة (دقائق):",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(200, 20),
            .Location = New Point(420, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _numSessionTimeout = New NumericUpDown With {
            .Size = New Size(100, 25),
            .Location = New Point(310, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Minimum = 5,
            .Maximum = 480,
            .Value = 30
        }
        yPosition += 60

        ' تفعيل الإشعارات
        _chkEnableNotifications = New CheckBox With {
            .Text = "تفعيل الإشعارات",
            .Size = New Size(400, 25),
            .Location = New Point(220, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Checked = True
        }

        ' إضافة العناصر للتبويب
        _tabSystem.Controls.AddRange({
            _chkEnableAuditLog,
            _chkRequirePasswordChange, lblPasswordExpire, _numPasswordExpireDays,
            lblSessionTimeout, _numSessionTimeout,
            _chkEnableNotifications
        })

        _tabControl.TabPages.Add(_tabSystem)
    End Sub

    ''' <summary>
    ''' إعداد تبويب إعدادات التقارير
    ''' </summary>
    Private Sub SetupReportsTab()
        _tabReports = New TabPage("إعدادات التقارير") With {
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }

        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 70

        ' رأس التقرير
        Dim lblReportHeader As New Label With {
            .Text = "رأس التقرير:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtReportHeader = New TextBox With {
            .Size = New Size(600, 40),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Multiline = True,
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += fieldHeight

        ' ذيل التقرير
        Dim lblReportFooter As New Label With {
            .Text = "ذيل التقرير:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtReportFooter = New TextBox With {
            .Size = New Size(600, 40),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Multiline = True,
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += fieldHeight

        ' إظهار الشعار
        _chkShowLogo = New CheckBox With {
            .Text = "إظهار شعار الشركة في التقارير",
            .Size = New Size(400, 25),
            .Location = New Point(220, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }
        yPosition += 40

        ' مسار الشعار
        Dim lblLogoPath As New Label With {
            .Text = "مسار ملف الشعار:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtLogoPath = New TextBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .ReadOnly = True
        }

        _btnBrowseLogo = New Button With {
            .Text = "استعراض",
            .Size = New Size(80, 25),
            .Location = New Point(540, yPosition + 25),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .BackColor = Color.FromArgb(0, 123, 255),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat
        }
        _btnBrowseLogo.FlatAppearance.BorderSize = 0

        ' إضافة العناصر للتبويب
        _tabReports.Controls.AddRange({
            lblReportHeader, _txtReportHeader,
            lblReportFooter, _txtReportFooter,
            _chkShowLogo,
            lblLogoPath, _txtLogoPath, _btnBrowseLogo
        })

        ' ربط الأحداث
        AddHandler _btnBrowseLogo.Click, AddressOf BtnBrowseLogo_Click

        _tabControl.TabPages.Add(_tabReports)
    End Sub

    ''' <summary>
    ''' إعداد تبويب إعدادات البريد الإلكتروني
    ''' </summary>
    Private Sub SetupEmailTab()
        _tabEmail = New TabPage("إعدادات البريد الإلكتروني") With {
            .BackColor = Color.White,
            .Padding = New Padding(20)
        }

        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 50

        ' خادم SMTP
        Dim lblSmtpServer As New Label With {
            .Text = "خادم SMTP:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(300, 20),
            .Location = New Point(320, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtSmtpServer = New TextBox With {
            .Size = New Size(250, 25),
            .Location = New Point(60, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }

        ' منفذ SMTP
        Dim lblSmtpPort As New Label With {
            .Text = "منفذ SMTP:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(100, 20),
            .Location = New Point(520, yPosition + 25),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _numSmtpPort = New NumericUpDown With {
            .Size = New Size(80, 25),
            .Location = New Point(430, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Minimum = 1,
            .Maximum = 65535,
            .Value = 587
        }
        yPosition += fieldHeight

        ' اسم المستخدم
        Dim lblEmailUsername As New Label With {
            .Text = "اسم المستخدم:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtEmailUsername = New TextBox With {
            .Size = New Size(600, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }
        yPosition += fieldHeight

        ' كلمة المرور
        Dim lblEmailPassword As New Label With {
            .Text = "كلمة المرور:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(600, 20),
            .Location = New Point(20, yPosition),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _txtEmailPassword = New TextBox With {
            .Size = New Size(600, 25),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .UseSystemPasswordChar = True
        }
        yPosition += fieldHeight

        ' تفعيل SSL
        _chkEnableSSL = New CheckBox With {
            .Text = "تفعيل SSL/TLS",
            .Size = New Size(200, 25),
            .Location = New Point(420, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .Checked = True
        }

        ' اختبار البريد الإلكتروني
        _btnTestEmail = New Button With {
            .Text = "اختبار الإعدادات",
            .Size = New Size(120, 35),
            .Location = New Point(280, yPosition),
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .BackColor = Color.FromArgb(255, 193, 7),
            .ForeColor = Color.Black,
            .FlatStyle = FlatStyle.Flat
        }
        _btnTestEmail.FlatAppearance.BorderSize = 0

        ' إضافة العناصر للتبويب
        _tabEmail.Controls.AddRange({
            lblSmtpServer, _txtSmtpServer,
            lblSmtpPort, _numSmtpPort,
            lblEmailUsername, _txtEmailUsername,
            lblEmailPassword, _txtEmailPassword,
            _chkEnableSSL,
            _btnTestEmail
        })

        ' ربط الأحداث
        AddHandler _btnTestEmail.Click, AddressOf BtnTestEmail_Click

        _tabControl.TabPages.Add(_tabEmail)
    End Sub

    ''' <summary>
    ''' إعداد أزرار التحكم
    ''' </summary>
    Private Sub SetupButtons()
        Dim buttonPanel As New Panel With {
            .Size = New Size(660, 60),
            .Location = New Point(20, 520),
            .BackColor = Color.Transparent
        }

        _btnSave = New Button With {
            .Text = "حفظ الإعدادات",
            .Size = New Size(120, 40),
            .Location = New Point(520, 10),
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnSave.FlatAppearance.BorderSize = 0

        _btnReset = New Button With {
            .Text = "استعادة افتراضي",
            .Size = New Size(120, 40),
            .Location = New Point(390, 10),
            .BackColor = Color.FromArgb(255, 193, 7),
            .ForeColor = Color.Black,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnReset.FlatAppearance.BorderSize = 0

        _btnCancel = New Button With {
            .Text = "إلغاء",
            .Size = New Size(100, 40),
            .Location = New Point(280, 10),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnCancel.FlatAppearance.BorderSize = 0

        ' ربط الأحداث
        AddHandler _btnSave.Click, AddressOf BtnSave_Click
        AddHandler _btnReset.Click, AddressOf BtnReset_Click
        AddHandler _btnCancel.Click, AddressOf BtnCancel_Click

        ' تأثيرات الماوس
        AddHandler _btnSave.MouseEnter, Sub() _btnSave.BackColor = Color.FromArgb(33, 136, 56)
        AddHandler _btnSave.MouseLeave, Sub() _btnSave.BackColor = Color.FromArgb(40, 167, 69)
        AddHandler _btnReset.MouseEnter, Sub() _btnReset.BackColor = Color.FromArgb(255, 183, 0)
        AddHandler _btnReset.MouseLeave, Sub() _btnReset.BackColor = Color.FromArgb(255, 193, 7)
        AddHandler _btnCancel.MouseEnter, Sub() _btnCancel.BackColor = Color.FromArgb(90, 98, 104)
        AddHandler _btnCancel.MouseLeave, Sub() _btnCancel.BackColor = Color.FromArgb(108, 117, 125)

        buttonPanel.Controls.AddRange({_btnSave, _btnReset, _btnCancel})
        Me.Controls.Add(buttonPanel)
    End Sub

#End Region

#Region "Data Methods"

    ''' <summary>
    ''' تحميل الإعدادات من قاعدة البيانات
    ''' </summary>
    Private Sub LoadSettings()
        Try
            ' تحميل الإعدادات العامة
            _txtCompanyName.Text = GetSettingValue("CompanyName", "شركة إدارة الديون")
            _txtCompanyPhone.Text = GetSettingValue("CompanyPhone", "")
            _txtCompanyEmail.Text = GetSettingValue("CompanyEmail", "")
            _txtCompanyAddress.Text = GetSettingValue("CompanyAddress", "")
            _txtExchangeRate.Text = GetSettingValue("ExchangeRateUSDToIQD", "1320")

            Dim defaultCurrency = GetSettingValue("DefaultCurrency", "IQD")
            _cmbDefaultCurrency.SelectedIndex = If(defaultCurrency = "USD", 1, 0)

            _chkAutoBackup.Checked = GetSettingValue("AutoBackupEnabled", "True") = "True"
            _numBackupDays.Value = Convert.ToDecimal(GetSettingValue("AutoBackupDays", "7"))

            ' تحميل إعدادات النظام
            _chkEnableAuditLog.Checked = GetSettingValue("AuditLogEnabled", "True") = "True"
            _chkRequirePasswordChange.Checked = GetSettingValue("RequirePasswordChange", "False") = "True"
            _numPasswordExpireDays.Value = Convert.ToDecimal(GetSettingValue("PasswordExpireDays", "90"))
            _numSessionTimeout.Value = Convert.ToDecimal(GetSettingValue("SessionTimeoutMinutes", "30"))
            _chkEnableNotifications.Checked = GetSettingValue("NotificationsEnabled", "True") = "True"

            ' تحميل إعدادات التقارير
            _txtReportHeader.Text = GetSettingValue("ReportHeader", "")
            _txtReportFooter.Text = GetSettingValue("ReportFooter", "")
            _chkShowLogo.Checked = GetSettingValue("ShowLogoInReports", "False") = "True"
            _txtLogoPath.Text = GetSettingValue("CompanyLogoPath", "")

            ' تحميل إعدادات البريد الإلكتروني
            _txtSmtpServer.Text = GetSettingValue("SmtpServer", "")
            _numSmtpPort.Value = Convert.ToDecimal(GetSettingValue("SmtpPort", "587"))
            _txtEmailUsername.Text = GetSettingValue("EmailUsername", "")
            _txtEmailPassword.Text = GetSettingValue("EmailPassword", "")
            _chkEnableSSL.Checked = GetSettingValue("EmailSSLEnabled", "True") = "True"

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على قيمة إعداد
    ''' </summary>
    Private Function GetSettingValue(key As String, defaultValue As String) As String
        Try
            Dim setting = _context.SystemSettings.FirstOrDefault(Function(s) s.SettingKey = key)
            Return If(setting?.SettingValue, defaultValue)
        Catch
            Return defaultValue
        End Try
    End Function

    ''' <summary>
    ''' حفظ الإعدادات في قاعدة البيانات
    ''' </summary>
    Private Function SaveSettings() As Boolean
        Try
            ' حفظ الإعدادات العامة
            SaveSetting("CompanyName", _txtCompanyName.Text)
            SaveSetting("CompanyPhone", _txtCompanyPhone.Text)
            SaveSetting("CompanyEmail", _txtCompanyEmail.Text)
            SaveSetting("CompanyAddress", _txtCompanyAddress.Text)
            SaveSetting("ExchangeRateUSDToIQD", _txtExchangeRate.Text)
            SaveSetting("DefaultCurrency", If(_cmbDefaultCurrency.SelectedIndex = 1, "USD", "IQD"))
            SaveSetting("AutoBackupEnabled", _chkAutoBackup.Checked.ToString())
            SaveSetting("AutoBackupDays", _numBackupDays.Value.ToString())

            ' حفظ إعدادات النظام
            SaveSetting("AuditLogEnabled", _chkEnableAuditLog.Checked.ToString())
            SaveSetting("RequirePasswordChange", _chkRequirePasswordChange.Checked.ToString())
            SaveSetting("PasswordExpireDays", _numPasswordExpireDays.Value.ToString())
            SaveSetting("SessionTimeoutMinutes", _numSessionTimeout.Value.ToString())
            SaveSetting("NotificationsEnabled", _chkEnableNotifications.Checked.ToString())

            ' حفظ إعدادات التقارير
            SaveSetting("ReportHeader", _txtReportHeader.Text)
            SaveSetting("ReportFooter", _txtReportFooter.Text)
            SaveSetting("ShowLogoInReports", _chkShowLogo.Checked.ToString())
            SaveSetting("CompanyLogoPath", _txtLogoPath.Text)

            ' حفظ إعدادات البريد الإلكتروني
            SaveSetting("SmtpServer", _txtSmtpServer.Text)
            SaveSetting("SmtpPort", _numSmtpPort.Value.ToString())
            SaveSetting("EmailUsername", _txtEmailUsername.Text)
            SaveSetting("EmailPassword", _txtEmailPassword.Text)
            SaveSetting("EmailSSLEnabled", _chkEnableSSL.Checked.ToString())

            _context.SaveChanges()
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' حفظ إعداد واحد
    ''' </summary>
    Private Sub SaveSetting(key As String, value As String)
        Dim setting = _context.SystemSettings.FirstOrDefault(Function(s) s.SettingKey = key)

        If setting Is Nothing Then
            setting = New SystemSetting With {
                .SettingKey = key,
                .SettingValue = value,
                .CreatedAt = DateTime.Now
            }
            _context.SystemSettings.Add(setting)
        Else
            setting.SettingValue = value
            setting.UpdatedAt = DateTime.Now
        End If
    End Sub

    ''' <summary>
    ''' استعادة الإعدادات الافتراضية
    ''' </summary>
    Private Sub ResetToDefaults()
        ' الإعدادات العامة
        _txtCompanyName.Text = "شركة إدارة الديون"
        _txtCompanyPhone.Clear()
        _txtCompanyEmail.Clear()
        _txtCompanyAddress.Clear()
        _txtExchangeRate.Text = "1320"
        _cmbDefaultCurrency.SelectedIndex = 0
        _chkAutoBackup.Checked = True
        _numBackupDays.Value = 7

        ' إعدادات النظام
        _chkEnableAuditLog.Checked = True
        _chkRequirePasswordChange.Checked = False
        _numPasswordExpireDays.Value = 90
        _numSessionTimeout.Value = 30
        _chkEnableNotifications.Checked = True

        ' إعدادات التقارير
        _txtReportHeader.Clear()
        _txtReportFooter.Clear()
        _chkShowLogo.Checked = False
        _txtLogoPath.Clear()

        ' إعدادات البريد الإلكتروني
        _txtSmtpServer.Clear()
        _numSmtpPort.Value = 587
        _txtEmailUsername.Clear()
        _txtEmailPassword.Clear()
        _chkEnableSSL.Checked = True
    End Sub

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' حفظ الإعدادات
    ''' </summary>
    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If SaveSettings() Then
            MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.DialogResult = DialogResult.OK
            Me.Close()
        End If
    End Sub

    ''' <summary>
    ''' استعادة الإعدادات الافتراضية
    ''' </summary>
    Private Sub BtnReset_Click(sender As Object, e As EventArgs)
        If MessageBox.Show("هل تريد استعادة الإعدادات الافتراضية؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            ResetToDefaults()
        End If
    End Sub

    ''' <summary>
    ''' إلغاء العملية
    ''' </summary>
    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    ''' <summary>
    ''' استعراض ملف الشعار
    ''' </summary>
    Private Sub BtnBrowseLogo_Click(sender As Object, e As EventArgs)
        Using openFileDialog As New OpenFileDialog()
            openFileDialog.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.bmp;*.gif"
            openFileDialog.Title = "اختر ملف الشعار"

            If openFileDialog.ShowDialog() = DialogResult.OK Then
                _txtLogoPath.Text = openFileDialog.FileName
            End If
        End Using
    End Sub

    ''' <summary>
    ''' اختبار إعدادات البريد الإلكتروني
    ''' </summary>
    Private Sub BtnTestEmail_Click(sender As Object, e As EventArgs)
        Try
            ' التحقق من الحقول المطلوبة
            If String.IsNullOrWhiteSpace(_txtSmtpServer.Text) OrElse String.IsNullOrWhiteSpace(_txtEmailUsername.Text) Then
                MessageBox.Show("يرجى ملء جميع الحقول المطلوبة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            ' محاولة إرسال بريد تجريبي
            MessageBox.Show("سيتم تنفيذ اختبار البريد الإلكتروني قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في اختبار البريد الإلكتروني: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' التحقق من الأرقام فقط
    ''' </summary>
    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If

        Dim textBox = DirectCast(sender, TextBox)
        If e.KeyChar = "."c AndAlso textBox.Text.Contains(".") Then
            e.Handled = True
        End If
    End Sub

#End Region

End Class
