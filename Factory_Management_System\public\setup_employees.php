<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول الأقسام
        $db->query("CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_employee_code (employee_code),
            INDEX idx_department (department_id),
            INDEX idx_status (status)
        )");
        
        // إنشاء جدول الحضور والانصراف
        $db->query("CREATE TABLE IF NOT EXISTS attendance (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            attendance_date DATE NOT NULL,
            check_in_time TIME,
            check_out_time TIME,
            break_duration INT DEFAULT 0,
            total_hours DECIMAL(4,2) DEFAULT 0.00,
            overtime_hours DECIMAL(4,2) DEFAULT 0.00,
            status ENUM('present', 'absent', 'late', 'half_day', 'holiday') DEFAULT 'present',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_employee_date (employee_id, attendance_date),
            INDEX idx_employee_date (employee_id, attendance_date),
            INDEX idx_attendance_date (attendance_date)
        )");
        
        // إنشاء جدول الرواتب
        $db->query("CREATE TABLE IF NOT EXISTS payroll (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            pay_period_start DATE NOT NULL,
            pay_period_end DATE NOT NULL,
            basic_salary DECIMAL(10,2) DEFAULT 0.00,
            overtime_amount DECIMAL(10,2) DEFAULT 0.00,
            bonus DECIMAL(10,2) DEFAULT 0.00,
            allowances DECIMAL(10,2) DEFAULT 0.00,
            deductions DECIMAL(10,2) DEFAULT 0.00,
            gross_salary DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            net_salary DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('draft', 'approved', 'paid') DEFAULT 'draft',
            payment_date DATE,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_employee_period (employee_id, pay_period_start, pay_period_end),
            INDEX idx_payment_date (payment_date)
        )");
        
        // إنشاء جدول المصاريف العامة
        $db->query("CREATE TABLE IF NOT EXISTS general_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            category ENUM('utilities', 'rent', 'maintenance', 'office', 'marketing', 'travel', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            department_id INT,
            vendor_name VARCHAR(200),
            invoice_number VARCHAR(100),
            payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') DEFAULT 'cash',
            status ENUM('pending', 'approved', 'paid', 'rejected') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_category (category),
            INDEX idx_expense_date (expense_date),
            INDEX idx_department (department_id)
        )");
        
        // إنشاء جدول مصاريف الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            production_order_id INT,
            category ENUM('labor', 'utilities', 'maintenance', 'materials', 'overhead', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            employee_id INT,
            cost_center VARCHAR(100),
            allocation_percentage DECIMAL(5,2) DEFAULT 100.00,
            status ENUM('pending', 'approved', 'allocated') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_production_order (production_order_id),
            INDEX idx_category (category),
            INDEX idx_expense_date (expense_date),
            INDEX idx_employee (employee_id)
        )");
        
        // إضافة الأقسام الافتراضية
        $departments = [
            ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
            ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
            ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
            ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
            ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
            ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
            ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
            ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
        ];
        
        foreach ($departments as $dept) {
            $existing = $db->fetchOne("SELECT id FROM departments WHERE name = ?", [$dept[0]]);
            if (!$existing) {
                $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
            }
        }
        
        // إضافة موظفين تجريبيين
        $employees = [
            ['EMP001', 'أحمد محمد علي', '<EMAIL>', '07701234567', 'بغداد - الكرادة', '12345678901', '1985-05-15', '2020-01-01', 1, 'مدير عام', 2000000.00, 0.00, 0.00],
            ['EMP002', 'فاطمة حسن محمود', '<EMAIL>', '07709876543', 'بغداد - الجادرية', '12345678902', '1990-08-20', '2021-03-15', 2, 'مشرف إنتاج', 1200000.00, 15000.00, 20000.00],
            ['EMP003', 'محمد عبد الله سالم', '<EMAIL>', '07751234567', 'بغداد - المنصور', '12345678903', '1988-12-10', '2019-06-01', 3, 'مندوب مبيعات', 800000.00, 12000.00, 15000.00],
            ['EMP004', 'سارة أحمد حسن', '<EMAIL>', '07781234567', 'بغداد - الكاظمية', '12345678904', '1992-03-25', '2022-01-10', 4, 'محاسب', 1000000.00, 0.00, 0.00],
            ['EMP005', 'علي حسين محمد', '<EMAIL>', '07791234567', 'بغداد - الدورة', '12345678905', '1987-07-30', '2020-09-01', 2, 'عامل إنتاج', 600000.00, 10000.00, 12000.00]
        ];
        
        foreach ($employees as $emp) {
            $existing = $db->fetchOne("SELECT id FROM employees WHERE employee_code = ?", [$emp[0]]);
            if (!$existing) {
                $db->query("INSERT INTO employees (employee_code, full_name, email, phone, address, national_id, birth_date, hire_date, department_id, position, salary, hourly_rate, overtime_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $emp);
            }
        }
        
        $db->commit();
        $success = 'تم إعداد نظام الموظفين والمصاريف بنجاح! تم إنشاء الجداول وإضافة البيانات التجريبية.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إعداد نظام الموظفين: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tables = ['departments', 'employees', 'attendance', 'payroll', 'general_expenses', 'production_expenses'];
$tableStatus = [];

foreach ($tables as $table) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        $tableStatus[$table] = !empty($result);
    } catch (Exception $e) {
        $tableStatus[$table] = false;
    }
}

$allTablesExist = !in_array(false, $tableStatus);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام الموظفين - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .table-status {
            font-size: 0.9rem;
        }
        .status-icon {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-users fa-2x mb-3"></i><br>إعداد نظام الموظفين والمصاريف</h1>
            <p class="mb-0">إعداد جداول وبيانات نظام إدارة الموظفين والمصاريف</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجداول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة جداول النظام</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-status">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $tableDescriptions = [
                                    'departments' => 'جدول الأقسام',
                                    'employees' => 'جدول الموظفين',
                                    'attendance' => 'جدول الحضور والانصراف',
                                    'payroll' => 'جدول الرواتب',
                                    'general_expenses' => 'جدول المصاريف العامة',
                                    'production_expenses' => 'جدول مصاريف الإنتاج'
                                ];
                                
                                foreach ($tableDescriptions as $table => $description):
                                    $exists = $tableStatus[$table] ?? false;
                                ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <i class="fas fa-check-circle text-success status-icon"></i> موجود
                                            <?php else: ?>
                                                <i class="fas fa-times-circle text-danger status-icon"></i> غير موجود
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $description ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- البيانات التجريبية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-building me-2"></i>الأقسام</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-users text-primary me-2"></i>الإدارة العامة</li>
                                <li><i class="fas fa-industry text-success me-2"></i>الإنتاج</li>
                                <li><i class="fas fa-chart-line text-warning me-2"></i>المبيعات والتسويق</li>
                                <li><i class="fas fa-calculator text-info me-2"></i>المحاسبة والمالية</li>
                                <li><i class="fas fa-user-tie text-secondary me-2"></i>الموارد البشرية</li>
                                <li><i class="fas fa-warehouse text-dark me-2"></i>المخازن</li>
                                <li><i class="fas fa-tools text-danger me-2"></i>الصيانة</li>
                                <li><i class="fas fa-award text-success me-2"></i>ضمان الجودة</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-money-bill me-2"></i>أنواع المصاريف</h5>
                        </div>
                        <div class="card-body">
                            <h6>المصاريف العامة:</h6>
                            <ul class="list-unstyled mb-3">
                                <li><i class="fas fa-bolt text-warning me-2"></i>الكهرباء والمرافق</li>
                                <li><i class="fas fa-home text-primary me-2"></i>الإيجار</li>
                                <li><i class="fas fa-wrench text-info me-2"></i>الصيانة</li>
                                <li><i class="fas fa-briefcase text-secondary me-2"></i>المكتبية</li>
                            </ul>
                            <h6>مصاريف الإنتاج:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-user-hard-hat text-success me-2"></i>العمالة</li>
                                <li><i class="fas fa-cogs text-danger me-2"></i>المواد والمعدات</li>
                                <li><i class="fas fa-chart-pie text-warning me-2"></i>التكاليف الإضافية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!$allTablesExist): ?>
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-setup btn-lg me-3">
                            <i class="fas fa-play me-2"></i>إعداد النظام
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="employees.php" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-users me-2"></i>الموظفين
                </a>
                
                <a href="attendance.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-clock me-2"></i>الحضور
                </a>
                
                <a href="expenses.php" class="btn btn-warning btn-lg">
                    <i class="fas fa-money-bill me-2"></i>المصاريف
                </a>
            </div>

            <?php if ($allTablesExist): ?>
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>النظام جاهز!</strong> يمكنك الآن إدارة الموظفين والحضور والرواتب والمصاريف.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
