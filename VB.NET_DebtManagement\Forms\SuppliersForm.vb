Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إدارة الموردين - Suppliers Management Form
''' </summary>
Public Class SuppliersForm
    Inherits Form
    
#Region "Fields"
    
    Private _context As DebtContext
    Private _currentUser As User
    
    ' عناصر التحكم الرئيسية
    Private _toolStrip As ToolStrip
    Private _searchPanel As Panel
    Private _dataGridView As DataGridView
    Private _statusStrip As StatusStrip
    
    ' أزرار شريط الأدوات
    Private _btnAdd As ToolStripButton
    Private _btnEdit As ToolStripButton
    Private _btnDelete As ToolStripButton
    Private _btnRefresh As ToolStripButton
    Private _btnExport As ToolStripButton
    Private _btnPrint As ToolStripButton
    
    ' عناصر البحث
    Private _txtSearch As TextBox
    Private _cmbSearchType As ComboBox
    Private _cmbSupplierType As ComboBox
    Private _btnSearch As Button
    Private _btnClearSearch As Button
    
    ' شريط الحالة
    Private _lblStatus As ToolStripStatusLabel
    Private _lblCount As ToolStripStatusLabel
    
    ' متغيرات أخرى
    Private _selectedSupplier As Supplier
    
#End Region

#Region "Constructor"
    
    ''' <summary>
    ''' منشئ نموذج إدارة الموردين
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        
        InitializeComponent()
        SetupForm()
        SetupToolStrip()
        SetupSearchPanel()
        SetupDataGridView()
        SetupStatusStrip()
        LoadSuppliers()
    End Sub
    
#End Region

#Region "Form Setup"
    
    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "إدارة الموردين"
        Me.Size = New Size(1300, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.SuppliersIcon
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolStrip()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }
        
        ' إنشاء الأزرار
        _btnAdd = New ToolStripButton With {
            .Text = "إضافة مورد",
            .Image = My.Resources.AddIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "إضافة مورد جديد"
        }
        
        _btnEdit = New ToolStripButton With {
            .Text = "تعديل",
            .Image = My.Resources.EditIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تعديل المورد المحدد",
            .Enabled = False
        }
        
        _btnDelete = New ToolStripButton With {
            .Text = "حذف",
            .Image = My.Resources.DeleteIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "حذف المورد المحدد",
            .Enabled = False
        }
        
        _btnRefresh = New ToolStripButton With {
            .Text = "تحديث",
            .Image = My.Resources.RefreshIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحديث قائمة الموردين"
        }
        
        _btnExport = New ToolStripButton With {
            .Text = "تصدير",
            .Image = My.Resources.ExportIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تصدير قائمة الموردين"
        }
        
        _btnPrint = New ToolStripButton With {
            .Text = "طباعة",
            .Image = My.Resources.PrintIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "طباعة قائمة الموردين"
        }
        
        ' إضافة الأزرار لشريط الأدوات
        _toolStrip.Items.AddRange({
            _btnAdd,
            New ToolStripSeparator(),
            _btnEdit,
            _btnDelete,
            New ToolStripSeparator(),
            _btnRefresh,
            New ToolStripSeparator(),
            _btnExport,
            _btnPrint
        })
        
        ' ربط الأحداث
        AddHandler _btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler _btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler _btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler _btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler _btnExport.Click, AddressOf BtnExport_Click
        AddHandler _btnPrint.Click, AddressOf BtnPrint_Click
        
        Me.Controls.Add(_toolStrip)
    End Sub
    
    ''' <summary>
    ''' إعداد لوحة البحث
    ''' </summary>
    Private Sub SetupSearchPanel()
        _searchPanel = New Panel With {
            .Height = 80,
            .Dock = DockStyle.Top,
            .BackColor = Color.White,
            .Padding = New Padding(10)
        }
        
        ' الصف الأول - البحث العام
        Dim lblSearch As New Label With {
            .Text = "البحث:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 25),
            .Location = New Point(Me.Width - 70, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        _cmbSearchType = New ComboBox With {
            .Size = New Size(120, 25),
            .Location = New Point(Me.Width - 200, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular)
        }
        _cmbSearchType.Items.AddRange({"الاسم", "الهاتف", "البريد الإلكتروني", "العنوان", "الرقم الضريبي"})
        _cmbSearchType.SelectedIndex = 0
        
        _txtSearch = New TextBox With {
            .Size = New Size(250, 25),
            .Location = New Point(Me.Width - 460, 15),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .PlaceholderText = "ادخل كلمة البحث..."
        }
        
        _btnSearch = New Button With {
            .Text = "بحث",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 540, 13),
            .BackColor = Color.FromArgb(0, 123, 255),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnSearch.FlatAppearance.BorderSize = 0
        
        _btnClearSearch = New Button With {
            .Text = "مسح",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 620, 13),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnClearSearch.FlatAppearance.BorderSize = 0
        
        ' الصف الثاني - فلترة حسب نوع المورد
        Dim lblSupplierType As New Label With {
            .Text = "نوع المورد:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(80, 25),
            .Location = New Point(Me.Width - 90, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        _cmbSupplierType = New ComboBox With {
            .Size = New Size(150, 25),
            .Location = New Point(Me.Width - 250, 45),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular)
        }
        _cmbSupplierType.Items.AddRange({"الكل", "شركة", "فرد", "مؤسسة", "أخرى"})
        _cmbSupplierType.SelectedIndex = 0
        
        ' إضافة العناصر للوحة
        _searchPanel.Controls.AddRange({
            lblSearch, _cmbSearchType, _txtSearch, _btnSearch, _btnClearSearch,
            lblSupplierType, _cmbSupplierType
        })
        
        ' ربط الأحداث
        AddHandler _txtSearch.KeyDown, AddressOf TxtSearch_KeyDown
        AddHandler _btnSearch.Click, AddressOf BtnSearch_Click
        AddHandler _btnClearSearch.Click, AddressOf BtnClearSearch_Click
        AddHandler _cmbSupplierType.SelectedIndexChanged, AddressOf CmbSupplierType_SelectedIndexChanged
        
        Me.Controls.Add(_searchPanel)
    End Sub
    
    ''' <summary>
    ''' إعداد شبكة البيانات
    ''' </summary>
    Private Sub SetupDataGridView()
        _dataGridView = New DataGridView With {
            .Dock = DockStyle.Fill,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            .ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.FromArgb(52, 58, 64),
                .ForeColor = Color.White,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .ColumnHeadersHeight = 40,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.White,
                .ForeColor = Color.FromArgb(33, 37, 41),
                .Font = New Font("Segoe UI", 9, FontStyle.Regular),
                .SelectionBackColor = Color.FromArgb(0, 123, 255),
                .SelectionForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .EnableHeadersVisualStyles = False,
            .GridColor = Color.FromArgb(222, 226, 230),
            .RowHeadersVisible = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .MultiSelect = False,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .RightToLeft = RightToLeft.Yes
        }
        
        ' إضافة الأعمدة
        _dataGridView.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Id", .HeaderText = "المعرف", .Visible = False},
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم المورد", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "SupplierType", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Phone", .HeaderText = "رقم الهاتف", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "Email", .HeaderText = "البريد الإلكتروني", .FillWeight = 18},
            New DataGridViewTextBoxColumn With {.Name = "Address", .HeaderText = "العنوان", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "TaxNumber", .HeaderText = "الرقم الضريبي", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "BalanceIQD", .HeaderText = "الرصيد (د.ع)", .FillWeight = 8},
            New DataGridViewTextBoxColumn With {.Name = "BalanceUSD", .HeaderText = "الرصيد ($)", .FillWeight = 7}
        })
        
        ' ربط الأحداث
        AddHandler _dataGridView.SelectionChanged, AddressOf DataGridView_SelectionChanged
        AddHandler _dataGridView.CellDoubleClick, AddressOf DataGridView_CellDoubleClick
        AddHandler _dataGridView.CellFormatting, AddressOf DataGridView_CellFormatting
        
        Me.Controls.Add(_dataGridView)
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الحالة
    ''' </summary>
    Private Sub SetupStatusStrip()
        _statusStrip = New StatusStrip With {
            .BackColor = Color.FromArgb(248, 249, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }
        
        _lblStatus = New ToolStripStatusLabel With {
            .Text = "جاهز",
            .Spring = True,
            .TextAlign = ContentAlignment.MiddleLeft
        }
        
        _lblCount = New ToolStripStatusLabel With {
            .Text = "عدد الموردين: 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }
        
        _statusStrip.Items.AddRange({_lblStatus, _lblCount})
        Me.Controls.Add(_statusStrip)
    End Sub
    
#End Region

#Region "Data Methods"
    
    ''' <summary>
    ''' تحميل قائمة الموردين
    ''' </summary>
    Private Sub LoadSuppliers(Optional searchText As String = "", Optional searchType As String = "الاسم", Optional supplierType As String = "الكل")
        Try
            _lblStatus.Text = "جاري تحميل البيانات..."
            Application.DoEvents()
            
            Dim query = _context.Suppliers.Where(Function(s) s.IsActive)
            
            ' تطبيق البحث
            If Not String.IsNullOrWhiteSpace(searchText) Then
                Select Case searchType
                    Case "الاسم"
                        query = query.Where(Function(s) s.Name.Contains(searchText))
                    Case "الهاتف"
                        query = query.Where(Function(s) s.Phone.Contains(searchText))
                    Case "البريد الإلكتروني"
                        query = query.Where(Function(s) s.Email.Contains(searchText))
                    Case "العنوان"
                        query = query.Where(Function(s) s.Address.Contains(searchText))
                    Case "الرقم الضريبي"
                        query = query.Where(Function(s) s.TaxNumber.Contains(searchText))
                End Select
            End If
            
            ' تطبيق فلترة نوع المورد
            If supplierType <> "الكل" Then
                query = query.Where(Function(s) s.SupplierType = supplierType)
            End If
            
            Dim suppliers = query.OrderBy(Function(s) s.Name).ToList()
            
            ' مسح البيانات الحالية
            _dataGridView.Rows.Clear()
            
            ' إضافة البيانات الجديدة
            For Each supplier In suppliers
                Dim row As New DataGridViewRow()
                row.CreateCells(_dataGridView)
                
                row.Cells("Id").Value = supplier.Id
                row.Cells("Name").Value = supplier.Name
                row.Cells("SupplierType").Value = supplier.SupplierType
                row.Cells("Phone").Value = supplier.Phone
                row.Cells("Email").Value = supplier.Email
                row.Cells("Address").Value = supplier.Address
                row.Cells("TaxNumber").Value = supplier.TaxNumber
                row.Cells("BalanceIQD").Value = supplier.CurrentBalanceIQD
                row.Cells("BalanceUSD").Value = supplier.CurrentBalanceUSD
                
                row.Tag = supplier
                _dataGridView.Rows.Add(row)
            Next
            
            ' تحديث شريط الحالة
            _lblCount.Text = $"عدد الموردين: {suppliers.Count}"
            _lblStatus.Text = "تم تحميل البيانات بنجاح"
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            _lblStatus.Text = "خطأ في تحميل البيانات"
        End Try
    End Sub
    
    ''' <summary>
    ''' حذف مورد
    ''' </summary>
    Private Sub DeleteSupplier(supplier As Supplier)
        Try
            ' التحقق من وجود معاملات مرتبطة
            Dim hasTransactions = _context.Transactions.Any(Function(t) t.EntityType = "Supplier" AndAlso t.EntityId = supplier.Id)
            
            If hasTransactions Then
                If MessageBox.Show("هذا المورد لديه معاملات مرتبطة. هل تريد إلغاء تفعيله بدلاً من حذفه؟", 
                                 "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    supplier.IsActive = False
                    supplier.UpdatedAt = DateTime.Now
                    supplier.UpdatedBy = _currentUser.Id
                    _context.SaveChanges()
                    
                    MessageBox.Show("تم إلغاء تفعيل المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadSuppliers()
                End If
            Else
                If MessageBox.Show($"هل أنت متأكد من حذف المورد '{supplier.Name}'؟", 
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    _context.Suppliers.Remove(supplier)
                    _context.SaveChanges()
                    
                    MessageBox.Show("تم حذف المورد بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadSuppliers()
                End If
            End If
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
#End Region

#Region "Event Handlers"
    
    ''' <summary>
    ''' إضافة مورد جديد
    ''' </summary>
    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim addForm As New SupplierAddEditForm(_context, _currentUser)
        If addForm.ShowDialog() = DialogResult.OK Then
            LoadSuppliers()
        End If
    End Sub
    
    ''' <summary>
    ''' تعديل المورد المحدد
    ''' </summary>
    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If _selectedSupplier IsNot Nothing Then
            Dim editForm As New SupplierAddEditForm(_context, _currentUser, _selectedSupplier)
            If editForm.ShowDialog() = DialogResult.OK Then
                LoadSuppliers()
            End If
        End If
    End Sub
    
    ''' <summary>
    ''' حذف المورد المحدد
    ''' </summary>
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If _selectedSupplier IsNot Nothing Then
            DeleteSupplier(_selectedSupplier)
        End If
    End Sub
    
    ''' <summary>
    ''' تحديث قائمة الموردين
    ''' </summary>
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadSuppliers()
    End Sub
    
    ''' <summary>
    ''' تصدير قائمة الموردين
    ''' </summary>
    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تنفيذ ميزة التصدير قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    ''' <summary>
    ''' طباعة قائمة الموردين
    ''' </summary>
    Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تنفيذ ميزة الطباعة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    ''' <summary>
    ''' البحث في قائمة الموردين
    ''' </summary>
    Private Sub BtnSearch_Click(sender As Object, e As EventArgs)
        LoadSuppliers(_txtSearch.Text.Trim(), _cmbSearchType.Text, _cmbSupplierType.Text)
    End Sub
    
    ''' <summary>
    ''' مسح البحث
    ''' </summary>
    Private Sub BtnClearSearch_Click(sender As Object, e As EventArgs)
        _txtSearch.Clear()
        _cmbSearchType.SelectedIndex = 0
        _cmbSupplierType.SelectedIndex = 0
        LoadSuppliers()
    End Sub
    
    ''' <summary>
    ''' تغيير نوع المورد
    ''' </summary>
    Private Sub CmbSupplierType_SelectedIndexChanged(sender As Object, e As EventArgs)
        LoadSuppliers(_txtSearch.Text.Trim(), _cmbSearchType.Text, _cmbSupplierType.Text)
    End Sub
    
    ''' <summary>
    ''' البحث عند الضغط على Enter
    ''' </summary>
    Private Sub TxtSearch_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            BtnSearch_Click(sender, e)
        End If
    End Sub
    
    ''' <summary>
    ''' تغيير التحديد في الشبكة
    ''' </summary>
    Private Sub DataGridView_SelectionChanged(sender As Object, e As EventArgs)
        If _dataGridView.SelectedRows.Count > 0 Then
            _selectedSupplier = DirectCast(_dataGridView.SelectedRows(0).Tag, Supplier)
            _btnEdit.Enabled = True
            _btnDelete.Enabled = True
        Else
            _selectedSupplier = Nothing
            _btnEdit.Enabled = False
            _btnDelete.Enabled = False
        End If
    End Sub
    
    ''' <summary>
    ''' النقر المزدوج لتعديل المورد
    ''' </summary>
    Private Sub DataGridView_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(sender, e)
        End If
    End Sub
    
    ''' <summary>
    ''' تنسيق خلايا الشبكة
    ''' </summary>
    Private Sub DataGridView_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs)
        If e.ColumnIndex = _dataGridView.Columns("BalanceIQD").Index AndAlso e.Value IsNot Nothing Then
            Dim balance As Decimal = Convert.ToDecimal(e.Value)
            e.Value = $"{balance:N0} د.ع"
            
            If balance > 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69)
            ElseIf balance < 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69)
            End If
            
        ElseIf e.ColumnIndex = _dataGridView.Columns("BalanceUSD").Index AndAlso e.Value IsNot Nothing Then
            Dim balance As Decimal = Convert.ToDecimal(e.Value)
            e.Value = $"${balance:N2}"
            
            If balance > 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69)
            ElseIf balance < 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69)
            End If
        End If
    End Sub
    
#End Region

End Class
