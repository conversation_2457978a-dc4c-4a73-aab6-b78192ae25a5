<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول أوامر الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_orders (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_number VARCHAR(50) NOT NULL UNIQUE,
            product_id INT NOT NULL,
            quantity_to_produce DECIMAL(10,3) NOT NULL,
            quantity_produced DECIMAL(10,3) DEFAULT 0.000,
            warehouse_id INT NOT NULL,
            start_date DATE,
            expected_end_date DATE,
            actual_end_date DATE,
            status ENUM('planned', 'in_progress', 'completed', 'cancelled') DEFAULT 'planned',
            total_cost DECIMAL(12,2) DEFAULT 0.00,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول وصفات الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_recipes (
            id INT PRIMARY KEY AUTO_INCREMENT,
            product_id INT NOT NULL,
            material_id INT NOT NULL,
            quantity_required DECIMAL(10,3) NOT NULL,
            cost_per_unit DECIMAL(10,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            UNIQUE KEY unique_product_material (product_id, material_id)
        )");
        
        // إنشاء جدول تفاصيل أوامر الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_order_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            order_id INT NOT NULL,
            material_id INT NOT NULL,
            quantity_required DECIMAL(10,3) NOT NULL,
            quantity_consumed DECIMAL(10,3) DEFAULT 0.000,
            unit_cost DECIMAL(10,2) DEFAULT 0.00,
            total_cost DECIMAL(12,2) GENERATED ALWAYS AS (quantity_consumed * unit_cost) STORED
        )");
        
        // إنشاء جدول فئات الأصناف إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الأصناف إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول المخازن إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إضافة فئات الأصناف
        $categories = [
            ['مواد خام', 'المواد الأولية المستخدمة في الإنتاج'],
            ['منتجات نهائية', 'المنتجات الجاهزة للبيع'],
            ['منتجات نصف مصنعة', 'منتجات في مراحل الإنتاج'],
            ['مواد استهلاكية', 'مواد مساعدة في العملية الإنتاجية']
        ];
        
        foreach ($categories as $category) {
            $existing = $db->fetchOne("SELECT id FROM item_categories WHERE name = ?", [$category[0]]);
            if (!$existing) {
                $db->query("INSERT INTO item_categories (name, description) VALUES (?, ?)", $category);
            }
        }
        
        // إضافة المخازن
        $warehouses = [
            ['المخزن الرئيسي', 'الطابق الأرضي - المبنى الرئيسي'],
            ['مخزن المواد الخام', 'الطابق الأول - المبنى الإنتاجي'],
            ['مخزن المنتجات النهائية', 'الطابق الثاني - المبنى الرئيسي']
        ];
        
        foreach ($warehouses as $warehouse) {
            $existing = $db->fetchOne("SELECT id FROM warehouses WHERE name = ?", [$warehouse[0]]);
            if (!$existing) {
                $db->query("INSERT INTO warehouses (name, location) VALUES (?, ?)", $warehouse);
            }
        }
        
        // إضافة أصناف تجريبية
        $items = [
            ['RAW001', 'مادة خام أ', 'مادة خام للإنتاج', 1, 'كيلو', 'raw_material', 50.00, 0.00],
            ['RAW002', 'مادة خام ب', 'مادة خام للإنتاج', 1, 'لتر', 'raw_material', 30.00, 0.00],
            ['RAW003', 'مادة خام ج', 'مادة خام للإنتاج', 1, 'متر', 'raw_material', 25.00, 0.00],
            ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100.00, 150.00],
            ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80.00, 120.00],
            ['SEMI001', 'منتج نصف مصنع أ', 'منتج في مرحلة التصنيع', 3, 'قطعة', 'semi_finished', 60.00, 0.00]
        ];
        
        foreach ($items as $item) {
            $existing = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$item[0]]);
            if (!$existing) {
                $db->query("INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)", $item);
            }
        }
        
        // إضافة وصفات إنتاج تجريبية
        $recipes = [
            // وصفة المنتج النهائي أ
            [4, 1, 2.5, 50.00], // منتج أ يحتاج 2.5 كيلو من مادة خام أ
            [4, 2, 1.0, 30.00], // منتج أ يحتاج 1 لتر من مادة خام ب
            [4, 3, 0.5, 25.00], // منتج أ يحتاج 0.5 متر من مادة خام ج
            
            // وصفة المنتج النهائي ب
            [5, 1, 1.8, 50.00], // منتج ب يحتاج 1.8 كيلو من مادة خام أ
            [5, 2, 0.8, 30.00], // منتج ب يحتاج 0.8 لتر من مادة خام ب
        ];
        
        foreach ($recipes as $recipe) {
            $existing = $db->fetchOne("SELECT id FROM production_recipes WHERE product_id = ? AND material_id = ?", [$recipe[0], $recipe[1]]);
            if (!$existing) {
                $db->query("INSERT INTO production_recipes (product_id, material_id, quantity_required, cost_per_unit) VALUES (?, ?, ?, ?)", $recipe);
            }
        }
        
        $db->commit();
        $success = 'تم إعداد نظام الإنتاج بنجاح! تم إنشاء الجداول وإضافة البيانات التجريبية.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إعداد نظام الإنتاج: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tables = ['production_orders', 'production_recipes', 'production_order_details', 'items', 'warehouses', 'item_categories'];
$tableStatus = [];

foreach ($tables as $table) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        $tableStatus[$table] = !empty($result);
    } catch (Exception $e) {
        $tableStatus[$table] = false;
    }
}

$allTablesExist = !in_array(false, $tableStatus);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .table-status {
            font-size: 0.9rem;
        }
        .status-icon {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-industry fa-2x mb-3"></i><br>إعداد نظام الإنتاج</h1>
            <p class="mb-0">إعداد جداول وبيانات نظام الإنتاج</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجداول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة جداول الإنتاج</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-status">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $tableDescriptions = [
                                    'production_orders' => 'جدول أوامر الإنتاج',
                                    'production_recipes' => 'جدول وصفات الإنتاج',
                                    'production_order_details' => 'جدول تفاصيل أوامر الإنتاج',
                                    'items' => 'جدول الأصناف',
                                    'warehouses' => 'جدول المخازن',
                                    'item_categories' => 'جدول فئات الأصناف'
                                ];
                                
                                foreach ($tableDescriptions as $table => $description):
                                    $exists = $tableStatus[$table] ?? false;
                                ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <i class="fas fa-check-circle text-success status-icon"></i> موجود
                                            <?php else: ?>
                                                <i class="fas fa-times-circle text-danger status-icon"></i> غير موجود
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $description ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!$allTablesExist): ?>
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-setup btn-lg me-3">
                            <i class="fas fa-play me-2"></i>إعداد نظام الإنتاج
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="new_production_order.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>أمر إنتاج جديد
                </a>
                
                <a href="production.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>أوامر الإنتاج
                </a>
            </div>

            <?php if ($allTablesExist): ?>
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>نظام الإنتاج جاهز!</strong> يمكنك الآن إنشاء أوامر إنتاج جديدة.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
