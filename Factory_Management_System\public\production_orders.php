<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$priority = $_GET['priority'] ?? '';
$department = $_GET['department'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

// بناء استعلام البحث
$whereConditions = ["1=1"];
$params = [];

if ($search) {
    $whereConditions[] = "(po.order_number LIKE ? OR i.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status) {
    $whereConditions[] = "po.status = ?";
    $params[] = $status;
}

if ($priority) {
    $whereConditions[] = "po.priority = ?";
    $params[] = $priority;
}

if ($department) {
    $whereConditions[] = "po.department_id = ?";
    $params[] = $department;
}

if ($dateFrom) {
    $whereConditions[] = "po.start_date >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "po.due_date <= ?";
    $params[] = $dateTo;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب أوامر الإنتاج
try {
    $orders = $db->fetchAll("
        SELECT po.*, i.name as product_name, i.unit as product_unit,
               d.name as department_name, e.full_name as supervisor_name,
               u.full_name as created_by_name
        FROM production_orders po
        LEFT JOIN items i ON po.product_id = i.id
        LEFT JOIN departments d ON po.department_id = d.id
        LEFT JOIN employees e ON po.supervisor_id = e.id
        LEFT JOIN users u ON po.created_by = u.id
        WHERE $whereClause
        ORDER BY po.created_at DESC
        LIMIT 50
    ", $params);
} catch (Exception $e) {
    $orders = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// جلب الأقسام
try {
    $departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $departments = [];
}

// إحصائيات أوامر الإنتاج
try {
    $stats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN priority = 'urgent' THEN 1 END) as urgent_orders,
            SUM(estimated_cost) as total_estimated_cost
        FROM production_orders
        WHERE start_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ");
} catch (Exception $e) {
    $stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'in_progress_orders' => 0,
        'completed_orders' => 0,
        'urgent_orders' => 0,
        'total_estimated_cost' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أوامر الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stats-card.pending { background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%); }
        .stats-card.in-progress { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .stats-card.completed { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .stats-card.urgent { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .order-row {
            transition: all 0.3s ease;
        }
        .order-row:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .priority-urgent { color: #dc3545; }
        .priority-high { color: #fd7e14; }
        .priority-normal { color: #28a745; }
        .priority-low { color: #6c757d; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-industry me-3"></i>أوامر الإنتاج</h1>
                    <p class="mb-0">إدارة أوامر الإنتاج والتصنيع</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add_production_order.php" class="btn btn-light me-2">
                        <i class="fas fa-plus me-2"></i>أمر جديد
                    </a>
                    <a href="production.php" class="btn btn-outline-light">
                        <i class="fas fa-cogs me-2"></i>الإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <br><a href="complete_fix.php" class="btn btn-sm btn-primary mt-2">إعداد النظام</a>
            </div>
        <?php endif; ?>

        <!-- إحصائيات أوامر الإنتاج -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="stats-card">
                    <h3><?= $stats['total_orders'] ?></h3>
                    <p class="mb-0">إجمالي الأوامر</p>
                    <small>(30 يوم)</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card pending">
                    <h3><?= $stats['pending_orders'] ?></h3>
                    <p class="mb-0">في الانتظار</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card in-progress">
                    <h3><?= $stats['in_progress_orders'] ?></h3>
                    <p class="mb-0">قيد التنفيذ</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card completed">
                    <h3><?= $stats['completed_orders'] ?></h3>
                    <p class="mb-0">مكتملة</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card urgent">
                    <h3><?= $stats['urgent_orders'] ?></h3>
                    <p class="mb-0">عاجلة</p>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stats-card">
                    <h3><?= number_format($stats['total_estimated_cost'], 0) ?></h3>
                    <p class="mb-0">التكلفة المقدرة</p>
                    <small>د.ع</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>البحث والفلترة</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">البحث</label>
                                <input type="text" name="search" class="form-control" 
                                       value="<?= htmlspecialchars($search) ?>" 
                                       placeholder="رقم الأمر أو اسم المنتج">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?= $status == 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                    <option value="in_progress" <?= $status == 'in_progress' ? 'selected' : '' ?>>قيد التنفيذ</option>
                                    <option value="completed" <?= $status == 'completed' ? 'selected' : '' ?>>مكتمل</option>
                                    <option value="cancelled" <?= $status == 'cancelled' ? 'selected' : '' ?>>ملغي</option>
                                    <option value="on_hold" <?= $status == 'on_hold' ? 'selected' : '' ?>>معلق</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الأولوية</label>
                                <select name="priority" class="form-select">
                                    <option value="">جميع الأولويات</option>
                                    <option value="low" <?= $priority == 'low' ? 'selected' : '' ?>>منخفضة</option>
                                    <option value="normal" <?= $priority == 'normal' ? 'selected' : '' ?>>عادية</option>
                                    <option value="high" <?= $priority == 'high' ? 'selected' : '' ?>>عالية</option>
                                    <option value="urgent" <?= $priority == 'urgent' ? 'selected' : '' ?>>عاجلة</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select name="department" class="form-select">
                                    <option value="">جميع الأقسام</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>" <?= $department == $dept['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($dept['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control" 
                                       value="<?= htmlspecialchars($dateFrom) ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control" 
                                       value="<?= htmlspecialchars($dateTo) ?>">
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة أوامر الإنتاج -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>أوامر الإنتاج (<?= count($orders) ?>)</h5>
                            <a href="production_report.php" class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i>تصدير
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($orders)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-industry fa-4x text-muted mb-3"></i>
                                <h5>لا توجد أوامر إنتاج</h5>
                                <p class="text-muted">ابدأ بإضافة أمر إنتاج جديد</p>
                                <a href="add_production_order.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>أمر جديد
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>رقم الأمر</th>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>التقدم</th>
                                            <th>تاريخ البدء</th>
                                            <th>تاريخ الاستحقاق</th>
                                            <th>الأولوية</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($orders as $order): ?>
                                            <tr class="order-row">
                                                <td>
                                                    <strong><?= htmlspecialchars($order['order_number']) ?></strong>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($order['product_name']) ?></strong>
                                                    <br><small class="text-muted"><?= htmlspecialchars($order['product_unit']) ?></small>
                                                </td>
                                                <td>
                                                    <div>
                                                        <strong><?= number_format($order['quantity_required'], 3) ?></strong>
                                                        <br><small class="text-success">منتج: <?= number_format($order['quantity_produced'], 3) ?></small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $progress = $order['quantity_required'] > 0 ? 
                                                        ($order['quantity_produced'] / $order['quantity_required']) * 100 : 0;
                                                    ?>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar" role="progressbar" 
                                                             style="width: <?= $progress ?>%">
                                                            <?= number_format($progress, 1) ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?= date('d/m/Y', strtotime($order['start_date'])) ?></td>
                                                <td>
                                                    <?= date('d/m/Y', strtotime($order['due_date'])) ?>
                                                    <?php if (strtotime($order['due_date']) < time() && $order['status'] != 'completed'): ?>
                                                        <br><small class="text-danger">متأخر</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $priorityNames = [
                                                        'low' => 'منخفضة',
                                                        'normal' => 'عادية',
                                                        'high' => 'عالية',
                                                        'urgent' => 'عاجلة'
                                                    ];
                                                    ?>
                                                    <span class="priority-<?= $order['priority'] ?>">
                                                        <i class="fas fa-flag"></i> <?= $priorityNames[$order['priority']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'pending' => 'secondary',
                                                        'in_progress' => 'info',
                                                        'completed' => 'success',
                                                        'cancelled' => 'danger',
                                                        'on_hold' => 'warning'
                                                    ];
                                                    $statusNames = [
                                                        'pending' => 'في الانتظار',
                                                        'in_progress' => 'قيد التنفيذ',
                                                        'completed' => 'مكتمل',
                                                        'cancelled' => 'ملغي',
                                                        'on_hold' => 'معلق'
                                                    ];
                                                    ?>
                                                    <span class="status-badge bg-<?= $statusColors[$order['status']] ?>">
                                                        <?= $statusNames[$order['status']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view_production_order.php?id=<?= $order['id'] ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_production_order.php?id=<?= $order['id'] ?>" 
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="print_production_order.php?id=<?= $order['id'] ?>" 
                                                           class="btn btn-sm btn-outline-info" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
