<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// معالجة طلب التقرير
$report_data = [];
$report_type = $_GET['type'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

if ($report_type) {
    try {
        switch ($report_type) {
            case 'transactions':
                $stmt = $pdo->prepare("
                    SELECT t.*, 
                           CASE 
                               WHEN t.entity_type = 'customer' THEN c.name
                               WHEN t.entity_type = 'supplier' THEN s.name
                           END as entity_name,
                           cb.name as cash_box_name
                    FROM transactions t
                    LEFT JOIN customers c ON t.entity_type = 'customer' AND t.entity_id = c.id
                    LEFT JOIN suppliers s ON t.entity_type = 'supplier' AND t.entity_id = s.id
                    LEFT JOIN cash_boxes cb ON t.cash_box_id = cb.id
                    WHERE t.transaction_date BETWEEN ? AND ?
                    ORDER BY t.transaction_date DESC, t.created_at DESC
                ");
                $stmt->execute([$date_from, $date_to]);
                $report_data = $stmt->fetchAll();
                break;

            case 'customers':
                $stmt = $pdo->prepare("
                    SELECT c.*,
                           (SELECT COUNT(*) FROM transactions WHERE entity_type = 'customer' AND entity_id = c.id AND transaction_date BETWEEN ? AND ?) as transaction_count,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'customer' AND entity_id = c.id AND transaction_date BETWEEN ? AND ? AND type = 'income' AND currency = 'IQD') as income_iqd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'customer' AND entity_id = c.id AND transaction_date BETWEEN ? AND ? AND type = 'expense' AND currency = 'IQD') as expense_iqd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'customer' AND entity_id = c.id AND transaction_date BETWEEN ? AND ? AND type = 'income' AND currency = 'USD') as income_usd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'customer' AND entity_id = c.id AND transaction_date BETWEEN ? AND ? AND type = 'expense' AND currency = 'USD') as expense_usd
                    FROM customers c
                    WHERE c.is_active = 1
                    ORDER BY c.name ASC
                ");
                $stmt->execute([$date_from, $date_to, $date_from, $date_to, $date_from, $date_to, $date_from, $date_to, $date_from, $date_to]);
                $report_data = $stmt->fetchAll();
                break;

            case 'suppliers':
                $stmt = $pdo->prepare("
                    SELECT s.*,
                           (SELECT COUNT(*) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id AND transaction_date BETWEEN ? AND ?) as transaction_count,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id AND transaction_date BETWEEN ? AND ? AND type = 'income' AND currency = 'IQD') as income_iqd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id AND transaction_date BETWEEN ? AND ? AND type = 'expense' AND currency = 'IQD') as expense_iqd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id AND transaction_date BETWEEN ? AND ? AND type = 'income' AND currency = 'USD') as income_usd,
                           (SELECT SUM(amount) FROM transactions WHERE entity_type = 'supplier' AND entity_id = s.id AND transaction_date BETWEEN ? AND ? AND type = 'expense' AND currency = 'USD') as expense_usd
                    FROM suppliers s
                    WHERE s.is_active = 1
                    ORDER BY s.name ASC
                ");
                $stmt->execute([$date_from, $date_to, $date_from, $date_to, $date_from, $date_to, $date_from, $date_to, $date_from, $date_to]);
                $report_data = $stmt->fetchAll();
                break;

            case 'cash_boxes':
                $stmt = $pdo->prepare("
                    SELECT cb.*,
                           c.name as currency_name, c.symbol as currency_symbol,
                           (SELECT COUNT(*) FROM transactions WHERE cash_box_id = cb.id AND transaction_date BETWEEN ? AND ?) as transaction_count,
                           (SELECT SUM(amount) FROM transactions WHERE cash_box_id = cb.id AND transaction_date BETWEEN ? AND ? AND type = 'income') as total_income,
                           (SELECT SUM(amount) FROM transactions WHERE cash_box_id = cb.id AND transaction_date BETWEEN ? AND ? AND type = 'expense') as total_expense
                    FROM cash_boxes cb
                    LEFT JOIN currencies c ON cb.currency_id = c.id
                    WHERE cb.is_active = 1
                    ORDER BY cb.name ASC
                ");
                $stmt->execute([$date_from, $date_to, $date_from, $date_to, $date_from, $date_to]);
                $report_data = $stmt->fetchAll();
                break;
        }
    } catch (PDOException $e) {
        error_log("خطأ في التقرير: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calculator me-2"></i>
                نظام إدارة الديون
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">التقارير</h1>
                    <?php if ($report_type && !empty($report_data)): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                تصدير Excel
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- نموذج اختيار التقرير -->
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">اختيار التقرير</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="type" class="form-label">نوع التقرير</label>
                                <select class="form-select" id="type" name="type" required>
                                    <option value="">اختر نوع التقرير</option>
                                    <option value="transactions" <?php echo $report_type === 'transactions' ? 'selected' : ''; ?>>تقرير المعاملات</option>
                                    <option value="customers" <?php echo $report_type === 'customers' ? 'selected' : ''; ?>>تقرير العملاء</option>
                                    <option value="suppliers" <?php echo $report_type === 'suppliers' ? 'selected' : ''; ?>>تقرير الموردين</option>
                                    <option value="cash_boxes" <?php echo $report_type === 'cash_boxes' ? 'selected' : ''; ?>>تقرير الصناديق</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block">
                                    <i class="fas fa-search me-1"></i>
                                    عرض التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- عرض التقرير -->
                <?php if ($report_type && !empty($report_data)): ?>
                <div class="card shadow" id="reportContent">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <?php
                            $report_titles = [
                                'transactions' => 'تقرير المعاملات',
                                'customers' => 'تقرير العملاء',
                                'suppliers' => 'تقرير الموردين',
                                'cash_boxes' => 'تقرير الصناديق'
                            ];
                            echo $report_titles[$report_type] ?? 'التقرير';
                            ?>
                            (من <?php echo $date_from; ?> إلى <?php echo $date_to; ?>)
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <?php if ($report_type === 'transactions'): ?>
                            <table class="table table-bordered" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>الطرف</th>
                                        <th>المبلغ</th>
                                        <th>العملة</th>
                                        <th>الصندوق</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d', strtotime($row['transaction_date'])); ?></td>
                                        <td>
                                            <span class="badge <?php echo $row['type'] == 'income' ? 'bg-success' : 'bg-danger'; ?>">
                                                <?php echo $row['type'] == 'income' ? 'وارد' : 'صادر'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($row['entity_name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo number_format($row['amount'], 2); ?></td>
                                        <td><?php echo $row['currency']; ?></td>
                                        <td><?php echo htmlspecialchars($row['cash_box_name'] ?? 'غير محدد'); ?></td>
                                        <td><?php echo htmlspecialchars($row['description'] ?: '-'); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            
                            <?php elseif ($report_type === 'customers'): ?>
                            <table class="table table-bordered" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>اسم العميل</th>
                                        <th>الهاتف</th>
                                        <th>عدد المعاملات</th>
                                        <th>وارد (دينار)</th>
                                        <th>صادر (دينار)</th>
                                        <th>وارد (دولار)</th>
                                        <th>صادر (دولار)</th>
                                        <th>الرصيد الحالي (دينار)</th>
                                        <th>الرصيد الحالي (دولار)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['phone'] ?: '-'); ?></td>
                                        <td><?php echo $row['transaction_count'] ?: 0; ?></td>
                                        <td><?php echo number_format($row['income_iqd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['expense_iqd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['income_usd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['expense_usd'] ?: 0, 2); ?></td>
                                        <td>
                                            <span class="<?php echo $row['current_balance_iqd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo number_format($row['current_balance_iqd'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $row['current_balance_usd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo number_format($row['current_balance_usd'], 2); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            
                            <?php elseif ($report_type === 'suppliers'): ?>
                            <table class="table table-bordered" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>اسم المورد</th>
                                        <th>الهاتف</th>
                                        <th>عدد المعاملات</th>
                                        <th>وارد (دينار)</th>
                                        <th>صادر (دينار)</th>
                                        <th>وارد (دولار)</th>
                                        <th>صادر (دولار)</th>
                                        <th>الرصيد الحالي (دينار)</th>
                                        <th>الرصيد الحالي (دولار)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['phone'] ?: '-'); ?></td>
                                        <td><?php echo $row['transaction_count'] ?: 0; ?></td>
                                        <td><?php echo number_format($row['income_iqd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['expense_iqd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['income_usd'] ?: 0, 2); ?></td>
                                        <td><?php echo number_format($row['expense_usd'] ?: 0, 2); ?></td>
                                        <td>
                                            <span class="<?php echo $row['current_balance_iqd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo number_format($row['current_balance_iqd'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $row['current_balance_usd'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo number_format($row['current_balance_usd'], 2); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            
                            <?php elseif ($report_type === 'cash_boxes'): ?>
                            <table class="table table-bordered" id="reportTable">
                                <thead>
                                    <tr>
                                        <th>اسم الصندوق</th>
                                        <th>العملة</th>
                                        <th>الرصيد الافتتاحي</th>
                                        <th>إجمالي الوارد</th>
                                        <th>إجمالي الصادر</th>
                                        <th>الرصيد الحالي</th>
                                        <th>عدد المعاملات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($report_data as $row): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                                        <td><?php echo htmlspecialchars($row['currency_name']); ?></td>
                                        <td><?php echo $row['currency_symbol']; ?><?php echo number_format($row['initial_balance'], 2); ?></td>
                                        <td><?php echo $row['currency_symbol']; ?><?php echo number_format($row['total_income'] ?: 0, 2); ?></td>
                                        <td><?php echo $row['currency_symbol']; ?><?php echo number_format($row['total_expense'] ?: 0, 2); ?></td>
                                        <td>
                                            <span class="<?php echo $row['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $row['currency_symbol']; ?><?php echo number_format($row['current_balance'], 2); ?>
                                            </span>
                                        </td>
                                        <td><?php echo $row['transaction_count'] ?: 0; ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <?php elseif ($report_type && empty($report_data)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد بيانات للفترة المحددة
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        function exportToExcel() {
            const table = document.getElementById('reportTable');
            if (!table) return;
            
            let csv = '';
            const rows = table.querySelectorAll('tr');
            
            rows.forEach(row => {
                const cols = row.querySelectorAll('td, th');
                const rowData = [];
                cols.forEach(col => {
                    rowData.push('"' + col.textContent.replace(/"/g, '""') + '"');
                });
                csv += rowData.join(',') + '\n';
            });
            
            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'report_' + new Date().toISOString().slice(0, 10) + '.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    </script>
</body>
</html>
