<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// إنشاء جدول الأصناف إذا لم يكن موجوداً
try {
    $db->query("CREATE TABLE IF NOT EXISTS items (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(200) NOT NULL,
        code VARCHAR(50) UNIQUE NOT NULL,
        type ENUM('product', 'raw_material', 'service') NOT NULL,
        category VARCHAR(100),
        unit VARCHAR(50) NOT NULL,
        cost_price DECIMAL(10,2) DEFAULT 0.00,
        selling_price DECIMAL(10,2) DEFAULT 0.00,
        description TEXT,
        barcode VARCHAR(100),
        min_stock_level DECIMAL(10,3) DEFAULT 0,
        max_stock_level DECIMAL(10,3) DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
    )");
} catch (Exception $e) {
    $error = "خطأ في إعداد قاعدة البيانات: " . $e->getMessage();
}

// معالجة إضافة صنف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_item'])) {
    try {
        $itemData = [
            'name' => trim($_POST['name']),
            'code' => trim($_POST['code']),
            'type' => $_POST['type'],
            'category' => trim($_POST['category'] ?? ''),
            'unit' => trim($_POST['unit']),
            'cost_price' => (float)($_POST['cost_price'] ?? 0),
            'selling_price' => (float)($_POST['selling_price'] ?? 0),
            'description' => trim($_POST['description'] ?? ''),
            'barcode' => trim($_POST['barcode'] ?? ''),
            'min_stock_level' => (float)($_POST['min_stock_level'] ?? 0),
            'max_stock_level' => (float)($_POST['max_stock_level'] ?? 0),
            'created_by' => $user['id']
        ];

        $db->insert('items', $itemData);
        $success = "تم إضافة الصنف بنجاح";

    } catch (Exception $e) {
        $error = "خطأ في إضافة الصنف: " . $e->getMessage();
    }
}

// معالجة تحديث الصنف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_item'])) {
    try {
        $itemId = (int)$_POST['item_id'];

        $updateData = [
            'name' => trim($_POST['name']),
            'code' => trim($_POST['code']),
            'type' => $_POST['type'],
            'category' => trim($_POST['category'] ?? ''),
            'unit' => trim($_POST['unit']),
            'cost_price' => (float)($_POST['cost_price'] ?? 0),
            'selling_price' => (float)($_POST['selling_price'] ?? 0),
            'description' => trim($_POST['description'] ?? ''),
            'barcode' => trim($_POST['barcode'] ?? ''),
            'min_stock_level' => (float)($_POST['min_stock_level'] ?? 0),
            'max_stock_level' => (float)($_POST['max_stock_level'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        $db->update('items', $updateData, ['id' => $itemId]);
        $success = "تم تحديث الصنف بنجاح";

    } catch (Exception $e) {
        $error = "خطأ في تحديث الصنف: " . $e->getMessage();
    }
}

// معالجة حذف الصنف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['delete_item'])) {
    try {
        $itemId = (int)$_POST['item_id'];

        // التحقق من وجود مخزون للصنف
        $inventoryCount = $db->fetchOne("SELECT COUNT(*) as count FROM inventory WHERE item_id = ?", [$itemId]);

        if ($inventoryCount['count'] > 0) {
            $error = "لا يمكن حذف الصنف لوجود مخزون مرتبط به";
        } else {
            $db->delete('items', ['id' => $itemId]);
            $success = "تم حذف الصنف بنجاح";
        }

    } catch (Exception $e) {
        $error = "خطأ في حذف الصنف: " . $e->getMessage();
    }
}

// جلب الأصناف
try {
    $items = $db->fetchAll("
        SELECT i.*,
               u.full_name as created_by_name,
               COALESCE(SUM(inv.quantity), 0) as total_stock
        FROM items i
        LEFT JOIN users u ON i.created_by = u.id
        LEFT JOIN inventory inv ON i.id = inv.item_id
        GROUP BY i.id
        ORDER BY i.name
    ");

} catch (Exception $e) {
    $items = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأصناف - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            direction: rtl;
        }

        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }

        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }

        .item-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #28a745;
            transition: all 0.3s ease;
        }

        .item-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .item-card.inactive {
            opacity: 0.7;
            border-left-color: #dc3545;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
        }

        .type-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .type-product { background: #d4edda; color: #155724; }
        .type-raw_material { background: #fff3cd; color: #856404; }
        .type-service { background: #d1ecf1; color: #0c5460; }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }

        .item-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-left: 1rem;
        }

        .price-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 0.5rem;
            text-align: center;
            margin: 0.5rem 0;
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            left: 30px;
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            text-decoration: none;
            box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-action:hover {
            transform: scale(1.1);
            color: white;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-cube fa-2x me-3"></i>إدارة الأصناف</h1>
                    <p class="mb-0 fs-5">إدارة المنتجات والمواد الخام والخدمات</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus me-2"></i>إضافة صنف
                    </button>
                    <a href="dashboard.php" class="btn btn-outline-light">
                        <i class="fas fa-home me-2"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة صنف -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-plus me-2"></i>إضافة صنف جديد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الصنف <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود الصنف <span class="text-danger">*</span></label>
                                <input type="text" name="code" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الصنف <span class="text-danger">*</span></label>
                                <select name="type" class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="product">منتج نهائي</option>
                                    <option value="raw_material">مادة خام</option>
                                    <option value="service">خدمة</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <input type="text" name="category" class="form-control" placeholder="مثال: إلكترونيات، أثاث">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة <span class="text-danger">*</span></label>
                                <input type="text" name="unit" class="form-control" placeholder="مثال: قطعة، كيلو، متر" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الباركود</label>
                                <input type="text" name="barcode" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر التكلفة (د.ع)</label>
                                <input type="number" name="cost_price" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع (د.ع)</label>
                                <input type="number" name="selling_price" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" name="min_stock_level" class="form-control" step="0.001" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأقصى للمخزون</label>
                                <input type="number" name="max_stock_level" class="form-control" step="0.001" min="0">
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" class="form-control" rows="3"
                                          placeholder="وصف مفصل عن الصنف"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="submit" name="add_item" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الصنف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل صنف -->
    <div class="modal fade" id="editItemModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>تعديل الصنف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editItemForm">
                    <input type="hidden" name="item_id" id="edit_item_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم الصنف <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="edit_name" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">كود الصنف <span class="text-danger">*</span></label>
                                <input type="text" name="code" id="edit_code" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">نوع الصنف <span class="text-danger">*</span></label>
                                <select name="type" id="edit_type" class="form-select" required>
                                    <option value="">اختر النوع</option>
                                    <option value="product">منتج نهائي</option>
                                    <option value="raw_material">مادة خام</option>
                                    <option value="service">خدمة</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة</label>
                                <input type="text" name="category" id="edit_category" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الوحدة <span class="text-danger">*</span></label>
                                <input type="text" name="unit" id="edit_unit" class="form-control" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الباركود</label>
                                <input type="text" name="barcode" id="edit_barcode" class="form-control">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر التكلفة (د.ع)</label>
                                <input type="number" name="cost_price" id="edit_cost_price" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">سعر البيع (د.ع)</label>
                                <input type="number" name="selling_price" id="edit_selling_price" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأدنى للمخزون</label>
                                <input type="number" name="min_stock_level" id="edit_min_stock_level" class="form-control" step="0.001" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الأقصى للمخزون</label>
                                <input type="number" name="max_stock_level" id="edit_max_stock_level" class="form-control" step="0.001" min="0">
                            </div>

                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="is_active" id="edit_is_active" class="form-check-input">
                                    <label class="form-check-label">الصنف نشط</label>
                                </div>
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف</label>
                                <textarea name="description" id="edit_description" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </button>
                        <button type="submit" name="update_item" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div class="modal fade" id="deleteItemModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-trash me-2"></i>تأكيد الحذف</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                        <h5>هل أنت متأكد من حذف الصنف؟</h5>
                        <p class="text-muted">سيتم حذف الصنف <strong id="delete_item_name"></strong> نهائياً</p>
                        <p class="text-danger"><small>تأكد من عدم وجود مخزون مرتبط بهذا الصنف</small></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="item_id" id="delete_item_id">
                        <button type="submit" name="delete_item" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>حذف نهائي
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- زر الإجراء العائم -->
    <button class="floating-action" data-bs-toggle="modal" data-bs-target="#addItemModal" title="إضافة صنف جديد">
        <i class="fas fa-plus"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعديل صنف
        function editItem(item) {
            document.getElementById('edit_item_id').value = item.id;
            document.getElementById('edit_name').value = item.name;
            document.getElementById('edit_code').value = item.code;
            document.getElementById('edit_type').value = item.type;
            document.getElementById('edit_category').value = item.category || '';
            document.getElementById('edit_unit').value = item.unit;
            document.getElementById('edit_barcode').value = item.barcode || '';
            document.getElementById('edit_cost_price').value = item.cost_price;
            document.getElementById('edit_selling_price').value = item.selling_price;
            document.getElementById('edit_min_stock_level').value = item.min_stock_level;
            document.getElementById('edit_max_stock_level').value = item.max_stock_level;
            document.getElementById('edit_description').value = item.description || '';
            document.getElementById('edit_is_active').checked = item.is_active == 1;

            new bootstrap.Modal(document.getElementById('editItemModal')).show();
        }

        // حذف صنف
        function deleteItem(itemId, itemName) {
            document.getElementById('delete_item_id').value = itemId;
            document.getElementById('delete_item_name').textContent = itemName;

            new bootstrap.Modal(document.getElementById('deleteItemModal')).show();
        }

        // تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك الأرقام في الإحصائيات
            const numbers = document.querySelectorAll('.stats-number');
            numbers.forEach(number => {
                const finalNumber = parseInt(number.textContent);
                let currentNumber = 0;
                const increment = finalNumber / 50;

                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        number.textContent = finalNumber;
                        clearInterval(timer);
                    } else {
                        number.textContent = Math.floor(currentNumber);
                    }
                }, 30);
            });
        });
    </script>
</body>
</html>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الأصناف -->
        <div class="row">
            <?php
            $totalItems = count($items);
            $activeItems = count(array_filter($items, function($i) { return $i['is_active']; }));
            $products = count(array_filter($items, function($i) { return $i['type'] == 'product'; }));
            $rawMaterials = count(array_filter($items, function($i) { return $i['type'] == 'raw_material'; }));
            ?>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $totalItems ?></div>
                    <div class="text-muted">إجمالي الأصناف</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $products ?></div>
                    <div class="text-muted">منتجات نهائية</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $rawMaterials ?></div>
                    <div class="text-muted">مواد خام</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $activeItems ?></div>
                    <div class="text-muted">أصناف نشطة</div>
                </div>
            </div>
        </div>

        <!-- قائمة الأصناف -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الأصناف</h5>
                    <span class="badge bg-light text-dark"><?= count($items) ?> صنف</span>
                </div>
            </div>
            <div class="card-body">
                <?php if (empty($items)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-cube fa-4x text-muted mb-3"></i>
                        <h5>لا توجد أصناف</h5>
                        <p class="text-muted">ابدأ بإضافة أول صنف في النظام</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-2"></i>إضافة صنف جديد
                        </button>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($items as $item): ?>
                            <div class="col-lg-6 col-xl-4">
                                <div class="item-card <?= !$item['is_active'] ? 'inactive' : '' ?>">
                                    <div class="d-flex align-items-start">
                                        <div class="item-icon">
                                            <i class="fas fa-<?= $item['type'] == 'product' ? 'box' : ($item['type'] == 'raw_material' ? 'industry' : 'cogs') ?>"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-0"><?= htmlspecialchars($item['name']) ?></h6>
                                                <span class="status-badge status-<?= $item['is_active'] ? 'active' : 'inactive' ?>">
                                                    <?= $item['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                </span>
                                            </div>

                                            <p class="text-muted mb-2">
                                                <small><i class="fas fa-barcode me-1"></i><?= htmlspecialchars($item['code']) ?></small>
                                            </p>

                                            <div class="mb-2">
                                                <?php
                                                $typeNames = [
                                                    'product' => 'منتج نهائي',
                                                    'raw_material' => 'مادة خام',
                                                    'service' => 'خدمة'
                                                ];
                                                ?>
                                                <span class="type-badge type-<?= $item['type'] ?>">
                                                    <?= $typeNames[$item['type']] ?>
                                                </span>
                                            </div>

                                            <?php if ($item['category']): ?>
                                                <p class="text-muted small mb-2">
                                                    <i class="fas fa-tag me-1"></i><?= htmlspecialchars($item['category']) ?>
                                                </p>
                                            <?php endif; ?>

                                            <div class="row text-center mb-2">
                                                <div class="col-6">
                                                    <div class="price-display">
                                                        <strong><?= number_format($item['cost_price'], 0) ?></strong>
                                                        <small class="d-block text-muted">سعر التكلفة</small>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="price-display">
                                                        <strong><?= number_format($item['selling_price'], 0) ?></strong>
                                                        <small class="d-block text-muted">سعر البيع</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <p class="mb-2">
                                                <i class="fas fa-balance-scale me-1 text-info"></i>
                                                <small>الوحدة: <?= htmlspecialchars($item['unit']) ?></small>
                                            </p>

                                            <p class="mb-2">
                                                <i class="fas fa-warehouse me-1 text-success"></i>
                                                <small>المخزون: <?= number_format($item['total_stock'], 2) ?></small>
                                            </p>

                                            <div class="d-flex justify-content-between align-items-center mt-3">
                                                <small class="text-muted">
                                                    <i class="fas fa-calendar me-1"></i>
                                                    <?= date('d/m/Y', strtotime($item['created_at'])) ?>
                                                </small>

                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-outline-primary btn-sm"
                                                            onclick="editItem(<?= htmlspecialchars(json_encode($item)) ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-sm"
                                                            onclick="deleteItem(<?= $item['id'] ?>, '<?= htmlspecialchars($item['name']) ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
