<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/SystemSettings.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$settings = new SystemSettings();
$db = new Database();

$success = '';
$error = '';

// معالجة حفظ إعدادات المعمل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['save_factory_settings'])) {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول إعدادات المعمل إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS factory_settings (
            id INT PRIMARY KEY AUTO_INCREMENT,
            factory_type VARCHAR(100),
            production_capacity DECIMAL(15,3),
            capacity_unit VARCHAR(50),
            working_hours_start TIME,
            working_hours_end TIME,
            working_days JSON,
            shift_count INT DEFAULT 1,
            quality_standards TEXT,
            safety_protocols TEXT,
            environmental_compliance TEXT,
            certifications JSON,
            machinery_count INT DEFAULT 0,
            employee_capacity INT DEFAULT 0,
            warehouse_count INT DEFAULT 1,
            production_lines INT DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // حذف الإعدادات السابقة
        $db->query("DELETE FROM factory_settings");
        
        // إعداد أيام العمل
        $workingDays = [];
        if (isset($_POST['working_days'])) {
            $workingDays = $_POST['working_days'];
        }
        
        // إعداد الشهادات
        $certifications = [];
        if (!empty($_POST['certifications'])) {
            $certifications = array_filter(explode("\n", $_POST['certifications']));
        }
        
        // إدراج الإعدادات الجديدة
        $factoryData = [
            'factory_type' => $_POST['factory_type'],
            'production_capacity' => (float)$_POST['production_capacity'],
            'capacity_unit' => $_POST['capacity_unit'],
            'working_hours_start' => $_POST['working_hours_start'],
            'working_hours_end' => $_POST['working_hours_end'],
            'working_days' => json_encode($workingDays),
            'shift_count' => (int)$_POST['shift_count'],
            'quality_standards' => $_POST['quality_standards'],
            'safety_protocols' => $_POST['safety_protocols'],
            'environmental_compliance' => $_POST['environmental_compliance'],
            'certifications' => json_encode($certifications),
            'machinery_count' => (int)$_POST['machinery_count'],
            'employee_capacity' => (int)$_POST['employee_capacity'],
            'warehouse_count' => (int)$_POST['warehouse_count'],
            'production_lines' => (int)$_POST['production_lines']
        ];
        
        $db->insert('factory_settings', $factoryData);
        
        // حفظ إعدادات إضافية في جدول النظام
        $settings->set('factory_established_year', $_POST['established_year']);
        $settings->set('factory_license_number', $_POST['license_number']);
        $settings->set('factory_area_sqm', $_POST['area_sqm']);
        $settings->set('factory_location_coordinates', $_POST['location_coordinates']);
        
        $db->commit();
        $success = "تم حفظ إعدادات المعمل بنجاح";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في حفظ إعدادات المعمل: " . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    $factorySettings = $db->fetchOne("SELECT * FROM factory_settings ORDER BY id DESC LIMIT 1");
    if (!$factorySettings) {
        $factorySettings = [
            'factory_type' => '',
            'production_capacity' => 0,
            'capacity_unit' => 'طن/شهر',
            'working_hours_start' => '08:00',
            'working_hours_end' => '16:00',
            'working_days' => '[]',
            'shift_count' => 1,
            'quality_standards' => '',
            'safety_protocols' => '',
            'environmental_compliance' => '',
            'certifications' => '[]',
            'machinery_count' => 0,
            'employee_capacity' => 0,
            'warehouse_count' => 1,
            'production_lines' => 1
        ];
    }
} catch (Exception $e) {
    $factorySettings = [];
}

$workingDays = json_decode($factorySettings['working_days'] ?? '[]', true) ?: [];
$certifications = json_decode($factorySettings['certifications'] ?? '[]', true) ?: [];

// أنواع المعامل
$factoryTypes = [
    'textile' => 'نسيج وملابس',
    'food' => 'صناعات غذائية',
    'chemical' => 'صناعات كيماوية',
    'metal' => 'صناعات معدنية',
    'plastic' => 'صناعات بلاستيكية',
    'pharmaceutical' => 'صناعات دوائية',
    'electronics' => 'صناعات إلكترونية',
    'automotive' => 'صناعات السيارات',
    'construction' => 'مواد البناء',
    'paper' => 'صناعات الورق',
    'furniture' => 'صناعة الأثاث',
    'other' => 'أخرى'
];

$daysOfWeek = [
    'sunday' => 'الأحد',
    'monday' => 'الاثنين',
    'tuesday' => 'الثلاثاء',
    'wednesday' => 'الأربعاء',
    'thursday' => 'الخميس',
    'friday' => 'الجمعة',
    'saturday' => 'السبت'
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المعمل - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        <?= $settings->getThemeCSS() ?>
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            overflow: hidden;
        }
        
        .main-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .settings-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .day-checkbox {
            margin: 5px;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .day-checkbox:hover {
            background: #f8f9fa;
        }
        
        .day-checkbox.selected {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-color);
        }
        
        .capacity-display {
            background: var(--gradient-primary);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin: 1rem 0;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="main-header">
            <h1><i class="fas fa-industry fa-2x mb-3"></i><br>إعدادات المعمل</h1>
            <p class="mb-0">تخصيص إعدادات المعمل والإنتاج حسب نوع صناعتك</p>
        </div>

        <div class="container-fluid p-4">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- معلومات أساسية -->
                    <div class="col-lg-8">
                        <div class="settings-section">
                            <h4><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع المعمل <span class="text-danger">*</span></label>
                                    <select name="factory_type" class="form-select" required>
                                        <option value="">اختر نوع المعمل</option>
                                        <?php foreach ($factoryTypes as $key => $type): ?>
                                            <option value="<?= $key ?>" <?= $factorySettings['factory_type'] == $key ? 'selected' : '' ?>>
                                                <?= $type ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سنة التأسيس</label>
                                    <input type="number" name="established_year" class="form-control" 
                                           min="1900" max="<?= date('Y') ?>" 
                                           value="<?= $settings->get('factory_established_year', '') ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الترخيص</label>
                                    <input type="text" name="license_number" class="form-control" 
                                           value="<?= $settings->get('factory_license_number', '') ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المساحة (متر مربع)</label>
                                    <input type="number" name="area_sqm" class="form-control" 
                                           min="0" step="0.01" 
                                           value="<?= $settings->get('factory_area_sqm', '') ?>">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الإحداثيات الجغرافية</label>
                                    <input type="text" name="location_coordinates" class="form-control" 
                                           placeholder="مثال: 33.3152, 44.3661"
                                           value="<?= $settings->get('factory_location_coordinates', '') ?>">
                                    <div class="form-text">يمكن الحصول على الإحداثيات من خرائط جوجل</div>
                                </div>
                            </div>
                        </div>

                        <!-- الطاقة الإنتاجية -->
                        <div class="settings-section">
                            <h4><i class="fas fa-chart-line me-2"></i>الطاقة الإنتاجية</h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الطاقة الإنتاجية <span class="text-danger">*</span></label>
                                    <input type="number" name="production_capacity" class="form-control" 
                                           step="0.001" min="0" required
                                           value="<?= $factorySettings['production_capacity'] ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">وحدة القياس</label>
                                    <select name="capacity_unit" class="form-select">
                                        <option value="طن/شهر" <?= $factorySettings['capacity_unit'] == 'طن/شهر' ? 'selected' : '' ?>>طن/شهر</option>
                                        <option value="قطعة/يوم" <?= $factorySettings['capacity_unit'] == 'قطعة/يوم' ? 'selected' : '' ?>>قطعة/يوم</option>
                                        <option value="لتر/ساعة" <?= $factorySettings['capacity_unit'] == 'لتر/ساعة' ? 'selected' : '' ?>>لتر/ساعة</option>
                                        <option value="متر/يوم" <?= $factorySettings['capacity_unit'] == 'متر/يوم' ? 'selected' : '' ?>>متر/يوم</option>
                                        <option value="كيلو/ساعة" <?= $factorySettings['capacity_unit'] == 'كيلو/ساعة' ? 'selected' : '' ?>>كيلو/ساعة</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">عدد خطوط الإنتاج</label>
                                    <input type="number" name="production_lines" class="form-control" 
                                           min="1" value="<?= $factorySettings['production_lines'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">عدد الآلات</label>
                                    <input type="number" name="machinery_count" class="form-control" 
                                           min="0" value="<?= $factorySettings['machinery_count'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">عدد المخازن</label>
                                    <input type="number" name="warehouse_count" class="form-control" 
                                           min="1" value="<?= $factorySettings['warehouse_count'] ?>">
                                </div>
                            </div>
                        </div>

                        <!-- أوقات العمل -->
                        <div class="settings-section">
                            <h4><i class="fas fa-clock me-2"></i>أوقات العمل</h4>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">بداية العمل</label>
                                    <input type="time" name="working_hours_start" class="form-control" 
                                           value="<?= $factorySettings['working_hours_start'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">نهاية العمل</label>
                                    <input type="time" name="working_hours_end" class="form-control" 
                                           value="<?= $factorySettings['working_hours_end'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">عدد الورديات</label>
                                    <select name="shift_count" class="form-select">
                                        <option value="1" <?= $factorySettings['shift_count'] == 1 ? 'selected' : '' ?>>وردية واحدة</option>
                                        <option value="2" <?= $factorySettings['shift_count'] == 2 ? 'selected' : '' ?>>ورديتان</option>
                                        <option value="3" <?= $factorySettings['shift_count'] == 3 ? 'selected' : '' ?>>ثلاث ورديات</option>
                                    </select>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">أيام العمل</label>
                                    <div class="d-flex flex-wrap">
                                        <?php foreach ($daysOfWeek as $key => $day): ?>
                                            <div class="day-checkbox <?= in_array($key, $workingDays) ? 'selected' : '' ?>" 
                                                 onclick="toggleDay('<?= $key ?>')">
                                                <input type="checkbox" name="working_days[]" value="<?= $key ?>" 
                                                       <?= in_array($key, $workingDays) ? 'checked' : '' ?> style="display: none;">
                                                <?= $day ?>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الطاقة الاستيعابية للموظفين</label>
                                    <input type="number" name="employee_capacity" class="form-control" 
                                           min="0" value="<?= $factorySettings['employee_capacity'] ?>">
                                </div>
                            </div>
                        </div>

                        <!-- معايير الجودة والأمان -->
                        <div class="settings-section">
                            <h4><i class="fas fa-shield-alt me-2"></i>معايير الجودة والأمان</h4>
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <label class="form-label">معايير الجودة</label>
                                    <textarea name="quality_standards" class="form-control" rows="3" 
                                              placeholder="اذكر معايير الجودة المطبقة في المعمل"><?= htmlspecialchars($factorySettings['quality_standards']) ?></textarea>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">بروتوكولات السلامة</label>
                                    <textarea name="safety_protocols" class="form-control" rows="3" 
                                              placeholder="اذكر إجراءات السلامة المطبقة"><?= htmlspecialchars($factorySettings['safety_protocols']) ?></textarea>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الامتثال البيئي</label>
                                    <textarea name="environmental_compliance" class="form-control" rows="3" 
                                              placeholder="اذكر المعايير البيئية المطبقة"><?= htmlspecialchars($factorySettings['environmental_compliance']) ?></textarea>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الشهادات والتراخيص</label>
                                    <textarea name="certifications" class="form-control" rows="4" 
                                              placeholder="اذكر الشهادات والتراخيص (كل شهادة في سطر منفصل)"><?= implode("\n", $certifications) ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الإحصائيات والملخص -->
                    <div class="col-lg-4">
                        <div class="stats-card">
                            <h5><i class="fas fa-chart-pie me-2"></i>ملخص المعمل</h5>
                            <div class="capacity-display">
                                <div class="stats-number" id="capacityDisplay">
                                    <?= number_format($factorySettings['production_capacity'], 2) ?>
                                </div>
                                <div id="capacityUnit"><?= $factorySettings['capacity_unit'] ?></div>
                                <small>الطاقة الإنتاجية</small>
                            </div>
                        </div>

                        <div class="stats-card">
                            <div class="stats-number"><?= $factorySettings['production_lines'] ?></div>
                            <div>خط إنتاج</div>
                        </div>

                        <div class="stats-card">
                            <div class="stats-number"><?= $factorySettings['machinery_count'] ?></div>
                            <div>آلة</div>
                        </div>

                        <div class="stats-card">
                            <div class="stats-number"><?= $factorySettings['employee_capacity'] ?></div>
                            <div>موظف (الطاقة الاستيعابية)</div>
                        </div>

                        <div class="stats-card">
                            <div class="stats-number"><?= count($workingDays) ?></div>
                            <div>أيام عمل في الأسبوع</div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>نصيحة:</strong> تأكد من تحديث هذه الإعدادات عند تطوير المعمل أو تغيير خطوط الإنتاج
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" name="save_factory_settings" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-save me-2"></i>حفظ إعدادات المعمل
                    </button>
                    <a href="system_setup.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>العودة للإعدادات
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تبديل أيام العمل
        function toggleDay(day) {
            const checkbox = document.querySelector(`input[value="${day}"]`);
            const dayDiv = checkbox.parentElement;
            
            checkbox.checked = !checkbox.checked;
            dayDiv.classList.toggle('selected');
        }
        
        // تحديث عرض الطاقة الإنتاجية
        document.querySelector('input[name="production_capacity"]').addEventListener('input', function() {
            document.getElementById('capacityDisplay').textContent = parseFloat(this.value || 0).toFixed(2);
        });
        
        document.querySelector('select[name="capacity_unit"]').addEventListener('change', function() {
            document.getElementById('capacityUnit').textContent = this.value;
        });
    </script>
</body>
</html>
