<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة مصروف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] == 'add_general_expense') {
            // توليد كود المصروف
            $prefix = 'GEN';
            $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM general_expenses");
            $sequence = ($countResult['count'] ?? 0) + 1;
            $expenseCode = $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);

            $expenseData = [
                'expense_code' => $expenseCode,
                'category' => $_POST['category'],
                'description' => Helper::cleanInput($_POST['description']),
                'amount' => (float)$_POST['amount'],
                'currency_id' => (int)$_POST['currency_id'],
                'expense_date' => $_POST['expense_date'],
                'department_id' => !empty($_POST['department_id']) ? (int)$_POST['department_id'] : null,
                'vendor_name' => Helper::cleanInput($_POST['vendor_name'] ?? ''),
                'invoice_number' => Helper::cleanInput($_POST['invoice_number'] ?? ''),
                'payment_method' => $_POST['payment_method'],
                'notes' => Helper::cleanInput($_POST['notes'] ?? ''),
                'created_by' => $user['id']
            ];

            $db->insert('general_expenses', $expenseData);
            $success = "تم إضافة المصروف العام بنجاح برقم: $expenseCode";

        } elseif ($_POST['action'] == 'add_production_expense') {
            // توليد كود مصروف الإنتاج
            $prefix = 'PROD';
            $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM production_expenses");
            $sequence = ($countResult['count'] ?? 0) + 1;
            $expenseCode = $prefix . str_pad($sequence, 4, '0', STR_PAD_LEFT);

            $expenseData = [
                'expense_code' => $expenseCode,
                'production_order_id' => !empty($_POST['production_order_id']) ? (int)$_POST['production_order_id'] : null,
                'category' => $_POST['category'],
                'description' => Helper::cleanInput($_POST['description']),
                'amount' => (float)$_POST['amount'],
                'currency_id' => (int)$_POST['currency_id'],
                'expense_date' => $_POST['expense_date'],
                'employee_id' => !empty($_POST['employee_id']) ? (int)$_POST['employee_id'] : null,
                'cost_center' => Helper::cleanInput($_POST['cost_center'] ?? ''),
                'allocation_percentage' => (float)($_POST['allocation_percentage'] ?? 100),
                'notes' => Helper::cleanInput($_POST['notes'] ?? ''),
                'created_by' => $user['id']
            ];

            $db->insert('production_expenses', $expenseData);
            $success = "تم إضافة مصروف الإنتاج بنجاح برقم: $expenseCode";
        }

    } catch (Exception $e) {
        $error = "خطأ في إضافة المصروف: " . $e->getMessage();
    }
}

// فلاتر البحث
$expenseType = $_GET['type'] ?? 'general';
$category = $_GET['category'] ?? '';
$dateFrom = $_GET['date_from'] ?? date('Y-m-01');
$dateTo = $_GET['date_to'] ?? date('Y-m-t');
$status = $_GET['status'] ?? '';

// جلب المصاريف حسب النوع
if ($expenseType == 'general') {
    $whereConditions = ["ge.expense_date BETWEEN ? AND ?"];
    $params = [$dateFrom, $dateTo];

    if ($category) {
        $whereConditions[] = "ge.category = ?";
        $params[] = $category;
    }

    if ($status) {
        $whereConditions[] = "ge.status = ?";
        $params[] = $status;
    }

    $whereClause = implode(' AND ', $whereConditions);

    $expenses = $db->fetchAll("
        SELECT ge.*, d.name as department_name, c.symbol as currency_symbol,
               u.full_name as created_by_name
        FROM general_expenses ge
        LEFT JOIN departments d ON ge.department_id = d.id
        LEFT JOIN currencies c ON ge.currency_id = c.id
        LEFT JOIN users u ON ge.created_by = u.id
        WHERE $whereClause
        ORDER BY ge.expense_date DESC, ge.created_at DESC
    ", $params);

} else {
    $whereConditions = ["pe.expense_date BETWEEN ? AND ?"];
    $params = [$dateFrom, $dateTo];

    if ($category) {
        $whereConditions[] = "pe.category = ?";
        $params[] = $category;
    }

    if ($status) {
        $whereConditions[] = "pe.status = ?";
        $params[] = $status;
    }

    $whereClause = implode(' AND ', $whereConditions);

    $expenses = $db->fetchAll("
        SELECT pe.*, e.full_name as employee_name, c.symbol as currency_symbol,
               u.full_name as created_by_name, po.order_number
        FROM production_expenses pe
        LEFT JOIN employees e ON pe.employee_id = e.id
        LEFT JOIN currencies c ON pe.currency_id = c.id
        LEFT JOIN users u ON pe.created_by = u.id
        LEFT JOIN production_orders po ON pe.production_order_id = po.id
        WHERE $whereClause
        ORDER BY pe.expense_date DESC, pe.created_at DESC
    ", $params);
}

// جلب البيانات المساعدة
$departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
$currencies = $db->fetchAll("SELECT id, name, symbol FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");
$employees = $db->fetchAll("SELECT id, full_name, employee_code FROM employees WHERE status = 'active' ORDER BY full_name");
$productionOrders = $db->fetchAll("SELECT id, order_number FROM production_orders WHERE status IN ('in_progress', 'pending') ORDER BY order_number");

// إحصائيات المصاريف
$generalStats = $db->fetchOne("
    SELECT
        COUNT(*) as total_count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
    FROM general_expenses
    WHERE expense_date BETWEEN ? AND ?
", [$dateFrom, $dateTo]);

$productionStats = $db->fetchOne("
    SELECT
        COUNT(*) as total_count,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount,
        SUM(CASE WHEN status = 'approved' THEN amount ELSE 0 END) as approved_amount
    FROM production_expenses
    WHERE expense_date BETWEEN ? AND ?
", [$dateFrom, $dateTo]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المصاريف - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stats-card.general { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .stats-card.production { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .expense-tabs {
            border-bottom: 3px solid #667eea;
            margin-bottom: 2rem;
        }
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            font-weight: 600;
            color: #667eea;
        }
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .expense-row {
            transition: all 0.3s ease;
        }
        .expense-row:hover {
            background-color: #f8f9fa;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .category-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-money-bill-wave me-3"></i>إدارة المصاريف</h1>
                    <p class="mb-0">إدارة المصاريف العامة ومصاريف الإنتاج</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="employees.php" class="btn btn-light me-2">
                        <i class="fas fa-users me-2"></i>الموظفين
                    </a>
                    <a href="financial.php" class="btn btn-outline-light">
                        <i class="fas fa-chart-bar me-2"></i>التقارير المالية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات المصاريف -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="stats-card general">
                    <h4>المصاريف العامة</h4>
                    <div class="row">
                        <div class="col-6">
                            <h3><?= number_format($generalStats['total_amount'] ?? 0, 0) ?></h3>
                            <p class="mb-0">إجمالي المبلغ</p>
                        </div>
                        <div class="col-6">
                            <h3><?= $generalStats['total_count'] ?? 0 ?></h3>
                            <p class="mb-0">عدد المصاريف</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card production">
                    <h4>مصاريف الإنتاج</h4>
                    <div class="row">
                        <div class="col-6">
                            <h3><?= number_format($productionStats['total_amount'] ?? 0, 0) ?></h3>
                            <p class="mb-0">إجمالي المبلغ</p>
                        </div>
                        <div class="col-6">
                            <h3><?= $productionStats['total_count'] ?? 0 ?></h3>
                            <p class="mb-0">عدد المصاريف</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويبات المصاريف -->
        <ul class="nav nav-tabs expense-tabs" id="expenseTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $expenseType == 'general' ? 'active' : '' ?>"
                        onclick="location.href='?type=general'">
                    <i class="fas fa-building me-2"></i>المصاريف العامة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link <?= $expenseType == 'production' ? 'active' : '' ?>"
                        onclick="location.href='?type=production'">
                    <i class="fas fa-industry me-2"></i>مصاريف الإنتاج
                </button>
            </li>
        </ul>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>الفلاتر</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <input type="hidden" name="type" value="<?= $expenseType ?>">

                            <div class="mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="date_from" class="form-control"
                                       value="<?= htmlspecialchars($dateFrom) ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="date_to" class="form-control"
                                       value="<?= htmlspecialchars($dateTo) ?>">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الفئة</label>
                                <select name="category" class="form-select">
                                    <option value="">جميع الفئات</option>
                                    <?php if ($expenseType == 'general'): ?>
                                        <option value="utilities" <?= $category == 'utilities' ? 'selected' : '' ?>>المرافق</option>
                                        <option value="rent" <?= $category == 'rent' ? 'selected' : '' ?>>الإيجار</option>
                                        <option value="maintenance" <?= $category == 'maintenance' ? 'selected' : '' ?>>الصيانة</option>
                                        <option value="office" <?= $category == 'office' ? 'selected' : '' ?>>مكتبية</option>
                                        <option value="marketing" <?= $category == 'marketing' ? 'selected' : '' ?>>تسويق</option>
                                        <option value="travel" <?= $category == 'travel' ? 'selected' : '' ?>>سفر</option>
                                        <option value="other" <?= $category == 'other' ? 'selected' : '' ?>>أخرى</option>
                                    <?php else: ?>
                                        <option value="labor" <?= $category == 'labor' ? 'selected' : '' ?>>العمالة</option>
                                        <option value="utilities" <?= $category == 'utilities' ? 'selected' : '' ?>>المرافق</option>
                                        <option value="maintenance" <?= $category == 'maintenance' ? 'selected' : '' ?>>الصيانة</option>
                                        <option value="materials" <?= $category == 'materials' ? 'selected' : '' ?>>المواد</option>
                                        <option value="overhead" <?= $category == 'overhead' ? 'selected' : '' ?>>تكاليف إضافية</option>
                                        <option value="other" <?= $category == 'other' ? 'selected' : '' ?>>أخرى</option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="pending" <?= $status == 'pending' ? 'selected' : '' ?>>في الانتظار</option>
                                    <option value="approved" <?= $status == 'approved' ? 'selected' : '' ?>>معتمد</option>
                                    <?php if ($expenseType == 'general'): ?>
                                        <option value="paid" <?= $status == 'paid' ? 'selected' : '' ?>>مدفوع</option>
                                        <option value="rejected" <?= $status == 'rejected' ? 'selected' : '' ?>>مرفوض</option>
                                    <?php else: ?>
                                        <option value="allocated" <?= $status == 'allocated' ? 'selected' : '' ?>>موزع</option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </form>
                    </div>
                </div>

                <!-- إضافة مصروف جديد -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>مصروف جديد</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-primary w-100 mb-2"
                                data-bs-toggle="modal" data-bs-target="#addGeneralExpenseModal">
                            <i class="fas fa-building me-2"></i>مصروف عام
                        </button>
                        <button type="button" class="btn btn-warning w-100"
                                data-bs-toggle="modal" data-bs-target="#addProductionExpenseModal">
                            <i class="fas fa-industry me-2"></i>مصروف إنتاج
                        </button>
                    </div>
                </div>
            </div>

            <!-- قائمة المصاريف -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                <?= $expenseType == 'general' ? 'المصاريف العامة' : 'مصاريف الإنتاج' ?>
                                (<?= count($expenses) ?>)
                            </h5>
                            <a href="expenses_report.php?type=<?= $expenseType ?>&date_from=<?= $dateFrom ?>&date_to=<?= $dateTo ?>"
                               class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i>تصدير
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($expenses)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-money-bill-wave fa-4x text-muted mb-3"></i>
                                <h5>لا توجد مصاريف</h5>
                                <p class="text-muted">ابدأ بإضافة مصروف جديد</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الكود</th>
                                            <th>الفئة</th>
                                            <th>الوصف</th>
                                            <th>المبلغ</th>
                                            <th>التاريخ</th>
                                            <?php if ($expenseType == 'general'): ?>
                                                <th>القسم</th>
                                                <th>المورد</th>
                                            <?php else: ?>
                                                <th>الموظف</th>
                                                <th>أمر الإنتاج</th>
                                            <?php endif; ?>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($expenses as $expense): ?>
                                            <tr class="expense-row">
                                                <td>
                                                    <strong><?= htmlspecialchars($expense['expense_code']) ?></strong>
                                                </td>
                                                <td>
                                                    <?php
                                                    $categoryIcons = [
                                                        'utilities' => ['fas fa-bolt', '#ffc107'],
                                                        'rent' => ['fas fa-home', '#17a2b8'],
                                                        'maintenance' => ['fas fa-wrench', '#6c757d'],
                                                        'office' => ['fas fa-briefcase', '#28a745'],
                                                        'marketing' => ['fas fa-bullhorn', '#dc3545'],
                                                        'travel' => ['fas fa-plane', '#fd7e14'],
                                                        'labor' => ['fas fa-user-hard-hat', '#20c997'],
                                                        'materials' => ['fas fa-boxes', '#6f42c1'],
                                                        'overhead' => ['fas fa-chart-pie', '#e83e8c'],
                                                        'other' => ['fas fa-ellipsis-h', '#6c757d']
                                                    ];

                                                    $categoryNames = [
                                                        'utilities' => 'المرافق',
                                                        'rent' => 'الإيجار',
                                                        'maintenance' => 'الصيانة',
                                                        'office' => 'مكتبية',
                                                        'marketing' => 'تسويق',
                                                        'travel' => 'سفر',
                                                        'labor' => 'العمالة',
                                                        'materials' => 'المواد',
                                                        'overhead' => 'تكاليف إضافية',
                                                        'other' => 'أخرى'
                                                    ];

                                                    $icon = $categoryIcons[$expense['category']] ?? ['fas fa-circle', '#6c757d'];
                                                    ?>
                                                    <div class="d-flex align-items-center">
                                                        <div class="category-icon me-2" style="background-color: <?= $icon[1] ?>">
                                                            <i class="<?= $icon[0] ?>"></i>
                                                        </div>
                                                        <span><?= $categoryNames[$expense['category']] ?? $expense['category'] ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="text-truncate" style="max-width: 200px;"
                                                         title="<?= htmlspecialchars($expense['description']) ?>">
                                                        <?= htmlspecialchars($expense['description']) ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?= number_format($expense['amount'], 2) ?></strong>
                                                    <small class="text-muted"><?= $expense['currency_symbol'] ?></small>
                                                </td>
                                                <td><?= date('d/m/Y', strtotime($expense['expense_date'])) ?></td>
                                                <?php if ($expenseType == 'general'): ?>
                                                    <td><?= htmlspecialchars($expense['department_name'] ?? '-') ?></td>
                                                    <td><?= htmlspecialchars($expense['vendor_name'] ?? '-') ?></td>
                                                <?php else: ?>
                                                    <td><?= htmlspecialchars($expense['employee_name'] ?? '-') ?></td>
                                                    <td><?= htmlspecialchars($expense['order_number'] ?? '-') ?></td>
                                                <?php endif; ?>
                                                <td>
                                                    <?php
                                                    $statusColors = [
                                                        'pending' => 'warning',
                                                        'approved' => 'success',
                                                        'paid' => 'primary',
                                                        'allocated' => 'info',
                                                        'rejected' => 'danger'
                                                    ];
                                                    $statusNames = [
                                                        'pending' => 'في الانتظار',
                                                        'approved' => 'معتمد',
                                                        'paid' => 'مدفوع',
                                                        'allocated' => 'موزع',
                                                        'rejected' => 'مرفوض'
                                                    ];
                                                    ?>
                                                    <span class="status-badge bg-<?= $statusColors[$expense['status']] ?>">
                                                        <?= $statusNames[$expense['status']] ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view_expense.php?id=<?= $expense['id'] ?>&type=<?= $expenseType ?>"
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_expense.php?id=<?= $expense['id'] ?>&type=<?= $expenseType ?>"
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <?php if ($expense['status'] == 'pending' && $user['role'] == 'admin'): ?>
                                                            <button class="btn btn-sm btn-outline-success"
                                                                    onclick="approveExpense(<?= $expense['id'] ?>, '<?= $expenseType ?>')">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مصروف عام -->
    <div class="modal fade" id="addGeneralExpenseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مصروف عام</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_general_expense">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select name="category" class="form-select" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="utilities">المرافق (كهرباء، ماء، غاز)</option>
                                    <option value="rent">الإيجار</option>
                                    <option value="maintenance">الصيانة</option>
                                    <option value="office">مكتبية</option>
                                    <option value="marketing">تسويق</option>
                                    <option value="travel">سفر</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <input type="number" name="amount" class="form-control"
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">العملة</label>
                                <select name="currency_id" class="form-select">
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?= $currency['id'] ?>" <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                                <input type="date" name="expense_date" class="form-control"
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">القسم</label>
                                <select name="department_id" class="form-select">
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>">
                                            <?= htmlspecialchars($dept['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select name="payment_method" class="form-select">
                                    <option value="cash">نقداً</option>
                                    <option value="bank_transfer">تحويل بنكي</option>
                                    <option value="check">شيك</option>
                                    <option value="credit_card">بطاقة ائتمان</option>
                                </select>
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف <span class="text-danger">*</span></label>
                                <textarea name="description" class="form-control" rows="3"
                                          required placeholder="وصف تفصيلي للمصروف"></textarea>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المورد</label>
                                <input type="text" name="vendor_name" class="form-control"
                                       placeholder="اسم المورد أو الشركة">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الفاتورة</label>
                                <input type="text" name="invoice_number" class="form-control"
                                       placeholder="رقم الفاتورة">
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="2"
                                          placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ المصروف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مصروف إنتاج -->
    <div class="modal fade" id="addProductionExpenseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مصروف إنتاج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_production_expense">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الفئة <span class="text-danger">*</span></label>
                                <select name="category" class="form-select" required>
                                    <option value="">اختر الفئة</option>
                                    <option value="labor">العمالة (أجور العمال)</option>
                                    <option value="utilities">المرافق (كهرباء الإنتاج)</option>
                                    <option value="maintenance">صيانة المعدات</option>
                                    <option value="materials">المواد والمعدات</option>
                                    <option value="overhead">تكاليف إضافية</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">المبلغ <span class="text-danger">*</span></label>
                                <input type="number" name="amount" class="form-control"
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">العملة</label>
                                <select name="currency_id" class="form-select">
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?= $currency['id'] ?>" <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">تاريخ المصروف <span class="text-danger">*</span></label>
                                <input type="date" name="expense_date" class="form-control"
                                       value="<?= date('Y-m-d') ?>" required>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">الموظف المرتبط</label>
                                <select name="employee_id" class="form-select">
                                    <option value="">اختر الموظف</option>
                                    <?php foreach ($employees as $emp): ?>
                                        <option value="<?= $emp['id'] ?>">
                                            <?= htmlspecialchars($emp['employee_code'] . ' - ' . $emp['full_name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">أمر الإنتاج</label>
                                <select name="production_order_id" class="form-select">
                                    <option value="">اختر أمر الإنتاج</option>
                                    <?php foreach ($productionOrders as $order): ?>
                                        <option value="<?= $order['id'] ?>">
                                            <?= htmlspecialchars($order['order_number']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">الوصف <span class="text-danger">*</span></label>
                                <textarea name="description" class="form-control" rows="3"
                                          required placeholder="وصف تفصيلي للمصروف"></textarea>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">مركز التكلفة</label>
                                <input type="text" name="cost_center" class="form-control"
                                       placeholder="مركز التكلفة">
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">نسبة التوزيع (%)</label>
                                <input type="number" name="allocation_percentage" class="form-control"
                                       step="0.01" min="0" max="100" value="100" placeholder="100">
                            </div>

                            <div class="col-12 mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="2"
                                          placeholder="ملاحظات إضافية (اختياري)"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">حفظ المصروف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function approveExpense(expenseId, expenseType) {
            if (confirm('هل أنت متأكد من اعتماد هذا المصروف؟')) {
                // إرسال طلب AJAX لاعتماد المصروف
                fetch('approve_expense.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        expense_id: expenseId,
                        expense_type: expenseType
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('خطأ في اعتماد المصروف: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ في الاتصال');
                });
            }
        }
    </script>
</body>
</html>
