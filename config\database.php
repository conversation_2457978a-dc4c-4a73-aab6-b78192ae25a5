<?php
/**
 * إعدادات قاعدة البيانات
 * نظام إدارة الديون
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'debt_management_system');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// إعدادات النظام
define('SYSTEM_NAME', 'نظام إدارة الديون');
define('SYSTEM_VERSION', '1.0.0');
define('DEFAULT_CURRENCY', 'IQD');
define('EXCHANGE_RATE_USD_TO_IQD', 1500); // سعر الصرف الافتراضي

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * إنشاء قاعدة البيانات والجداول
     */
    public static function createDatabase() {
        try {
            // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
            $pdo = new PDO("mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET, DB_USER, DB_PASS);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // الاتصال بقاعدة البيانات الجديدة
            $pdo->exec("USE " . DB_NAME);

            // إنشاء الجداول
            self::createTables($pdo);
            
            return true;
        } catch(PDOException $e) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
            return false;
        }
    }

    /**
     * إنشاء جداول قاعدة البيانات
     */
    private static function createTables($pdo) {
        $tables = [
            // جدول المستخدمين
            "CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role ENUM('admin', 'user') DEFAULT 'user',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )",

            // جدول العملات
            "CREATE TABLE IF NOT EXISTS currencies (
                id INT AUTO_INCREMENT PRIMARY KEY,
                code VARCHAR(3) UNIQUE NOT NULL,
                name VARCHAR(50) NOT NULL,
                symbol VARCHAR(10) NOT NULL,
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                is_default BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )",

            // جدول الصناديق
            "CREATE TABLE IF NOT EXISTS cash_boxes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                currency_id INT NOT NULL,
                initial_balance DECIMAL(15,2) DEFAULT 0.00,
                current_balance DECIMAL(15,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (currency_id) REFERENCES currencies(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )",

            // جدول العملاء
            "CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                initial_balance_iqd DECIMAL(15,2) DEFAULT 0.00,
                initial_balance_usd DECIMAL(15,2) DEFAULT 0.00,
                current_balance_iqd DECIMAL(15,2) DEFAULT 0.00,
                current_balance_usd DECIMAL(15,2) DEFAULT 0.00,
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )",

            // جدول الموردين
            "CREATE TABLE IF NOT EXISTS suppliers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                initial_balance_iqd DECIMAL(15,2) DEFAULT 0.00,
                initial_balance_usd DECIMAL(15,2) DEFAULT 0.00,
                current_balance_iqd DECIMAL(15,2) DEFAULT 0.00,
                current_balance_usd DECIMAL(15,2) DEFAULT 0.00,
                notes TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )",

            // جدول المعاملات
            "CREATE TABLE IF NOT EXISTS transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type ENUM('income', 'expense') NOT NULL,
                entity_type ENUM('customer', 'supplier') NOT NULL,
                entity_id INT NOT NULL,
                cash_box_id INT NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                currency VARCHAR(3) NOT NULL,
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                description TEXT,
                reference_number VARCHAR(50),
                transaction_date DATE NOT NULL,
                created_by INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (cash_box_id) REFERENCES cash_boxes(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                INDEX idx_entity (entity_type, entity_id),
                INDEX idx_date (transaction_date),
                INDEX idx_type (type)
            )",

            // جدول الديون (للمتابعة السريعة)
            "CREATE TABLE IF NOT EXISTS debts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                entity_type ENUM('customer', 'supplier') NOT NULL,
                entity_id INT NOT NULL,
                currency VARCHAR(3) NOT NULL,
                balance DECIMAL(15,2) NOT NULL,
                last_transaction_date DATE,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_debt (entity_type, entity_id, currency),
                INDEX idx_balance (balance),
                INDEX idx_entity (entity_type, entity_id)
            )",

            // جدول إعدادات النظام
            "CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                description TEXT,
                updated_by INT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )"
        ];

        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }

        // إدراج البيانات الأساسية
        self::insertInitialData($pdo);
    }

    /**
     * إدراج البيانات الأساسية
     */
    private static function insertInitialData($pdo) {
        // إدراج العملات الأساسية
        $pdo->exec("INSERT IGNORE INTO currencies (code, name, symbol, exchange_rate, is_default) VALUES 
            ('IQD', 'دينار عراقي', 'د.ع', 1.0000, TRUE),
            ('USD', 'دولار أمريكي', '$', " . EXCHANGE_RATE_USD_TO_IQD . ", FALSE)");

        // إدراج مستخدم افتراضي (admin/admin123)
        $password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (username, email, password, full_name, role) VALUES 
            ('admin', '<EMAIL>', '$password', 'مدير النظام', 'admin')");

        // إدراج إعدادات النظام الأساسية
        $pdo->exec("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES 
            ('exchange_rate_usd_iqd', '" . EXCHANGE_RATE_USD_TO_IQD . "', 'سعر صرف الدولار مقابل الدينار'),
            ('default_currency', 'IQD', 'العملة الافتراضية للنظام'),
            ('company_name', 'شركة إدارة الديون', 'اسم الشركة'),
            ('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي')");
    }
}

// إنشاء اتصال عام
$database = new Database();
$pdo = $database->getConnection();

// التحقق من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
if (!$pdo) {
    Database::createDatabase();
    $pdo = $database->getConnection();
}
?>
