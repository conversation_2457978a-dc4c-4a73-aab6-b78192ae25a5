<?php

class SystemSettings {
    private $db;
    private static $settings = null;
    
    public function __construct() {
        $this->db = new Database();
        $this->loadSettings();
    }
    
    /**
     * تحميل جميع الإعدادات من قاعدة البيانات
     */
    private function loadSettings() {
        if (self::$settings === null) {
            try {
                $settingsData = $this->db->fetchAll("SELECT setting_key, setting_value FROM system_settings");
                self::$settings = [];
                foreach ($settingsData as $setting) {
                    self::$settings[$setting['setting_key']] = $setting['setting_value'];
                }
            } catch (Exception $e) {
                self::$settings = $this->getDefaultSettings();
            }
        }
    }
    
    /**
     * الحصول على قيمة إعداد معين
     */
    public function get($key, $default = null) {
        return self::$settings[$key] ?? $default;
    }
    
    /**
     * تحديث قيمة إعداد معين
     */
    public function set($key, $value) {
        try {
            $this->db->query("UPDATE system_settings SET setting_value = ? WHERE setting_key = ?", [$value, $key]);
            self::$settings[$key] = $value;
            return true;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getAll() {
        return self::$settings;
    }
    
    /**
     * الحصول على إعدادات فئة معينة
     */
    public function getByCategory($category) {
        try {
            $settings = $this->db->fetchAll("SELECT * FROM system_settings WHERE category = ? ORDER BY display_name", [$category]);
            return $settings;
        } catch (Exception $e) {
            return [];
        }
    }
    
    /**
     * الإعدادات الافتراضية
     */
    private function getDefaultSettings() {
        return [
            'company_name' => 'شركة المعامل المتقدمة',
            'company_name_en' => 'Advanced Factory Company',
            'company_phone' => '07901234567',
            'company_email' => '<EMAIL>',
            'company_address' => 'العراق - بغداد',
            'system_name' => 'نظام إدارة المعامل',
            'system_version' => '1.0.0',
            'default_currency' => 'IQD',
            'date_format' => 'd/m/Y',
            'time_format' => 'H:i',
            'timezone' => 'Asia/Baghdad',
            'language' => 'ar',
            'theme_color' => '#6f42c1',
            'secondary_color' => '#e83e8c',
            'sidebar_color' => '#343a40'
        ];
    }
    
    /**
     * تطبيق إعدادات المظهر
     */
    public function getThemeCSS() {
        $primaryColor = $this->get('theme_color', '#6f42c1');
        $secondaryColor = $this->get('secondary_color', '#e83e8c');
        $sidebarColor = $this->get('sidebar_color', '#343a40');
        
        return "
        :root {
            --primary-color: {$primaryColor};
            --secondary-color: {$secondaryColor};
            --sidebar-color: {$sidebarColor};
            --gradient-primary: linear-gradient(135deg, {$primaryColor} 0%, {$secondaryColor} 100%);
        }
        
        .btn-primary, .bg-primary {
            background: var(--gradient-primary) !important;
            border-color: var(--primary-color) !important;
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .border-primary {
            border-color: var(--primary-color) !important;
        }
        
        .main-header, .card-header.bg-primary {
            background: var(--gradient-primary) !important;
        }
        
        .sidebar {
            background-color: var(--sidebar-color) !important;
        }
        
        .nav-pills .nav-link.active {
            background: var(--gradient-primary) !important;
        }
        ";
    }
    
    /**
     * تنسيق التاريخ حسب إعدادات النظام
     */
    public function formatDate($date, $includeTime = false) {
        $format = $this->get('date_format', 'd/m/Y');
        if ($includeTime) {
            $format .= ' ' . $this->get('time_format', 'H:i');
        }
        
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        
        return $date->format($format);
    }
    
    /**
     * الحصول على معلومات الشركة
     */
    public function getCompanyInfo() {
        return [
            'name' => $this->get('company_name', 'شركة المعامل المتقدمة'),
            'name_en' => $this->get('company_name_en', 'Advanced Factory Company'),
            'phone' => $this->get('company_phone', '07901234567'),
            'email' => $this->get('company_email', '<EMAIL>'),
            'address' => $this->get('company_address', 'العراق - بغداد'),
            'website' => $this->get('company_website', ''),
            'logo' => $this->get('company_logo', ''),
            'tax_number' => $this->get('tax_number', ''),
            'commercial_record' => $this->get('commercial_record', '')
        ];
    }
    
    /**
     * تحديث المنطقة الزمنية
     */
    public function setTimezone() {
        $timezone = $this->get('timezone', 'Asia/Baghdad');
        date_default_timezone_set($timezone);
    }
    
    /**
     * التحقق من تفعيل ميزة معينة
     */
    public function isFeatureEnabled($feature) {
        return (bool)$this->get($feature, false);
    }
    
    /**
     * إعادة تحميل الإعدادات من قاعدة البيانات
     */
    public function reload() {
        self::$settings = null;
        $this->loadSettings();
    }
    
    /**
     * تصدير الإعدادات إلى ملف JSON
     */
    public function exportSettings() {
        return json_encode(self::$settings, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    }
    
    /**
     * استيراد الإعدادات من ملف JSON
     */
    public function importSettings($jsonData) {
        try {
            $settings = json_decode($jsonData, true);
            if ($settings) {
                foreach ($settings as $key => $value) {
                    $this->set($key, $value);
                }
                return true;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * الحصول على إعدادات الأمان
     */
    public function getSecuritySettings() {
        return [
            'session_timeout' => (int)$this->get('session_timeout', 3600),
            'max_login_attempts' => (int)$this->get('max_login_attempts', 5),
            'password_min_length' => (int)$this->get('password_min_length', 6),
            'enable_2fa' => $this->isFeatureEnabled('enable_2fa')
        ];
    }
    
    /**
     * الحصول على إعدادات النسخ الاحتياطي
     */
    public function getBackupSettings() {
        return [
            'frequency' => $this->get('backup_frequency', 'daily'),
            'retention_days' => (int)$this->get('backup_retention_days', 30),
            'auto_backup' => $this->isFeatureEnabled('auto_backup')
        ];
    }
}
?>
