Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج التقارير - Reports Form
''' </summary>
Public Class ReportsForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User

    ' عناصر التحكم الرئيسية
    Private _splitContainer As SplitContainer
    Private _treeViewReports As TreeView
    Private _panelReportViewer As Panel
    Private _toolStrip As ToolStrip

    ' أزرار شريط الأدوات
    Private _btnGenerate As ToolStripButton
    Private _btnPrint As ToolStripButton
    Private _btnExport As ToolStripButton
    Private _btnRefresh As ToolStripButton

    ' لوحة المعايير
    Private _panelCriteria As Panel
    Private _dtpFromDate As DateTimePicker
    Private _dtpToDate As DateTimePicker
    Private _cmbEntity As ComboBox
    Private _cmbCurrency As ComboBox
    Private _cmbReportFormat As ComboBox

    ' عارض التقرير
    Private _dataGridViewReport As DataGridView
    Private _lblReportTitle As Label
    Private _lblReportSummary As Label

    ' متغيرات أخرى
    Private _selectedReportType As String

#End Region

#Region "Constructor"

    ''' <summary>
    ''' منشئ نموذج التقارير
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser

        InitializeComponent()
        SetupForm()
        SetupToolStrip()
        SetupSplitContainer()
        SetupReportsTree()
        SetupCriteriaPanel()
        SetupReportViewer()
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "التقارير"
        Me.Size = New Size(1400, 800)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.ReportsIcon
    End Sub

    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolStrip()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }

        ' إنشاء الأزرار
        _btnGenerate = New ToolStripButton With {
            .Text = "إنشاء التقرير",
            .Image = My.Resources.GenerateIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "إنشاء التقرير المحدد",
            .Enabled = False
        }

        _btnPrint = New ToolStripButton With {
            .Text = "طباعة",
            .Image = My.Resources.PrintIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "طباعة التقرير",
            .Enabled = False
        }

        _btnExport = New ToolStripButton With {
            .Text = "تصدير",
            .Image = My.Resources.ExportIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تصدير التقرير",
            .Enabled = False
        }

        _btnRefresh = New ToolStripButton With {
            .Text = "تحديث",
            .Image = My.Resources.RefreshIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحديث التقرير"
        }

        ' إضافة الأزرار لشريط الأدوات
        _toolStrip.Items.AddRange({
            _btnGenerate,
            New ToolStripSeparator(),
            _btnPrint,
            _btnExport,
            New ToolStripSeparator(),
            _btnRefresh
        })

        ' ربط الأحداث
        AddHandler _btnGenerate.Click, AddressOf BtnGenerate_Click
        AddHandler _btnPrint.Click, AddressOf BtnPrint_Click
        AddHandler _btnExport.Click, AddressOf BtnExport_Click
        AddHandler _btnRefresh.Click, AddressOf BtnRefresh_Click

        Me.Controls.Add(_toolStrip)
    End Sub

    ''' <summary>
    ''' إعداد الحاوي المقسم
    ''' </summary>
    Private Sub SetupSplitContainer()
        _splitContainer = New SplitContainer With {
            .Dock = DockStyle.Fill,
            .SplitterDistance = 300,
            .BackColor = Color.FromArgb(248, 249, 250),
            .Panel1MinSize = 250,
            .Panel2MinSize = 400
        }

        Me.Controls.Add(_splitContainer)
    End Sub

    ''' <summary>
    ''' إعداد شجرة التقارير
    ''' </summary>
    Private Sub SetupReportsTree()
        _treeViewReports = New TreeView With {
            .Dock = DockStyle.Fill,
            .BackColor = Color.White,
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes,
            .RightToLeftLayout = True,
            .ShowLines = True,
            .ShowPlusMinus = True,
            .ShowRootLines = True,
            .FullRowSelect = True,
            .HideSelection = False
        }

        ' إضافة عقد التقارير
        CreateReportNodes()

        ' ربط الأحداث
        AddHandler _treeViewReports.AfterSelect, AddressOf TreeViewReports_AfterSelect

        _splitContainer.Panel1.Controls.Add(_treeViewReports)
    End Sub

    ''' <summary>
    ''' إنشاء عقد التقارير
    ''' </summary>
    Private Sub CreateReportNodes()
        _treeViewReports.Nodes.Clear()

        ' تقارير العملاء
        Dim customersNode As New TreeNode("تقارير العملاء") With {.Tag = "CustomersReports"}
        customersNode.Nodes.AddRange({
            New TreeNode("قائمة العملاء") With {.Tag = "CustomersList"},
            New TreeNode("أرصدة العملاء") With {.Tag = "CustomersBalances"},
            New TreeNode("كشف حساب عميل") With {.Tag = "CustomerStatement"},
            New TreeNode("العملاء المدينون") With {.Tag = "DebtorCustomers"}
        })

        ' تقارير الموردين
        Dim suppliersNode As New TreeNode("تقارير الموردين") With {.Tag = "SuppliersReports"}
        suppliersNode.Nodes.AddRange({
            New TreeNode("قائمة الموردين") With {.Tag = "SuppliersList"},
            New TreeNode("أرصدة الموردين") With {.Tag = "SuppliersBalances"},
            New TreeNode("كشف حساب مورد") With {.Tag = "SupplierStatement"},
            New TreeNode("الموردين الدائنون") With {.Tag = "CreditorSuppliers"}
        })

        ' تقارير المعاملات
        Dim transactionsNode As New TreeNode("تقارير المعاملات") With {.Tag = "TransactionsReports"}
        transactionsNode.Nodes.AddRange({
            New TreeNode("المعاملات اليومية") With {.Tag = "DailyTransactions"},
            New TreeNode("المعاملات الشهرية") With {.Tag = "MonthlyTransactions"},
            New TreeNode("تقرير الإيرادات والمصروفات") With {.Tag = "IncomeExpenseReport"},
            New TreeNode("المعاملات حسب العملة") With {.Tag = "TransactionsByCurrency"}
        })

        ' تقارير الصناديق
        Dim cashBoxesNode As New TreeNode("تقارير الصناديق") With {.Tag = "CashBoxesReports"}
        cashBoxesNode.Nodes.AddRange({
            New TreeNode("أرصدة الصناديق") With {.Tag = "CashBoxesBalances"},
            New TreeNode("حركة الصندوق") With {.Tag = "CashBoxMovement"},
            New TreeNode("ملخص الصناديق") With {.Tag = "CashBoxesSummary"}
        })

        ' تقارير الديون
        Dim debtsNode As New TreeNode("تقارير الديون") With {.Tag = "DebtsReports"}
        debtsNode.Nodes.AddRange({
            New TreeNode("الديون النشطة") With {.Tag = "ActiveDebts"},
            New TreeNode("الديون المستحقة") With {.Tag = "OverdueDebts"},
            New TreeNode("الديون المسددة") With {.Tag = "PaidDebts"},
            New TreeNode("تقرير أعمار الديون") With {.Tag = "DebtAgingReport"}
        })

        ' تقارير إحصائية
        Dim statisticsNode As New TreeNode("التقارير الإحصائية") With {.Tag = "StatisticsReports"}
        statisticsNode.Nodes.AddRange({
            New TreeNode("الملخص المالي") With {.Tag = "FinancialSummary"},
            New TreeNode("تحليل الأداء الشهري") With {.Tag = "MonthlyPerformance"},
            New TreeNode("مقارنة الفترات") With {.Tag = "PeriodComparison"},
            New TreeNode("أهم العملاء") With {.Tag = "TopCustomers"}
        })

        ' إضافة العقد الرئيسية
        _treeViewReports.Nodes.AddRange({
            customersNode,
            suppliersNode,
            transactionsNode,
            cashBoxesNode,
            debtsNode,
            statisticsNode
        })

        ' توسيع العقد الرئيسية
        For Each node As TreeNode In _treeViewReports.Nodes
            node.Expand()
        Next
    End Sub

    ''' <summary>
    ''' إعداد لوحة المعايير
    ''' </summary>
    Private Sub SetupCriteriaPanel()
        _panelCriteria = New Panel With {
            .Height = 100,
            .Dock = DockStyle.Top,
            .BackColor = Color.White,
            .Padding = New Padding(10),
            .BorderStyle = BorderStyle.FixedSingle
        }

        ' عنوان اللوحة
        Dim lblCriteriaTitle As New Label With {
            .Text = "معايير التقرير",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 58, 64),
            .Size = New Size(200, 25),
            .Location = New Point(850, 10),
            .TextAlign = ContentAlignment.MiddleRight
        }

        ' من تاريخ
        Dim lblFromDate As New Label With {
            .Text = "من تاريخ:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(60, 20),
            .Location = New Point(980, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _dtpFromDate = New DateTimePicker With {
            .Size = New Size(150, 25),
            .Location = New Point(820, 45),
            .Format = DateTimePickerFormat.Short,
            .Value = DateTime.Today.AddDays(-30)
        }

        ' إلى تاريخ
        Dim lblToDate As New Label With {
            .Text = "إلى تاريخ:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(60, 20),
            .Location = New Point(740, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _dtpToDate = New DateTimePicker With {
            .Size = New Size(150, 25),
            .Location = New Point(580, 45),
            .Format = DateTimePickerFormat.Short,
            .Value = DateTime.Today
        }

        ' الطرف
        Dim lblEntity As New Label With {
            .Text = "الطرف:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 20),
            .Location = New Point(510, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbEntity = New ComboBox With {
            .Size = New Size(200, 25),
            .Location = New Point(300, 45),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }

        ' العملة
        Dim lblCurrency As New Label With {
            .Text = "العملة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 20),
            .Location = New Point(230, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbCurrency = New ComboBox With {
            .Size = New Size(100, 25),
            .Location = New Point(120, 45),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbCurrency.Items.AddRange({"الكل", "دينار", "دولار"})
        _cmbCurrency.SelectedIndex = 0

        ' تنسيق التقرير
        Dim lblFormat As New Label With {
            .Text = "التنسيق:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(60, 20),
            .Location = New Point(50, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbReportFormat = New ComboBox With {
            .Size = New Size(100, 25),
            .Location = New Point(10, 70),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbReportFormat.Items.AddRange({"جدول", "PDF", "Excel"})
        _cmbReportFormat.SelectedIndex = 0

        ' إضافة العناصر للوحة
        _panelCriteria.Controls.AddRange({
            lblCriteriaTitle,
            lblFromDate, _dtpFromDate,
            lblToDate, _dtpToDate,
            lblEntity, _cmbEntity,
            lblCurrency, _cmbCurrency,
            lblFormat, _cmbReportFormat
        })

        _splitContainer.Panel2.Controls.Add(_panelCriteria)
    End Sub

    ''' <summary>
    ''' إعداد عارض التقرير
    ''' </summary>
    Private Sub SetupReportViewer()
        _panelReportViewer = New Panel With {
            .Dock = DockStyle.Fill,
            .BackColor = Color.White,
            .Padding = New Padding(10)
        }

        ' عنوان التقرير
        _lblReportTitle = New Label With {
            .Text = "اختر تقريراً من القائمة",
            .Font = New Font("Segoe UI", 16, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 58, 64),
            .Size = New Size(1000, 30),
            .Location = New Point(10, 10),
            .TextAlign = ContentAlignment.MiddleRight
        }

        ' ملخص التقرير
        _lblReportSummary = New Label With {
            .Text = "",
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Size = New Size(1000, 25),
            .Location = New Point(10, 50),
            .TextAlign = ContentAlignment.MiddleRight
        }

        ' شبكة بيانات التقرير
        _dataGridViewReport = New DataGridView With {
            .Size = New Size(1000, 500),
            .Location = New Point(10, 85),
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            .ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.FromArgb(52, 58, 64),
                .ForeColor = Color.White,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .ColumnHeadersHeight = 40,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.White,
                .ForeColor = Color.FromArgb(33, 37, 41),
                .Font = New Font("Segoe UI", 9, FontStyle.Regular),
                .SelectionBackColor = Color.FromArgb(0, 123, 255),
                .SelectionForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .EnableHeadersVisualStyles = False,
            .GridColor = Color.FromArgb(222, 226, 230),
            .RowHeadersVisible = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .MultiSelect = False,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .RightToLeft = RightToLeft.Yes,
            .Visible = False
        }

        ' إضافة العناصر للوحة
        _panelReportViewer.Controls.AddRange({
            _lblReportTitle,
            _lblReportSummary,
            _dataGridViewReport
        })

        _splitContainer.Panel2.Controls.Add(_panelReportViewer)
    End Sub

#End Region

#Region "Report Generation Methods"

    ''' <summary>
    ''' إنشاء التقرير المحدد
    ''' </summary>
    Private Sub GenerateReport()
        If String.IsNullOrEmpty(_selectedReportType) Then
            MessageBox.Show("يرجى اختيار نوع التقرير", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If

        Try
            _lblReportSummary.Text = "جاري إنشاء التقرير..."
            Application.DoEvents()

            Select Case _selectedReportType
                Case "CustomersList"
                    GenerateCustomersListReport()
                Case "CustomersBalances"
                    GenerateCustomersBalancesReport()
                Case "SuppliersList"
                    GenerateSuppliersListReport()
                Case "SuppliersBalances"
                    GenerateSuppliersBalancesReport()
                Case "DailyTransactions"
                    GenerateDailyTransactionsReport()
                Case "MonthlyTransactions"
                    GenerateMonthlyTransactionsReport()
                Case "CashBoxesBalances"
                    GenerateCashBoxesBalancesReport()
                Case "ActiveDebts"
                    GenerateActiveDebtsReport()
                Case "OverdueDebts"
                    GenerateOverdueDebtsReport()
                Case "FinancialSummary"
                    GenerateFinancialSummaryReport()
                Case Else
                    MessageBox.Show("هذا التقرير قيد التطوير", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Return
            End Select

            _dataGridViewReport.Visible = True
            _btnPrint.Enabled = True
            _btnExport.Enabled = True

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            _lblReportSummary.Text = "خطأ في إنشاء التقرير"
        End Try
    End Sub

    ''' <summary>
    ''' تقرير قائمة العملاء
    ''' </summary>
    Private Sub GenerateCustomersListReport()
        _lblReportTitle.Text = "تقرير قائمة العملاء"

        Dim customers = _context.Customers.Where(Function(c) c.IsActive).OrderBy(Function(c) c.Name).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم العميل", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "Phone", .HeaderText = "الهاتف", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Email", .HeaderText = "البريد الإلكتروني", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Address", .HeaderText = "العنوان", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "BalanceIQD", .HeaderText = "الرصيد (د.ع)", .FillWeight = 15}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        For Each customer In customers
            _dataGridViewReport.Rows.Add(
                customer.Name,
                customer.Phone,
                customer.Email,
                customer.Address,
                $"{customer.CurrentBalanceIQD:N0} د.ع"
            )
        Next

        _lblReportSummary.Text = $"إجمالي العملاء: {customers.Count}"
    End Sub

    ''' <summary>
    ''' تقرير أرصدة العملاء
    ''' </summary>
    Private Sub GenerateCustomersBalancesReport()
        _lblReportTitle.Text = "تقرير أرصدة العملاء"

        Dim customers = _context.Customers.Where(Function(c) c.IsActive).OrderByDescending(Function(c) c.CurrentBalanceIQD).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم العميل", .FillWeight = 30},
            New DataGridViewTextBoxColumn With {.Name = "BalanceIQD", .HeaderText = "الرصيد (د.ع)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "BalanceUSD", .HeaderText = "الرصيد ($)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Status", .HeaderText = "الحالة", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "LastTransaction", .HeaderText = "آخر معاملة", .FillWeight = 15}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalIQD As Decimal = 0
        Dim totalUSD As Decimal = 0

        For Each customer In customers
            Dim status = If(customer.CurrentBalanceIQD > 0, "دائن", If(customer.CurrentBalanceIQD < 0, "مدين", "متوازن"))
            Dim lastTransaction = _context.Transactions.Where(Function(t) t.EntityType = "Customer" AndAlso t.EntityId = customer.Id).OrderByDescending(Function(t) t.CreatedAt).FirstOrDefault()

            _dataGridViewReport.Rows.Add(
                customer.Name,
                $"{customer.CurrentBalanceIQD:N0} د.ع",
                $"${customer.CurrentBalanceUSD:N2}",
                status,
                lastTransaction?.TransactionDate.ToString("dd/MM/yyyy") ?? "لا يوجد"
            )

            totalIQD += customer.CurrentBalanceIQD
            totalUSD += customer.CurrentBalanceUSD
        Next

        _lblReportSummary.Text = $"إجمالي العملاء: {customers.Count} | الإجمالي: {totalIQD:N0} د.ع، ${totalUSD:N2}"
    End Sub

    ''' <summary>
    ''' تقرير المعاملات اليومية
    ''' </summary>
    Private Sub GenerateDailyTransactionsReport()
        _lblReportTitle.Text = "تقرير المعاملات اليومية"

        Dim fromDate = _dtpFromDate.Value.Date
        Dim toDate = _dtpToDate.Value.Date

        Dim transactions = _context.Transactions.Where(Function(t) t.TransactionDate >= fromDate AndAlso t.TransactionDate <= toDate).OrderByDescending(Function(t) t.TransactionDate).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Date", .HeaderText = "التاريخ", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "InvoiceNumber", .HeaderText = "رقم الفاتورة", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Entity", .HeaderText = "الطرف", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Amount", .HeaderText = "المبلغ", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Currency", .HeaderText = "العملة", .FillWeight = 8},
            New DataGridViewTextBoxColumn With {.Name = "Description", .HeaderText = "الوصف", .FillWeight = 20}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalIncomeIQD As Decimal = 0
        Dim totalExpenseIQD As Decimal = 0
        Dim totalIncomeUSD As Decimal = 0
        Dim totalExpenseUSD As Decimal = 0

        For Each transaction In transactions
            Dim entityName = GetEntityName(transaction.EntityType, transaction.EntityId)

            _dataGridViewReport.Rows.Add(
                transaction.TransactionDate.ToString("dd/MM/yyyy"),
                transaction.InvoiceNumber,
                transaction.TypeInArabic,
                entityName,
                $"{transaction.Amount:N2}",
                transaction.Currency,
                transaction.Description
            )

            ' حساب الإجماليات
            If transaction.Type = "Income" Then
                If transaction.Currency = "IQD" Then
                    totalIncomeIQD += transaction.Amount
                Else
                    totalIncomeUSD += transaction.Amount
                End If
            Else
                If transaction.Currency = "IQD" Then
                    totalExpenseIQD += transaction.Amount
                Else
                    totalExpenseUSD += transaction.Amount
                End If
            End If
        Next

        _lblReportSummary.Text = $"المعاملات: {transactions.Count} | الواردات: {totalIncomeIQD:N0} د.ع، ${totalIncomeUSD:N2} | الصادرات: {totalExpenseIQD:N0} د.ع، ${totalExpenseUSD:N2}"
    End Sub

    ''' <summary>
    ''' الحصول على اسم الكيان
    ''' </summary>
    Private Function GetEntityName(entityType As String, entityId As Integer) As String
        Try
            If entityType = "Customer" Then
                Dim customer = _context.Customers.Find(entityId)
                Return customer?.Name ?? "عميل غير معروف"
            ElseIf entityType = "Supplier" Then
                Dim supplier = _context.Suppliers.Find(entityId)
                Return supplier?.Name ?? "مورد غير معروف"
            End If
        Catch
        End Try

        Return "غير محدد"
    End Function

    ''' <summary>
    ''' تقرير قائمة الموردين
    ''' </summary>
    Private Sub GenerateSuppliersListReport()
        _lblReportTitle.Text = "تقرير قائمة الموردين"

        Dim suppliers = _context.Suppliers.Where(Function(s) s.IsActive).OrderBy(Function(s) s.Name).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم المورد", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Phone", .HeaderText = "الهاتف", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Email", .HeaderText = "البريد الإلكتروني", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "TaxNumber", .HeaderText = "الرقم الضريبي", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Balance", .HeaderText = "الرصيد", .FillWeight = 20}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        For Each supplier In suppliers
            _dataGridViewReport.Rows.Add(
                supplier.Name,
                supplier.SupplierType,
                supplier.Phone,
                supplier.Email,
                supplier.TaxNumber,
                $"{supplier.CurrentBalanceIQD:N0} د.ع"
            )
        Next

        _lblReportSummary.Text = $"إجمالي الموردين: {suppliers.Count}"
    End Sub

    ''' <summary>
    ''' تقرير أرصدة الموردين
    ''' </summary>
    Private Sub GenerateSuppliersBalancesReport()
        _lblReportTitle.Text = "تقرير أرصدة الموردين"

        Dim suppliers = _context.Suppliers.Where(Function(s) s.IsActive).OrderByDescending(Function(s) s.CurrentBalanceIQD).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم المورد", .FillWeight = 30},
            New DataGridViewTextBoxColumn With {.Name = "BalanceIQD", .HeaderText = "الرصيد (د.ع)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "BalanceUSD", .HeaderText = "الرصيد ($)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Status", .HeaderText = "الحالة", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "LastTransaction", .HeaderText = "آخر معاملة", .FillWeight = 15}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalIQD As Decimal = 0
        Dim totalUSD As Decimal = 0

        For Each supplier In suppliers
            Dim status = If(supplier.CurrentBalanceIQD > 0, "دائن", If(supplier.CurrentBalanceIQD < 0, "مدين", "متوازن"))
            Dim lastTransaction = _context.Transactions.Where(Function(t) t.EntityType = "Supplier" AndAlso t.EntityId = supplier.Id).OrderByDescending(Function(t) t.CreatedAt).FirstOrDefault()

            _dataGridViewReport.Rows.Add(
                supplier.Name,
                $"{supplier.CurrentBalanceIQD:N0} د.ع",
                $"${supplier.CurrentBalanceUSD:N2}",
                status,
                lastTransaction?.TransactionDate.ToString("dd/MM/yyyy") ?? "لا يوجد"
            )

            totalIQD += supplier.CurrentBalanceIQD
            totalUSD += supplier.CurrentBalanceUSD
        Next

        _lblReportSummary.Text = $"إجمالي الموردين: {suppliers.Count} | الإجمالي: {totalIQD:N0} د.ع، ${totalUSD:N2}"
    End Sub

    ''' <summary>
    ''' تقرير المعاملات الشهرية
    ''' </summary>
    Private Sub GenerateMonthlyTransactionsReport()
        _lblReportTitle.Text = "تقرير المعاملات الشهرية"

        Dim fromDate = _dtpFromDate.Value.Date
        Dim toDate = _dtpToDate.Value.Date

        Dim monthlyData = _context.Transactions _
            .Where(Function(t) t.TransactionDate >= fromDate AndAlso t.TransactionDate <= toDate) _
            .GroupBy(Function(t) New With {t.TransactionDate.Year, t.TransactionDate.Month}) _
            .Select(Function(g) New With {
                .Year = g.Key.Year,
                .Month = g.Key.Month,
                .IncomeIQD = g.Where(Function(t) t.Type = "Income" AndAlso t.Currency = "IQD").Sum(Function(t) t.Amount),
                .ExpenseIQD = g.Where(Function(t) t.Type = "Expense" AndAlso t.Currency = "IQD").Sum(Function(t) t.Amount),
                .IncomeUSD = g.Where(Function(t) t.Type = "Income" AndAlso t.Currency = "USD").Sum(Function(t) t.Amount),
                .ExpenseUSD = g.Where(Function(t) t.Type = "Expense" AndAlso t.Currency = "USD").Sum(Function(t) t.Amount),
                .Count = g.Count()
            }) _
            .OrderBy(Function(x) x.Year).ThenBy(Function(x) x.Month) _
            .ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Month", .HeaderText = "الشهر", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "IncomeIQD", .HeaderText = "الواردات (د.ع)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "ExpenseIQD", .HeaderText = "الصادرات (د.ع)", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "NetIQD", .HeaderText = "الصافي (د.ع)", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "IncomeUSD", .HeaderText = "الواردات ($)", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "ExpenseUSD", .HeaderText = "الصادرات ($)", .FillWeight = 15}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        For Each item In monthlyData
            Dim monthName = New DateTime(item.Year, item.Month, 1).ToString("MMMM yyyy", New Globalization.CultureInfo("ar-SA"))
            Dim netIQD = item.IncomeIQD - item.ExpenseIQD

            _dataGridViewReport.Rows.Add(
                monthName,
                $"{item.IncomeIQD:N0}",
                $"{item.ExpenseIQD:N0}",
                $"{netIQD:N0}",
                $"{item.IncomeUSD:N2}",
                $"{item.ExpenseUSD:N2}"
            )
        Next

        _lblReportSummary.Text = $"عدد الأشهر: {monthlyData.Count}"
    End Sub

    ''' <summary>
    ''' تقرير أرصدة الصناديق
    ''' </summary>
    Private Sub GenerateCashBoxesBalancesReport()
        _lblReportTitle.Text = "تقرير أرصدة الصناديق"

        Dim cashBoxes = _context.CashBoxes.Include("Currency").Where(Function(cb) cb.IsActive).OrderBy(Function(cb) cb.Name).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم الصندوق", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Currency", .HeaderText = "العملة", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "InitialBalance", .HeaderText = "الرصيد الافتتاحي", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "CurrentBalance", .HeaderText = "الرصيد الحالي", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Difference", .HeaderText = "الفرق", .FillWeight = 10}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalCurrentIQD As Decimal = 0
        Dim totalCurrentUSD As Decimal = 0

        For Each cashBox In cashBoxes
            Dim difference = cashBox.CurrentBalance - cashBox.InitialBalance
            Dim currencySymbol = If(cashBox.Currency?.Code = "USD", "$", "د.ع")

            _dataGridViewReport.Rows.Add(
                cashBox.Name,
                cashBox.CashBoxTypeInArabic,
                cashBox.Currency?.Name,
                $"{cashBox.InitialBalance:N2} {currencySymbol}",
                $"{cashBox.CurrentBalance:N2} {currencySymbol}",
                $"{difference:N2} {currencySymbol}"
            )

            If cashBox.Currency?.Code = "IQD" Then
                totalCurrentIQD += cashBox.CurrentBalance
            Else
                totalCurrentUSD += cashBox.CurrentBalance
            End If
        Next

        _lblReportSummary.Text = $"عدد الصناديق: {cashBoxes.Count} | الإجمالي: {totalCurrentIQD:N0} د.ع، ${totalCurrentUSD:N2}"
    End Sub

    ''' <summary>
    ''' تقرير الديون النشطة
    ''' </summary>
    Private Sub GenerateActiveDebtsReport()
        _lblReportTitle.Text = "تقرير الديون النشطة"

        Dim debts = _context.Debts.Where(Function(d) d.Status = "Active").OrderBy(Function(d) d.DueDate).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Entity", .HeaderText = "الطرف", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "OriginalAmount", .HeaderText = "المبلغ الأصلي", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "PaidAmount", .HeaderText = "المدفوع", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "RemainingAmount", .HeaderText = "المتبقي", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "DueDate", .HeaderText = "تاريخ الاستحقاق", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "DaysRemaining", .HeaderText = "الأيام المتبقية", .FillWeight = 8}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalRemaining As Decimal = 0

        For Each debt In debts
            Dim entityName = GetEntityName(debt.EntityType, debt.EntityId)
            Dim daysRemaining = If(debt.DueDate.HasValue, (debt.DueDate.Value - DateTime.Today).Days, 0)

            _dataGridViewReport.Rows.Add(
                entityName,
                debt.EntityTypeInArabic,
                debt.GetFormattedOriginalAmount(),
                debt.GetFormattedPaidAmount(),
                debt.GetFormattedRemainingAmount(),
                debt.DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد",
                If(daysRemaining >= 0, $"{daysRemaining} يوم", $"متأخر {Math.Abs(daysRemaining)} يوم")
            )

            totalRemaining += debt.RemainingAmount
        Next

        _lblReportSummary.Text = $"عدد الديون: {debts.Count} | إجمالي المتبقي: {totalRemaining:N2}"
    End Sub

    ''' <summary>
    ''' تقرير الديون المستحقة
    ''' </summary>
    Private Sub GenerateOverdueDebtsReport()
        _lblReportTitle.Text = "تقرير الديون المستحقة"

        Dim overdueDebts = _context.Debts.Where(Function(d) d.Status = "Active" AndAlso d.DueDate.HasValue AndAlso d.DueDate.Value < DateTime.Today).OrderBy(Function(d) d.DueDate).ToList()

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Entity", .HeaderText = "الطرف", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "RemainingAmount", .HeaderText = "المبلغ المتبقي", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "DueDate", .HeaderText = "تاريخ الاستحقاق", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "DaysOverdue", .HeaderText = "أيام التأخير", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Priority", .HeaderText = "الأولوية", .FillWeight = 15}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        Dim totalOverdue As Decimal = 0

        For Each debt In overdueDebts
            Dim entityName = GetEntityName(debt.EntityType, debt.EntityId)
            Dim daysOverdue = (DateTime.Today - debt.DueDate.Value).Days
            Dim priority = If(daysOverdue > 30, "عالية", If(daysOverdue > 7, "متوسطة", "منخفضة"))

            _dataGridViewReport.Rows.Add(
                entityName,
                debt.EntityTypeInArabic,
                debt.GetFormattedRemainingAmount(),
                debt.DueDate.Value.ToString("dd/MM/yyyy"),
                $"{daysOverdue} يوم",
                priority
            )

            totalOverdue += debt.RemainingAmount
        Next

        _lblReportSummary.Text = $"عدد الديون المستحقة: {overdueDebts.Count} | إجمالي المتأخر: {totalOverdue:N2}"
    End Sub

    ''' <summary>
    ''' تقرير الملخص المالي
    ''' </summary>
    Private Sub GenerateFinancialSummaryReport()
        _lblReportTitle.Text = "تقرير الملخص المالي"

        ' حساب الإحصائيات
        Dim totalCustomers = _context.Customers.Count(Function(c) c.IsActive)
        Dim totalSuppliers = _context.Suppliers.Count(Function(s) s.IsActive)
        Dim totalCashBoxesIQD = _context.CashBoxes.Where(Function(cb) cb.Currency.Code = "IQD").Sum(Function(cb) cb.CurrentBalance)
        Dim totalCashBoxesUSD = _context.CashBoxes.Where(Function(cb) cb.Currency.Code = "USD").Sum(Function(cb) cb.CurrentBalance)
        Dim totalActiveDebts = _context.Debts.Where(Function(d) d.Status = "Active").Sum(Function(d) d.RemainingAmount)
        Dim totalOverdueDebts = _context.Debts.Where(Function(d) d.Status = "Active" AndAlso d.DueDate.HasValue AndAlso d.DueDate.Value < DateTime.Today).Sum(Function(d) d.RemainingAmount)

        ' إعداد الأعمدة
        _dataGridViewReport.Columns.Clear()
        _dataGridViewReport.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Category", .HeaderText = "الفئة", .FillWeight = 40},
            New DataGridViewTextBoxColumn With {.Name = "Value", .HeaderText = "القيمة", .FillWeight = 30},
            New DataGridViewTextBoxColumn With {.Name = "Description", .HeaderText = "الوصف", .FillWeight = 30}
        })

        ' إضافة البيانات
        _dataGridViewReport.Rows.Clear()
        _dataGridViewReport.Rows.Add("عدد العملاء النشطين", totalCustomers.ToString(), "إجمالي العملاء المفعلين في النظام")
        _dataGridViewReport.Rows.Add("عدد الموردين النشطين", totalSuppliers.ToString(), "إجمالي الموردين المفعلين في النظام")
        _dataGridViewReport.Rows.Add("إجمالي الصناديق (دينار)", $"{totalCashBoxesIQD:N0} د.ع", "مجموع أرصدة الصناديق بالدينار العراقي")
        _dataGridViewReport.Rows.Add("إجمالي الصناديق (دولار)", $"${totalCashBoxesUSD:N2}", "مجموع أرصدة الصناديق بالدولار الأمريكي")
        _dataGridViewReport.Rows.Add("إجمالي الديون النشطة", $"{totalActiveDebts:N2}", "مجموع الديون غير المسددة")
        _dataGridViewReport.Rows.Add("إجمالي الديون المستحقة", $"{totalOverdueDebts:N2}", "مجموع الديون المتأخرة عن موعد الاستحقاق")

        _lblReportSummary.Text = $"تم إنشاء الملخص المالي بتاريخ {DateTime.Now:dd/MM/yyyy HH:mm}"
    End Sub

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' اختيار تقرير من الشجرة
    ''' </summary>
    Private Sub TreeViewReports_AfterSelect(sender As Object, e As TreeViewEventArgs)
        If e.Node.Tag IsNot Nothing AndAlso Not e.Node.Tag.ToString().EndsWith("Reports") Then
            _selectedReportType = e.Node.Tag.ToString()
            _lblReportTitle.Text = e.Node.Text
            _lblReportSummary.Text = "اضغط على 'إنشاء التقرير' لعرض البيانات"
            _btnGenerate.Enabled = True

            ' إخفاء التقرير السابق
            _dataGridViewReport.Visible = False
            _btnPrint.Enabled = False
            _btnExport.Enabled = False

            ' تحديث قائمة الأطراف حسب نوع التقرير
            UpdateEntityComboBox()
        Else
            _selectedReportType = ""
            _btnGenerate.Enabled = False
        End If
    End Sub

    ''' <summary>
    ''' تحديث قائمة الأطراف حسب نوع التقرير
    ''' </summary>
    Private Sub UpdateEntityComboBox()
        _cmbEntity.Items.Clear()
        _cmbEntity.Items.Add("الكل")

        Try
            If _selectedReportType.Contains("Customer") Then
                Dim customers = _context.Customers.Where(Function(c) c.IsActive).OrderBy(Function(c) c.Name).ToList()
                For Each customer In customers
                    _cmbEntity.Items.Add(New ComboBoxItem With {.Text = customer.Name, .Value = customer.Id})
                Next
            ElseIf _selectedReportType.Contains("Supplier") Then
                Dim suppliers = _context.Suppliers.Where(Function(s) s.IsActive).OrderBy(Function(s) s.Name).ToList()
                For Each supplier In suppliers
                    _cmbEntity.Items.Add(New ComboBoxItem With {.Text = supplier.Name, .Value = supplier.Id})
                Next
            End If

            _cmbEntity.SelectedIndex = 0

        Catch ex As Exception
            ' تجاهل الأخطاء
        End Try
    End Sub

    ''' <summary>
    ''' إنشاء التقرير
    ''' </summary>
    Private Sub BtnGenerate_Click(sender As Object, e As EventArgs)
        GenerateReport()
    End Sub

    ''' <summary>
    ''' طباعة التقرير
    ''' </summary>
    Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
        Try
            ' إنشاء مربع حوار الطباعة
            Using printDialog As New PrintDialog()
                If printDialog.ShowDialog() = DialogResult.OK Then
                    MessageBox.Show("سيتم تنفيذ ميزة الطباعة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تصدير التقرير
    ''' </summary>
    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        Try
            Using saveDialog As New SaveFileDialog()
                saveDialog.Filter = "Excel Files|*.xlsx|CSV Files|*.csv|PDF Files|*.pdf"
                saveDialog.Title = "تصدير التقرير"
                saveDialog.FileName = $"{_lblReportTitle.Text}_{DateTime.Now:yyyyMMdd}"

                If saveDialog.ShowDialog() = DialogResult.OK Then
                    Select Case System.IO.Path.GetExtension(saveDialog.FileName).ToLower()
                        Case ".xlsx"
                            ExportToExcel(saveDialog.FileName)
                        Case ".csv"
                            ExportToCSV(saveDialog.FileName)
                        Case ".pdf"
                            ExportToPDF(saveDialog.FileName)
                    End Select
                End If
            End Using

        Catch ex As Exception
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحديث التقرير
    ''' </summary>
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        If Not String.IsNullOrEmpty(_selectedReportType) Then
            GenerateReport()
        End If
    End Sub

#End Region

#Region "Export Methods"

    ''' <summary>
    ''' تصدير إلى Excel
    ''' </summary>
    Private Sub ExportToExcel(fileName As String)
        MessageBox.Show("سيتم تنفيذ ميزة التصدير إلى Excel قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ''' <summary>
    ''' تصدير إلى CSV
    ''' </summary>
    Private Sub ExportToCSV(fileName As String)
        Try
            Using writer As New System.IO.StreamWriter(fileName, False, System.Text.Encoding.UTF8)
                ' كتابة رؤوس الأعمدة
                Dim headers As New List(Of String)
                For Each column As DataGridViewColumn In _dataGridViewReport.Columns
                    If column.Visible Then
                        headers.Add($"""{column.HeaderText}""")
                    End If
                Next
                writer.WriteLine(String.Join(",", headers))

                ' كتابة البيانات
                For Each row As DataGridViewRow In _dataGridViewReport.Rows
                    If Not row.IsNewRow Then
                        Dim values As New List(Of String)
                        For Each cell As DataGridViewCell In row.Cells
                            If _dataGridViewReport.Columns(cell.ColumnIndex).Visible Then
                                values.Add($"""{cell.Value?.ToString() ?? ""}""")
                            End If
                        Next
                        writer.WriteLine(String.Join(",", values))
                    End If
                Next
            End Using

            MessageBox.Show($"تم تصدير التقرير بنجاح إلى: {fileName}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"خطأ في تصدير CSV: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تصدير إلى PDF
    ''' </summary>
    Private Sub ExportToPDF(fileName As String)
        MessageBox.Show("سيتم تنفيذ ميزة التصدير إلى PDF قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

#End Region

End Class
