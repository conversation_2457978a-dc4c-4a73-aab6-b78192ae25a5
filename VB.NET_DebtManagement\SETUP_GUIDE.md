# 🚀 دليل إعداد وتشغيل نظام إدارة الديون

## 📋 المتطلبات الأساسية

### 🔧 البرامج المطلوبة:
- ✅ **Visual Studio 2019/2022** (Community أو أعلى)
- ✅ **.NET Framework 4.7.2** أو أحدث
- ✅ **SQL Server 2016** أو أحدث (أو SQL Server Express)
- ✅ **Entity Framework 6.4.4**

## 🛠️ خطوات الإعداد

### 1. 📁 إنشاء المشروع

```bash
# افتح Visual Studio
# File > New > Project
# اختر: Visual Basic > Windows Forms App (.NET Framework)
# اسم المشروع: DebtManagementSystem
# Framework: .NET Framework 4.7.2
```

### 2. 📦 تثبيت المكتبات المطلوبة

افتح **Package Manager Console** وشغل الأوامر التالية:

```powershell
# تثبيت Entity Framework
Install-Package EntityFramework -Version 6.4.4

# تثبيت SQL Server Provider
Install-Package System.Data.SqlClient

# تثبيت مكتبات إضافية (اختيارية)
Install-Package Microsoft.Office.Interop.Excel  # للتصدير إلى Excel
```

### 3. 🗃️ إعداد قاعدة البيانات

#### أ. إنشاء قاعدة البيانات:
```sql
-- افتح SQL Server Management Studio
-- شغل ملف: Database/CreateDatabase.sql
-- أو انسخ والصق المحتوى وشغله
```

#### ب. تحديث سلسلة الاتصال:
```xml
<!-- في ملف App.config -->
<connectionStrings>
  <add name="DebtManagementConnection" 
       connectionString="Server=localhost;Database=DebtManagementDB;Integrated Security=true;" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 4. 📂 إضافة الملفات للمشروع

#### أ. إنشاء المجلدات:
```
Solution Explorer > Add > New Folder
- Models
- Data  
- Forms
- My Project
```

#### ب. إضافة الملفات:
```
1. انسخ جميع ملفات .vb إلى المجلدات المناسبة
2. Add > Existing Item لكل ملف
3. تأكد من إضافة ملفات .resx للنماذج
```

### 5. 🔧 حل مشاكل الموارد

#### أ. إذا ظهرت رسالة خطأ الموارد:

```vb
' في كل نموذج، استبدل مراجع الموارد بـ:
' بدلاً من: My.Resources.IconName
' استخدم: Nothing أو أنشئ أيقونة بسيطة

' مثال في MainForm.vb:
Me.Icon = Nothing  ' أو استخدم أيقونة افتراضية
```

#### ب. إنشاء أيقونات بديلة:
```vb
' في النماذج، يمكنك استخدام:
Private Function CreateSimpleIcon(color As Color) As Bitmap
    Dim bmp As New Bitmap(16, 16)
    Using g As Graphics = Graphics.FromImage(bmp)
        g.FillRectangle(New SolidBrush(color), 0, 0, 16, 16)
    End Using
    Return bmp
End Function

' ثم استخدمها:
_btnAdd.Image = CreateSimpleIcon(Color.Green)
```

### 6. 🏗️ بناء المشروع

```bash
# في Visual Studio:
Build > Build Solution

# أو اضغط: Ctrl + Shift + B
```

### 7. ▶️ تشغيل التطبيق

```bash
# اضغط F5 أو Debug > Start Debugging
# أو Ctrl + F5 للتشغيل بدون تصحيح
```

## 🔐 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 🐛 حل المشاكل الشائعة

### ❌ مشكلة: "Could not find file .resx"

**الحل:**
```vb
' في كل نموذج، علق أو احذف مراجع الموارد:
' Me.Icon = My.Resources.AppIcon
Me.Icon = Nothing

' أو أنشئ ملفات .resx فارغة:
' Right-click on Form > Add > Resource File
```

### ❌ مشكلة: "Entity Framework not found"

**الحل:**
```powershell
# في Package Manager Console:
Update-Package EntityFramework -Reinstall
```

### ❌ مشكلة: "Database connection failed"

**الحل:**
```xml
<!-- تحقق من سلسلة الاتصال في App.config -->
<connectionStrings>
  <add name="DebtManagementConnection" 
       connectionString="Server=.\SQLEXPRESS;Database=DebtManagementDB;Integrated Security=true;" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### ❌ مشكلة: "Namespace not found"

**الحل:**
```vb
' أضف في بداية كل ملف:
Imports System.Data.Entity
Imports System.Linq
Imports System.Windows.Forms
Imports System.Drawing
```

## 📁 هيكل المشروع النهائي

```
DebtManagementSystem/
├── Models/
│   ├── Customer.vb
│   ├── Supplier.vb
│   ├── Transaction.vb
│   ├── CashBox.vb
│   ├── Currency.vb
│   ├── Debt.vb
│   ├── User.vb
│   └── SystemSetting.vb
├── Data/
│   ├── DebtContext.vb
│   └── DebtDatabaseInitializer.vb
├── Forms/
│   ├── LoginForm.vb
│   ├── MainForm.vb
│   ├── DashboardControl.vb
│   ├── CustomersForm.vb
│   ├── CustomerAddEditForm.vb
│   ├── SuppliersForm.vb
│   ├── SupplierAddEditForm.vb
│   ├── TransactionsForm.vb
│   ├── TransactionAddEditForm.vb
│   ├── CashBoxesForm.vb
│   ├── CashBoxAddEditForm.vb
│   ├── SettingsForm.vb
│   └── ReportsForm.vb
├── My Project/
│   ├── Application.Designer.vb
│   ├── Application.myapp
│   ├── AssemblyInfo.vb
│   ├── Resources.Designer.vb
│   └── Resources.resx
├── App.config
├── packages.config
└── DebtManagementSystem.vbproj
```

## 🎯 اختبار النظام

### 1. تسجيل الدخول:
- شغل التطبيق
- ادخل: admin / admin123
- اضغط دخول

### 2. اختبار الوظائف:
- ✅ لوحة التحكم
- ✅ إضافة عميل جديد
- ✅ إضافة مورد جديد
- ✅ إنشاء معاملة مالية
- ✅ عرض التقارير

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **تحقق من ملف App.config**
2. **تأكد من تثبيت Entity Framework**
3. **تحقق من اتصال قاعدة البيانات**
4. **راجع رسائل الخطأ في Output Window**

---

**🎉 مبروك! نظام إدارة الديون جاهز للاستخدام**
