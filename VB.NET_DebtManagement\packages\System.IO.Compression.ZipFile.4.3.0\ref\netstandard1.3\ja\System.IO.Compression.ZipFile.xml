﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression.ZipFile</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.ZipFile">
      <summary>zip アーカイブの作成、抽出、および解凍の静的メソッドを提供します。</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String)">
      <summary>指定したディレクトリのファイルおよびディレクトリを含む zip アーカイブを作成します。</summary>
      <param name="sourceDirectoryName">アーカイブするディレクトリのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="destinationArchiveFileName">作成するアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>指定したディレクトリのファイルおよびディレクトリを含む zip アーカイブを作成し、指定した圧縮レベルを使用し、オプションでベース ディレクトリを含みます。</summary>
      <param name="sourceDirectoryName">アーカイブするディレクトリのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="destinationArchiveFileName">作成するアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="compressionLevel">エントリの作成時に速度または圧縮の有効性を強調するかどうかを示す列挙値の 1 つ。</param>
      <param name="includeBaseDirectory">アーカイブのルートにある <paramref name="sourceDirectoryName" /> からのディレクトリ名を含める場合は true。ディレクトリの内容のみを含める場合は false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean,System.Text.Encoding)">
      <summary>指定したディレクトリのファイルおよびディレクトリを含む zip アーカイブを作成し、指定した圧縮レベルとエントリ名の文字エンコーディングを使用し、オプションでベース ディレクトリを含みます。</summary>
      <param name="sourceDirectoryName">アーカイブするディレクトリのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="destinationArchiveFileName">作成するアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="compressionLevel">エントリの作成時に速度または圧縮の有効性を強調するかどうかを示す列挙値の 1 つ。</param>
      <param name="includeBaseDirectory">アーカイブのルートにある <paramref name="sourceDirectoryName" /> からのディレクトリ名を含める場合は true。ディレクトリの内容のみを含める場合は false。</param>
      <param name="entryNameEncoding">このアーカイブのエントリ名の読み取りまたは書き込み時に使用するエンコード。エントリ名の UTF-8 エンコードをサポートしない zip アーカイブ ツールとライブラリとの相互運用性のためにエンコードが必要な場合にのみ、このパラメーターの値を指定してください。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String)">
      <summary>指定した zip アーカイブのすべてのファイルをファイル システムのディレクトリに抽出します。</summary>
      <param name="sourceArchiveFileName">抽出するアーカイブのパス。</param>
      <param name="destinationDirectoryName">抽出ファイルを置くディレクトリのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String,System.Text.Encoding)">
      <summary>指定した zip アーカイブのすべてのファイルをファイル システムのディレクトリに抽出し、エントリ名に指定した文字エンコーディングを使用します。</summary>
      <param name="sourceArchiveFileName">抽出するアーカイブのパス。</param>
      <param name="destinationDirectoryName">抽出ファイルを置くディレクトリのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="entryNameEncoding">このアーカイブのエントリ名の読み取りまたは書き込み時に使用するエンコード。エントリ名の UTF-8 エンコードをサポートしない zip アーカイブ ツールとライブラリとの相互運用性のためにエンコードが必要な場合にのみ、このパラメーターの値を指定してください。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode)">
      <summary>指定したパスとモードで zip アーカイブを開きます。</summary>
      <returns>開いている zip アーカイブ。</returns>
      <param name="archiveFileName">開くアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="mode">開いているアーカイブのエントリで許可されている操作を指定する列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode,System.Text.Encoding)">
      <summary>指定されたモードで、エントリ名に指定された文字エンコーディングを使用して指定されたパスの zip のアーカイブを開きます。</summary>
      <returns>開いている zip アーカイブ。</returns>
      <param name="archiveFileName">開くアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="mode">開いているアーカイブのエントリで許可されている操作を指定する列挙値の 1 つ。</param>
      <param name="entryNameEncoding">このアーカイブのエントリ名の読み取りまたは書き込み時に使用するエンコード。エントリ名の UTF-8 エンコードをサポートしない zip アーカイブ ツールとライブラリとの相互運用性のためにエンコードが必要な場合にのみ、このパラメーターの値を指定してください。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.OpenRead(System.String)">
      <summary>指定されたパスで読み取りのための zip のアーカイブを開きます。</summary>
      <returns>開いている zip アーカイブ。</returns>
      <param name="archiveFileName">開くアーカイブのパス。相対パスまたは絶対パスとして指定します。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipFileExtensions">
      <summary>
        <see cref="T:System.IO.Compression.ZipArchive" /> と <see cref="T:System.IO.Compression.ZipArchiveEntry" /> のクラスの拡張メソッドを提供します。</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String)">
      <summary>圧縮し、zip アーカイブに追加することでファイルをアーカイブします。</summary>
      <returns>zip アーカイブ内の新しいエントリのラッパー。</returns>
      <param name="destination">ファイルに追加するzip アーカイブ。</param>
      <param name="sourceFileName">アーカイブするファイルへのパス。相対パスまたは絶対パスを指定できます。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="entryName">zip アーカイブ内に作成するエントリの名前。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> が <see cref="F:System.String.Empty" /> であるか、空白文字のみが含まれているか、無効な文字が少なくとも 1 つ含まれています。または<paramref name="entryName" /> は <see cref="F:System.String.Empty" /> なので、</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> または <paramref name="entryName" /> が null です。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="sourceFileName" /> で、指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="sourceFileName" /> で指定されたファイルを開けません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> はディレクトリを指定します。または<paramref name="sourceFileName" /> によって指定されたファイルにアクセスするために必要なアクセス許可が呼び出し元にありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> で指定されたファイルが見つかりません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> パラメーターの書式が無効です。またはzip アーカイブは書き込みをサポートしません。</exception>
      <exception cref="T:System.ObjectDisposedException">zip アーカイブが破棄されました。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String,System.IO.Compression.CompressionLevel)">
      <summary>指定した圧縮レベルで圧縮し、zip アーカイブに追加することでファイルをアーカイブします。</summary>
      <returns>zip アーカイブ内の新しいエントリのラッパー。</returns>
      <param name="destination">ファイルに追加するzip アーカイブ。</param>
      <param name="sourceFileName">アーカイブするファイルへのパス。相対パスまたは絶対パスを指定できます。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="entryName">zip アーカイブ内に作成するエントリの名前。</param>
      <param name="compressionLevel">エントリの作成時に速度または圧縮の有効性を強調するかどうかを示す列挙値の 1 つ。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> が <see cref="F:System.String.Empty" /> であるか、空白文字のみが含まれているか、無効な文字が少なくとも 1 つ含まれています。または<paramref name="entryName" /> は <see cref="F:System.String.Empty" /> なので、</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> または <paramref name="entryName" /> が null です。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> が無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.PathTooLongException">
        <paramref name="sourceFileName" /> で、指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="sourceFileName" /> で指定されたファイルを開けません。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> はディレクトリを指定します。または<paramref name="sourceFileName" /> によって指定されたファイルにアクセスするために必要なアクセス許可が呼び出し元にありません。</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceFileName" /> で指定されたファイルが見つかりません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> パラメーターの書式が無効です。またはzip アーカイブは書き込みをサポートしません。</exception>
      <exception cref="T:System.ObjectDisposedException">zip アーカイブが破棄されました。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToDirectory(System.IO.Compression.ZipArchive,System.String)">
      <summary>zip アーカイブのすべてのファイルをファイル システムのディレクトリに抽出します。</summary>
      <param name="source">ファイルの抽出元となる zip アーカイブ。</param>
      <param name="destinationDirectoryName">抽出ファイルを置くディレクトリのパス。相対パスまたは絶対パスを指定できます。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> が <see cref="F:System.String.Empty" /> であるか、空白文字のみが含まれているか、無効な文字が少なくとも 1 つ含まれています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> は null なので、</exception>
      <exception cref="T:System.IO.PathTooLongException">指定されたパスがシステムで定義されている最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationDirectoryName" /> で指定されたディレクトリは既に存在します。またはアーカイブ内のエントリの名前が <see cref="F:System.String.Empty" /> であるか、名前に空白だけが含まれているか、無効な文字が少なくとも 1 つ含まれています。またはアーカイブからエントリを抽出すると、<paramref name="destinationDirectoryName" /> で指定されているディレクトリの外部にファイルが作成されます。(たとえば、エントリ名に親ディレクトリのアクセサーが含まれている場合に発生する可能性があります。)またはアーカイブの複数のエントリの名前が同じです。</exception>
      <exception cref="T:System.UnauthorizedAccessException">呼び出し元に、目的のディレクトリに書き込みするために必要な許可がありません。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> に無効な書式指定が格納されています。</exception>
      <exception cref="T:System.IO.InvalidDataException">アーカイブ エントリが見つからないか、破損しています。またはアーカイブ エントリはサポートされていない圧縮方法を使用して圧縮されました。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String)">
      <summary>zip アーカイブのエントリをファイルに抽出します。</summary>
      <param name="source">ファイルの抽出元となる zip アーカイブ エントリ。</param>
      <param name="destinationFileName">エントリの内容から作成するファイルのパス。相対パスまたは絶対パスを指定できます。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> が、長さが 0 の文字列である、空白しか含んでいない、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または<paramref name="destinationFileName" /> はディレクトリを指定します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> は null なので、</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> は既に存在します。またはI/O エラーが発生しました。または書き込みのため、エントリが現在開いています。またはエントリがアーカイブから削除されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">新しいファイルを作成するために必要なアクセス許可が呼び出し元にありません。</exception>
      <exception cref="T:System.IO.InvalidDataException">エントリがアーカイブにないか、または破損していて読み取ることができません。またはエントリは、サポートされていない圧縮方法を使用して圧縮されています。</exception>
      <exception cref="T:System.ObjectDisposedException">このエントリが属する zip アーカイブは破棄されています。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> の形式が無効です。またはこのエントリの zip アーカイブは、エントリの取得が許可されない <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> モードで開かれました。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String,System.Boolean)">
      <summary>zip アーカイブのエントリをファイルに抽出し、オプションとして同じ名前を持つ既存のファイルを上書きします。</summary>
      <param name="source">ファイルの抽出元となる zip アーカイブ エントリ。</param>
      <param name="destinationFileName">エントリの内容から作成するファイルのパス。相対パスまたは絶対パスを指定できます。相対パスは、現在の作業ディレクトリに対して相対的に解釈されます。</param>
      <param name="overwrite">ターゲットのファイルと同じ名前を持つ既存のファイルを上書きする場合は true。それ以外の場合は false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> が、長さが 0 の文字列である、空白しか含んでいない、<see cref="F:System.IO.Path.InvalidPathChars" /> で定義されている無効な文字を 1 つ以上含んでいます。または<paramref name="destinationFileName" /> はディレクトリを指定します。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> は null なので、</exception>
      <exception cref="T:System.IO.PathTooLongException">指定したパス、ファイル名、またはその両方がシステム定義の最大長を超えています。たとえば、Windows ベースのプラットフォームの場合、パスの長さは 248 文字未満、ファイル名の長さは 260 文字未満である必要があります。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定したパスが無効です (割り当てられていないドライブであるなど)。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> は既に存在しており、<paramref name="overwrite" /> が false です。またはI/O エラーが発生しました。または書き込みのため、エントリが現在開いています。またはエントリがアーカイブから削除されています。</exception>
      <exception cref="T:System.UnauthorizedAccessException">新しいファイルを作成するために必要なアクセス許可が呼び出し元にありません。</exception>
      <exception cref="T:System.IO.InvalidDataException">エントリがアーカイブにないか、または破損していて読み取ることができません。またはエントリは、サポートされていない圧縮方法を使用して圧縮されています。</exception>
      <exception cref="T:System.ObjectDisposedException">このエントリが属する zip アーカイブは破棄されています。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> の形式が無効です。またはこのエントリの zip アーカイブは、エントリの取得が許可されない <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> モードで開かれました。</exception>
    </member>
  </members>
</doc>