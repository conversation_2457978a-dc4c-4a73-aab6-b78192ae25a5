<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Application Icons -->
  <data name="AppIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>app_icon.ico;System.Drawing.Icon, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <!-- Form Icons -->
  <data name="CustomersIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>customers_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="SuppliersIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>suppliers_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="TransactionsIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>transactions_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="CashBoxIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>cashbox_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="ReportsIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>reports_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="SettingsIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>settings_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <!-- Toolbar Icons -->
  <data name="AddIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>add_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="EditIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>edit_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="DeleteIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>delete_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="RefreshIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>refresh_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="PrintIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>print_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="ExportIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>export_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="TransferIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>transfer_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="StatementIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>statement_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <data name="GenerateIcon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>generate_icon.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  
  <!-- Application Strings -->
  <data name="AppName" xml:space="preserve">
    <value>نظام إدارة الديون</value>
  </data>
  
  <data name="AppVersion" xml:space="preserve">
    <value>1.0.0</value>
  </data>
  
  <data name="CompanyName" xml:space="preserve">
    <value>شركة إدارة الديون</value>
  </data>
  
  <!-- Error Messages -->
  <data name="ErrorDatabaseConnection" xml:space="preserve">
    <value>خطأ في الاتصال بقاعدة البيانات</value>
  </data>
  
  <data name="ErrorInvalidLogin" xml:space="preserve">
    <value>اسم المستخدم أو كلمة المرور غير صحيحة</value>
  </data>
  
  <data name="ErrorRequiredField" xml:space="preserve">
    <value>هذا الحقل مطلوب</value>
  </data>
  
  <!-- Success Messages -->
  <data name="SuccessSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  
  <data name="SuccessDelete" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  
  <data name="SuccessUpdate" xml:space="preserve">
    <value>تم التحديث بنجاح</value>
  </data>
  
</root>
