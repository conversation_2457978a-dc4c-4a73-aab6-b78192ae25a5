<?php
/**
 * ملف الإعدادات العامة للنظام
 * System Configuration File
 */

// منع الوصول المباشر
if (!defined('SYSTEM_ACCESS')) {
    define('SYSTEM_ACCESS', true);
}

/**
 * إعدادات النظام العامة
 */
class Config {
    // إعدادات التطبيق
    const APP_NAME = 'نظام إدارة المعامل';
    const APP_VERSION = '1.0.0';
    const APP_URL = 'http://localhost/Factory_Management_System';
    const APP_DESCRIPTION = 'نظام متكامل لإدارة المعامل والإنتاج';
    
    // إعدادات الأمان
    const SESSION_TIMEOUT = 3600; // ساعة واحدة بالثواني
    const PASSWORD_MIN_LENGTH = 6;
    const MAX_LOGIN_ATTEMPTS = 3;
    const REMEMBER_ME_DURATION = 2592000; // 30 يوم بالثواني
    
    // إعدادات الملفات
    const UPLOAD_PATH = 'uploads/';
    const MAX_FILE_SIZE = 5242880; // 5MB بالبايت
    const ALLOWED_EXTENSIONS = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
    
    // إعدادات التقارير
    const REPORTS_PATH = 'reports/';
    const BACKUP_PATH = 'backups/';
    const TEMP_PATH = 'temp/';
    
    // إعدادات العملة الافتراضية
    const DEFAULT_CURRENCY = 'IQD';
    const DECIMAL_PLACES = 2;
    const CURRENCY_SYMBOL_IQD = 'د.ع';
    const CURRENCY_SYMBOL_USD = '$';
    const CURRENCY_SYMBOL_EUR = '€';
    
    // إعدادات التاريخ والوقت
    const TIMEZONE = 'Asia/Baghdad';
    const DATE_FORMAT = 'Y-m-d';
    const DATETIME_FORMAT = 'Y-m-d H:i:s';
    const DISPLAY_DATE_FORMAT = 'd/m/Y';
    const DISPLAY_DATETIME_FORMAT = 'd/m/Y H:i';
    
    // إعدادات قاعدة البيانات
    const DB_CHARSET = 'utf8mb4';
    const DB_COLLATION = 'utf8mb4_unicode_ci';
    
    // إعدادات الصفحات
    const ITEMS_PER_PAGE = 25;
    const MAX_SEARCH_RESULTS = 100;
    
    // إعدادات التنبيهات
    const LOW_STOCK_THRESHOLD = 10;
    const OVERDUE_PAYMENT_DAYS = 30;
    
    // إعدادات البريد الإلكتروني
    const MAIL_FROM_NAME = 'نظام إدارة المعامل';
    const MAIL_FROM_EMAIL = '<EMAIL>';
    
    // إعدادات النسخ الاحتياطي
    const BACKUP_RETENTION_DAYS = 30;
    const AUTO_BACKUP_ENABLED = true;
    const BACKUP_SCHEDULE = 'daily'; // daily, weekly, monthly
    
    /**
     * الحصول على إعداد
     */
    public static function get($key, $default = null) {
        return defined("self::{$key}") ? constant("self::{$key}") : $default;
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public static function getAll() {
        $reflection = new ReflectionClass(__CLASS__);
        return $reflection->getConstants();
    }
    
    /**
     * التحقق من وجود إعداد
     */
    public static function has($key) {
        return defined("self::{$key}");
    }
    
    /**
     * الحصول على مسار التطبيق
     */
    public static function getAppPath() {
        return dirname(__DIR__);
    }
    
    /**
     * الحصول على مسار الملفات العامة
     */
    public static function getPublicPath() {
        return self::getAppPath() . '/public';
    }
    
    /**
     * الحصول على مسار الرفع
     */
    public static function getUploadPath() {
        return self::getAppPath() . '/' . self::UPLOAD_PATH;
    }
    
    /**
     * الحصول على مسار التقارير
     */
    public static function getReportsPath() {
        return self::getAppPath() . '/' . self::REPORTS_PATH;
    }
    
    /**
     * الحصول على مسار النسخ الاحتياطي
     */
    public static function getBackupPath() {
        return self::getAppPath() . '/' . self::BACKUP_PATH;
    }
    
    /**
     * تحويل حجم الملف إلى نص قابل للقراءة
     */
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * التحقق من صحة امتداد الملف
     */
    public static function isAllowedFileExtension($extension) {
        return in_array(strtolower($extension), self::ALLOWED_EXTENSIONS);
    }
    
    /**
     * الحصول على رمز العملة
     */
    public static function getCurrencySymbol($currencyCode) {
        switch (strtoupper($currencyCode)) {
            case 'IQD':
                return self::CURRENCY_SYMBOL_IQD;
            case 'USD':
                return self::CURRENCY_SYMBOL_USD;
            case 'EUR':
                return self::CURRENCY_SYMBOL_EUR;
            default:
                return $currencyCode;
        }
    }
    
    /**
     * إنشاء المجلدات المطلوبة
     */
    public static function createRequiredDirectories() {
        $directories = [
            self::getUploadPath(),
            self::getReportsPath(),
            self::getBackupPath(),
            self::getAppPath() . '/' . self::TEMP_PATH
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }
    
    /**
     * الحصول على معلومات النظام
     */
    public static function getSystemInfo() {
        return [
            'app_name' => self::APP_NAME,
            'app_version' => self::APP_VERSION,
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'timezone' => self::TIMEZONE,
            'max_upload_size' => self::formatFileSize(self::MAX_FILE_SIZE),
            'session_timeout' => self::SESSION_TIMEOUT / 60 . ' دقيقة'
        ];
    }
}

/**
 * ثوابت النظام
 */
define('SYSTEM_NAME', Config::APP_NAME);
define('SYSTEM_VERSION', Config::APP_VERSION);
define('SYSTEM_TIMEZONE', Config::TIMEZONE);

// تعيين المنطقة الزمنية
date_default_timezone_set(Config::TIMEZONE);

// إنشاء المجلدات المطلوبة
Config::createRequiredDirectories();

/**
 * دوال مساعدة عامة
 */

/**
 * تنسيق المبلغ مع العملة
 */
function formatMoney($amount, $currency = 'IQD', $decimals = 2) {
    $formatted = number_format($amount, $decimals);
    $symbol = Config::getCurrencySymbol($currency);
    
    if ($currency === 'USD' || $currency === 'EUR') {
        return $symbol . $formatted;
    } else {
        return $formatted . ' ' . $symbol;
    }
}

/**
 * تنسيق التاريخ
 */
function formatDate($date, $format = null) {
    if (!$format) $format = Config::DISPLAY_DATE_FORMAT;
    return date($format, strtotime($date));
}

/**
 * تنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = null) {
    if (!$format) $format = Config::DISPLAY_DATETIME_FORMAT;
    return date($format, strtotime($datetime));
}

/**
 * تنظيف النص من الأكواد الضارة
 */
function cleanInput($data) {
    if (is_array($data)) {
        return array_map('cleanInput', $data);
    }
    return htmlspecialchars(trim(stripslashes($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * توليد رقم عشوائي
 */
function generateRandomNumber($prefix = '', $length = 6) {
    $number = str_pad(mt_rand(1, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    return $prefix . $number;
}

/**
 * توليد رمز مميز آمن
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * تحويل العملة
 */
function convertCurrency($amount, $fromRate, $toRate) {
    if ($fromRate <= 0 || $toRate <= 0) {
        return $amount;
    }
    return ($amount / $fromRate) * $toRate;
}

/**
 * حساب النسبة المئوية
 */
function calculatePercentage($value, $total) {
    return $total > 0 ? ($value / $total) * 100 : 0;
}

/**
 * حساب الخصم
 */
function calculateDiscount($amount, $percentage) {
    return $amount * ($percentage / 100);
}

/**
 * حساب الضريبة
 */
function calculateTax($amount, $rate) {
    return $amount * ($rate / 100);
}

/**
 * التحقق من كون الرقم موجب
 */
function isPositiveNumber($number) {
    return is_numeric($number) && $number > 0;
}

/**
 * تقريب المبلغ
 */
function roundMoney($amount, $decimals = 2) {
    return round($amount, $decimals);
}

/**
 * الحصول على الوقت الحالي بتنسيق قاعدة البيانات
 */
function getCurrentDateTime() {
    return date(Config::DATETIME_FORMAT);
}

/**
 * الحصول على التاريخ الحالي
 */
function getCurrentDate() {
    return date(Config::DATE_FORMAT);
}
?>
