Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج المعاملة المالية - Transaction Model
''' </summary>
<Table("Transactions")>
Public Class Transaction
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف المعاملة الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' نوع المعاملة (وارد/صادر)
    ''' </summary>
    <Required(ErrorMessage:="نوع المعاملة مطلوب")>
    <StringLength(10, ErrorMessage:="نوع المعاملة يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Type As String ' Income, Expense
    
    ''' <summary>
    ''' نوع الطرف (عميل/مورد)
    ''' </summary>
    <Required(ErrorMessage:="نوع الطرف مطلوب")>
    <StringLength(10, ErrorMessage:="نوع الطرف يجب أن يكون أقل من 10 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property EntityType As String ' Customer, Supplier
    
    ''' <summary>
    ''' معرف الطرف (العميل أو المورد)
    ''' </summary>
    <Required(ErrorMessage:="معرف الطرف مطلوب")>
    Public Property EntityId As Integer
    
    ''' <summary>
    ''' معرف الصندوق
    ''' </summary>
    <Required(ErrorMessage:="معرف الصندوق مطلوب")>
    Public Property CashBoxId As Integer
    
    ''' <summary>
    ''' مبلغ المعاملة
    ''' </summary>
    <Required(ErrorMessage:="مبلغ المعاملة مطلوب")>
    <Range(0.01, Double.MaxValue, ErrorMessage:="مبلغ المعاملة يجب أن يكون أكبر من صفر")>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property Amount As Decimal
    
    ''' <summary>
    ''' العملة
    ''' </summary>
    <Required(ErrorMessage:="العملة مطلوبة")>
    <StringLength(3, ErrorMessage:="رمز العملة يجب أن يكون 3 أحرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Currency As String ' IQD, USD
    
    ''' <summary>
    ''' سعر الصرف
    ''' </summary>
    <Column(TypeName:="DECIMAL(10,4)")>
    Public Property ExchangeRate As Decimal = 1
    
    ''' <summary>
    ''' وصف المعاملة
    ''' </summary>
    <StringLength(500, ErrorMessage:="وصف المعاملة يجب أن يكون أقل من 500 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Description As String
    
    ''' <summary>
    ''' رقم المرجع
    ''' </summary>
    <StringLength(50, ErrorMessage:="رقم المرجع يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property ReferenceNumber As String
    
    ''' <summary>
    ''' تاريخ المعاملة
    ''' </summary>
    <Required(ErrorMessage:="تاريخ المعاملة مطلوب")>
    <Column(TypeName:="DATE")>
    Public Property TransactionDate As Date
    
    ''' <summary>
    ''' حالة المعاملة
    ''' </summary>
    <StringLength(20, ErrorMessage:="حالة المعاملة يجب أن تكون أقل من 20 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Status As String = "Completed" ' Pending, Completed, Cancelled
    
    ''' <summary>
    ''' ملاحظات إضافية
    ''' </summary>
    <StringLength(1000, ErrorMessage:="الملاحظات يجب أن تكون أقل من 1000 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Notes As String
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' معرف المستخدم الذي أنشأ السجل
    ''' </summary>
    <Required(ErrorMessage:="معرف المستخدم مطلوب")>
    Public Property CreatedBy As Integer
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' الصندوق المرتبط بالمعاملة
    ''' </summary>
    <ForeignKey("CashBoxId")>
    Public Overridable Property CashBox As CashBox
    
    ''' <summary>
    ''' المستخدم الذي أنشأ المعاملة
    ''' </summary>
    <ForeignKey("CreatedBy")>
    Public Overridable Property Creator As User
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' اسم الطرف (محسوب)
    ''' </summary>
    <NotMapped>
    Public Property EntityName As String
    
    ''' <summary>
    ''' المبلغ بالدينار العراقي (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property AmountInIQD As Decimal
        Get
            If Currency.ToUpper() = "IQD" Then
                Return Amount
            Else
                Return Amount * ExchangeRate
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' المبلغ بالدولار الأمريكي (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property AmountInUSD As Decimal
        Get
            If Currency.ToUpper() = "USD" Then
                Return Amount
            Else
                Return Amount / ExchangeRate
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' نوع المعاملة بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TypeInArabic As String
        Get
            Select Case Type.ToLower()
                Case "income"
                    Return "وارد"
                Case "expense"
                    Return "صادر"
                Case Else
                    Return Type
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' نوع الطرف بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property EntityTypeInArabic As String
        Get
            Select Case EntityType.ToLower()
                Case "customer"
                    Return "عميل"
                Case "supplier"
                    Return "مورد"
                Case Else
                    Return EntityType
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة المعاملة بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property StatusInArabic As String
        Get
            Select Case Status.ToLower()
                Case "pending"
                    Return "معلقة"
                Case "completed"
                    Return "مكتملة"
                Case "cancelled"
                    Return "ملغية"
                Case Else
                    Return Status
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' رقم الفاتورة (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property InvoiceNumber As String
        Get
            Return String.Format("INV-{0:000000}", Id)
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تنسيق المبلغ للعرض
    ''' </summary>
    ''' <returns>المبلغ منسق</returns>
    Public Function GetFormattedAmount() As String
        If Currency.ToUpper() = "IQD" Then
            Return String.Format("{0:N0} د.ع", Amount)
        ElseIf Currency.ToUpper() = "USD" Then
            Return String.Format("${0:N2}", Amount)
        Else
            Return Amount.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(Type) Then
            errors.Add("نوع المعاملة مطلوب")
        ElseIf Not {"Income", "Expense"}.Contains(Type) Then
            errors.Add("نوع المعاملة غير صحيح")
        End If
        
        If String.IsNullOrWhiteSpace(EntityType) Then
            errors.Add("نوع الطرف مطلوب")
        ElseIf Not {"Customer", "Supplier"}.Contains(EntityType) Then
            errors.Add("نوع الطرف غير صحيح")
        End If
        
        If EntityId <= 0 Then
            errors.Add("معرف الطرف غير صحيح")
        End If
        
        If CashBoxId <= 0 Then
            errors.Add("معرف الصندوق غير صحيح")
        End If
        
        If Amount <= 0 Then
            errors.Add("مبلغ المعاملة يجب أن يكون أكبر من صفر")
        End If
        
        If String.IsNullOrWhiteSpace(Currency) Then
            errors.Add("العملة مطلوبة")
        ElseIf Not {"IQD", "USD"}.Contains(Currency.ToUpper()) Then
            errors.Add("العملة غير مدعومة")
        End If
        
        If ExchangeRate <= 0 Then
            errors.Add("سعر الصرف يجب أن يكون أكبر من صفر")
        End If
        
        If TransactionDate > Date.Today Then
            errors.Add("تاريخ المعاملة لا يمكن أن يكون في المستقبل")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' نسخ المعاملة
    ''' </summary>
    ''' <returns>نسخة من المعاملة</returns>
    Public Function Clone() As Transaction
        Return New Transaction With {
            .Type = Me.Type,
            .EntityType = Me.EntityType,
            .EntityId = Me.EntityId,
            .CashBoxId = Me.CashBoxId,
            .Amount = Me.Amount,
            .Currency = Me.Currency,
            .ExchangeRate = Me.ExchangeRate,
            .Description = Me.Description,
            .ReferenceNumber = Me.ReferenceNumber,
            .TransactionDate = Me.TransactionDate,
            .Status = Me.Status,
            .Notes = Me.Notes
        }
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للمعاملة
    ''' </summary>
    ''' <returns>وصف المعاملة</returns>
    Public Overrides Function ToString() As String
        Return String.Format("{0} - {1} - {2}", InvoiceNumber, TypeInArabic, GetFormattedAmount())
    End Function
    
#End Region

End Class
