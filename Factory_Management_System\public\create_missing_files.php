<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $created = [];
        
        // إنشاء صفحة الإنتاج
        if (!file_exists('production.php')) {
            $productionContent = '<?php
session_start();
require_once "../config/database.php";
require_once "../classes/Auth.php";

$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header("Location: login.php");
    exit;
}

$user = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-industry me-3"></i>إدارة الإنتاج</h1>
        <div class="alert alert-info">
            <h4>قيد التطوير</h4>
            <p>نظام إدارة الإنتاج قيد التطوير حالياً.</p>
            <a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>
        </div>
    </div>
</body>
</html>';
            file_put_contents('production.php', $productionContent);
            $created[] = 'production.php';
        }
        
        // إنشاء صفحة التقارير المالية
        if (!file_exists('financial.php')) {
            $financialContent = '<?php
session_start();
require_once "../config/database.php";
require_once "../classes/Auth.php";

$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header("Location: login.php");
    exit;
}

$user = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المالية - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-chart-bar me-3"></i>التقارير المالية</h1>
        <div class="alert alert-info">
            <h4>قيد التطوير</h4>
            <p>نظام التقارير المالية قيد التطوير حالياً.</p>
            <a href="dashboard.php" class="btn btn-primary">العودة للوحة التحكم</a>
        </div>
    </div>
</body>
</html>';
            file_put_contents('financial.php', $financialContent);
            $created[] = 'financial.php';
        }
        
        // إنشاء صفحة الإعدادات
        if (!file_exists('settings.php')) {
            $settingsContent = '<?php
session_start();
require_once "../config/database.php";
require_once "../classes/Auth.php";

$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header("Location: login.php");
    exit;
}

$user = $auth->getCurrentUser();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-cog me-3"></i>إعدادات النظام</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>أدوات الإعداد</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="complete_setup.php" class="btn btn-success">
                                <i class="fas fa-rocket me-2"></i>الإعداد الكامل
                            </a>
                            <a href="system_diagnosis.php" class="btn btn-info">
                                <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                            </a>
                            <a href="setup_employees.php" class="btn btn-primary">
                                <i class="fas fa-users me-2"></i>إعداد الموظفين
                            </a>
                            <a href="setup_inventory.php" class="btn btn-warning">
                                <i class="fas fa-warehouse me-2"></i>إعداد المخزون
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user me-2"></i>معلومات المستخدم</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> <?= htmlspecialchars($user["full_name"]) ?></p>
                        <p><strong>البريد:</strong> <?= htmlspecialchars($user["email"]) ?></p>
                        <p><strong>الدور:</strong> <?= htmlspecialchars($user["role"]) ?></p>
                        <a href="profile.php" class="btn btn-outline-primary">تعديل الملف الشخصي</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>';
            file_put_contents('settings.php', $settingsContent);
            $created[] = 'settings.php';
        }
        
        // إنشاء كلاس Helper إذا لم يكن موجوداً
        if (!file_exists('../classes/Helper.php')) {
            $helperContent = '<?php
class Helper {
    public static function cleanInput($input) {
        if (is_array($input)) {
            return array_map([self::class, "cleanInput"], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, "UTF-8");
    }
    
    public static function formatCurrency($amount, $currency = "د.ع") {
        return number_format($amount, 2) . " " . $currency;
    }
    
    public static function formatDate($date, $format = "d/m/Y") {
        return date($format, strtotime($date));
    }
    
    public static function generateCode($prefix, $length = 4) {
        $timestamp = time();
        $random = rand(1000, 9999);
        return $prefix . str_pad($random, $length, "0", STR_PAD_LEFT);
    }
    
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }
    
    public static function validatePhone($phone) {
        return preg_match("/^[0-9+\-\s()]+$/", $phone);
    }
}';
            file_put_contents('../classes/Helper.php', $helperContent);
            $created[] = '../classes/Helper.php';
        }
        
        if (!empty($created)) {
            $success = 'تم إنشاء الملفات التالية بنجاح: ' . implode(', ', $created);
        } else {
            $success = 'جميع الملفات موجودة بالفعل.';
        }
        
    } catch (Exception $e) {
        $error = 'خطأ في إنشاء الملفات: ' . $e->getMessage();
    }
}

// فحص الملفات المفقودة
$missingFiles = [];
$requiredFiles = [
    'production.php' => 'صفحة الإنتاج',
    'financial.php' => 'صفحة التقارير المالية',
    'settings.php' => 'صفحة الإعدادات',
    '../classes/Helper.php' => 'كلاس Helper'
];

foreach ($requiredFiles as $file => $name) {
    if (!file_exists($file)) {
        $missingFiles[$file] = $name;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء الملفات المفقودة - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .fix-body {
            padding: 40px;
        }
        .btn-fix {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1><i class="fas fa-file-medical fa-2x mb-3"></i><br>إنشاء الملفات المفقودة</h1>
            <p class="mb-0">إنشاء الملفات والكلاسات المطلوبة للنظام</p>
        </div>
        
        <div class="fix-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                
                <div class="text-center">
                    <a href="system_diagnosis.php" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                    </a>
                    <a href="dashboard.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-home me-2"></i>لوحة التحكم
                    </a>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($missingFiles)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>الملفات المفقودة</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <?php foreach ($missingFiles as $file => $name): ?>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong><?= $name ?></strong>
                                            <br><small class="text-muted"><code><?= $file ?></code></small>
                                        </div>
                                        <span class="badge bg-danger">مفقود</span>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>

                    <div class="text-center">
                        <form method="POST" class="d-inline">
                            <button type="submit" class="btn btn-fix btn-lg me-3">
                                <i class="fas fa-magic me-2"></i>إنشاء الملفات المفقودة
                            </button>
                        </form>
                        
                        <a href="system_diagnosis.php" class="btn btn-info btn-lg">
                            <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                        </a>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        <strong>ممتاز!</strong> جميع الملفات المطلوبة موجودة.
                    </div>
                    
                    <div class="text-center">
                        <a href="system_diagnosis.php" class="btn btn-info btn-lg me-3">
                            <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                        </a>
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>لوحة التحكم
                        </a>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
