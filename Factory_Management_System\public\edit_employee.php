<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$employeeId = (int)($_GET['id'] ?? 0);
if (!$employeeId) {
    header('Location: employees.php');
    exit;
}

$success = '';
$error = '';

// جلب بيانات الموظف
try {
    $employee = $db->fetchOne("
        SELECT e.*, d.name as department_name 
        FROM employees e 
        LEFT JOIN departments d ON e.department_id = d.id 
        WHERE e.id = ?
    ", [$employeeId]);
    
    if (!$employee) {
        header('Location: employees.php');
        exit;
    }
} catch (Exception $e) {
    $error = "خطأ في جلب بيانات الموظف: " . $e->getMessage();
    $employee = null;
}

// معالجة تحديث بيانات الموظف
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_employee'])) {
    try {
        $db->beginTransaction();
        
        // معالجة رفع الصورة الجديدة
        $photoPath = $employee['photo']; // الاحتفاظ بالصورة الحالية
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = '../uploads/employees/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $fileType = $_FILES['photo']['type'];
            
            if (in_array($fileType, $allowedTypes)) {
                $extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
                $fileName = $employee['employee_code'] . '_' . time() . '.' . $extension;
                $uploadPath = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['photo']['tmp_name'], $uploadPath)) {
                    // حذف الصورة القديمة
                    if ($employee['photo'] && file_exists('../' . $employee['photo'])) {
                        unlink('../' . $employee['photo']);
                    }
                    $photoPath = 'uploads/employees/' . $fileName;
                }
            }
        }
        
        // تحديث البيانات
        $updateData = [
            'full_name' => trim($_POST['full_name']),
            'email' => trim($_POST['email'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'national_id' => trim($_POST['national_id'] ?? ''),
            'birth_date' => !empty($_POST['birth_date']) ? $_POST['birth_date'] : null,
            'hire_date' => $_POST['hire_date'],
            'department_id' => !empty($_POST['department_id']) ? (int)$_POST['department_id'] : null,
            'position' => trim($_POST['position']),
            'salary' => (float)($_POST['salary'] ?? 0),
            'hourly_rate' => (float)($_POST['hourly_rate'] ?? 0),
            'overtime_rate' => (float)($_POST['overtime_rate'] ?? 0),
            'status' => $_POST['status'],
            'bank_account' => trim($_POST['bank_account'] ?? ''),
            'emergency_contact' => trim($_POST['emergency_contact'] ?? ''),
            'photo' => $photoPath,
            'notes' => trim($_POST['notes'] ?? '')
        ];
        
        $db->update('employees', $updateData, ['id' => $employeeId]);
        $db->commit();
        
        $success = "تم تحديث بيانات الموظف بنجاح";
        
        // إعادة جلب البيانات المحدثة
        $employee = $db->fetchOne("
            SELECT e.*, d.name as department_name 
            FROM employees e 
            LEFT JOIN departments d ON e.department_id = d.id 
            WHERE e.id = ?
        ", [$employeeId]);
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في تحديث بيانات الموظف: " . $e->getMessage();
    }
}

// جلب الأقسام
try {
    $departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $departments = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الموظف - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .employee-photo {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid #28a745;
        }
        .photo-placeholder {
            width: 150px;
            height: 150px;
            background: #e9ecef;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <?php if (!$employee): ?>
        <div class="container mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                الموظف غير موجود أو حدث خطأ في جلب البيانات
                <br><a href="employees.php" class="btn btn-sm btn-primary mt-2">العودة إلى قائمة الموظفين</a>
            </div>
        </div>
    <?php else: ?>
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1><i class="fas fa-user-edit me-3"></i>تعديل بيانات الموظف</h1>
                        <p class="mb-0"><?= htmlspecialchars($employee['full_name']) ?> - <?= htmlspecialchars($employee['employee_code']) ?></p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="employees.php" class="btn btn-light">
                            <i class="fas fa-users me-2"></i>قائمة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <!-- صورة الموظف -->
                    <div class="col-lg-3">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-camera me-2"></i>صورة الموظف</h5>
                            </div>
                            <div class="card-body text-center">
                                <?php if ($employee['photo'] && file_exists($employee['photo'])): ?>
                                    <img src="<?= htmlspecialchars($employee['photo']) ?>" class="employee-photo mb-3" alt="صورة الموظف">
                                <?php else: ?>
                                    <div class="photo-placeholder mb-3">
                                        <i class="fas fa-user fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="mb-3">
                                    <label class="form-label">تغيير الصورة</label>
                                    <input type="file" name="photo" class="form-control" accept="image/*">
                                    <div class="form-text">PNG, JPG, GIF (حد أقصى 2MB)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات سريعة -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات سريعة</h5>
                            </div>
                            <div class="card-body">
                                <p><strong>كود الموظف:</strong> <?= htmlspecialchars($employee['employee_code']) ?></p>
                                <p><strong>تاريخ التوظيف:</strong> <?= date('d/m/Y', strtotime($employee['hire_date'])) ?></p>
                                <p><strong>القسم:</strong> <?= htmlspecialchars($employee['department_name'] ?: 'غير محدد') ?></p>
                                <p class="mb-0"><strong>الحالة:</strong> 
                                    <?php
                                    $statusNames = [
                                        'active' => 'نشط',
                                        'inactive' => 'غير نشط',
                                        'terminated' => 'منتهي الخدمة'
                                    ];
                                    $statusColors = [
                                        'active' => 'success',
                                        'inactive' => 'warning',
                                        'terminated' => 'danger'
                                    ];
                                    ?>
                                    <span class="badge bg-<?= $statusColors[$employee['status']] ?>">
                                        <?= $statusNames[$employee['status']] ?>
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- بيانات الموظف -->
                    <div class="col-lg-9">
                        <div class="row">
                            <!-- المعلومات الشخصية -->
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>المعلومات الشخصية</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" name="full_name" class="form-control" 
                                                   value="<?= htmlspecialchars($employee['full_name']) ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني</label>
                                            <input type="email" name="email" class="form-control" 
                                                   value="<?= htmlspecialchars($employee['email']) ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="text" name="phone" class="form-control" 
                                                   value="<?= htmlspecialchars($employee['phone']) ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">العنوان</label>
                                            <textarea name="address" class="form-control" rows="3"><?= htmlspecialchars($employee['address']) ?></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهوية</label>
                                            <input type="text" name="national_id" class="form-control" 
                                                   value="<?= htmlspecialchars($employee['national_id']) ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ الميلاد</label>
                                            <input type="date" name="birth_date" class="form-control" 
                                                   value="<?= $employee['birth_date'] ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات العمل -->
                            <div class="col-lg-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                            <input type="date" name="hire_date" class="form-control" 
                                                   value="<?= $employee['hire_date'] ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">القسم</label>
                                            <select name="department_id" class="form-select">
                                                <option value="">اختر القسم</option>
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= $dept['id'] ?>" 
                                                            <?= $employee['department_id'] == $dept['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($dept['name']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">المنصب <span class="text-danger">*</span></label>
                                            <input type="text" name="position" class="form-control" 
                                                   value="<?= htmlspecialchars($employee['position']) ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">الحالة</label>
                                            <select name="status" class="form-select">
                                                <option value="active" <?= $employee['status'] == 'active' ? 'selected' : '' ?>>نشط</option>
                                                <option value="inactive" <?= $employee['status'] == 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                                                <option value="terminated" <?= $employee['status'] == 'terminated' ? 'selected' : '' ?>>منتهي الخدمة</option>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">الراتب الأساسي (د.ع)</label>
                                            <input type="number" name="salary" class="form-control" step="0.01" min="0" 
                                                   value="<?= $employee['salary'] ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">أجر الساعة (د.ع)</label>
                                            <input type="number" name="hourly_rate" class="form-control" step="0.01" min="0" 
                                                   value="<?= $employee['hourly_rate'] ?>">
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">أجر الساعة الإضافية (د.ع)</label>
                                            <input type="number" name="overtime_rate" class="form-control" step="0.01" min="0" 
                                                   value="<?= $employee['overtime_rate'] ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الحساب البنكي</label>
                                        <input type="text" name="bank_account" class="form-control" 
                                               value="<?= htmlspecialchars($employee['bank_account']) ?>">
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">جهة الاتصال في الطوارئ</label>
                                        <input type="text" name="emergency_contact" class="form-control" 
                                               value="<?= htmlspecialchars($employee['emergency_contact']) ?>">
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea name="notes" class="form-control" rows="3"><?= htmlspecialchars($employee['notes']) ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" name="update_employee" class="btn btn-primary btn-lg me-3">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                            <a href="employees.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
