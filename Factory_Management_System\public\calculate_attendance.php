<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$db = new Database();

// قراءة البيانات من JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['employee_id']) || !isset($input['period_start']) || !isset($input['period_end'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

try {
    $employeeId = (int)$input['employee_id'];
    $periodStart = $input['period_start'];
    $periodEnd = $input['period_end'];
    
    // حساب إحصائيات الحضور
    $attendanceStats = $db->fetchOne("
        SELECT 
            SUM(total_hours) as total_hours,
            SUM(overtime_hours) as overtime_hours,
            COUNT(*) as working_days,
            COUNT(CASE WHEN status = 'present' THEN 1 END) as present_days,
            COUNT(CASE WHEN status = 'absent' THEN 1 END) as absent_days,
            COUNT(CASE WHEN status = 'late' THEN 1 END) as late_days
        FROM attendance 
        WHERE employee_id = ? AND attendance_date BETWEEN ? AND ?
    ", [$employeeId, $periodStart, $periodEnd]);
    
    $response = [
        'success' => true,
        'working_days' => (int)($attendanceStats['working_days'] ?? 0),
        'present_days' => (int)($attendanceStats['present_days'] ?? 0),
        'absent_days' => (int)($attendanceStats['absent_days'] ?? 0),
        'late_days' => (int)($attendanceStats['late_days'] ?? 0),
        'total_hours' => round((float)($attendanceStats['total_hours'] ?? 0), 2),
        'overtime_hours' => round((float)($attendanceStats['overtime_hours'] ?? 0), 2)
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
