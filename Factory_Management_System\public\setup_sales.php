<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول العملاء
        $db->query("CREATE TABLE IF NOT EXISTS customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول فواتير المبيعات
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(50) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
            status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول تفاصيل فواتير المبيعات
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoice_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED
        )");
        
        // إضافة عملاء تجريبيين
        $customers = [
            ['CUST001', 'شركة النور للتجارة', 'company', '07701234567', '<EMAIL>', 'بغداد - الكرادة', '*********', 5000000.00],
            ['CUST002', 'أحمد محمد علي', 'individual', '07709876543', '<EMAIL>', 'البصرة - المعقل', '', 1000000.00],
            ['CUST003', 'مؤسسة الفرات للمقاولات', 'institution', '07801234567', '<EMAIL>', 'النجف - المركز', '*********', 10000000.00],
            ['CUST004', 'سارة أحمد حسن', 'individual', '07751234567', '<EMAIL>', 'أربيل - عنكاوا', '', 500000.00],
            ['CUST005', 'شركة دجلة للصناعات', 'company', '07781234567', '<EMAIL>', 'بغداد - الدورة', '*********', 8000000.00]
        ];
        
        foreach ($customers as $customer) {
            $existing = $db->fetchOne("SELECT id FROM customers WHERE code = ?", [$customer[0]]);
            if (!$existing) {
                $db->query("INSERT INTO customers (code, name, type, phone, email, address, tax_number, credit_limit, currency_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)", $customer);
            }
        }
        
        // إضافة أصناف للبيع إذا لم تكن موجودة
        $salesItems = [
            ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100.00, 150.00],
            ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80.00, 120.00],
            ['PROD003', 'منتج نهائي ج', 'منتج جاهز للبيع', 2, 'علبة', 'finished_product', 200.00, 280.00],
            ['SEMI001', 'منتج نصف مصنع أ', 'منتج في مرحلة التصنيع', 3, 'قطعة', 'semi_finished', 60.00, 90.00],
            ['SEMI002', 'منتج نصف مصنع ب', 'منتج في مرحلة التصنيع', 3, 'كيلو', 'semi_finished', 45.00, 70.00]
        ];
        
        foreach ($salesItems as $item) {
            $existing = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$item[0]]);
            if (!$existing) {
                $db->query("INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)", $item);
            }
        }
        
        $db->commit();
        $success = 'تم إعداد نظام المبيعات بنجاح! تم إنشاء الجداول وإضافة البيانات التجريبية.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إعداد نظام المبيعات: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tables = ['customers', 'sales_invoices', 'sales_invoice_details', 'items', 'warehouses', 'currencies'];
$tableStatus = [];

foreach ($tables as $table) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        $tableStatus[$table] = !empty($result);
    } catch (Exception $e) {
        $tableStatus[$table] = false;
    }
}

$allTablesExist = !in_array(false, $tableStatus);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام المبيعات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .table-status {
            font-size: 0.9rem;
        }
        .status-icon {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-shopping-cart fa-2x mb-3"></i><br>إعداد نظام المبيعات</h1>
            <p class="mb-0">إعداد جداول وبيانات نظام المبيعات</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجداول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة جداول المبيعات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-status">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $tableDescriptions = [
                                    'customers' => 'جدول العملاء',
                                    'sales_invoices' => 'جدول فواتير المبيعات',
                                    'sales_invoice_details' => 'جدول تفاصيل فواتير المبيعات',
                                    'items' => 'جدول الأصناف',
                                    'warehouses' => 'جدول المخازن',
                                    'currencies' => 'جدول العملات'
                                ];
                                
                                foreach ($tableDescriptions as $table => $description):
                                    $exists = $tableStatus[$table] ?? false;
                                ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <i class="fas fa-check-circle text-success status-icon"></i> موجود
                                            <?php else: ?>
                                                <i class="fas fa-times-circle text-danger status-icon"></i> غير موجود
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $description ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- معلومات العملاء التجريبيين -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users me-2"></i>العملاء التجريبيين</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الكود</th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>الهاتف</th>
                                    <th>حد الائتمان</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>CUST001</code></td>
                                    <td>شركة النور للتجارة</td>
                                    <td><span class="badge bg-primary">شركة</span></td>
                                    <td>07701234567</td>
                                    <td>5,000,000 د.ع</td>
                                </tr>
                                <tr>
                                    <td><code>CUST002</code></td>
                                    <td>أحمد محمد علي</td>
                                    <td><span class="badge bg-success">فرد</span></td>
                                    <td>07709876543</td>
                                    <td>1,000,000 د.ع</td>
                                </tr>
                                <tr>
                                    <td><code>CUST003</code></td>
                                    <td>مؤسسة الفرات للمقاولات</td>
                                    <td><span class="badge bg-warning">مؤسسة</span></td>
                                    <td>07801234567</td>
                                    <td>10,000,000 د.ع</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!$allTablesExist): ?>
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-setup btn-lg me-3">
                            <i class="fas fa-play me-2"></i>إعداد نظام المبيعات
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="add_sale.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>فاتورة جديدة
                </a>
                
                <a href="sales.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>المبيعات
                </a>
            </div>

            <?php if ($allTablesExist): ?>
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>نظام المبيعات جاهز!</strong> يمكنك الآن إنشاء فواتير مبيعات جديدة.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
