<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <!-- إعدادات التطبيق -->
  <appSettings>
    <!-- إعدادات عامة -->
    <add key="ApplicationName" value="نظام إدارة الديون" />
    <add key="ApplicationVersion" value="1.0.0" />
    <add key="SeedTestData" value="true" />
    
    <!-- إعدادات الأمان -->
    <add key="PasswordMinLength" value="6" />
    <add key="SessionTimeoutMinutes" value="30" />
    <add key="MaxLoginAttempts" value="3" />
    
    <!-- إعدادات النسخ الاحتياطي -->
    <add key="BackupEnabled" value="true" />
    <add key="BackupPath" value="C:\DebtManagement\Backups" />
    <add key="BackupRetentionDays" value="30" />
    
    <!-- إعدادات التقارير -->
    <add key="ReportsPath" value="C:\DebtManagement\Reports" />
    <add key="TempPath" value="C:\DebtManagement\Temp" />
    
    <!-- إعدادات الطباعة -->
    <add key="DefaultPrinter" value="" />
    <add key="PrintPreview" value="true" />
    
    <!-- إعدادات التصدير -->
    <add key="ExportPath" value="C:\DebtManagement\Exports" />
    <add key="ExportFormats" value="PDF,Excel,Word" />
  </appSettings>
  
  <!-- سلاسل الاتصال -->
  <connectionStrings>
    <!-- اتصال SQL Server المحلي -->
    <add name="DebtContext" 
         connectionString="Data Source=.;Initial Catalog=DebtManagementSystem;Integrated Security=True;MultipleActiveResultSets=True;Application Name=DebtManagement" 
         providerName="System.Data.SqlClient" />
    
    <!-- اتصال SQL Server Express (بديل) -->
    <!--<add name="DebtContext" 
         connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementSystem;Integrated Security=True;MultipleActiveResultSets=True;Application Name=DebtManagement" 
         providerName="System.Data.SqlClient" />-->
    
    <!-- اتصال SQL Server مع مصادقة SQL (بديل) -->
    <!--<add name="DebtContext" 
         connectionString="Data Source=ServerName;Initial Catalog=DebtManagementSystem;User ID=username;Password=password;MultipleActiveResultSets=True;Application Name=DebtManagement" 
         providerName="System.Data.SqlClient" />-->
  </connectionStrings>
  
  <!-- إعدادات Entity Framework -->
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  
  <!-- إعدادات وقت التشغيل -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*********" newVersion="*********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  
  <!-- إعدادات النظام -->
  <system.data>
    <DbProviderFactories>
      <remove invariant="System.Data.SQLite.EF6" />
      <add name="SQLite Data Provider (Entity Framework 6)" invariant="System.Data.SQLite.EF6" description=".NET Framework Data Provider for SQLite (Entity Framework 6)" type="System.Data.SQLite.EF6.SQLiteProviderFactory, System.Data.SQLite.EF6" />
    </DbProviderFactories>
  </system.data>
  
  <!-- إعدادات التشخيص -->
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="textWriterTraceListener" 
             type="System.Diagnostics.TextWriterTraceListener" 
             initializeData="C:\DebtManagement\Logs\trace.log" />
      </listeners>
    </trace>
  </system.diagnostics>
  
  <!-- إعدادات .NET Framework -->
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  
  <!-- إعدادات مخصصة للتطبيق -->
  <system.windows.forms jitDebugging="true" />
  
</configuration>
