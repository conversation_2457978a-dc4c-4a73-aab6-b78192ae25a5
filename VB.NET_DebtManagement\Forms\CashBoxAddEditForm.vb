Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إضافة/تعديل الصندوق - CashBox Add/Edit Form
''' </summary>
Public Class CashBoxAddEditForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User
    Private _cashBox As CashBox
    Private _isEditMode As Boolean

    ' عناصر التحكم
    Private _txtName As TextBox
    Private _cmbCashBoxType As ComboBox
    Private _cmbCurrency As ComboBox
    Private _txtInitialBalance As TextBox
    Private _txtMinimumBalance As TextBox
    Private _txtMaximumBalance As TextBox
    Private _txtBankName As TextBox
    Private _txtAccountNumber As TextBox
    Private _txtIBAN As TextBox
    Private _txtSwiftCode As TextBox
    Private _txtBranchName As TextBox
    Private _txtDescription As TextBox
    Private _chkIsActive As CheckBox
    Private _btnSave As Button
    Private _btnCancel As Button

    ' تسميات الحقول
    Private _lblName As Label
    Private _lblCashBoxType As Label
    Private _lblCurrency As Label
    Private _lblInitialBalance As Label
    Private _lblMinimumBalance As Label
    Private _lblMaximumBalance As Label
    Private _lblBankName As Label
    Private _lblAccountNumber As Label
    Private _lblIBAN As Label
    Private _lblSwiftCode As Label
    Private _lblBranchName As Label
    Private _lblDescription As Label

    ' لوحات التجميع
    Private _panelMain As Panel
    Private _panelBankInfo As Panel
    Private _panelButtons As Panel

#End Region

#Region "Constructors"

    ''' <summary>
    ''' منشئ لإضافة صندوق جديد
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        _cashBox = New CashBox()
        _isEditMode = False

        InitializeComponent()
        SetupForm()
        SetupControls()
        LoadCurrencies()
        Me.Text = "إضافة صندوق جديد"
    End Sub

    ''' <summary>
    ''' منشئ لتعديل صندوق موجود
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User, cashBox As CashBox)
        _context = context
        _currentUser = currentUser
        _cashBox = cashBox
        _isEditMode = True

        InitializeComponent()
        SetupForm()
        SetupControls()
        LoadCurrencies()
        LoadCashBoxData()
        Me.Text = $"تعديل الصندوق: {cashBox.Name}"
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Size = New Size(600, 750)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.CashBoxIcon
    End Sub

    ''' <summary>
    ''' إعداد عناصر التحكم
    ''' </summary>
    Private Sub SetupControls()
        ' اللوحة الرئيسية
        _panelMain = New Panel With {
            .Size = New Size(560, 400),
            .Location = New Point(20, 20),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .Padding = New Padding(20)
        }

        ' لوحة معلومات البنك
        _panelBankInfo = New Panel With {
            .Size = New Size(560, 250),
            .Location = New Point(20, 440),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .Padding = New Padding(20)
        }

        ' لوحة الأزرار
        _panelButtons = New Panel With {
            .Size = New Size(560, 60),
            .Location = New Point(20, 710),
            .BackColor = Color.Transparent
        }

        ' إنشاء الحقول
        CreateMainFields()
        CreateBankFields()
        CreateButtons()

        Me.Controls.AddRange({_panelMain, _panelBankInfo, _panelButtons})
    End Sub

    ''' <summary>
    ''' إنشاء الحقول الرئيسية
    ''' </summary>
    Private Sub CreateMainFields()
        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 50

        ' اسم الصندوق
        _lblName = CreateLabel("اسم الصندوق *:", yPosition)
        _txtName = CreateTextBox(yPosition + 25, True)
        yPosition += fieldHeight

        ' نوع الصندوق
        _lblCashBoxType = CreateLabel("نوع الصندوق *:", yPosition)
        _cmbCashBoxType = CreateComboBox(yPosition + 25)
        _cmbCashBoxType.Items.AddRange({"نقدي", "بنكي", "رقمي"})
        _cmbCashBoxType.SelectedIndex = 0
        yPosition += fieldHeight

        ' العملة
        _lblCurrency = CreateLabel("العملة *:", yPosition)
        _cmbCurrency = CreateComboBox(yPosition + 25)
        yPosition += fieldHeight

        ' الرصيد الافتتاحي
        _lblInitialBalance = CreateLabel("الرصيد الافتتاحي:", yPosition)
        _txtInitialBalance = CreateNumericTextBox(yPosition + 25)
        yPosition += fieldHeight

        ' الحد الأدنى
        _lblMinimumBalance = CreateLabel("الحد الأدنى للرصيد:", yPosition)
        _txtMinimumBalance = CreateNumericTextBox(yPosition + 25)
        yPosition += fieldHeight

        ' الحد الأقصى
        _lblMaximumBalance = CreateLabel("الحد الأقصى للرصيد:", yPosition)
        _txtMaximumBalance = CreateNumericTextBox(yPosition + 25)
        yPosition += fieldHeight

        ' الوصف
        _lblDescription = CreateLabel("الوصف:", yPosition)
        _txtDescription = New TextBox With {
            .Size = New Size(500, 60),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .TextAlign = HorizontalAlignment.Right
        }
        yPosition += 90

        ' نشط
        _chkIsActive = New CheckBox With {
            .Text = "نشط",
            .Size = New Size(100, 25),
            .Location = New Point(420, yPosition),
            .Checked = True,
            .Font = New Font("Segoe UI", 10, FontStyle.Regular)
        }

        ' إضافة العناصر للوحة الرئيسية
        _panelMain.Controls.AddRange({
            _lblName, _txtName,
            _lblCashBoxType, _cmbCashBoxType,
            _lblCurrency, _cmbCurrency,
            _lblInitialBalance, _txtInitialBalance,
            _lblMinimumBalance, _txtMinimumBalance,
            _lblMaximumBalance, _txtMaximumBalance,
            _lblDescription, _txtDescription,
            _chkIsActive
        })

        ' ربط الأحداث
        AddHandler _cmbCashBoxType.SelectedIndexChanged, AddressOf CmbCashBoxType_SelectedIndexChanged
    End Sub

    ''' <summary>
    ''' إنشاء حقول معلومات البنك
    ''' </summary>
    Private Sub CreateBankFields()
        ' عنوان القسم
        Dim lblBankSection As New Label With {
            .Text = "معلومات البنك (للصناديق البنكية فقط)",
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(52, 58, 64),
            .Size = New Size(500, 25),
            .Location = New Point(20, 10),
            .TextAlign = ContentAlignment.MiddleRight
        }

        Dim yPosition As Integer = 45
        Dim fieldHeight As Integer = 45

        ' اسم البنك
        _lblBankName = CreateLabel("اسم البنك:", yPosition, _panelBankInfo)
        _txtBankName = CreateTextBox(yPosition + 25, False, _panelBankInfo)
        yPosition += fieldHeight

        ' رقم الحساب
        _lblAccountNumber = CreateLabel("رقم الحساب:", yPosition, _panelBankInfo)
        _txtAccountNumber = CreateTextBox(yPosition + 25, False, _panelBankInfo)
        yPosition += fieldHeight

        ' IBAN
        _lblIBAN = CreateLabel("رقم IBAN:", yPosition, _panelBankInfo)
        _txtIBAN = CreateTextBox(yPosition + 25, False, _panelBankInfo)
        yPosition += fieldHeight

        ' Swift Code
        _lblSwiftCode = CreateLabel("رمز Swift:", yPosition, _panelBankInfo)
        _txtSwiftCode = CreateTextBox(yPosition + 25, False, _panelBankInfo)
        yPosition += fieldHeight

        ' اسم الفرع
        _lblBranchName = CreateLabel("اسم الفرع:", yPosition, _panelBankInfo)
        _txtBranchName = CreateTextBox(yPosition + 25, False, _panelBankInfo)

        ' إضافة العناصر للوحة البنك
        _panelBankInfo.Controls.AddRange({
            lblBankSection,
            _lblBankName, _txtBankName,
            _lblAccountNumber, _txtAccountNumber,
            _lblIBAN, _txtIBAN,
            _lblSwiftCode, _txtSwiftCode,
            _lblBranchName, _txtBranchName
        })

        ' إخفاء لوحة البنك افتراضياً
        _panelBankInfo.Enabled = False
        _panelBankInfo.BackColor = Color.FromArgb(248, 249, 250)
    End Sub

    ''' <summary>
    ''' إنشاء تسمية
    ''' </summary>
    Private Function CreateLabel(text As String, y As Integer, Optional parent As Panel = Nothing) As Label
        Return New Label With {
            .Text = text,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = Color.FromArgb(73, 80, 87),
            .Size = New Size(500, 20),
            .Location = New Point(20, y),
            .TextAlign = ContentAlignment.MiddleRight
        }
    End Function

    ''' <summary>
    ''' إنشاء مربع نص
    ''' </summary>
    Private Function CreateTextBox(y As Integer, Optional isRequired As Boolean = False, Optional parent As Panel = Nothing) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Right
        }

        If isRequired Then
            textBox.BackColor = Color.FromArgb(255, 248, 220)
        End If

        Return textBox
    End Function

    ''' <summary>
    ''' إنشاء قائمة منسدلة
    ''' </summary>
    Private Function CreateComboBox(y As Integer) As ComboBox
        Return New ComboBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
    End Function

    ''' <summary>
    ''' إنشاء مربع نص رقمي
    ''' </summary>
    Private Function CreateNumericTextBox(y As Integer) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Center,
            .Text = "0"
        }

        AddHandler textBox.KeyPress, AddressOf NumericTextBox_KeyPress

        Return textBox
    End Function

    ''' <summary>
    ''' إنشاء الأزرار
    ''' </summary>
    Private Sub CreateButtons()
        _btnSave = New Button With {
            .Text = If(_isEditMode, "حفظ التغييرات", "إضافة الصندوق"),
            .Size = New Size(140, 40),
            .Location = New Point(400, 10),
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnSave.FlatAppearance.BorderSize = 0

        _btnCancel = New Button With {
            .Text = "إلغاء",
            .Size = New Size(100, 40),
            .Location = New Point(290, 10),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnCancel.FlatAppearance.BorderSize = 0

        ' ربط الأحداث
        AddHandler _btnSave.Click, AddressOf BtnSave_Click
        AddHandler _btnCancel.Click, AddressOf BtnCancel_Click

        ' تأثيرات الماوس
        AddHandler _btnSave.MouseEnter, Sub() _btnSave.BackColor = Color.FromArgb(33, 136, 56)
        AddHandler _btnSave.MouseLeave, Sub() _btnSave.BackColor = Color.FromArgb(40, 167, 69)
        AddHandler _btnCancel.MouseEnter, Sub() _btnCancel.BackColor = Color.FromArgb(90, 98, 104)
        AddHandler _btnCancel.MouseLeave, Sub() _btnCancel.BackColor = Color.FromArgb(108, 117, 125)

        _panelButtons.Controls.AddRange({_btnSave, _btnCancel})
    End Sub

#End Region

#Region "Data Methods"

    ''' <summary>
    ''' تحميل العملات
    ''' </summary>
    Private Sub LoadCurrencies()
        Try
            _cmbCurrency.Items.Clear()

            Dim currencies = _context.Currencies.Where(Function(c) c.IsActive).OrderBy(Function(c) c.Name).ToList()

            For Each currency In currencies
                _cmbCurrency.Items.Add(New ComboBoxItem With {.Text = currency.Name, .Value = currency.Id})
            Next

            If _cmbCurrency.Items.Count > 0 Then
                _cmbCurrency.SelectedIndex = 0
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل العملات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحميل بيانات الصندوق للتعديل
    ''' </summary>
    Private Sub LoadCashBoxData()
        If _cashBox IsNot Nothing Then
            _txtName.Text = _cashBox.Name
            _cmbCashBoxType.Text = _cashBox.CashBoxTypeInArabic

            ' تحديد العملة
            For i As Integer = 0 To _cmbCurrency.Items.Count - 1
                Dim item = DirectCast(_cmbCurrency.Items(i), ComboBoxItem)
                If item.Value = _cashBox.CurrencyId Then
                    _cmbCurrency.SelectedIndex = i
                    Exit For
                End If
            Next

            _txtInitialBalance.Text = _cashBox.InitialBalance.ToString("F2")
            _txtMinimumBalance.Text = _cashBox.MinimumBalance.ToString("F2")
            _txtMaximumBalance.Text = _cashBox.MaximumBalance.ToString("F2")
            _txtDescription.Text = _cashBox.Description
            _chkIsActive.Checked = _cashBox.IsActive

            ' معلومات البنك
            _txtBankName.Text = _cashBox.BankName
            _txtAccountNumber.Text = _cashBox.AccountNumber
            _txtIBAN.Text = _cashBox.IBAN
            _txtSwiftCode.Text = _cashBox.SwiftCode
            _txtBranchName.Text = _cashBox.BranchName

            ' تفعيل لوحة البنك إذا كان النوع بنكي
            If _cashBox.CashBoxType = "Bank" Then
                _panelBankInfo.Enabled = True
                _panelBankInfo.BackColor = Color.White
            End If
        End If
    End Sub

    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    Private Function ValidateData() As Boolean
        Dim errors As New List(Of String)

        If String.IsNullOrWhiteSpace(_txtName.Text) Then
            errors.Add("اسم الصندوق مطلوب")
        End If

        If _cmbCurrency.SelectedItem Is Nothing Then
            errors.Add("يجب اختيار العملة")
        End If

        Dim initialBalance As Decimal
        If Not Decimal.TryParse(_txtInitialBalance.Text, initialBalance) Then
            errors.Add("الرصيد الافتتاحي يجب أن يكون رقم صحيح")
        End If

        Dim minimumBalance As Decimal
        If Not Decimal.TryParse(_txtMinimumBalance.Text, minimumBalance) Then
            errors.Add("الحد الأدنى يجب أن يكون رقم صحيح")
        End If

        Dim maximumBalance As Decimal
        If Not Decimal.TryParse(_txtMaximumBalance.Text, maximumBalance) Then
            errors.Add("الحد الأقصى يجب أن يكون رقم صحيح")
        End If

        If maximumBalance > 0 AndAlso minimumBalance > maximumBalance Then
            errors.Add("الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى")
        End If

        ' التحقق من معلومات البنك إذا كان النوع بنكي
        If _cmbCashBoxType.Text = "بنكي" Then
            If String.IsNullOrWhiteSpace(_txtBankName.Text) Then
                errors.Add("اسم البنك مطلوب للصناديق البنكية")
            End If

            If String.IsNullOrWhiteSpace(_txtAccountNumber.Text) Then
                errors.Add("رقم الحساب مطلوب للصناديق البنكية")
            End If
        End If

        If errors.Count > 0 Then
            MessageBox.Show(String.Join(Environment.NewLine, errors), "أخطاء في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' حفظ بيانات الصندوق
    ''' </summary>
    Private Function SaveCashBox() As Boolean
        Try
            ' تحديث بيانات الصندوق
            _cashBox.Name = _txtName.Text.Trim()
            _cashBox.CashBoxType = GetCashBoxTypeFromArabic(_cmbCashBoxType.Text)
            _cashBox.CurrencyId = DirectCast(_cmbCurrency.SelectedItem, ComboBoxItem).Value
            _cashBox.Description = If(String.IsNullOrWhiteSpace(_txtDescription.Text), Nothing, _txtDescription.Text.Trim())
            _cashBox.IsActive = _chkIsActive.Checked

            Dim initialBalance As Decimal = Decimal.Parse(_txtInitialBalance.Text)
            Dim minimumBalance As Decimal = Decimal.Parse(_txtMinimumBalance.Text)
            Dim maximumBalance As Decimal = Decimal.Parse(_txtMaximumBalance.Text)

            _cashBox.MinimumBalance = minimumBalance
            _cashBox.MaximumBalance = maximumBalance

            ' معلومات البنك
            If _cmbCashBoxType.Text = "بنكي" Then
                _cashBox.BankName = _txtBankName.Text.Trim()
                _cashBox.AccountNumber = _txtAccountNumber.Text.Trim()
                _cashBox.IBAN = If(String.IsNullOrWhiteSpace(_txtIBAN.Text), Nothing, _txtIBAN.Text.Trim())
                _cashBox.SwiftCode = If(String.IsNullOrWhiteSpace(_txtSwiftCode.Text), Nothing, _txtSwiftCode.Text.Trim())
                _cashBox.BranchName = If(String.IsNullOrWhiteSpace(_txtBranchName.Text), Nothing, _txtBranchName.Text.Trim())
            Else
                _cashBox.BankName = Nothing
                _cashBox.AccountNumber = Nothing
                _cashBox.IBAN = Nothing
                _cashBox.SwiftCode = Nothing
                _cashBox.BranchName = Nothing
            End If

            If _isEditMode Then
                ' تحديث الصندوق الموجود
                _cashBox.UpdatedAt = DateTime.Now

                ' تحديث الرصيد إذا تغير
                If _cashBox.InitialBalance <> initialBalance Then
                    Dim difference = initialBalance - _cashBox.InitialBalance
                    _cashBox.CurrentBalance += difference
                    _cashBox.InitialBalance = initialBalance
                End If
            Else
                ' إضافة صندوق جديد
                _cashBox.InitialBalance = initialBalance
                _cashBox.CurrentBalance = initialBalance
                _cashBox.CreatedAt = DateTime.Now

                _context.CashBoxes.Add(_cashBox)
            End If

            _context.SaveChanges()
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' تحويل نوع الصندوق من العربية
    ''' </summary>
    Private Function GetCashBoxTypeFromArabic(arabicType As String) As String
        Select Case arabicType
            Case "نقدي"
                Return "Cash"
            Case "بنكي"
                Return "Bank"
            Case "رقمي"
                Return "Digital"
            Case Else
                Return "Cash"
        End Select
    End Function

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' تغيير نوع الصندوق
    ''' </summary>
    Private Sub CmbCashBoxType_SelectedIndexChanged(sender As Object, e As EventArgs)
        If _cmbCashBoxType.Text = "بنكي" Then
            _panelBankInfo.Enabled = True
            _panelBankInfo.BackColor = Color.White
        Else
            _panelBankInfo.Enabled = False
            _panelBankInfo.BackColor = Color.FromArgb(248, 249, 250)

            ' مسح معلومات البنك
            _txtBankName.Clear()
            _txtAccountNumber.Clear()
            _txtIBAN.Clear()
            _txtSwiftCode.Clear()
            _txtBranchName.Clear()
        End If
    End Sub

    ''' <summary>
    ''' حفظ البيانات
    ''' </summary>
    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateData() Then
            If SaveCashBox() Then
                Me.DialogResult = DialogResult.OK
                Me.Close()
            End If
        End If
    End Sub

    ''' <summary>
    ''' إلغاء العملية
    ''' </summary>
    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    ''' <summary>
    ''' التحقق من الأرقام فقط
    ''' </summary>
    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso e.KeyChar <> "-"c AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If

        Dim textBox = DirectCast(sender, TextBox)
        If e.KeyChar = "."c AndAlso textBox.Text.Contains(".") Then
            e.Handled = True
        End If

        If e.KeyChar = "-"c AndAlso (textBox.SelectionStart <> 0 OrElse textBox.Text.Contains("-")) Then
            e.Handled = True
        End If
    End Sub

    ''' <summary>
    ''' التحقق عند إغلاق النموذج
    ''' </summary>
    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If Me.DialogResult = DialogResult.None Then
            If MessageBox.Show("هل تريد إغلاق النموذج بدون حفظ؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                e.Cancel = True
                Return
            End If
        End If

        MyBase.OnFormClosing(e)
    End Sub

#End Region

End Class
