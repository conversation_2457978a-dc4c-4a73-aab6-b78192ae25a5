<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ClosedXML</name>
    </assembly>
    <members>
        <member name="T:ClosedXML.Excel.Caching.IXLRepository">
            <summary>
            Base interface for an abstract repository.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Caching.IXLRepository.Clear">
            <summary>
            Clear the repository;
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Caching.IXLRepository`2.Store(`0@,`1)">
            <summary>
            Put the <paramref name="value"/> into the repository under the specified <paramref name="key"/>
            if there is no such key present.
            </summary>
            <param name="key">Key to identify the value.</param>
            <param name="value">Value to put into the repository if key does not exist.</param>
            <returns>Value stored in the repository under the specified <paramref name="key"/>. If key already existed
            returned value may differ from the input one.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Caching.XLRepositoryBase`2.ContainsKey(`0@,`1@)">
            <summary>
            Check if the specified key is presented in the repository.
            </summary>
            <param name="key">Key to look for.</param>
            <param name="value">Value from the repository stored under specified key or null if key does
            not exist or the entry under this key has already bee GCed.</param>
            <returns>True if entry exists and alive, false otherwise.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Caching.XLRepositoryBase`2.Store(`0@,`1)">
            <summary>
            Put the entity into the repository under the specified key if no other entity with
            the same key is presented.
            </summary>
            <param name="key">Key to identify the entity.</param>
            <param name="value">Entity to store.</param>
            <returns>Entity that is stored in the repository under the specified key
            (it can be either the <paramref name="value"/> or another entity that has been added to
            the repository before.)</returns>
        </member>
        <member name="M:ClosedXML.Excel.Caching.XLRepositoryBase`2.GetEnumerator">
            <summary>
            Enumerate items in repository removing "dead" entries.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.Caching.XLWorkbookElementRepositoryBase`2">
            <summary>
            Base repository for <see cref="T:ClosedXML.Excel.XLWorkbook"/> elements.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.CalcEngine">
            <summary>
            CalcEngine parses strings and returns Expression objects that can
            be evaluated.
            </summary>
            <remarks>
            <para>This class has three extensibility points:</para>
            <para>Use the <b>DataContext</b> property to add an object's properties to the engine scope.</para>
            <para>Use the <b>RegisterFunction</b> method to define custom functions.</para>
            <para>Override the <b>GetExternalObject</b> method to add arbitrary variables to the engine scope.</para>
            </remarks>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngine.Parse(System.String)">
            <summary>
            Parses a string into an <see cref="T:ClosedXML.Excel.CalcEngine.Expression"/>.
            </summary>
            <param name="expression">String to parse.</param>
            <returns>An <see cref="T:ClosedXML.Excel.CalcEngine.Expression"/> object that can be evaluated.</returns>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngine.Evaluate(System.String)">
            <summary>
            Evaluates a string.
            </summary>
            <param name="expression">Expression to evaluate.</param>
            <returns>The value of the expression.</returns>
            <remarks>
            If you are going to evaluate the same expression several times,
            it is more efficient to parse it only once using the <see cref="M:ClosedXML.Excel.CalcEngine.CalcEngine.Parse(System.String)"/>
            method and then using the Expression.Evaluate method to evaluate
            the parsed expression.
            </remarks>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.CacheExpressions">
            <summary>
            Gets or sets whether the calc engine should keep a cache with parsed
            expressions.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.OptimizeExpressions">
            <summary>
            Gets or sets whether the calc engine should optimize expressions when
            they are parsed.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.IdentifierChars">
            <summary>
            Gets or sets a string that specifies special characters that are valid for identifiers.
            </summary>
            <remarks>
            Identifiers must start with a letter or an underscore, which may be followed by
            additional letters, underscores, or digits. This string allows you to specify
            additional valid characters such as ':' or '!' (used in Excel range references
            for example).
            </remarks>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngine.RegisterFunction(System.String,System.Int32,System.Int32,ClosedXML.Excel.CalcEngine.CalcEngineFunction)">
            <summary>
            Registers a function that can be evaluated by this <see cref="T:ClosedXML.Excel.CalcEngine.CalcEngine"/>.
            </summary>
            <param name="functionName">Function name.</param>
            <param name="parmMin">Minimum parameter count.</param>
            <param name="parmMax">Maximum parameter count.</param>
            <param name="fn">Delegate that evaluates the function.</param>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngine.RegisterFunction(System.String,System.Int32,ClosedXML.Excel.CalcEngine.CalcEngineFunction)">
            <summary>
            Registers a function that can be evaluated by this <see cref="T:ClosedXML.Excel.CalcEngine.CalcEngine"/>.
            </summary>
            <param name="functionName">Function name.</param>
            <param name="parmCount">Parameter count.</param>
            <param name="fn">Delegate that evaluates the function.</param>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngine.GetExternalObject(System.String)">
            <summary>
            Gets an external object based on an identifier.
            </summary>
            <remarks>
            This method is useful when the engine needs to create objects dynamically.
            For example, a spreadsheet calc engine would use this method to dynamically create cell
            range objects based on identifiers that cannot be enumerated at design time
            (such as "AB12", "A1:AB12", etc.)
            </remarks>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.DataContext">
            <summary>
            Gets or sets the DataContext for this <see cref="T:ClosedXML.Excel.CalcEngine.CalcEngine"/>.
            </summary>
            <remarks>
            Once a DataContext is set, all public properties of the object become available
            to the CalcEngine, including sub-properties such as "Address.Street". These may
            be used with expressions just like any other constant.
            </remarks>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.Functions">
            <summary>
            Gets the dictionary that contains function definitions.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.Variables">
            <summary>
            Gets the dictionary that contains simple variables (not in the DataContext).
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.CalcEngine.CalcEngine.CultureInfo">
            <summary>
            Gets or sets the <see cref="P:ClosedXML.Excel.CalcEngine.CalcEngine.CultureInfo"/> to use when parsing numbers and dates.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.CalcEngineFunction">
            <summary>
            Delegate that represents CalcEngine functions.
            </summary>
            <param name="parms">List of <see cref="T:ClosedXML.Excel.CalcEngine.Expression"/> objects that represent the
            parameters to be used in the function call.</param>
            <returns>The function result.</returns>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.CalcEngineHelpers.GetTotalCellsCount(ClosedXML.Excel.CalcEngine.XObjectExpression)">
            <summary>
            Get total count of cells in the specified range without initalizing them all
            (which might cause serious performance issues on column-wide calculations).
            </summary>
            <param name="rangeExpression">Expression referring to the cell range.</param>
            <returns>Total number of cells in the range.</returns>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.CellReferenceException">
            <summary>
            This error occurs when you delete a cell referred to in the
            formula or if you paste cells over the ones referred to in the
            formula.
            Corresponds to the #REF! error in Excel
            </summary>
            <seealso cref="T:ClosedXML.Excel.CalcEngine.Exceptions.CalcEngineException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.CellValueException">
            <summary>
            This error is most often the result of specifying a
            mathematical operation with one or more cells that contain
            text.
            Corresponds to the #VALUE! error in Excel
            </summary>
            <seealso cref="T:ClosedXML.Excel.CalcEngine.Exceptions.CalcEngineException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.DivisionByZeroException">
            <summary>
            The division operation in your formula refers to a cell that
            contains the value 0 or is blank.
            Corresponds to the #DIV/0! error in Excel
            </summary>
            <seealso cref="T:System.DivideByZeroException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.NameNotRecognizedException">
            <summary>
            This error value appears when you incorrectly type the range
            name, refer to a deleted range name, or forget to put quotation
            marks around a text string in a formula.
            Corresponds to the #NAME? error in Excel
            </summary>
            <seealso cref="T:System.ApplicationException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.NoValueAvailableException">
            <summary>
            Technically, this is not an error value but a special value
            that you can manually enter into a cell to indicate that you
            don’t yet have a necessary value.
            Corresponds to the #N/A error in Excel.
            </summary>
            <seealso cref="T:System.ApplicationException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.NullValueException">
            <summary>
            Because a space indicates an intersection, this error will
            occur if you insert a space instead of a comma(the union operator)
            between ranges used in function arguments.
            Corresponds to the #NULL! error in Excel
            </summary>
            <seealso cref="T:ClosedXML.Excel.CalcEngine.Exceptions.CalcEngineException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Exceptions.NumberException">
            <summary>
            This error can be caused by an invalid argument in an Excel
            function or a formula that produces a number too large or too small
            to be represented in the worksheet.
            Corresponds to the #NUM! error in Excel
            </summary>
            <seealso cref="T:ClosedXML.Excel.CalcEngine.Exceptions.CalcEngineException" />
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Expression">
            <summary>
            Base class that represents parsed expressions.
            </summary>
            <remarks>
            For example:
            <code>
            Expression expr = scriptEngine.Parse(strExpression);
            object val = expr.Evaluate();
            </code>
            </remarks>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.UnaryExpression">
            <summary>
            Unary expression, e.g. +123
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.BinaryExpression">
            <summary>
            Binary expression, e.g. 1+2
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.FunctionExpression">
            <summary>
            Function call expression, e.g. sin(0.5)
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.VariableExpression">
            <summary>
            Simple variable reference.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.XObjectExpression">
            <summary>
            Expression that represents an external object.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.EmptyValueExpression">
            <summary>
            Expression that represents an omitted parameter.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.IValueObject">
            <summary>
            Interface supported by external objects that have to return a value
            other than themselves (e.g. a cell range object should return the
            cell content instead of the range itself).
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.ExpressionCache">
            <summary>
            Caches expressions based on their string representation.
            This saves parsing time.
            </summary>
            <remarks>
            Uses weak references to avoid accumulating unused expressions.
            </remarks>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.ExpressionParseException">
            <summary>
            The exception that is thrown when the strings to be parsed to an expression is invalid.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.ExpressionParseException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the ExpressionParseException class with a 
            specified error message.
            </summary>
            <param name="message">The message that describes the error.</param>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.FunctionDefinition">
            <summary>
            Function definition class (keeps function name, parameter counts, and delegate).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.Functions.DateAndTime.BusinessDaysUntil(System.DateTime,System.DateTime,System.Collections.Generic.IEnumerable{System.DateTime})">
            <summary>
            Calculates number of business days, taking into account:
             - weekends (Saturdays and Sundays)
             - bank holidays in the middle of the week
            </summary>
            <param name="firstDay">First day in the time interval</param>
            <param name="lastDay">Last day in the time interval</param>
            <param name="bankHolidays">List of bank holidays excluding weekends</param>
            <returns>Number of business days during the 'span'</returns>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.Text.TextJoin(System.Collections.Generic.List{ClosedXML.Excel.CalcEngine.Expression})">
            <summary>
            A function to Join text https://support.office.com/en-us/article/textjoin-function-357b449a-ec91-49d0-80c3-0e8fc845691c
            </summary>
            <param name="p">Parameters</param>
            <returns> string </returns>
            <exception cref="T:System.ApplicationException">
            Delimiter in first param must be a string
            or
            Second param must be a boolen (TRUE/FALSE)
            </exception>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.Token">
            <summary>
            Represents a node in the expression tree.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.TKTYPE">
            <summary>
            Token types (used when building expressions, sequence defines operator priority)
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.CalcEngine.TKID">
            <summary>
            Token ID (used when evaluating expressions)
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.CalcEngine.XLCalcEngine.GetPrecedentRanges(System.String)">
            <summary>
            Get a collection of cell ranges included into the expression. Order is not preserved.
            </summary>
            <param name="expression">Formula to parse.</param>
            <returns>Collection of ranges included into the expression.</returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.Address">
            <summary>Gets this cell's address, relative to the worksheet.</summary>
            <value>The cell's address.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.CachedValue">
            <summary>
            Calculated value of cell formula. Is used for decreasing number of computations perfromed.
            May hold invalid value when <see cref="P:ClosedXML.Excel.IXLCell.NeedsRecalculation"/> flag is True.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.CurrentRegion">
            <summary>
            Returns the current region. The current region is a range bounded by any combination of blank rows and blank columns
            </summary>
            <value>
            The current region.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.DataType">
            <summary>
            Gets or sets the type of this cell's data.
            <para>Changing the data type will cause ClosedXML to covert the current value to the new data type.</para>
            <para>An exception will be thrown if the current value cannot be converted to the new data type.</para>
            </summary>
            <value>
            The type of the cell's data.
            </value>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.FormulaA1">
            <summary>
            Gets or sets the cell's formula with A1 references.
            </summary>
            <value>The formula with A1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.FormulaR1C1">
            <summary>
            Gets or sets the cell's formula with R1C1 references.
            </summary>
            <value>The formula with R1C1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.NeedsRecalculation">
            <summary>
            Flag indicating that previously calculated cell value may be not valid anymore and has to be re-evaluated.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.ShareString">
            <summary>
            Gets or sets a value indicating whether this cell's text should be shared or not.
            </summary>
            <value>
              If false the cell's text will not be shared and stored as an inline value.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.Style">
            <summary>
            Gets or sets the cell's style.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLCell.Value">
            <summary>
            Gets or sets the cell's value. To get or set a strongly typed value, use the GetValue&lt;T&gt; and SetValue methods.
            <para>ClosedXML will try to detect the data type through parsing. If it can't then the value will be left as a string.</para>
            <para>If the object is an IEnumerable, ClosedXML will copy the collection's data into a table starting from this cell.</para>
            <para>If the object is a range, ClosedXML will copy the range starting from this cell.</para>
            <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
            <para>If the value starts with a single quote, ClosedXML will assume the value is a text variable and will prefix the value with a single quote in Excel too.</para>
            </summary>
            <value>
            The object containing the value(s) to set.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.AddToNamed(System.String)">
            <summary>
            Creates a named range out of this cell.
            <para>If the named range exists, it will add this range to that named range.</para>
            <para>The default scope for the named range is Workbook.</para>
            </summary>
            <param name="rangeName">Name of the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.AddToNamed(System.String,ClosedXML.Excel.XLScope)">
            <summary>
            Creates a named range out of this cell.
            <para>If the named range exists, it will add this range to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.AddToNamed(System.String,ClosedXML.Excel.XLScope,System.String)">
            <summary>
            Creates a named range out of this cell.
            <para>If the named range exists, it will add this range to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            <param name="comment">The comments for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.AsRange">
            <summary>
            Returns this cell as an IXLRange.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this cell.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.Delete(ClosedXML.Excel.XLShiftDeletedCells)">
            <summary>
            Deletes the current cell and shifts the surrounding cells according to the shiftDeleteCells parameter.
            </summary>
            <param name="shiftDeleteCells">How to shift the surrounding cells.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetBoolean">
            <summary>
            Gets the cell's value converted to Boolean.
            <para>ClosedXML will try to covert the current value to Boolean.</para>
            <para>An exception will be thrown if the current value cannot be converted to Boolean.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetDateTime">
            <summary>
            Gets the cell's value converted to DateTime.
            <para>ClosedXML will try to covert the current value to DateTime.</para>
            <para>An exception will be thrown if the current value cannot be converted to DateTime.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetDouble">
            <summary>
            Gets the cell's value converted to Double.
            <para>ClosedXML will try to covert the current value to Double.</para>
            <para>An exception will be thrown if the current value cannot be converted to Double.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetFormattedString">
            <summary>
            Gets the cell's value formatted depending on the cell's data type and style.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetString">
            <summary>
            Gets the cell's value converted to a String.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetTimeSpan">
            <summary>
            Gets the cell's value converted to TimeSpan.
            <para>ClosedXML will try to covert the current value to TimeSpan.</para>
            <para>An exception will be thrown if the current value cannot be converted to TimeSpan.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.GetValue``1">
            <summary>
            Gets the cell's value converted to the T type.
            <para>ClosedXML will try to covert the current value to the T type.</para>
            <para>An exception will be thrown if the current value cannot be converted to the T type.</para>
            </summary>
            <typeparam name="T">The return type.</typeparam>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertData(System.Collections.IEnumerable)">
            <summary>
            Inserts the IEnumerable data elements and returns the range it occupies.
            </summary>
            <param name="data">The IEnumerable data.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertData(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            Inserts the IEnumerable data elements and returns the range it occupies.
            </summary>
            <param name="data">The IEnumerable data.</param>
            <param name="transpose">if set to <c>true</c> the data will be transposed before inserting.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertData(System.Data.DataTable)">
            <summary>
            Inserts the data of a data table.
            </summary>
            <param name="dataTable">The data table.</param>
            <returns>The range occupied by the inserted data</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable``1(System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Inserts the IEnumerable data elements as a table and returns it.
            <para>The new table will receive a generic name: Table#</para>
            </summary>
            <param name="data">The table data.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Inserts the IEnumerable data elements as a table and returns it.
            <para>The new table will receive a generic name: Table#</para>
            </summary>
            <param name="data">The table data.</param>
            <param name="createTable">
            if set to <c>true</c> it will create an Excel table.
            <para>if set to <c>false</c> the table will be created in memory.</para>
            </param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable``1(System.Collections.Generic.IEnumerable{``0},System.String)">
            <summary>
            Creates an Excel table from the given IEnumerable data elements.
            </summary>
            <param name="data">The table data.</param>
            <param name="tableName">Name of the table.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable``1(System.Collections.Generic.IEnumerable{``0},System.String,System.Boolean)">
            <summary>
            Inserts the IEnumerable data elements as a table and returns it.
            </summary>
            <param name="data">The table data.</param>
            <param name="tableName">Name of the table.</param>
            <param name="createTable">
            if set to <c>true</c> it will create an Excel table.
            <para>if set to <c>false</c> the table will be created in memory.</para>
            </param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable(System.Data.DataTable)">
            <summary>
            Inserts the DataTable data elements as a table and returns it.
            <para>The new table will receive a generic name: Table#</para>
            </summary>
            <param name="data">The table data.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable(System.Data.DataTable,System.Boolean)">
            <summary>
            Inserts the DataTable data elements as a table and returns it.
            <para>The new table will receive a generic name: Table#</para>
            </summary>
            <param name="data">The table data.</param>
            <param name="createTable">
            if set to <c>true</c> it will create an Excel table.
            <para>if set to <c>false</c> the table will be created in memory.</para>
            </param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable(System.Data.DataTable,System.String)">
            <summary>
            Creates an Excel table from the given DataTable data elements.
            </summary>
            <param name="data">The table data.</param>
            <param name="tableName">Name of the table.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InsertTable(System.Data.DataTable,System.String,System.Boolean)">
            <summary>
            Inserts the DataTable data elements as a table and returns it.
            </summary>
            <param name="data">The table data.</param>
            <param name="tableName">Name of the table.</param>
            <param name="createTable">
            if set to <c>true</c> it will create an Excel table.
            <para>if set to <c>false</c> the table will be created in memory.</para>
            </param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.InvalidateFormula">
            <summary>
            Invalidate <see cref="P:ClosedXML.Excel.IXLCell.CachedValue"/> so the formula will be re-evaluated next time <see cref="P:ClosedXML.Excel.IXLCell.Value"/> is accessed.
            If cell does not contain formula nothing happens.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.SetDataType(ClosedXML.Excel.XLDataType)">
            <summary>
            Sets the type of this cell's data.
            <para>Changing the data type will cause ClosedXML to covert the current value to the new data type.</para>
            <para>An exception will be thrown if the current value cannot be converted to the new data type.</para>
            </summary>
            <param name="dataType">Type of the data.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.SetValue``1(``0)">
            <summary>
            Sets the cell's value.
            <para>If the object is an IEnumerable ClosedXML will copy the collection's data into a table starting from this cell.</para>
            <para>If the object is a range ClosedXML will copy the range starting from this cell.</para>
            <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
            <para>ClosedXML will try to translate it to the corresponding type, if it can't then the value will be left as a string.</para>
            </summary>
            <value>
            The object containing the value(s) to set.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLCell.ToString(System.String)">
            <summary>
            Returns a string that represents the current state of the cell according to the format.
            </summary>
            <param name="format">A: address, F: formula, NF: number format, BG: background color, FG: foreground color, V: formatted value</param>
            <returns></returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLCells.Value">
            <summary>
            Sets the cells' value.
            <para>If the object is an IEnumerable ClosedXML will copy the collection's data into a table starting from each cell.</para>
            <para>If the object is a range ClosedXML will copy the range starting from each cell.</para>
            <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
            <para>ClosedXML will try to translate it to the corresponding type, if it can't then the value will be left as a string.</para>
            </summary>
            <value>
            The object containing the value(s) to set.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCells.DataType">
            <summary>
            Sets the type of the cells' data.
            <para>Changing the data type will cause ClosedXML to covert the current value to the new data type.</para>
            <para>An exception will be thrown if the current value cannot be converted to the new data type.</para>
            </summary>
            <value>
            The type of the cell's data.
            </value>
            <exception cref="T:System.ArgumentException"></exception>
        </member>
        <member name="M:ClosedXML.Excel.IXLCells.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these cells.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLCells.DeleteComments">
            <summary>
            Delete the comments of these cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLCells.DeleteSparklines">
            <summary>
            Delete the sparklines of these cells.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLCells.FormulaA1">
            <summary>
            Sets the cells' formula with A1 references.
            </summary>
            <value>The formula with A1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLCells.FormulaR1C1">
            <summary>
            Sets the cells' formula with R1C1 references.
            </summary>
            <value>The formula with R1C1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.DataValidation">
            <summary>
            Get the data validation rule containing current cell or create a new one if no rule was defined for cell.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.IsEvaluating">
            <summary>
            Flag showing that the cell is in formula evaluation state.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLCell.RecalculateFormula(System.String)">
            <summary>
            Calculate a value of the specified formula.
            </summary>
            <param name="fA1">Cell formula to evaluate.</param>
            <returns>Null if formula is empty or null, calculated value otherwise.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLCell.Evaluate(System.Boolean)">
            <summary>
            Perform an evaluation of cell formula. If cell does not contain formula nothing happens, if cell does not need
            recalculation (<see cref="P:ClosedXML.Excel.XLCell.NeedsRecalculation"/> is False) nothing happens either, unless <paramref name="force"/> flag is specified.
            Otherwise recalculation is perfomed, result value is preserved in <see cref="P:ClosedXML.Excel.XLCell.CachedValue"/> and returned.
            </summary>
            <param name="force">Flag indicating whether a recalculation must be performed even is cell does not need it.</param>
            <returns>Null if cell does not contain a formula. Calculated value otherwise.</returns>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.NeedsRecalculation">
            <summary>
            Flag indicating that previously calculated cell value may be not valid anymore and has to be re-evaluated.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.ModifiedAtVersion">
            <summary>
            The value of <see cref="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter"/> that workbook had at the moment of cell last modification.
            If this value is greater than <see cref="P:ClosedXML.Excel.XLCell.EvaluatedAtVersion"/> then cell needs re-evaluation, as well as all dependent cells do.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.EvaluatedAtVersion">
            <summary>
            The value of <see cref="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter"/> that workbook had at the moment of cell formula evaluation.
            If this value equals to <see cref="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter"/> it indicates that <see cref="P:ClosedXML.Excel.XLCell.CachedValue"/> stores
            correct value and no re-evaluation has to be performed.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.NeedsRecalculationEvaluatedAtVersion">
            <summary>
            The value of <see cref="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter"/> that workbook had at the moment of determining whether the cell
            needs re-evaluation (due to it has been edited or some of the affecting cells has). If thie value equals to <see cref="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter"/>
            it indicates that <see cref="F:ClosedXML.Excel.XLCell._recalculationNeededLastValue"/> stores correct value and no check has to be performed.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLCell.Sparkline">
            <summary> The sparkline assigned to the cell </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLCell.GetDataValidation">
            <summary>
            Get the data validation rule containing current cell.
            </summary>
            <returns>The data validation rule applying to the current cell or null if there is no such rule.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLCellsCollection.DecrementUsage(System.Collections.Generic.Dictionary{System.Int32,System.Int32},System.Int32)">
            <summary/>
            <returns>True if the number was lowered to zero so MaxColumnUsed or MaxRowUsed may require
            recomputation.</returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLColumn.Width">
            <summary>
            Gets or sets the width of this column.
            </summary>
            <value>
            The width of this column.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Delete">
            <summary>
            Deletes this column and shifts the columns at the right of this one accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.ColumnNumber">
            <summary>
            Gets this column's number
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.ColumnLetter">
            <summary>
            Gets this column's letter
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.InsertColumnsAfter(System.Int32)">
            <summary>
            Inserts X number of columns at the right of this one.
            <para>All columns at the right will be shifted accordingly.</para>
            </summary>
            <param name="numberOfColumns">The number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.InsertColumnsBefore(System.Int32)">
            <summary>
            Inserts X number of columns at the left of this one.
            <para>This column and all at the right will be shifted accordingly.</para>
            </summary>
            <param name="numberOfColumns">The number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Cell(System.Int32)">
            <summary>
            Gets the cell in the specified row.
            </summary>
            <param name="rowNumber">The cell's row.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Cells(System.String)">
            <summary>
            Returns the specified group of cells, separated by commas.
            <para>e.g. Cells("1"), Cells("1:5"), Cells("1,3:5")</para>
            </summary>
            <param name="cellsInColumn">The column cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Cells(System.Int32,System.Int32)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstRow">The first row in the group of cells to return.</param>
            <param name="lastRow">The last row in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.AdjustToContents">
            <summary>
            Adjusts the width of the column based on its contents.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.AdjustToContents(System.Int32)">
            <summary>
            Adjusts the width of the column based on its contents, starting from the startRow.
            </summary>
            <param name="startRow">The row to start calculating the column width.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.AdjustToContents(System.Int32,System.Int32)">
            <summary>
            Adjusts the width of the column based on its contents, starting from the startRow and ending at endRow.
            </summary>
            <param name="startRow">The row to start calculating the column width.</param>
            <param name="endRow">The row to end calculating the column width.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Hide">
            <summary>
            Hides this column.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Unhide">
            <summary>Unhides this column.</summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLColumn.IsHidden">
            <summary>
            Gets a value indicating whether this column is hidden or not.
            </summary>
            <value>
              <c>true</c> if this column is hidden; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLColumn.OutlineLevel">
            <summary>
            Gets or sets the outline level of this column.
            </summary>
            <value>
            The outline level of this column.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Group">
            <summary>
            Adds this column to the next outline level (Increments the outline level for this column by 1).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Group(System.Boolean)">
            <summary>
            Adds this column to the next outline level (Increments the outline level for this column by 1).
            </summary>
            <param name="collapse">If set to <c>true</c> the column will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Group(System.Int32)">
            <summary>
            Sets outline level for this column.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Group(System.Int32,System.Boolean)">
            <summary>
            Sets outline level for this column.
            </summary>
            <param name="outlineLevel">The outline level.</param>
            <param name="collapse">If set to <c>true</c> the column will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Ungroup">
            <summary>
            Adds this column to the previous outline level (decrements the outline level for this column by 1).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Ungroup(System.Boolean)">
            <summary>
            Adds this column to the previous outline level (decrements the outline level for this column by 1).
            </summary>
            <param name="fromAll">If set to <c>true</c> it will remove this column from all outline levels.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Collapse">
            <summary>
            Show this column as collapsed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Expand">
            <summary>Expands this column (if it's collapsed).</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.AddVerticalPageBreak">
            <summary>
            Adds a vertical page break after this column.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumn.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this column.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLColumns.Width">
            <summary>
            Sets the width of all columns.
            </summary>
            <value>
            The width of all columns.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Delete">
            <summary>
            Deletes all columns and shifts the columns at the right of them accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.AdjustToContents">
            <summary>
            Adjusts the width of all columns based on its contents.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.AdjustToContents(System.Int32)">
            <summary>
            Adjusts the width of all columns based on its contents, starting from the startRow.
            </summary>
            <param name="startRow">The row to start calculating the column width.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.AdjustToContents(System.Int32,System.Int32)">
            <summary>
            Adjusts the width of all columns based on its contents, starting from the startRow and ending at endRow.
            </summary>
            <param name="startRow">The row to start calculating the column width.</param>
            <param name="endRow">The row to end calculating the column width.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Hide">
            <summary>
            Hides all columns.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Unhide">
            <summary>Unhides all columns.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Group">
            <summary>
            Increments the outline level of all columns by 1.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Group(System.Boolean)">
            <summary>
            Increments the outline level of all columns by 1.
            </summary>
            <param name="collapse">If set to <c>true</c> the columns will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Group(System.Int32)">
            <summary>
            Sets outline level for all columns.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Group(System.Int32,System.Boolean)">
            <summary>
            Sets outline level for all columns.
            </summary>
            <param name="outlineLevel">The outline level.</param>
            <param name="collapse">If set to <c>true</c> the columns will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Ungroup">
            <summary>
            Decrements the outline level of all columns by 1.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Ungroup(System.Boolean)">
            <summary>
            Decrements the outline level of all columns by 1.
            </summary>
            <param name="fromAll">If set to <c>true</c> it will remove the columns from all outline levels.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Collapse">
            <summary>
            Show all columns as collapsed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Expand">
            <summary>Expands all columns (if they're collapsed).</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.AddVerticalPageBreaks">
            <summary>
            Adds a vertical page break after these columns.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLColumns.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these columns.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLColumn.#ctor(ClosedXML.Excel.XLWorksheet,System.Int32)">
            <summary>
            The direct contructor should only be used in <see cref="P:ClosedXML.Excel.XLWorksheet.RangeFactory"/>.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLColumn.AddVerticalPageBreak">
            <summary>
              Adds a vertical page break after this column.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLColumns.#ctor(ClosedXML.Excel.XLWorksheet,ClosedXML.Excel.XLStyleValue)">
            <summary>
            Create a new instance of <see cref="T:ClosedXML.Excel.XLColumns"/>.
            </summary>
            <param name="worksheet">If worksheet is specified it means that the created instance represents
            all columns on a worksheet so changing its width will affect all columns.</param>
            <param name="defaultStyle">Default style to use when initializing child entries.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLColumns.AddVerticalPageBreaks">
            <summary>
              Adds a vertical page break after this column.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLComment.Author">
            <summary>
            Gets or sets this comment's author's name
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLComment.SetAuthor(System.String)">
            <summary>
            Sets the name of the comment's author
            </summary>
            <param name="value">Author's name</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLComment.AddSignature">
            <summary>
            Adds a bolded line with the author's name
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLConditionalFormat.Range">
            <summary>
            The first of the <see cref="P:ClosedXML.Excel.IXLConditionalFormat.Ranges"/>.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLConditionalFormats.Consolidate">
            <summary>
            The method consolidate the same conditional formats, which are located in adjacent ranges.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLConditionalFormats.ReorderAccordingToOriginalPriority">
            <summary>
            Reorders the according to original priority. Done during load process
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLAddress.Create(System.String)">
            <summary>
            Create address without worksheet. For calculation only!
            </summary>
            <param name="cellAddressString"></param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.XLAddress.#ctor(System.Int32,System.String,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new <see cref = "T:ClosedXML.Excel.XLAddress" /> struct using a mixed notation.  Attention: without worksheet for calculation only!
            </summary>
            <param name = "rowNumber">The row number of the cell address.</param>
            <param name = "columnLetter">The column letter of the cell address.</param>
            <param name = "fixedRow"></param>
            <param name = "fixedColumn"></param>
        </member>
        <member name="M:ClosedXML.Excel.XLAddress.#ctor(ClosedXML.Excel.XLWorksheet,System.Int32,System.String,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new <see cref = "T:ClosedXML.Excel.XLAddress" /> struct using a mixed notation.
            </summary>
            <param name = "worksheet"></param>
            <param name = "rowNumber">The row number of the cell address.</param>
            <param name = "columnLetter">The column letter of the cell address.</param>
            <param name = "fixedRow"></param>
            <param name = "fixedColumn"></param>
        </member>
        <member name="M:ClosedXML.Excel.XLAddress.#ctor(System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new <see cref = "T:ClosedXML.Excel.XLAddress" /> struct using R1C1 notation. Attention: without worksheet for calculation only!
            </summary>
            <param name = "rowNumber">The row number of the cell address.</param>
            <param name = "columnNumber">The column number of the cell address.</param>
            <param name = "fixedRow"></param>
            <param name = "fixedColumn"></param>
        </member>
        <member name="M:ClosedXML.Excel.XLAddress.#ctor(ClosedXML.Excel.XLWorksheet,System.Int32,System.Int32,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new <see cref = "T:ClosedXML.Excel.XLAddress" /> struct using R1C1 notation.
            </summary>
            <param name = "worksheet"></param>
            <param name = "rowNumber">The row number of the cell address.</param>
            <param name = "columnNumber">The column number of the cell address.</param>
            <param name = "fixedRow"></param>
            <param name = "fixedColumn"></param>
        </member>
        <member name="P:ClosedXML.Excel.XLAddress.RowNumber">
            <summary>
            Gets the row number of this address.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLAddress.ColumnNumber">
            <summary>
            Gets the column number of this address.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLAddress.ColumnLetter">
            <summary>
            Gets the column letter(s) of this address.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLDataValidation.Ranges">
            <summary>
            A collection of ranges the data validation rule applies too.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidation.AddRange(ClosedXML.Excel.IXLRange)">
            <summary>
            Add a range to the collection of ranges this rule applies to.
            If the specified range does not belong to the worksheet of the data validation
            rule it is transferred to the target worksheet.
            </summary>
            <param name="range">A range to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidation.AddRanges(System.Collections.Generic.IEnumerable{ClosedXML.Excel.IXLRange})">
            <summary>
            Add a collection of ranges to the collection of ranges this rule applies to.
            Ranges that do not belong to the worksheet of the data validation
            rule are transferred to the target worksheet.
            </summary>
            <param name="ranges">Ranges to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidation.ClearRanges">
            <summary>
            Detach data validation rule of all ranges it applies to.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidation.RemoveRange(ClosedXML.Excel.IXLRange)">
            <summary>
            Remove the specified range from the collection of range this rule applies to.
            </summary>
            <param name="range">A range to remove.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidations.Add(ClosedXML.Excel.IXLDataValidation)">
            <summary>
            Add data validation rule to the collection. If the specified rule refers to another
            worksheet than the collection, the copy will be created and its ranges will refer
            to the worksheet of the collection. Otherwise the original instance will be placed
            in the collection.
            </summary>
            <param name="dataValidation">A data validation rule to add.</param>
            <returns>The instance that has actually been added in the collection
            (may be a copy of the specified one).</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidations.TryGet(ClosedXML.Excel.IXLRangeAddress,ClosedXML.Excel.IXLDataValidation@)">
            <summary>
            Get the data validation rule for the range with the specified address if it exists.
            </summary>
            <param name="rangeAddress">A range address.</param>
            <param name="dataValidation">Data validation rule which ranges collection includes the specified
            address. The specified range should be fully covered with the data validation rule.
            For example, if the rule is applied to ranges A1:A3,C1:C3 then this method will
            return True for ranges A1:A3, C1:C2, A2:A3, and False for ranges A1:C3, A1:C1, etc.</param>
            <returns>True is the data validation rule was found, false otherwise.</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLDataValidations.GetAllInRange(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Get all data validation rules applied to ranges that intersect the specified range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidation.AddRange(ClosedXML.Excel.IXLRange)">
            <summary>
            Add a range to the collection of ranges this rule applies to.
            If the specified range does not belong to the worksheet of the data validation
            rule it is transferred to the target worksheet.
            </summary>
            <param name="range">A range to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidation.AddRanges(System.Collections.Generic.IEnumerable{ClosedXML.Excel.IXLRange})">
            <summary>
            Add a collection of ranges to the collection of ranges this rule applies to.
            Ranges that do not belong to the worksheet of the data validation
            rule are transferred to the target worksheet.
            </summary>
            <param name="ranges">Ranges to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidation.ClearRanges">
            <summary>
            Detach data validation rule of all ranges it applies to.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidation.RemoveRange(ClosedXML.Excel.IXLRange)">
            <summary>
            Remove the specified range from the collection of range this rule applies to.
            </summary>
            <param name="range">A range to remove.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidations.TryGet(ClosedXML.Excel.IXLRangeAddress,ClosedXML.Excel.IXLDataValidation@)">
            <summary>
            Get the data validation rule for the range with the specified address if it exists.
            </summary>
            <param name="rangeAddress">A range address.</param>
            <param name="dataValidation">Data validation rule which ranges collection includes the specified
            address. The specified range should be fully covered with the data validation rule.
            For example, if the rule is applied to ranges A1:A3,C1:C3 then this method will
            return True for ranges A1:A3, C1:C2, A2:A3, and False for ranges A1:C3, A1:C1, etc.</param>
            <returns>True is the data validation rule was found, false otherwise.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLDataValidations.GetAllInRange(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Get all data validation rules applied to ranges that intersect the specified range.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLDataValidations._skipSplittingExistingRanges">
            <summary>
            The flag used to avoid unnecessary check for splitting intersected ranges when we already
            are performing the splitting.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.XLDataValidations.XLDataValidationIndexEntry">
            <summary>
            Class used for indexing data validation rules.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLDataValidations.XLDataValidationIndexEntry.RangeAddress">
            <summary>
              Gets an object with the boundaries of this range.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Drawings.IXLPicture.Format">
            <summary>
            Type of image. The supported formats are defined by OpenXML's ImagePartType.
            Default value is "jpeg"
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Drawings.IXLPicture.CopyTo(ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Create a copy of the picture on a different worksheet.
            </summary>
            <param name="targetSheet">The worksheet to which the picture will be copied.</param>
            <returns>A created copy of the picture.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Drawings.IXLPicture.Delete">
            <summary>
            Deletes this picture.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Drawings.IXLPicture.Duplicate">
            <summary>
            Create a copy of the picture on the same worksheet.
            </summary>
            <returns>A created copy of the picture.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Drawings.XLPicture.CopyTo(ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Create a copy of the picture on a different worksheet.
            </summary>
            <param name="targetSheet">The worksheet to which the picture will be copied.</param>
            <returns>A created copy of the picture.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Drawings.XLPicture.Duplicate">
            <summary>
            Create a copy of the picture on the same worksheet.
            </summary>
            <returns>A created copy of the picture.</returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.SplitColumn">
            <summary>
            Gets or sets the column after which the horizontal split should take place.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.SplitRow">
            <summary>
            Gets or sets the row after which the vertical split should take place.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.TopLeftCellAddress">
            <summary>
            Gets or sets the location of the top left visible cell
            </summary>
            <value>
            The scroll position's top left cell.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.ZoomScale">
            <summary>
            Window zoom magnification for current view representing percent values. Horizontal and vertical scale together.
            </summary>
            <remarks>Representing percent values ranging from 10 to 400.</remarks>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.ZoomScaleNormal">
            <summary>
            Zoom magnification to use when in normal view. Horizontal and vertical scale together
            </summary>
            <remarks>Representing percent values ranging from 10 to 400.</remarks>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.ZoomScalePageLayoutView">
            <summary>
            Zoom magnification to use when in page layout view. Horizontal and vertical scale together.
            </summary>
            <remarks>Representing percent values ranging from 10 to 400.</remarks>
        </member>
        <member name="P:ClosedXML.Excel.IXLSheetView.ZoomScaleSheetLayoutView">
            <summary>
            Zoom magnification to use when in page break preview. Horizontal and vertical scale together.
            </summary>
            <remarks>Representing percent values ranging from 10 to 400.</remarks>
        </member>
        <member name="M:ClosedXML.Excel.IXLSheetView.Freeze(System.Int32,System.Int32)">
            <summary>
            Freezes the specified rows and columns.
            </summary>
            <param name="rows">The rows to freeze.</param>
            <param name="columns">The columns to freeze.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLSheetView.FreezeColumns(System.Int32)">
            <summary>
            Freezes the left X columns.
            </summary>
            <param name="columns">The columns to freeze.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLSheetView.FreezeRows(System.Int32)">
            <summary>
            Freezes the top X rows.
            </summary>
            <param name="rows">The rows to freeze.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.CalculateMode">
            <summary>
              Gets or sets the workbook's calculation mode.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.ColumnWidth">
            <summary>
              Gets or sets the default column width for the workbook.
              <para>All new worksheets will use this column width.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.NamedRanges">
            <summary>
              Gets an object to manipulate this workbook's named ranges.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.Outline">
            <summary>
              Gets or sets the default outline options for the workbook.
              <para>All new worksheets will use these outline options.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.PageOptions">
            <summary>
              Gets or sets the default page options for the workbook.
              <para>All new worksheets will use these page options.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.Properties">
            <summary>
              Gets or sets the workbook's properties.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.ReferenceStyle">
            <summary>
              Gets or sets the workbook's reference style.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.RowHeight">
            <summary>
              Gets or sets the default row height for the workbook.
              <para>All new worksheets will use this row height.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.Style">
            <summary>
              Gets or sets the default style for the workbook.
              <para>All new worksheets will use this style.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.Theme">
            <summary>
              Gets an object to manipulate this workbook's theme.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorkbook.Worksheets">
            <summary>
              Gets an object to manipulate the worksheets.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.RecalculateAllFormulas">
            <summary>
            Force recalculation of all cell formulas.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.Save">
            <summary>
              Saves the current workbook.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.Save(System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook and optionally performs validation
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.SaveAs(System.String)">
            <summary>
              Saves the current workbook to a file.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.SaveAs(System.String,System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook to a file and optionally validates it.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.SaveAs(System.IO.Stream)">
            <summary>
              Saves the current workbook to a stream.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.SaveAs(System.IO.Stream,System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook to a stream and optionally validates it.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.Search(System.String,System.Globalization.CompareOptions,System.Boolean)">
            <summary>
            Searches the cells' contents for a given piece of text
            </summary>
            <param name="searchText">The search text.</param>
            <param name="compareOptions">The compare options.</param>
            <param name="searchFormulae">if set to <c>true</c> search formulae instead of cell values.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorkbook.Table(System.String,System.StringComparison)">
            <summary>
            Gets the Excel table of the given name
            </summary>
            <param name="tableName">Name of the table to return.</param>
            <param name="comparisonType">One of the enumeration values that specifies how the strings will be compared.</param>
            <returns>The table with given name</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">If no tables with this name could be found in the workbook.</exception>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.Workbook">
            <summary>
            Gets the workbook that contains this worksheet
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.ColumnWidth">
            <summary>
            Gets or sets the default column width for this worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.RowHeight">
            <summary>
            Gets or sets the default row height for this worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.Name">
            <summary>
            Gets or sets the name (caption) of this worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.Position">
            <summary>
            Gets or sets the position of the sheet.
            <para>When setting the Position all other sheets' positions are shifted accordingly.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.PageSetup">
            <summary>
            Gets an object to manipulate the sheet's print options.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.Outline">
            <summary>
            Gets an object to manipulate the Outline levels.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstRow">
            <summary>
            Gets the first row of the worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstRowUsed">
            <summary>
            Gets the first row of the worksheet that contains a cell with a value.
            <para>Formatted empty cells do not count.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstRowUsed(System.Boolean)">
            <summary>
            Gets the first row of the worksheet that contains a cell with a value.
            </summary>
            <param name="includeFormats">If set to <c>true</c> formatted empty cells will count as used.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastRow">
            <summary>
            Gets the last row of the worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastRowUsed">
            <summary>
            Gets the last row of the worksheet that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastRowUsed(System.Boolean)">
            <summary>
            Gets the last row of the worksheet that contains a cell with a value.
            </summary>
            <param name="includeFormats">If set to <c>true</c> formatted empty cells will count as used.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstColumn">
            <summary>
            Gets the first column of the worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstColumnUsed">
            <summary>
            Gets the first column of the worksheet that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.FirstColumnUsed(System.Boolean)">
            <summary>
            Gets the first column of the worksheet that contains a cell with a value.
            </summary>
            <param name="includeFormats">If set to <c>true</c> formatted empty cells will count as used.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastColumn">
            <summary>
            Gets the last column of the worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastColumnUsed">
            <summary>
            Gets the last column of the worksheet that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.LastColumnUsed(System.Boolean)">
            <summary>
            Gets the last column of the worksheet that contains a cell with a value.
            </summary>
            <param name="includeFormats">If set to <c>true</c> formatted empty cells will count as used.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Columns">
            <summary>
            Gets a collection of all columns in this worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Columns(System.String)">
            <summary>
            Gets a collection of the specified columns in this worksheet, separated by commas.
            <para>e.g. Columns("G:H"), Columns("10:11,13:14"), Columns("P:Q,S:T"), Columns("V")</para>
            </summary>
            <param name="columns">The columns to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Columns(System.String,System.String)">
            <summary>
            Gets a collection of the specified columns in this worksheet.
            </summary>
            <param name="firstColumn">The first column to return.</param>
            <param name="lastColumn">The last column to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Columns(System.Int32,System.Int32)">
            <summary>
            Gets a collection of the specified columns in this worksheet.
            </summary>
            <param name="firstColumn">The first column to return.</param>
            <param name="lastColumn">The last column to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Rows">
            <summary>
            Gets a collection of all rows in this worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Rows(System.String)">
            <summary>
            Gets a collection of the specified rows in this worksheet, separated by commas.
            <para>e.g. Rows("4:5"), Rows("7:8,10:11"), Rows("13")</para>
            </summary>
            <param name="rows">The rows to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Rows(System.Int32,System.Int32)">
            <summary>
            Gets a collection of the specified rows in this worksheet.
            </summary>
            <param name="firstRow">The first row to return.</param>
            <param name="lastRow">The last row to return.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Row(System.Int32)">
            <summary>
            Gets the specified row of the worksheet.
            </summary>
            <param name="row">The worksheet's row.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Column(System.Int32)">
            <summary>
            Gets the specified column of the worksheet.
            </summary>
            <param name="column">The worksheet's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Column(System.String)">
            <summary>
            Gets the specified column of the worksheet.
            </summary>
            <param name="column">The worksheet's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Cell(System.Int32,System.Int32)">
            <summary>
            Gets the cell at the specified row and column.
            </summary>
            <param name="row">The cell's row.</param>
            <param name="column">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Cell(System.String)">
            <summary>Gets the cell at the specified address.</summary>
            <param name="cellAddressInRange">The cell address in the worksheet.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Cell(System.Int32,System.String)">
            <summary>
            Gets the cell at the specified row and column.
            </summary>
            <param name="row">The cell's row.</param>
            <param name="column">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Cell(ClosedXML.Excel.IXLAddress)">
            <summary>Gets the cell at the specified address.</summary>
            <param name="cellAddressInRange">The cell address in the worksheet.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Returns the specified range.
            </summary>
            <param name="rangeAddress">The range boundaries.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(System.String)">
            <summary>Returns the specified range.</summary>
            <para>e.g. Range("A1"), Range("A1:C2")</para>
            <param name="rangeAddress">The range boundaries.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLCell)">
            <summary>Returns the specified range.</summary>
            <param name="firstCell">The first cell in the range.</param>
            <param name="lastCell"> The last cell in the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(System.String,System.String)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellAddress">The first cell address in the worksheet.</param>
            <param name="lastCellAddress"> The last cell address in the worksheet.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(ClosedXML.Excel.IXLAddress,ClosedXML.Excel.IXLAddress)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellAddress">The first cell address in the worksheet.</param>
            <param name="lastCellAddress"> The last cell address in the worksheet.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Ranges(System.String)">
            <summary>Returns a collection of ranges, separated by commas.</summary>
            <para>e.g. Ranges("A1"), Ranges("A1:C2"), Ranges("A1:B2,D1:D4")</para>
            <param name="ranges">The ranges to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Range(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellRow">   The first cell's row of the range to return.</param>
            <param name="firstCellColumn">The first cell's column of the range to return.</param>
            <param name="lastCellRow">    The last cell's row of the range to return.</param>
            <param name="lastCellColumn"> The last cell's column of the range to return.</param>
            <returns>.</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.RowCount">
            <summary>Gets the number of rows in this worksheet.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.ColumnCount">
            <summary>Gets the number of columns in this worksheet.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.CollapseRows">
            <summary>
            Collapses all outlined rows.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.CollapseColumns">
            <summary>
            Collapses all outlined columns.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.ExpandRows">
            <summary>
            Expands all outlined rows.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.ExpandColumns">
            <summary>
            Expands all outlined columns.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.CollapseRows(System.Int32)">
            <summary>
            Collapses the outlined rows of the specified level.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.CollapseColumns(System.Int32)">
            <summary>
            Collapses the outlined columns of the specified level.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.ExpandRows(System.Int32)">
            <summary>
            Expands the outlined rows of the specified level.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.ExpandColumns(System.Int32)">
            <summary>
            Expands the outlined columns of the specified level.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Delete">
            <summary>
            Deletes this worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.NamedRanges">
            <summary>
            Gets an object to manage this worksheet's named ranges.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.NamedRange(System.String)">
            <summary>
            Gets the specified named range.
            </summary>
            <param name="rangeName">Name of the range.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.SheetView">
            <summary>
            Gets an object to manage how the worksheet is going to displayed by Excel.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Table(System.Int32)">
            <summary>
            Gets the Excel table of the given index
            </summary>
            <param name="index">Index of the table to return</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.Table(System.String)">
            <summary>
            Gets the Excel table of the given name
            </summary>
            <param name="name">Name of the table to return</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLWorksheet.Tables">
            <summary>
            Gets an object to manage this worksheet's Excel tables
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.CopyTo(System.String)">
            <summary>
            Copies the
            </summary>
            <param name="newSheetName"></param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLWorksheet.RecalculateAllFormulas">
            <summary>
            Force recalculation of all cell formulas.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.Comment">
            <summary>
            Gets or sets the comment for this named range.
            </summary>
            <value>
            The comment for this named range.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.IsValid">
            <summary>
            Checks if the named range contains invalid references (#REF!).
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.Name">
            <summary>
            Gets or sets the name of the range.
            </summary>
            <value>
            The name of the range.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.Ranges">
            <summary>
            Gets the ranges associated with this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.Scope">
            <summary>
            Gets the scope of this named range.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLNamedRange.Visible">
            <summary>
            Gets or sets the visibility of this named range.
            </summary>
            <value>
              <c>true</c> if visible; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Add(ClosedXML.Excel.XLWorkbook,System.String)">
            <summary>
            Adds the specified range to this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="workbook">Workbook containing the range</param>
            <param name="rangeAddress">The range address to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Add(ClosedXML.Excel.IXLRange)">
            <summary>
            Adds the specified range to this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="range">The range to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Add(ClosedXML.Excel.IXLRanges)">
            <summary>
            Adds the specified ranges to this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="ranges">The ranges to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Clear">
            <summary>
            Clears the list of ranges associated with this named range.
            <para>(it does not clear the cells)</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Delete">
            <summary>
            Deletes this named range (not the cells).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Remove(System.String)">
            <summary>
            Removes the specified range from this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="rangeAddress">The range address to remove.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Remove(ClosedXML.Excel.IXLRange)">
            <summary>
            Removes the specified range from this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="range">The range to remove.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRange.Remove(ClosedXML.Excel.IXLRanges)">
            <summary>
            Removes the specified ranges from this named range.
            <para>Note: A named range can point to multiple ranges.</para>
            </summary>
            <param name="ranges">The ranges to remove.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.NamedRange(System.String)">
            <summary>
            Gets the specified named range.
            </summary>
            <param name="rangeName">Name of the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,System.String)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the range to add.</param>
            <param name="rangeAddress">The range address to add.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,ClosedXML.Excel.IXLRange)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the range to add.</param>
            <param name="range">The range to add.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,ClosedXML.Excel.IXLRanges)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the range to add.</param>
            <param name="ranges">The ranges to add.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,System.String,System.String)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the ranges to add.</param>
            <param name="rangeAddress">The range address to add.</param>
            <param name="comment">The comment for the new named range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,ClosedXML.Excel.IXLRange,System.String)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the ranges to add.</param>
            <param name="range">The range to add.</param>
            <param name="comment">The comment for the new named range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Add(System.String,ClosedXML.Excel.IXLRanges,System.String)">
            <summary>
            Adds a new named range.
            </summary>
            <param name="rangeName">Name of the ranges to add.</param>
            <param name="ranges">The ranges to add.</param>
            <param name="comment">The comment for the new named range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Delete(System.String)">
            <summary>
            Deletes the specified named range (not the cells).
            </summary>
            <param name="rangeName">Name of the range to delete.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.Delete(System.Int32)">
            <summary>
            Deletes the specified named range's index (not the cells).
            </summary>
            <param name="rangeIndex">Index of the named range to delete.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.DeleteAll">
            <summary>
            Deletes all named ranges (not the cells).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.ValidNamedRanges">
            <summary>
            Returns a subset of named ranges that do not have invalid references.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLNamedRanges.InvalidNamedRanges">
            <summary>
            Returns a subset of named ranges that do have invalid references.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLNamedRange.IsValid">
            <summary>
            Checks if the named range contains invalid references (#REF!).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLNamedRanges.Add(System.String,System.String,System.String,System.Boolean,System.Boolean)">
            <summary>
            Adds the specified range name.
            </summary>
            <param name="rangeName">Name of the range.</param>
            <param name="rangeAddress">The range address.</param>
            <param name="comment">The comment.</param>
            <param name="validateName">if set to <c>true</c> validates the name.</param>
            <param name="validateRangeAddress">if set to <c>true</c> range address will be checked for validity.</param>
            <returns></returns>
            <exception cref="T:System.NotSupportedException"></exception>
            <exception cref="T:System.ArgumentException">
            For named ranges in the workbook scope, specify the sheet name in the reference.
            </exception>
        </member>
        <member name="M:ClosedXML.Excel.XLNamedRanges.ValidNamedRanges">
            <summary>
            Returns a subset of named ranges that do not have invalid references.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLNamedRanges.InvalidNamedRanges">
            <summary>
            Returns a subset of named ranges that do have invalid references.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLHeaderFooter.Left">
            <summary>
            Gets the left header/footer item.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLHeaderFooter.Center">
            <summary>
            Gets the middle header/footer item.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLHeaderFooter.Right">
            <summary>
            Gets the right header/footer item.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLHeaderFooter.GetText(ClosedXML.Excel.XLHFOccurrence)">
            <summary>
            Gets the text of the specified header/footer occurrence.
            </summary>
            <param name="occurrence">The occurrence.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLHFItem.GetText(ClosedXML.Excel.XLHFOccurrence)">
            <summary>
            Gets the text of the specified header/footer occurrence.
            </summary>
            <param name="occurrence">The occurrence.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLHFItem.AddText(ClosedXML.Excel.XLHFPredefinedText)">
            <summary>
            Adds the given predefined text to this header/footer item.
            </summary>
            <param name="predefinedText">The predefined text to add to this header/footer item.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLHFItem.AddText(System.String,ClosedXML.Excel.XLHFOccurrence)">
            <summary>
            Adds the given text to this header/footer item.
            </summary>
            <param name="text">The text to add to this header/footer item.</param>
            <param name="occurrence">The occurrence for the text.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLHFItem.AddText(ClosedXML.Excel.XLHFPredefinedText,ClosedXML.Excel.XLHFOccurrence)">
            <summary>
            Adds the given predefined text to this header/footer item.
            </summary>
            <param name="predefinedText">The predefined text to add to this header/footer item.</param>
            <param name="occurrence">The occurrence for the predefined text.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLHFItem.Clear(ClosedXML.Excel.XLHFOccurrence)">
            <summary>Clears the text/formats of this header/footer item.</summary>
            <param name="occurrence">The occurrence to clear.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Left">
            <summary>Gets or sets the Left margin.</summary>
            <value>The Left margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Right">
            <summary>Gets or sets the Right margin.</summary>
            <value>The Right margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Top">
            <summary>Gets or sets the Top margin.</summary>
            <value>The Top margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Bottom">
            <summary>Gets or sets the Bottom margin.</summary>
            <value>The Bottom margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Header">
            <summary>Gets or sets the Header margin.</summary>
            <value>The Header margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLMargins.Footer">
            <summary>Gets or sets the Footer margin.</summary>
            <value>The Footer margin.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PrintAreas">
            <summary>
            Gets an object to manage the print areas of the worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.FirstRowToRepeatAtTop">
            <summary>
            Gets the first row that will repeat on the top of the printed pages.
            <para>Use SetRowsToRepeatAtTop() to set the rows that will be repeated on the top of the printed pages.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.LastRowToRepeatAtTop">
            <summary>
            Gets the last row that will repeat on the top of the printed pages.
            <para>Use SetRowsToRepeatAtTop() to set the rows that will be repeated on the top of the printed pages.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.SetRowsToRepeatAtTop(System.String)">
            <summary>
            Sets the rows to repeat on the top of the printed pages.
            </summary>
            <param name="range">The range of rows to repeat on the top of the printed pages.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.SetRowsToRepeatAtTop(System.Int32,System.Int32)">
            <summary>
            Sets the rows to repeat on the top of the printed pages.
            </summary>
            <param name="firstRowToRepeatAtTop">The first row to repeat at top.</param>
            <param name="lastRowToRepeatAtTop">The last row to repeat at top.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.FirstColumnToRepeatAtLeft">
            <summary>Gets the first column to repeat on the left of the printed pages.</summary>
            <value>The first column to repeat on the left of the printed pages.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.LastColumnToRepeatAtLeft">
            <summary>Gets the last column to repeat on the left of the printed pages.</summary>
            <value>The last column to repeat on the left of the printed pages.</value>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.SetColumnsToRepeatAtLeft(System.Int32,System.Int32)">
            <summary>
            Sets the rows to repeat on the left of the printed pages.
            </summary>
            <param name="firstColumnToRepeatAtLeft">The first column to repeat at left.</param>
            <param name="lastColumnToRepeatAtLeft">The last column to repeat at left.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.SetColumnsToRepeatAtLeft(System.String)">
            <summary>
            Sets the rows to repeat on the left of the printed pages.
            </summary>
            <param name="range">The range of rows to repeat on the left of the printed pages.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PageOrientation">
            <summary>Gets or sets the page orientation for printing.</summary>
            <value>The page orientation.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PagesWide">
            <summary>
            Gets or sets the number of pages wide (horizontal) the worksheet will be printed on.
            <para>If you don't specify the PagesTall, Excel will adjust that value</para>
            <para>based on the contents of the worksheet and the PagesWide number.</para>
            <para>Setting this value will override the Scale value.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PagesTall">
            <summary>
            Gets or sets the number of pages tall (vertical) the worksheet will be printed on.
            <para>If you don't specify the PagesWide, Excel will adjust that value</para>
            <para>based on the contents of the worksheet and the PagesTall number.</para>
            <para>Setting this value will override the Scale value.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.Scale">
            <summary>
            Gets or sets the scale at which the worksheet will be printed.
            <para>The worksheet will be printed on as many pages as necessary to print at the given scale.</para>
            <para>Setting this value will override the PagesWide and PagesTall values.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.HorizontalDpi">
            <summary>
            Gets or sets the horizontal dpi for printing the worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.VerticalDpi">
            <summary>
            Gets or sets the vertical dpi for printing the worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.FirstPageNumber">
            <summary>
            Gets or sets the page number that will begin the printout.
            <para>For example, the first page of your printout could be numbered page 5.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.CenterHorizontally">
            <summary>
            Gets or sets a value indicating whether the worksheet will be centered on the page horizontally.
            </summary>
            <value>
              <c>true</c> if the worksheet will be centered on the page horizontally; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.CenterVertically">
            <summary>
            Gets or sets a value indicating whether the worksheet will be centered on the page vertically.
            </summary>
            <value>
              <c>true</c> if the worksheet will be centered on the page vartically; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.AdjustTo(System.Int32)">
            <summary>
            Sets the scale at which the worksheet will be printed. This is equivalent to setting the Scale property.
            <para>The worksheet will be printed on as many pages as necessary to print at the given scale.</para>
            <para>Setting this value will override the PagesWide and PagesTall values.</para>
            </summary>
            <param name="percentageOfNormalSize">The scale at which the worksheet will be printed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.FitToPages(System.Int32,System.Int32)">
            <summary>
            Gets or sets the number of pages the worksheet will be printed on.
            <para>This is equivalent to setting both PagesWide and PagesTall properties.</para>
            <para>Setting this value will override the Scale value.</para>
            </summary>
            <param name="pagesWide">The pages wide.</param>
            <param name="pagesTall">The pages tall.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PaperSize">
            <summary>
            Gets or sets the size of the paper to print the worksheet.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.Margins">
            <summary>
            Gets an object to work with the page margins.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.Header">
            <summary>
            Gets an object to work with the page headers.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.Footer">
            <summary>
            Gets an object to work with the page footers.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.ScaleHFWithDocument">
            <summary>
            Gets or sets a value indicating whether Excel will automatically adjust the font size to the scale of the worksheet.
            </summary>
            <value>
            	<c>true</c> if Excel will automatically adjust the font size to the scale of the worksheet; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.AlignHFWithMargins">
            <summary>
            Gets or sets a value indicating whether the header and footer margins are aligned with the left and right margins of the worksheet.
            </summary>
            <value>
              <c>true</c> if the header and footer margins are aligned with the left and right margins of the worksheet; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.ShowGridlines">
            <summary>
            Gets or sets a value indicating whether the gridlines will be printed.
            </summary>
            <value>
              <c>true</c> if the gridlines will be printed; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.ShowRowAndColumnHeadings">
            <summary>
            Gets or sets a value indicating whether to show row numbers and column letters/numbers.
            </summary>
            <value>
            	<c>true</c> to show row numbers and column letters/numbers; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.BlackAndWhite">
            <summary>
            Gets or sets a value indicating whether the worksheet will be printed in black and white.
            </summary>
            <value>
              <c>true</c> if the worksheet will be printed in black and white; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.DraftQuality">
            <summary>
            Gets or sets a value indicating whether the worksheet will be printed in draft quality.
            </summary>
            <value>
              <c>true</c> if the worksheet will be printed in draft quality; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PageOrder">
            <summary>
            Gets or sets the page order for printing.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.ShowComments">
            <summary>
            Gets or sets how the comments will be printed.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.RowBreaks">
            <summary>
            Gets a list with the row breaks (for printing).
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.ColumnBreaks">
            <summary>
            Gets a list with the column breaks (for printing).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.AddHorizontalPageBreak(System.Int32)">
            <summary>
            Adds a horizontal page break after the given row.
            </summary>
            <param name="row">The row to insert the break.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPageSetup.AddVerticalPageBreak(System.Int32)">
            <summary>
            Adds a vertical page break after the given column.
            </summary>
            <param name="column">The column to insert the break.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLPageSetup.PrintErrorValue">
            <summary>
            Gets or sets how error values will be printed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLPrintAreas.Clear">
            <summary>Removes the print areas from the worksheet.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLPrintAreas.Add(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Adds a range to the print areas.</summary>
            <param name="firstCellRow">   The first cell row.</param>
            <param name="firstCellColumn">The first cell column.</param>
            <param name="lastCellRow">    The last cell row.</param>
            <param name="lastCellColumn"> The last cell column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPrintAreas.Add(System.String)">
            <summary>Adds a range to the print areas.</summary>
            <param name="rangeAddress">The range address to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPrintAreas.Add(System.String,System.String)">
            <summary>Adds a range to the print areas.</summary>
            <param name="firstCellAddress">The first cell address.</param>
            <param name="lastCellAddress"> The last cell address.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLPrintAreas.Add(ClosedXML.Excel.IXLAddress,ClosedXML.Excel.IXLAddress)">
            <summary>Adds a range to the print areas.</summary>
            <param name="firstCellAddress">The first cell address.</param>
            <param name="lastCellAddress"> The last cell address.</param>
        </member>
        <member name="T:ClosedXML.Excel.Patterns.Quadrant">
            <summary>
            Implementation of QuadTree adapted to Excel worksheet specifics. Differences with the classic implementation
            are that the topmost level is split to 128 square parts (2 columns of 64 blocks, each 8192*8192 cells) and that splitting
            the quadrant onto 4 smaller quadrants does not depend on the number of items in this quadrant. When the range is added to the
            QuadTree it is placed on the bottommost level where it fits to a single quadrant. That means, row-wide and column-wide ranges
            are always placed at the level 0, and the smaller the range is the deeper it goes down the tree. This approach eliminates
            the need of transferring ranges between levels.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.Children">
            <summary>
            Smaller quadrants which the current one is splitted to. Is NULL until ranges are added to child quadrants.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.Level">
            <summary>
            The level of current quadrant. Top most has level 0, child quadrants has levels (Level + 1).
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.MinimumColumn">
            <summary>
            Minimum column included in this quadrant.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.MinimumRow">
            <summary>
            Minimum row included in this quadrant.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.MaximumColumn">
            <summary>
            Maximum column included in this quadrant.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.MaximumRow">
            <summary>
            Maximum row included in this quadrant.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.Ranges">
            <summary>
            Collection of ranges belonging to this quadrant (does not include ranges from child quadrants).
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.X">
            <summary>
            The number of current quadrant by horizontal axis.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.Patterns.Quadrant.Y">
            <summary>
            The number of current quadrant by vertical axis.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.Add(ClosedXML.Excel.IXLAddressable)">
            <summary>
            Add a range to the quadrant or to one of the child quadrants (recursively).
            </summary>
            <returns>True, if range was successfully added, false if it has been added before.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.GetAll">
            <summary>
            Get all ranges from the quadrant and all child quadrants (recursively).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.GetIntersectedRanges(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Get all ranges from the quadrant and all child quadrants (recursively) that intersect the specified address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.GetIntersectedRanges(ClosedXML.Excel.IXLAddress)">
            <summary>
            Get all ranges from the quadrant and all child quadrants (recursively) that cover the specified address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.Remove(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Remove the range from the quadrant or from child quadrants (recursively).
            </summary>
            <returns>True if the range was removed, false if it does not exist in the QuadTree.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.RemoveAll(System.Predicate{ClosedXML.Excel.IXLAddressable})">
            <summary>
            Remove all the ranges matching specified criteria from the quadrant and its child quadrants (recursively).
            Don't use it for searching intersections as it would be much less efficient than <see cref="M:ClosedXML.Excel.Patterns.Quadrant.GetIntersectedRanges(ClosedXML.Excel.IXLRangeAddress)"/>.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.Patterns.Quadrant.MAX_LEVEL">
            <summary>
            Maximum depth of the QuadTree. Value 10 corresponds to the smallest quadrants having size 16*16 cells.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.Patterns.Quadrant._ranges">
            <summary>
            Collection of ranges belonging to the current quadrant (that cannot fit into child quadrants).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.AddInternal(ClosedXML.Excel.IXLAddressable)">
            <summary>
            Add a range to the collection of quadrant's own ranges.
            </summary>
            <returns>True if the range was successfully added, false if it had been added before.</returns>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.Covers(ClosedXML.Excel.IXLRangeAddress@)">
            <summary>
            Check if the current quadrant fully covers the specified address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.Covers(ClosedXML.Excel.IXLAddress@)">
            <summary>
            Check if the current quadrant covers the specified address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.Intersects(ClosedXML.Excel.IXLRangeAddress@)">
            <summary>
            Check if the current quadrant intersects the specified address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.Patterns.Quadrant.CreateChildren">
            <summary>
            Create a collection of child quadrants dividing the current one.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.Patterns.Quadrant`1">
            <summary>
            A generic version of <see cref="T:ClosedXML.Excel.Patterns.Quadrant"/>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.AbstractPivotFieldReference.Match(ClosedXML.Excel.XLWorkbook.PivotTableInfo,ClosedXML.Excel.IXLPivotTable)">
            <summary>
              <P>Helper function used during saving to calculate the indices of the filtered values</P>
            </summary>
            <returns>Indices of the filtered values</returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLElementProtection`1.AllowedElements">
            <summary>Gets or sets the elements that are allowed to be edited by the user, i.e. those that are not protected.</summary>
            <value>The allowed elements.</value>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.AllowElement(`0,System.Boolean)">
            <summary>
            Adds the specified element to the list of allowed elements.
            Beware that if you pass through "None", this will have no effect.
            </summary>
            <param name="element">The element to add</param>
            <param name="allowed">Set to <c>true</c> to allow the element or <c>false</c> to disallow the element</param>
            <returns>The current protection instance</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.AllowEverything">
            <summary>Allows all elements to be edited.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.AllowNone">
            <summary>Allows no elements to be edited. Protects all elements.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.CopyFrom(ClosedXML.Excel.IXLElementProtection{`0})">
            <summary>Copies all the protection settings from a different instance.</summary>
            <param name="protectable">The protectable.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.DisallowElement(`0)">
            <summary>
            Removes the element to the list of allowed elements.
            Beware that if you pass through "None", this will have no effect.
            </summary>
            <param name="element">The element to remove</param>
            <returns>The current protection instance</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.Protect">
            <summary>Protects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.Protect(System.String,ClosedXML.Excel.XLProtectionAlgorithm.Algorithm)">
            <summary>Protects this instance using the specified password and password hash algorithm.</summary>
            <param name="password">The password.</param>
            <param name="algorithm">The algorithm.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.Unprotect">
            <summary>Unprotects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLElementProtection`1.Unprotect(System.String)">
            <summary>Unprotects this instance using the specified password.</summary>
            <param name="password">The password.</param>
            <returns></returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLElementProtection.Algorithm">
            <summary>Gets the algorithm used to hash the password.</summary>
            <value>The algorithm.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLElementProtection.IsPasswordProtected">
            <summary>Gets a value indicating whether this instance is protected with a password.</summary>
            <value>
              <c>true</c> if this instance is password protected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLElementProtection.IsProtected">
            <summary>Gets a value indicating whether this instance is protected, either with or without a password.</summary>
            <value>
              <c>true</c> if this instance is protected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable`2.Protect">
            <summary>Protects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable`2.Protect(System.String,ClosedXML.Excel.XLProtectionAlgorithm.Algorithm)">
            <summary>Protects this instance using the specified password and password hash algorithm.</summary>
            <param name="password">The password.</param>
            <param name="algorithm">The algorithm.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable`2.Protect(System.String,ClosedXML.Excel.XLProtectionAlgorithm.Algorithm,`1)">
            <summary>Protects this instance with the specified password, password hash algorithm and set elements that the user is allowed to change.</summary>
            <param name="password">The password.</param>
            <param name="algorithm">The algorithm.</param>
            <param name="allowedElements">The allowed elements.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable`2.Unprotect">
            <summary>Unprotects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable`2.Unprotect(System.String)">
            <summary>Unprotects this instance using the specified password.</summary>
            <param name="password">The password.</param>
            <returns></returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLProtectable.IsPasswordProtected">
            <summary>Gets a value indicating whether this instance is protected with a password.</summary>
            <value>
              <c>true</c> if this instance is password protected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLProtectable.IsProtected">
            <summary>Gets a value indicating whether this instance is protected, either with or without a password.</summary>
            <value>
              <c>true</c> if this instance is protected; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable.Protect">
            <summary>Protects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable.Protect(System.String,ClosedXML.Excel.XLProtectionAlgorithm.Algorithm)">
            <summary>Protects this instance using the specified password and password hash algorithm.</summary>
            <param name="password">The password.</param>
            <param name="algorithm">The algorithm.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable.Unprotect">
            <summary>Unprotects this instance without a password.</summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLProtectable.Unprotect(System.String)">
            <summary>Unprotects this instance using the specified password.</summary>
            <param name="password">The password.</param>
            <returns></returns>
        </member>
        <member name="F:ClosedXML.Excel.XLWorkbookProtectionElements.Windows">
            <summary>
            The Windows option is available only in Excel 2007, Excel 2010, Excel for Mac 2011, and Excel 2016 for Mac. Select the Windows option if you want to prevent users from moving, resizing, or closing the workbook window, or hide/unhide windows.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.Ranges.Index.IXLRangeIndex">
            <summary>
            Interface for the engine aimed to speed-up the search for the range intersections.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.Ranges.Index.XLRangeIndex">
            <summary>
            Implementation of <see cref="T:ClosedXML.Excel.Ranges.Index.IXLRangeIndex"/> internally using QuadTree.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.Ranges.Index.XLRangeIndex.MinimumCountForIndexing">
            <summary>
            The minimum number of ranges to be included into a QuadTree. Until it is reached the ranges
            are added into a simple list to minimize the overhead of searching intersections on small collections.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.Ranges.Index.XLRangeIndex._rangeList">
            <summary>
            A collection of ranges used before the QuadTree is initialized (until <see cref="F:ClosedXML.Excel.Ranges.Index.XLRangeIndex.MinimumCountForIndexing"/>
            is reached.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.Ranges.Index.XLRangeIndex`1">
            <summary>
            Generic version of <see cref="T:ClosedXML.Excel.Ranges.Index.XLRangeIndex"/>.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.IXLAddressable">
            <summary>
            A very lightweight interface for entities that have an address as
            a rectangular range.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAddressable.RangeAddress">
            <summary>
              Gets an object with the boundaries of this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.AddToNamed(System.String)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <para>The default scope for the named range is Workbook.</para>
            </summary>
            <param name="rangeName">Name of the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.AddToNamed(System.String,ClosedXML.Excel.XLScope)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.AddToNamed(System.String,ClosedXML.Excel.XLScope,System.String)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            <param name="comment">The comments for the named range.</param>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLBaseCollection`2.Value">
            <summary>
            Sets the cells' value.
            <para>If the object is an IEnumerable ClosedXML will copy the collection's data into a table starting from each cell.</para>
            <para>If the object is a range ClosedXML will copy the range starting from each cell.</para>
            <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
            <para>ClosedXML will try to translate it to the corresponding type, if it can't then the value will be left as a string.</para>
            </summary>
            <value>
            The object containing the value(s) to set.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLBaseCollection`2.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these ranges.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Cell(System.Int32,System.Int32)">
            <summary>
            Gets the cell at the specified row and column.
            <para>The cell address is relative to the parent range.</para>
            </summary>
            <param name="row">The cell's row.</param>
            <param name="column">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Cell(System.String)">
            <summary>Gets the cell at the specified address.</summary>
            <para>The cell address is relative to the parent range.</para>
            <param name="cellAddressInRange">The cell address in the parent range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Cell(System.Int32,System.String)">
            <summary>
            Gets the cell at the specified row and column.
            <para>The cell address is relative to the parent range.</para>
            </summary>
            <param name="row">The cell's row.</param>
            <param name="column">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Cell(ClosedXML.Excel.IXLAddress)">
            <summary>Gets the cell at the specified address.</summary>
            <para>The cell address is relative to the parent range.</para>
            <param name="cellAddressInRange">The cell address in the parent range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Column(System.Int32)">
            <summary>
            Gets the specified column of the range.
            </summary>
            <param name="columnNumber">1-based column number relative to the first column of this range.</param>
            <returns>The relevant column</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Column(System.String)">
            <summary>
            Gets the specified column of the range.
            </summary>
            <param name="columnLetter">Column letter.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FirstColumn(System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Gets the first column of the range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FirstColumnUsed(System.Boolean,System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Gets the first column of the range that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.LastColumn(System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Gets the last column of the range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.LastColumnUsed(System.Boolean,System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Gets the last column of the range that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Columns(System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Gets a collection of all columns in this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Columns(System.Int32,System.Int32)">
            <summary>
            Gets a collection of the specified columns in this range.
            </summary>
            <param name="firstColumn">The first column to return. 1-based column number relative to the first column of this range.</param>
            <param name="lastColumn">The last column to return. 1-based column number relative to the first column of this range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Columns(System.String,System.String)">
            <summary>
            Gets a collection of the specified columns in this range.
            </summary>
            <param name="firstColumn">The first column to return.</param>
            <param name="lastColumn">The last column to return.</param>
            <returns>The relevant columns</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Columns(System.String)">
            <summary>
            Gets a collection of the specified columns in this range, separated by commas.
            <para>e.g. Columns("G:H"), Columns("10:11,13:14"), Columns("P:Q,S:T"), Columns("V")</para>
            </summary>
            <param name="columns">The columns to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FindColumn(System.Func{ClosedXML.Excel.IXLRangeColumn,System.Boolean})">
            <summary>
            Returns the first row that matches the given predicate
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FindRow(System.Func{ClosedXML.Excel.IXLRangeRow,System.Boolean})">
            <summary>
            Returns the first row that matches the given predicate
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FirstRow(System.Func{ClosedXML.Excel.IXLRangeRow,System.Boolean})">
            <summary>
            Gets the first row of the range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.FirstRowUsed(System.Boolean,System.Func{ClosedXML.Excel.IXLRangeRow,System.Boolean})">
            <summary>
            Gets the first row of the range that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.LastRow(System.Func{ClosedXML.Excel.IXLRangeRow,System.Boolean})">
            <summary>
            Gets the last row of the range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.LastRowUsed(System.Boolean,System.Func{ClosedXML.Excel.IXLRangeRow,System.Boolean})">
            <summary>
            Gets the last row of the range that contains a cell with a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Row(System.Int32)">
            <summary>
            Gets the specified row of the range.
            </summary>
            <param name="row">1-based row number relative to the first row of this range.</param>
            <returns>The relevant row</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Rows(System.Int32,System.Int32)">
            <summary>
            Gets a collection of the specified rows in this range.
            </summary>
            <param name="firstRow">The first row to return. 1-based row number relative to the first row of this range.</param>
            <param name="lastRow">The last row to return. 1-based row number relative to the first row of this range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Rows(System.String)">
            <summary>
            Gets a collection of the specified rows in this range, separated by commas.
            <para>e.g. Rows("4:5"), Rows("7:8,10:11"), Rows("13")</para>
            </summary>
            <param name="rows">The rows to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Returns the specified range.
            </summary>
            <param name="rangeAddress">The range boundaries.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(System.String)">
            <summary>Returns the specified range.</summary>
            <para>e.g. Range("A1"), Range("A1:C2")</para>
            <param name="rangeAddress">The range boundaries.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLCell)">
            <summary>Returns the specified range.</summary>
            <param name="firstCell">The first cell in the range.</param>
            <param name="lastCell"> The last cell in the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(System.String,System.String)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellAddress">The first cell address in the range.</param>
            <param name="lastCellAddress"> The last cell address in the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(ClosedXML.Excel.IXLAddress,ClosedXML.Excel.IXLAddress)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellAddress">The first cell address in the range.</param>
            <param name="lastCellAddress"> The last cell address in the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Ranges(System.String)">
            <summary>Returns a collection of ranges, separated by commas.</summary>
            <para>e.g. Ranges("A1"), Ranges("A1:C2"), Ranges("A1:B2,D1:D4")</para>
            <param name="ranges">The ranges to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Range(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>Returns the specified range.</summary>
            <param name="firstCellRow">   The first cell's row of the range to return.</param>
            <param name="firstCellColumn">The first cell's column of the range to return.</param>
            <param name="lastCellRow">    The last cell's row of the range to return.</param>
            <param name="lastCellColumn"> The last cell's column of the range to return.</param>
            <returns>.</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.RowCount">
            <summary>Gets the number of rows in this range.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.ColumnCount">
            <summary>Gets the number of columns in this range.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.InsertColumnsAfter(System.Int32)">
            <summary>
            Inserts X number of columns to the right of this range.
            <para>All cells to the right of this range will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.InsertColumnsBefore(System.Int32)">
            <summary>
            Inserts X number of columns to the left of this range.
            <para>This range and all cells to the right of this range will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.InsertRowsAbove(System.Int32)">
            <summary>
            Inserts X number of rows on top of this range.
            <para>This range and all cells below this range will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.InsertRowsBelow(System.Int32)">
            <summary>
            Inserts X number of rows below this range.
            <para>All cells below this range will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Delete(ClosedXML.Excel.XLShiftDeletedCells)">
            <summary>
            Deletes this range and shifts the surrounding cells accordingly.
            </summary>
            <param name="shiftDeleteCells">How to shift the surrounding cells.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Transpose(ClosedXML.Excel.XLTransposeOptions)">
            <summary>
            Transposes the contents and styles of all cells in this range.
            </summary>
            <param name="transposeOption">How to handle the surrounding cells when transposing the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRange.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this range.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.ColumnSpan">
            <summary>
            Gets the number of columns in the area covered by the range address.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.FirstAddress">
            <summary>
            Gets or sets the first address in the range.
            </summary>
            <value>
            The first address.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.IsValid">
            <summary>
            Gets or sets a value indicating whether this range is valid.
            </summary>
            <value>
            	<c>true</c> if this instance is valid; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.LastAddress">
            <summary>
            Gets or sets the last address in the range.
            </summary>
            <value>
            The last address.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.NumberOfCells">
            <summary>
            Gets the number of cells in the area covered by the range address.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeAddress.RowSpan">
            <summary>
            Gets the number of rows in the area covered by the range address.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.AsRange">
            <summary>Allocates the current range address in the internal range repository and returns it</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.Intersection(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Returns the intersection of this range address with another range address on the same worksheet.
            </summary>
            <param name="otherRangeAddress">The other range address.</param>
            <returns>The intersection's range address</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.IsEntireColumn">
            <summary>
            Determines whether range address spans the entire column.
            </summary>
            <returns>
              <c>true</c> if is entire column; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.IsEntireRow">
            <summary>
            Determines whether range address spans the entire row.
            </summary>
            <returns>
              <c>true</c> if is entire row; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.IsEntireSheet">
            <summary>
            Determines whether the range address spans the entire worksheet.
            </summary>
            <returns>
              <c>true</c> if is entire sheet; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeAddress.Relative(ClosedXML.Excel.IXLRangeAddress,ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Returns a range address so that its offset from the target base address is equal to the offset of the current range address to the source base address.
            For example, if the current range address is D4:E4, the source base address is A1:C3, then the relative address to the target base address B10:D13 is E14:F14
            </summary>
            <param name="sourceRangeAddress">The source base range address.</param>
            <param name="targetRangeAddress">The target base range address.</param>
            <returns>The relative range</returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeBase.Value">
            <summary>
              Sets a value to every cell in this range.
              <para>If the object is an IEnumerable ClosedXML will copy the collection's data into a table starting from each cell.</para>
              <para>If the object is a range ClosedXML will copy the range starting from each cell.</para>
              <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
              <para>ClosedXML will try to translate it to the corresponding type, if it can't then the value will be left as a string.</para>
            </summary>
            <value>
              The object containing the value(s) to set.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeBase.DataType">
            <summary>
              Sets the type of the cells' data.
              <para>Changing the data type will cause ClosedXML to covert the current value to the new data type.</para>
              <para>An exception will be thrown if the current value cannot be converted to the new data type.</para>
            </summary>
            <value>
              The type of the cell's data.
            </value>
            <exception cref = "T:System.ArgumentException"></exception>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeBase.FormulaA1">
            <summary>
              Sets the cells' formula with A1 references.
            </summary>
            <value>The formula with A1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeBase.FormulaR1C1">
            <summary>
              Sets the cells' formula with R1C1 references.
            </summary>
            <value>The formula with R1C1 references.</value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRangeBase.ShareString">
            <summary>
              Gets or sets a value indicating whether this cell's text should be shared or not.
            </summary>
            <value>
              If false the cell's text will not be shared and stored as an inline value.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Cells">
            <summary>
              Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.CellsUsed">
            <summary>
              Returns the collection of cells that have a value. Formats are ignored.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.CellsUsed(System.Boolean)">
            <summary>
              Returns the collection of cells that have a value.
            </summary>
            <param name = "includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Search(System.String,System.Globalization.CompareOptions,System.Boolean)">
            <summary>
            Searches the cells' contents for a given piece of text
            </summary>
            <param name="searchText">The search text.</param>
            <param name="compareOptions">The compare options.</param>
            <param name="searchFormulae">if set to <c>true</c> search formulae instead of cell values.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.FirstCell">
            <summary>
              Returns the first cell of this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.FirstCellUsed">
            <summary>
              Returns the first cell with a value of this range. Formats are ignored.
              <para>The cell's address is going to be ([First Row with a value], [First Column with a value])</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.FirstCellUsed(System.Boolean)">
            <summary>
              Returns the first cell with a value of this range.
            </summary>
            <para>The cell's address is going to be ([First Row with a value], [First Column with a value])</para>
            <param name = "includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.LastCell">
            <summary>
              Returns the last cell of this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.LastCellUsed">
            <summary>
              Returns the last cell with a value of this range. Formats are ignored.
              <para>The cell's address is going to be ([Last Row with a value], [Last Column with a value])</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.LastCellUsed(System.Boolean)">
            <summary>
              Returns the last cell with a value of this range.
            </summary>
            <para>The cell's address is going to be ([Last Row with a value], [Last Column with a value])</para>
            <param name = "includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Contains(System.String)">
            <summary>
              Determines whether this range contains the specified range (completely).
              <para>For partial matches use the range.Intersects method.</para>
            </summary>
            <param name = "rangeAddress">The range address.</param>
            <returns>
              <c>true</c> if this range contains the specified range; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Contains(ClosedXML.Excel.IXLRangeBase)">
            <summary>
              Determines whether this range contains the specified range (completely).
              <para>For partial matches use the range.Intersects method.</para>
            </summary>
            <param name = "range">The range to match.</param>
            <returns>
              <c>true</c> if this range contains the specified range; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Intersects(System.String)">
            <summary>
              Determines whether this range intersects the specified range.
              <para>For whole matches use the range.Contains method.</para>
            </summary>
            <param name = "rangeAddress">The range address.</param>
            <returns>
              <c>true</c> if this range intersects the specified range; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Intersects(ClosedXML.Excel.IXLRangeBase)">
            <summary>
              Determines whether this range contains the specified range.
              <para>For whole matches use the range.Contains method.</para>
            </summary>
            <param name = "range">The range to match.</param>
            <returns>
              <c>true</c> if this range intersects the specified range; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Unmerge">
            <summary>
              Unmerges this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Merge">
            <summary>
              Merges this range.
              <para>The contents and style of the merged cells will be equal to the first cell.</para>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.AddToNamed(System.String)">
            <summary>
              Creates a named range out of this range.
              <para>If the named range exists, it will add this range to that named range.</para>
              <para>The default scope for the named range is Workbook.</para>
            </summary>
            <param name = "rangeName">Name of the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.AddToNamed(System.String,ClosedXML.Excel.XLScope)">
            <summary>
              Creates a named range out of this range.
              <para>If the named range exists, it will add this range to that named range.</para>
              <param name = "rangeName">Name of the range.</param>
              <param name = "scope">The scope for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.AddToNamed(System.String,ClosedXML.Excel.XLScope,System.String)">
            <summary>
              Creates a named range out of this range.
              <para>If the named range exists, it will add this range to that named range.</para>
              <param name = "rangeName">Name of the range.</param>
              <param name = "scope">The scope for the named range.</param>
              <param name = "comment">The comments for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this range.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.DeleteComments">
            <summary>
              Deletes the cell comments from this range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.AsRange">
            <summary>
              Converts this object to a range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.IsEntireColumn">
            <summary>
            Determines whether range address spans the entire column.
            </summary>
            <returns>
              <c>true</c> if is entire column; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.IsEntireRow">
            <summary>
            Determines whether range address spans the entire row.
            </summary>
            <returns>
              <c>true</c> if is entire row; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.IsEntireSheet">
            <summary>
            Determines whether the range address spans the entire worksheet.
            </summary>
            <returns>
              <c>true</c> if is entire sheet; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Grow">
            <summary>
            Grows this the current range by one cell to each side
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Grow(System.Int32)">
            <summary>
            Grows this the current range by the specified number of cells to each side.
            </summary>
            <param name="growCount">The grow count.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Shrink">
            <summary>
            Shrinks this current range by one cell.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Shrink(System.Int32)">
            <summary>
            Shrinks the current range by the specified number of cells from each side.
            </summary>
            <param name="shrinkCount">The shrink count.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Intersection(ClosedXML.Excel.IXLRangeBase,System.Func{ClosedXML.Excel.IXLCell,System.Boolean},System.Func{ClosedXML.Excel.IXLCell,System.Boolean})">
            <summary>
            Returns the intersection of this range with another range on the same worksheet.
            </summary>
            <param name="otherRange">The other range.</param>
            <param name="thisRangePredicate">Predicate applied to this range's cells.</param>
            <param name="otherRangePredicate">Predicate applied to the other range's cells.</param>
            <returns>The range address of the intersection</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.SurroundingCells(System.Func{ClosedXML.Excel.IXLCell,System.Boolean})">
            <summary>
            Returns the set of cells surrounding the current range.
            </summary>
            <param name="predicate">The predicate to apply on the resulting set of cells.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Union(ClosedXML.Excel.IXLRangeBase,System.Func{ClosedXML.Excel.IXLCell,System.Boolean},System.Func{ClosedXML.Excel.IXLCell,System.Boolean})">
            <summary>
            Calculates the union of two ranges on the same worksheet.
            </summary>
            <param name="otherRange">The other range.</param>
            <param name="thisRangePredicate">Predicate applied to this range's cells.</param>
            <param name="otherRangePredicate">Predicate applied to the other range's cells.</param>
            <returns>
            The union
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Difference(ClosedXML.Excel.IXLRangeBase,System.Func{ClosedXML.Excel.IXLCell,System.Boolean},System.Func{ClosedXML.Excel.IXLCell,System.Boolean})">
            <summary>
            Returns all cells in the current range that are not in the other range.
            </summary>
            <param name="otherRange">The other range.</param>
            <param name="thisRangePredicate">Predicate applied to this range's cells.</param>
            <param name="otherRangePredicate">Predicate applied to the other range's cells.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeBase.Relative(ClosedXML.Excel.IXLRangeBase,ClosedXML.Excel.IXLRangeBase)">
            <summary>
            Returns a range so that its offset from the target base range is equal to the offset of the current range to the source base range.
            For example, if the current range is D4:E4, the source base range is A1:C3, then the relative range to the target base range B10:D13 is E14:F14
            </summary>
            <param name="sourceBaseRange">The source base range.</param>
            <param name="targetBaseRange">The target base range.</param>
            <returns>The relative range</returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Cell(System.Int32)">
            <summary>
            Gets the cell in the specified row.
            </summary>
            <param name="rowNumber">The cell's row.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Cells(System.String)">
            <summary>
            Returns the specified group of cells, separated by commas.
            <para>e.g. Cells("1"), Cells("1:5"), Cells("1:2,4:5")</para>
            </summary>
            <param name="cellsInColumn">The column cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Cells(System.Int32,System.Int32)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstRow">The first row in the group of cells to return.</param>
            <param name="lastRow">The last row in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.InsertColumnsAfter(System.Int32)">
            <summary>
            Inserts X number of columns to the right of this range.
            <para>All cells to the right of this range will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.InsertColumnsBefore(System.Int32)">
            <summary>
            Inserts X number of columns to the left of this range.
            <para>This range and all cells to the right of this range will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of columns to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.InsertCellsAbove(System.Int32)">
            <summary>
            Inserts X number of cells on top of this column.
            <para>This column and all cells below it will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of cells to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.InsertCellsBelow(System.Int32)">
            <summary>
            Inserts X number of cells below this range.
            <para>All cells below this column will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of cells to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Delete">
            <summary>
            Deletes this range and shifts the cells at the right.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Delete(ClosedXML.Excel.XLShiftDeletedCells)">
            <summary>
            Deletes this range and shifts the surrounding cells accordingly.
            </summary>
            <param name="shiftDeleteCells">How to shift the surrounding cells.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.ColumnNumber">
            <summary>
            Gets this column's number in the range
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.ColumnLetter">
            <summary>
            Gets this column's letter in the range
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumn.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this column.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.Add(ClosedXML.Excel.IXLRangeColumn)">
            <summary>
            Adds a column range to this group.
            </summary>
            <param name="columRange">The column range to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.Delete">
            <summary>
            Deletes all columns and shifts the columns at the right of them accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeColumns.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these columns.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Cell(System.Int32)">
            <summary>
            Gets the cell in the specified column.
            </summary>
            <param name="columnNumber">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Cell(System.String)">
            <summary>
            Gets the cell in the specified column.
            </summary>
            <param name="columnLetter">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Cells(System.String)">
            <summary>
            Returns the specified group of cells, separated by commas.
            <para>e.g. Cells("1"), Cells("1:5"), Cells("1:2,4:5")</para>
            </summary>
            <param name="cellsInRow">The row's cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Cells(System.Int32,System.Int32)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstColumn">The first column in the group of cells to return.</param>
            <param name="lastColumn">The last column in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Cells(System.String,System.String)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstColumn">The first column in the group of cells to return.</param>
            <param name="lastColumn">The last column in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.InsertCellsAfter(System.Int32)">
            <summary>
            Inserts X number of cells to the right of this row.
            <para>All cells to the right of this row will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of cells to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.InsertCellsBefore(System.Int32)">
            <summary>
            Inserts X number of cells to the left of this row.
            <para>This row and all cells to the right of it will be shifted X number of columns.</para>
            </summary>
            <param name="numberOfColumns">Number of cells to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.InsertRowsAbove(System.Int32)">
            <summary>
            Inserts X number of rows on top of this row.
            <para>This row and all cells below it will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.InsertRowsBelow(System.Int32)">
            <summary>
            Inserts X number of rows below this row.
            <para>All cells below this row will be shifted X number of rows.</para>
            </summary>
            <param name="numberOfRows">Number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Delete">
            <summary>
            Deletes this range and shifts the cells below.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Delete(ClosedXML.Excel.XLShiftDeletedCells)">
            <summary>
            Deletes this range and shifts the surrounding cells accordingly.
            </summary>
            <param name="shiftDeleteCells">How to shift the surrounding cells.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.RowNumber">
            <summary>
            Gets this row's number in the range
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRow.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this row.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.Add(ClosedXML.Excel.IXLRangeRow)">
            <summary>
            Adds a row range to this group.
            </summary>
            <param name="rowRange">The row range to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.Delete">
            <summary>
            Deletes all rows and shifts the rows below them accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRangeRows.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these rows.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.Add(ClosedXML.Excel.IXLRangeBase)">
            <summary>
            Adds the specified range to this group.
            </summary>
            <param name="range">The range to add to this group.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.Remove(ClosedXML.Excel.IXLRange)">
            <summary>
            Removes the specified range from this group.
            </summary>
            <param name="range">The range to remove from this group.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.RemoveAll(System.Predicate{ClosedXML.Excel.IXLRange},System.Boolean)">
            <summary>
            Removes ranges matching the criteria from the collection, optionally releasing their event handlers.
            </summary>
            <param name="match">Criteria to filter ranges. Only those ranges that satisfy the criteria will be removed.
            Null means the entire collection should be cleared.</param>
            <param name="releaseEventHandlers">Specify whether or not should removed ranges be unsubscribed from 
            row/column shifting events. Until ranges are unsubscribed they cannot be collected by GC.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.GetIntersectedRanges(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Filter ranges from a collection that intersect the specified address. Is much more efficient
            that using Linq expression .Where().
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.GetIntersectedRanges(ClosedXML.Excel.IXLAddress)">
            <summary>
            Filter ranges from a collection that intersect the specified address. Is much more efficient
            that using Linq expression .Where().
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.GetIntersectedRanges(ClosedXML.Excel.IXLCell)">
            <summary>
            Filter ranges from a collection that intersect the specified cell. Is much more efficient
            that using Linq expression .Where().
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.AddToNamed(System.String)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <para>The default scope for the named range is Workbook.</para>
            </summary>
            <param name="rangeName">Name of the range.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.AddToNamed(System.String,ClosedXML.Excel.XLScope)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.AddToNamed(System.String,ClosedXML.Excel.XLScope,System.String)">
            <summary>
            Creates a named range out of these ranges.
            <para>If the named range exists, it will add these ranges to that named range.</para>
            <param name="rangeName">Name of the range.</param>
            <param name="scope">The scope for the named range.</param>
            <param name="comment">The comments for the named range.</param>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLRanges.Value">
            <summary>
            Sets the cells' value.
            <para>If the object is an IEnumerable ClosedXML will copy the collection's data into a table starting from each cell.</para>
            <para>If the object is a range ClosedXML will copy the range starting from each cell.</para>
            <para>Setting the value to an object (not IEnumerable/range) will call the object's ToString() method.</para>
            <para>ClosedXML will try to translate it to the corresponding type, if it can't then the value will be left as a string.</para>
            </summary>
            <value>
            The object containing the value(s) to set.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these ranges.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRanges.Consolidate">
            <summary>
            Create a new collection of ranges which are consolidated version of source ranges.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeAddress.Normalize">
            <summary>
            Lead a range address to a normal form - when <see cref="P:ClosedXML.Excel.XLRangeAddress.FirstAddress"/> points to the top-left address and
            <see cref="P:ClosedXML.Excel.XLRangeAddress.LastAddress"/> points to the bottom-right address.
            </summary>
            <returns></returns>
        </member>
        <member name="P:ClosedXML.Excel.XLRangeBase.DataValidation">
            <summary>
            Get the data validation rule containing current range or create a new one if no rule was defined for range.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeBase.CellValues">
            <summary>
            Return the collection of cell values not initializing empty cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeColumn.#ctor(ClosedXML.Excel.XLRangeParameters)">
            <summary>
            The direct contructor should only be used in <see cref="P:ClosedXML.Excel.XLWorksheet.RangeFactory"/>.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.XLRangeConsolidationEngine">
            <summary>
            Engine for ranges consolidation. Supports IXLRanges including ranges from either one or multiple worksheets.
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.XLRangeConsolidationEngine.XLRangeConsolidationMatrix">
            <summary>
            Class representing the area covering ranges to be consolidated as a set of bit matrices. Does all the dirty job
            of ranges consolidation.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeConsolidationEngine.XLRangeConsolidationMatrix.#ctor(ClosedXML.Excel.IXLWorksheet,System.Collections.Generic.IEnumerable{ClosedXML.Excel.IXLRange})">
            <summary>
            Constructor.
            </summary>
            <param name="worksheet">Current worksheet.</param>
            <param name="ranges">Ranges to be consolidated. They are expected to belong to the current worksheet, no check is performed.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeConsolidationEngine.XLRangeConsolidationMatrix.GetConsolidatedRanges">
            <summary>
            Get consolidated ranges equivalent to the input ones.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeKey.Equals(ClosedXML.Excel.XLRangeKey)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <returns>true if the current object is equal to the <paramref name="other" /> parameter; otherwise, false.</returns>
            <param name="other">An object to compare with this object.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeKey.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary>
            <returns>true if <paramref name="obj" /> and this instance are the same type and represent the same value; otherwise, false.</returns>
            <param name="obj">Another object to compare to. </param>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeKey.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
            <returns>A 32-bit signed integer that is the hash code for this instance.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLRangeRow.#ctor(ClosedXML.Excel.XLRangeParameters)">
            <summary>
            The direct contructor should only be used in <see cref="P:ClosedXML.Excel.XLWorksheet.RangeFactory"/>.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLRanges._indexes">
            <summary>
            Normally, XLRanges collection includes ranges from a single worksheet, but not necessarily.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRanges.RemoveAll(System.Predicate{ClosedXML.Excel.IXLRange},System.Boolean)">
            <summary>
            Removes ranges matching the criteria from the collection, optionally releasing their event handlers.
            </summary>
            <param name="match">Criteria to filter ranges. Only those ranges that satisfy the criteria will be removed.
            Null means the entire collection should be cleared.</param>
            <param name="releaseEventHandlers">Specify whether or not should removed ranges be unsubscribed from
            row/column shifting events. Until ranges are unsubscribed they cannot be collected by GC.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLRanges.GetIntersectedRanges(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Filter ranges from a collection that intersect the specified address. Is much more efficient
            that using Linq expression .Where().
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRanges.GetIntersectedRanges(ClosedXML.Excel.IXLAddress)">
            <summary>
            Filter ranges from a collection that intersect the specified address. Is much more efficient
            that using Linq expression .Where().
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLRow.Height">
            <summary>
            Gets or sets the height of this row.
            </summary>
            <value>
            The width of this row.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.ClearHeight">
            <summary>
            Clears the height for the row and defaults it to the spreadsheet row height.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Delete">
            <summary>
            Deletes this row and shifts the rows below this one accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.RowNumber">
            <summary>
            Gets this row's number
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.InsertRowsBelow(System.Int32)">
            <summary>
            Inserts X number of rows below this one.
            <para>All rows below will be shifted accordingly.</para>
            </summary>
            <param name="numberOfRows">The number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.InsertRowsAbove(System.Int32)">
            <summary>
            Inserts X number of rows above this one.
            <para>This row and all below will be shifted accordingly.</para>
            </summary>
            <param name="numberOfRows">The number of rows to insert.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.AdjustToContents(System.Int32)">
            <summary>
            Adjusts the height of the row based on its contents, starting from the startColumn.
            </summary>
            <param name="startColumn">The column to start calculating the row height.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.AdjustToContents(System.Int32,System.Int32)">
            <summary>
            Adjusts the height of the row based on its contents, starting from the startColumn and ending at endColumn.
            </summary>
            <param name="startColumn">The column to start calculating the row height.</param>
            <param name="endColumn">The column to end calculating the row height.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Hide">
            <summary>Hides this row.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Unhide">
            <summary>Unhides this row.</summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLRow.IsHidden">
            <summary>
            Gets a value indicating whether this row is hidden or not.
            </summary>
            <value>
              <c>true</c> if this row is hidden; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLRow.OutlineLevel">
            <summary>
            Gets or sets the outline level of this row.
            </summary>
            <value>
            The outline level of this row.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Group">
            <summary>
            Adds this row to the next outline level (Increments the outline level for this row by 1).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Group(System.Boolean)">
            <summary>
            Adds this row to the next outline level (Increments the outline level for this row by 1).
            </summary>
            <param name="collapse">If set to <c>true</c> the row will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Group(System.Int32)">
            <summary>
            Sets outline level for this row.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Group(System.Int32,System.Boolean)">
            <summary>
            Sets outline level for this row.
            </summary>
            <param name="outlineLevel">The outline level.</param>
            <param name="collapse">If set to <c>true</c> the row will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Ungroup">
            <summary>
            Adds this row to the previous outline level (decrements the outline level for this row by 1).
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Ungroup(System.Boolean)">
            <summary>
            Adds this row to the previous outline level (decrements the outline level for this row by 1).
            </summary>
            <param name="fromAll">If set to <c>true</c> it will remove this row from all outline levels.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Collapse">
            <summary>
            Show this row as collapsed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Cell(System.Int32)">
            <summary>
            Gets the cell in the specified column.
            </summary>
            <param name="columnNumber">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Cell(System.String)">
            <summary>
            Gets the cell in the specified column.
            </summary>
            <param name="columnLetter">The cell's column.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Cells(System.String)">
            <summary>
            Returns the specified group of cells, separated by commas.
            <para>e.g. Cells("1"), Cells("1:5"), Cells("1,3:5")</para>
            </summary>
            <param name="cellsInRow">The row's cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Cells(System.Int32,System.Int32)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstColumn">The first column in the group of cells to return.</param>
            <param name="lastColumn">The last column in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Cells(System.String,System.String)">
            <summary>
            Returns the specified group of cells.
            </summary>
            <param name="firstColumn">The first column in the group of cells to return.</param>
            <param name="lastColumn">The last column in the group of cells to return.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Expand">
            <summary>Expands this row (if it's collapsed).</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.AddHorizontalPageBreak">
            <summary>
            Adds a horizontal page break after this row.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRow.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this row.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="P:ClosedXML.Excel.IXLRows.Height">
            <summary>
            Sets the height of all rows.
            </summary>
            <value>
            The height of all rows.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Delete">
            <summary>
            Deletes all rows and shifts the rows below them accordingly.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.AdjustToContents">
            <summary>
            Adjusts the height of all rows based on its contents.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.AdjustToContents(System.Int32)">
            <summary>
            Adjusts the height of all rows based on its contents, starting from the startColumn.
            </summary>
            <param name="startColumn">The column to start calculating the row height.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.AdjustToContents(System.Int32,System.Int32)">
            <summary>
            Adjusts the height of all rows based on its contents, starting from the startColumn and ending at endColumn.
            </summary>
            <param name="startColumn">The column to start calculating the row height.</param>
            <param name="endColumn">The column to end calculating the row height.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Hide">
            <summary>
            Hides all rows.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Unhide">
            <summary>Unhides all rows.</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Group">
            <summary>
            Increments the outline level of all rows by 1.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Group(System.Boolean)">
            <summary>
            Increments the outline level of all rows by 1.
            </summary>
            <param name="collapse">If set to <c>true</c> the rows will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Group(System.Int32)">
            <summary>
            Sets outline level for all rows.
            </summary>
            <param name="outlineLevel">The outline level.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Group(System.Int32,System.Boolean)">
            <summary>
            Sets outline level for all rows.
            </summary>
            <param name="outlineLevel">The outline level.</param>
            <param name="collapse">If set to <c>true</c> the rows will be shown collapsed.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Ungroup">
            <summary>
            Decrements the outline level of all rows by 1.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Ungroup(System.Boolean)">
            <summary>
            Decrements the outline level of all rows by 1.
            </summary>
            <param name="fromAll">If set to <c>true</c> it will remove the rows from all outline levels.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Collapse">
            <summary>
            Show all rows as collapsed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Expand">
            <summary>Expands all rows (if they're collapsed).</summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.AddHorizontalPageBreaks">
            <summary>
            Adds a horizontal page break after these rows.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLRows.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these rows.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLRow.#ctor(ClosedXML.Excel.XLWorksheet,System.Int32)">
            <summary>
            The direct contructor should only be used in <see cref="P:ClosedXML.Excel.XLWorksheet.RangeFactory"/>.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLRows.#ctor(ClosedXML.Excel.XLWorksheet,ClosedXML.Excel.XLStyleValue)">
            <summary>
            Create a new instance of <see cref="T:ClosedXML.Excel.XLRows"/>.
            </summary>
            <param name="worksheet">If worksheet is specified it means that the created instance represents
            all rows on a worksheet so changing its height will affect all rows.</param>
            <param name="defaultStyle">Default style to use when initializing child entries.</param>
        </member>
        <member name="P:ClosedXML.Excel.SaveOptions.FilterPrivacy">
            <summary>
            Gets or sets the filter privacy flag. Set to null to leave the current property in saved workbook unchanged
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLSparkline.#ctor(ClosedXML.Excel.IXLSparklineGroup,ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLRange)">
            <summary>
            Create a new sparkline
            </summary>
            <param name="sparklineGroup">The sparkline group to add the sparkline to</param>
            <param name="cell">The cell to place the sparkline in</param>
            <param name="sourceData">The range the sparkline gets data from</param>
        </member>
        <member name="P:ClosedXML.Excel.XLSparklineGroup.Worksheet">
            <summary>
            The worksheet this sparkline group is associated with
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.#ctor(ClosedXML.Excel.IXLWorksheet,ClosedXML.Excel.IXLSparklineGroup)">
            <summary>
            Add a new sparkline group copied from an existing sparkline group to the specified worksheet
            </summary>
            <param name="targetWorksheet">The worksheet the sparkline group is being added to</param>
            <param name="copyFrom">The sparkline group to copy from</param>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.#ctor(ClosedXML.Excel.IXLWorksheet,System.String,System.String)">
            <summary>
            Add a new sparkline group copied from an existing sparkline group to the specified worksheet
            </summary>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.#ctor(ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLRange)">
            <summary>
            Add a new sparkline group copied from an existing sparkline group to the specified worksheet
            </summary>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.#ctor(ClosedXML.Excel.IXLRange,ClosedXML.Excel.IXLRange)">
            <summary>
            Add a new sparkline group copied from an existing sparkline group to the specified worksheet
            </summary>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.Add(ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLRange)">
            <summary>
            Add a sparkline to the group.
            </summary>
            <param name="location">The cell to add sparklines to. If it already contains a sparkline
            it will be replaced.</param>
            <param name="sourceData">The range the sparkline gets data from</param>
            <returns>A newly created sparkline.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.CopyFrom(ClosedXML.Excel.IXLSparklineGroup)">
            <summary>
            Copy the details from a specified sparkline group
            </summary>
            <param name="sparklineGroup">The sparkline group to copy from</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.CopyTo(ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Copy this sparkline group to the specified worksheet
            </summary>
            <param name="targetSheet">The worksheet to copy this sparkline group to</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.Remove(ClosedXML.Excel.IXLCell)">
            <summary>
            Remove all sparklines in the specified cell from this group
            </summary>
            <param name="cell">The cell to remove sparklines from</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.Remove(ClosedXML.Excel.IXLSparkline)">
            <summary>
            Remove the sparkline from this group
            </summary>
            <param name="sparkline"></param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.RemoveAll">
            <summary>
            Remove all sparklines from this group
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroup.#ctor(ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Add a new sparkline group to the specified worksheet
            </summary>
            <param name="targetWorksheet">The worksheet the sparkline group is being added to</param>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.Add">
            <summary>
            Add empty sparkline group.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.Add(ClosedXML.Excel.IXLSparklineGroup)">
            <summary>
            Add the sparkline group to the collection.
            </summary>
            <param name="sparklineGroup">The sparkline group to add to the collection</param>
            <returns>The same sparkline group</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.AddCopy(ClosedXML.Excel.IXLSparklineGroup,ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Add a copy of an existing sparkline group to the specified worksheet
            </summary>
            <param name="sparklineGroupToCopy">The sparkline group to copy</param>
            <param name="targetWorksheet">The worksheet the sparkline group is being added to</param>
            <returns>The new sparkline group added</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.CopyTo(ClosedXML.Excel.IXLWorksheet)">
            <summary>
            Copy this sparkline group to a different worksheet
            </summary>
            <param name="targetSheet">The worksheet to copy the sparkline group to</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.GetSparkline(ClosedXML.Excel.IXLCell)">
            <summary>
            Search for the first sparkline that is in the specified cell
            </summary>
            <param name="cell">The cell to find the sparkline for</param>
            <returns>The sparkline in the cell or null if no sparklines are found</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.GetSparklines(ClosedXML.Excel.IXLRangeBase)">
            <summary>
            Find all sparklines located in a given range
            </summary>
            <param name="searchRange">The range to search</param>
            <returns>The sparkline in the cell or null if no sparklines are found</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.Remove(ClosedXML.Excel.IXLCell)">
            <summary>
            Remove all sparklines in the specified cell
            </summary>
            <param name="cell">The cell to remove sparklines from</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.Remove(ClosedXML.Excel.IXLSparklineGroup)">
            <summary>
            Remove the sparkline group from the worksheet
            </summary>
            <param name="sparklineGroup">The sparkline group to remove</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.Remove(ClosedXML.Excel.IXLSparkline)">
            <summary>
            Remove the sparkline from the worksheet
            </summary>
            <param name="sparkline">The sparkline to remove</param>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineGroups.RemoveAll">
            <summary>
            Remove all sparkline groups and their contents from the worksheet.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineStyle.op_Inequality(ClosedXML.Excel.XLSparklineStyle,ClosedXML.Excel.XLSparklineStyle)">
            <summary>Returns a value that indicates whether two <see cref="T:ClosedXML.Excel.XLSparklineStyle" /> objects have different values.</summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns>true if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, false.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineStyle.op_Equality(ClosedXML.Excel.XLSparklineStyle,ClosedXML.Excel.XLSparklineStyle)">
            <summary>Returns a value that indicates whether the values of two <see cref="T:ClosedXML.Excel.XLSparklineStyle" /> objects are equal.</summary>
            <param name="left">The first value to compare.</param>
            <param name="right">The second value to compare.</param>
            <returns>true if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, false.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineStyle.Equals(ClosedXML.Excel.XLSparklineStyle)">
            <summary>Indicates whether the current object is equal to another object of the same type.</summary>
            <param name="other">An object to compare with this object.</param>
            <returns>true if the current object is equal to the <paramref name="other">other</paramref> parameter; otherwise, false.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineStyle.Equals(System.Object)">
            <summary>Determines whether the specified object is equal to the current object.</summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object  is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLSparklineStyle.GetHashCode">
            <summary>Serves as the default hash function.</summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="F:ClosedXML.Excel.XLColor.TOOLTIPCOLORINDEX">
            <summary>
            Usually indexed colors are limited to max 63
            Index 81 is some special case.
            Some people claim it's the index for tooltip color.
            We'll return normal black when index 81 is found.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.Horizontal">
            <summary>
            Gets or sets the cell's horizontal alignment.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.Vertical">
            <summary>
            Gets or sets the cell's vertical alignment.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.Indent">
            <summary>
            Gets or sets the cell's text indentation.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.JustifyLastLine">
            <summary>
            Gets or sets whether the cell's last line is justified or not.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.ReadingOrder">
            <summary>
            Gets or sets the cell's reading order.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.RelativeIndent">
            <summary>
            Gets or sets the cell's relative indent.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.ShrinkToFit">
            <summary>
            Gets or sets whether the cell's font size should decrease to fit the contents.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.TextRotation">
            <summary>
            Gets or sets the cell's text rotation.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.WrapText">
            <summary>
            Gets or sets whether the cell's text should wrap if it doesn't fit.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLAlignment.TopToBottom">
            <summary>
            Gets or sets wheter the cell's text should be displayed from to to bottom
            <para>(as opposed to the normal left to right).</para>
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Ansi">
            <summary>
            ASCII character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Default">
            <summary>
            System default character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Symbol">
            <summary>
            Symbol character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Mac">
            <summary>
            Characters used by Macintosh.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.ShiftJIS">
            <summary>
            Japanese character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Hangul">
            <summary>
            Korean character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Hangeul">
            <summary>
            Another common spelling of the Korean character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Johab">
            <summary>
            Korean character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.GB2312">
            <summary>
            Chinese character set used in mainland China.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.ChineseBig5">
            <summary>
            Chinese character set used mostly in Hong Kong SAR and Taiwan.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Greek">
            <summary>
            Greek character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Turkish">
            <summary>
            Turkish character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Vietnamese">
            <summary>
            Vietnamese character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Hebrew">
            <summary>
            Hebrew character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Arabic">
            <summary>
            Arabic character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Baltic">
            <summary>
            Baltic character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Russian">
            <summary>
            Russian character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Thai">
            <summary>
            Thai character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.EastEurope">
            <summary>
            Eastern European character set.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLFontCharSet.Oem">
            <summary>
            Extended ASCII character set used with disk operating system (DOS) and some Microsoft Windows fonts.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLStylized.StyleValue">
            <summary>
            Immutable style
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLAlignment.#ctor(ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLAlignmentValue)">
            <summary>
            Create an instance of XLAlignment initializing it with the specified value.
            </summary>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLBorder.#ctor(ClosedXML.Excel.IXLStylized,ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLBorderValue)">
            <summary>
            Create an instance of XLBorder initializing it with the specified value.
            </summary>
            <param name="container">Container the border is applied to.</param>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="T:ClosedXML.Excel.XLBorder.RestoreOutsideBorder">
            <summary>
            Helper class that remembers outside border state before editing (in constructor) and restore afterwards (on disposing).
            It presumes that size of the range does not change during the editing, else it will fail.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLFill.#ctor(ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLFillValue)">
            <summary>
            Create an instance of XLFill initializing it with the specified value.
            </summary>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLFont.#ctor(ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLFontValue)">
            <summary>
            Create an instance of XLFont initializing it with the specified value.
            </summary>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLNumberFormat.#ctor(ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLNumberFormatValue)">
            <summary>
            Create an instance of XLNumberFormat initializing it with the specified value.
            </summary>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="T:ClosedXML.Excel.XLPredefinedFormat">
            <summary>
            Reference point of date/number formats available.
            See more at: https://msdn.microsoft.com/en-us/library/documentformat.openxml.spreadsheet.numberingformat.aspx
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLPredefinedFormat.General">
            <summary>
            General
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.General">
            <summary>
            General
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Integer">
            <summary>
            0
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Precision2">
            <summary>
            0.00
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.IntegerWithSeparator">
            <summary>
            #,##0
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Precision2WithSeparator">
            <summary>
            #,##0.00
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.PercentInteger">
            <summary>
            0%
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.PercentPrecision2">
            <summary>
            0.00%
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.ScientificPrecision2">
            <summary>
            0.00E+00
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.FractionPrecision1">
            <summary>
            # ?/?
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.FractionPrecision2">
            <summary>
            # ??/??
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.IntegerWithSeparatorAndParens">
            <summary>
            #,##0 ,(#,##0)
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.IntegerWithSeparatorAndParensRed">
            <summary>
            #,##0 ,[Red](#,##0)
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Precision2WithSeparatorAndParens">
            <summary>
            #,##0.00,(#,##0.00)
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Precision2WithSeparatorAndParensRed">
            <summary>
            #,##0.00,[Red](#,##0.00)
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.ScientificUpToHundredsAndPrecision1">
            <summary>
            ##0.0E+0
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.Number.Text">
            <summary>
            @
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.General">
            <summary>
            General
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.DayMonthYear4WithSlashes">
            <summary>
            d/m/yyyy
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.DayMonthAbbrYear2WithDashes">
            <summary>
            d-mmm-yy
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.DayMonthAbbrWithDash">
            <summary>
            d-mmm
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.MonthAbbrYear2WithDash">
            <summary>
            mmm-yy
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Hour12MinutesAmPm">
            <summary>
            h:mm tt
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Hour12MinutesSecondsAmPm">
            <summary>
            h:mm:ss tt
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Hour24Minutes">
            <summary>
            H:mm
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Hour24MinutesSeconds">
            <summary>
            H:mm:ss
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.MonthDayYear4WithDashesHour24Minutes">
            <summary>
            m/d/yyyy H:mm
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.MinutesSeconds">
            <summary>
            mm:ss
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Hour12MinutesSeconds">
            <summary>
            [h]:mm:ss
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.MinutesSecondsMillis1">
            <summary>
            mmss.0
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLPredefinedFormat.DateTime.Text">
            <summary>
            @
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLProtection.#ctor(ClosedXML.Excel.XLStyle,ClosedXML.Excel.XLProtectionValue)">
            <summary>
            Create an instance of XLProtection initializing it with the specified value.
            </summary>
            <param name="style">Style to attach the new instance to.</param>
            <param name="value">Style value to use.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLStyle.#ctor(ClosedXML.Excel.XLStyleValue)">
            <summary>
            To initialize XLStyle.Default only
            </summary>
        </member>
        <member name="T:ClosedXML.Excel.XLStylizedBase">
            <summary>
            Base class for any workbook element that has or may have a style.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLStylizedBase.StyleValue">
            <summary>
            Read-only style property.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLStylizedBase.Style">
            <summary>
            Editable style of the workbook element. Modification of this property DOES affect styles of child objects as well - they will
            be changed accordingly. Accessing this property causes a new <see cref="T:ClosedXML.Excel.XLStyle"/> instance generated so use this property
            with caution. If you need only _read_ the style consider using <see cref="P:ClosedXML.Excel.XLStylizedBase.StyleValue"/> property instead.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLStylizedBase.InnerStyle">
            <summary>
            Editable style of the workbook element. Modification of this property DOES NOT affect styles of child objects.
            Accessing this property causes a new <see cref="T:ClosedXML.Excel.XLStyle"/> instance generated so use this property with caution. If you need
            only _read_ the style consider using <see cref="P:ClosedXML.Excel.XLStylizedBase.StyleValue"/> property instead.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLStylizedBase.Children">
            <summary>
            Get a collection of stylized entities which current entity's style changes should be propagated to.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLStylizedBase.SetStyle(ClosedXML.Excel.XLStyleValue,System.Boolean)">
            <summary>
            Apply specified style to the container.
            </summary>
            <param name="value">Style to apply.</param>
            <param name="propagate">Whether or not propagate the style to inner ranges.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this table.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AppendData(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            Appends the IEnumerable data elements and returns the range of the new rows.
            </summary>
            <param name="data">The IEnumerable data.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The range of the new rows.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AppendData(System.Collections.IEnumerable,System.Boolean,System.Boolean)">
            <summary>
            Appends the IEnumerable data elements and returns the range of the new rows.
            </summary>
            <param name="data">The IEnumerable data.</param>
            <param name="transpose">if set to <c>true</c> the data will be transposed before inserting.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The range of the new rows.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AppendData(System.Data.DataTable,System.Boolean)">
            <summary>
            Appends the data of a data table and returns the range of the new rows.
            </summary>
            <param name="dataTable">The data table.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The range of the new rows.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AppendData``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Appends the IEnumerable data elements and returns the range of the new rows.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="data">The table data.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The range of the new rows.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.ReplaceData(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            Replaces the IEnumerable data elements and returns the table's data range.
            </summary>
            <param name="data">The IEnumerable data.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The table's data range.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.ReplaceData(System.Collections.IEnumerable,System.Boolean,System.Boolean)">
            <summary>
            Replaces the IEnumerable data elements and returns the table's data range.
            </summary>
            <param name="data">The IEnumerable data.</param>
            <param name="transpose">if set to <c>true</c> the data will be transposed before inserting.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The table's data range.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.ReplaceData(System.Data.DataTable,System.Boolean)">
            <summary>
            Replaces the data from the records of a data table and returns the table's data range.
            </summary>
            <param name="dataTable">The data table.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The table's data range.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.ReplaceData``1(System.Collections.Generic.IEnumerable{``0},System.Boolean)">
            <summary>
            Replaces the IEnumerable data elements as a table and the table's data range.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="data">The table data.</param>
            <param name="propagateExtraColumns">if set to <c>true</c> propagate extra columns' values and formulas.</param>
            <returns>
            The table's data range.
            </returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(ClosedXML.Excel.IXLRange)">
            <summary>
            Resizes the table to the specified range address.
            </summary>
            <param name="range">The new table range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(ClosedXML.Excel.IXLRangeAddress)">
            <summary>
            Resizes the table to the specified range address.
            </summary>
            <param name="rangeAddress">The range boundaries.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(System.String)">
            <summary>
            Resizes the table to the specified range address.
            </summary>
            <param name="rangeAddress">The range boundaries.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(ClosedXML.Excel.IXLCell,ClosedXML.Excel.IXLCell)">
            <summary>
            Resizes the table to the specified range.
            </summary>
            <param name="firstCell">The first cell in the range.</param>
            <param name="lastCell">The last cell in the range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(System.String,System.String)">
            <summary>
            Resizes the table to the specified range.
            </summary>
            <param name="firstCellAddress">The first cell address in the worksheet.</param>
            <param name="lastCellAddress">The last cell address in the worksheet.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(ClosedXML.Excel.IXLAddress,ClosedXML.Excel.IXLAddress)">
            <summary>
            Resizes the table to the specified range.
            </summary>
            <param name="firstCellAddress">The first cell address in the worksheet.</param>
            <param name="lastCellAddress">The last cell address in the worksheet.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.Resize(System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Resizes the table to the specified range.
            </summary>
            <param name="firstCellRow">The first cell's row of the range to return.</param>
            <param name="firstCellColumn">The first cell's column of the range to return.</param>
            <param name="lastCellRow">The last cell's row of the range to return.</param>
            <param name="lastCellColumn">The last cell's column of the range to return.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AsDynamicEnumerable">
            <summary>
            Converts the table to an enumerable of dynamic objects
            </summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTable.AsNativeDataTable">
            <summary>
            Converts the table to a standard .NET System.Data.DataTable
            </summary>
            <returns></returns>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.Column">
            <summary>
            Gets the corresponding column for this table field.
            Includes the header and footer cells
            </summary>
            <value>
            The column.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.DataCells">
            <summary>
            Gets the collection of data cells for this field
            Excludes the header and footer cells
            </summary>
            <value>
            The data cells
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.TotalsCell">
            <summary>
            Gets the footer cell for the table field.
            </summary>
            <value>
            The footer cell.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.HeaderCell">
            <summary>
            Gets the header cell for the table field.
            </summary>
            <value>
            The header cell.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.Index">
            <summary>
            Gets the index of the column (0-based).
            </summary>
            <value>
            The index.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.Name">
            <summary>
            Gets or sets the name/header of this table field.
            The corresponding header cell's value will change if you set this.
            </summary>
            <value>
            The name.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.Table">
            <summary>
            Gets the underlying table for this table field.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.TotalsRowFormulaA1">
            <summary>
            Gets or sets the totals row formula in A1 format.
            </summary>
            <value>
            The totals row formula a1.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.TotalsRowFormulaR1C1">
            <summary>
            Gets or sets the totals row formula in R1C1 format.
            </summary>
            <value>
            The totals row formula r1 c1.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.TotalsRowFunction">
            <summary>
            Gets or sets the totals row function.
            </summary>
            <value>
            The totals row function.
            </value>
        </member>
        <member name="P:ClosedXML.Excel.IXLTableField.TotalsRowLabel">
            <summary>
            Gets or sets the totals row label.
            </summary>
            <value>
            The totals row label.
            </value>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableField.Delete">
            <summary>
            Deletes this table field from the table.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableField.IsConsistentDataType">
            <summary>
            Determines whether all cells this table field have a consistent data type.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableField.IsConsistentFormula">
            <summary>
            Determines whether all cells this table field have a consistent formula.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableField.IsConsistentStyle">
            <summary>
            Determines whether all cells this table field have a consistent style.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRange.Row(System.Int32)">
            <summary>
            Rows the specified row.
            </summary>
            <param name="row">1-based row number relative to the first row of this range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRange.Rows(System.Int32,System.Int32)">
            <summary>
            Returns a subset of the rows
            </summary>
            <param name="firstRow">The first row to return. 1-based row number relative to the first row of this range.</param>
            <param name="lastRow">The last row to return. 1-based row number relative to the first row of this range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRow.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of this row.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRows.Add(ClosedXML.Excel.IXLTableRow)">
            <summary>
            Adds a table row to this group.
            </summary>
            <param name="tableRow">The row table to add.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRows.Cells">
            <summary>
            Returns the collection of cells.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRows.CellsUsed">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRows.CellsUsed(System.Boolean)">
            <summary>
            Returns the collection of cells that have a value.
            </summary>
            <param name="includeFormats">if set to <c>true</c> will return all cells with a value or a style different than the default.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTableRows.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these rows.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.IXLTables.Clear(ClosedXML.Excel.XLClearOptions)">
            <summary>
            Clears the contents of these tables.
            </summary>
            <param name="clearOptions">Specify what you want to clear.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLTable.#ctor(ClosedXML.Excel.XLRangeParameters)">
            <summary>
            The direct contructor should only be used in <see cref="P:ClosedXML.Excel.XLWorksheet.RangeFactory"/>.
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLCellSetValueBehavior.Smart">
            <summary>
              Analyze input string and convert value. For avoid analyzing use escape symbol '
            </summary>
        </member>
        <member name="F:ClosedXML.Excel.XLCellSetValueBehavior.Simple">
            <summary>
              Direct set value. If value has unsupported type - value will be stored as string returned by <see
               cref = "M:System.Object.ToString" />
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.CellSetValueBehavior">
            <summary>
              Behavior for <see cref = "M:ClosedXML.Excel.IXLCell.set_Value(System.Object)" />
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.RecalculationCounter">
            <summary>
            Counter increasing at workbook data change. Serves to determine if the cell formula
            has to be recalculated.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.InvalidateFormulas">
            <summary>
            Notify that workbook data has been changed which means that cached formula values
            need to be re-evaluated.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.Worksheets">
            <summary>
              Gets an object to manipulate the worksheets.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.NamedRanges">
            <summary>
              Gets an object to manipulate this workbook's named ranges.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.Theme">
            <summary>
              Gets an object to manipulate this workbook's theme.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.Style">
            <summary>
              Gets or sets the default style for the workbook.
              <para>All new worksheets will use this style.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.RowHeight">
            <summary>
              Gets or sets the default row height for the workbook.
              <para>All new worksheets will use this row height.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.ColumnWidth">
            <summary>
              Gets or sets the default column width for the workbook.
              <para>All new worksheets will use this column width.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.PageOptions">
            <summary>
              Gets or sets the default page options for the workbook.
              <para>All new worksheets will use these page options.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.Outline">
            <summary>
              Gets or sets the default outline options for the workbook.
              <para>All new worksheets will use these outline options.</para>
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.Properties">
            <summary>
              Gets or sets the workbook's properties.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.CalculateMode">
            <summary>
              Gets or sets the workbook's calculation mode.
            </summary>
        </member>
        <member name="P:ClosedXML.Excel.XLWorkbook.ReferenceStyle">
            <summary>
              Gets or sets the workbook's reference style.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.Save">
            <summary>
              Saves the current workbook.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.Save(System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook and optionally performs validation
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.SaveAs(System.String)">
            <summary>
              Saves the current workbook to a file.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.SaveAs(System.String,System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook to a file and optionally validates it.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.SaveAs(System.IO.Stream)">
            <summary>
              Saves the current workbook to a stream.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.SaveAs(System.IO.Stream,System.Boolean,System.Boolean)">
            <summary>
              Saves the current workbook to a stream and optionally validates it.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.Search(System.String,System.Globalization.CompareOptions,System.Boolean)">
            <summary>
            Searches the cells' contents for a given piece of text
            </summary>
            <param name="searchText">The search text.</param>
            <param name="compareOptions">The compare options.</param>
            <param name="searchFormulae">if set to <c>true</c> search formulae instead of cell values.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.#ctor">
            <summary>
              Creates a new Excel workbook.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.#ctor(System.String)">
            <summary>
              Opens an existing workbook from a file.
            </summary>
            <param name = "file">The file to open.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.#ctor(System.IO.Stream)">
            <summary>
              Opens an existing workbook from a stream.
            </summary>
            <param name = "stream">The stream to open.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.RecalculateAllFormulas">
            <summary>
            Force recalculation of all cell formulas.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.ParseCellValue(DocumentFormat.OpenXml.Spreadsheet.RstType,ClosedXML.Excel.XLCell)">
            <summary>
            Parses the cell value for normal or rich text
            Input element should either be a shared string or inline string
            </summary>
            <param name="element">The element (either a shared string or inline string)</param>
            <param name="xlCell">The cell.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.LoadConditionalFormatting(DocumentFormat.OpenXml.Spreadsheet.ConditionalFormatting,ClosedXML.Excel.XLWorksheet,System.Collections.Generic.Dictionary{System.Int32,DocumentFormat.OpenXml.Spreadsheet.DifferentialFormat})">
            <summary>
            Loads the conditional formatting.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.AddDifferentialFormats(DocumentFormat.OpenXml.Packaging.WorkbookStylesPart,ClosedXML.Excel.XLWorkbook.SaveContext)">
            <summary>
            Populates the differential formats that are currently in the file to the SaveContext
            </summary>
            <param name="workbookStylesPart">The workbook styles part.</param>
            <param name="context">The context.</param>
        </member>
        <member name="M:ClosedXML.Excel.XLWorkbook.CellFormatsAreEqual(DocumentFormat.OpenXml.Spreadsheet.CellFormat,ClosedXML.Excel.XLWorkbook.StyleInfo,System.Boolean)">
            <summary>
            Check if two style are equivalent.
            </summary>
            <param name="f">Style in the OpenXML format.</param>
            <param name="styleInfo">Style in the ClosedXML format.</param>
            <param name="compareAlignment">Flag specifying whether or not compare the alignments of two styles.
            Styles in x:cellStyleXfs section do not include alignment so we don't have to compare it in this case.
            Styles in x:cellXfs section, on the opposite, do include alignments, and we must compare them.
            </param>
            <returns>True if two formats are equivalent, false otherwise.</returns>
        </member>
        <member name="F:ClosedXML.Excel.XLWorksheet.InvalidAddress">
            <summary>
            Fake address to be used everywhere the invalid address is needed.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorksheet.RecalculateAllFormulas">
            <summary>
            Force recalculation of all cell formulas.
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLWorksheet.GetCellValue(System.Int32,System.Int32)">
            <summary>
            Get a cell value not initializing it if it has not been initialized yet.
            </summary>
            <param name="ro">Row number</param>
            <param name="co">Column number</param>
            <returns>Current value of the specified cell. Empty string for non-initialized cells.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLWorksheet.RangeRow(ClosedXML.Excel.XLRangeAddress,ClosedXML.Excel.IXLStyle)">
            <summary>
            Get a range row from the shared repository or create a new one.
            </summary>
            <param name="address">Address of range row.</param>
            <param name="defaultStyle">Style to apply. If null the worksheet's style is applied.</param>
            <returns>Range row with the specified address.</returns>
        </member>
        <member name="M:ClosedXML.Excel.XLWorksheet.RangeColumn(ClosedXML.Excel.XLRangeAddress,ClosedXML.Excel.IXLStyle)">
            <summary>
            Get a range column from the shared repository or create a new one.
            </summary>
            <param name="address">Address of range column.</param>
            <param name="defaultStyle">Style to apply. If null the worksheet's style is applied.</param>
            <returns>Range column with the specified address.</returns>
        </member>
        <member name="T:ClosedXML.Excel.XLHelper">
            <summary>
            	Common methods
            </summary>
        </member>
        <member name="M:ClosedXML.Excel.XLHelper.GetColumnNumberFromLetter(System.String)">
            <summary>
            Gets the column number of a given column letter.
            </summary>
            <param name="columnLetter"> The column letter to translate into a column number. </param>
        </member>
        <member name="M:ClosedXML.Excel.XLHelper.GetColumnLetterFromNumber(System.Int32,System.Boolean)">
            <summary>
            Gets the column letter of a given column number.
            </summary>
            <param name="columnNumber">The column number to translate into a column letter.</param>
            <param name="trimToAllowed">if set to <c>true</c> the column letter will be restricted to the allowed range.</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Excel.XLHelper.GetTimeSpan(System.Double)">
            <summary>
            A backward compatible version of <see cref="M:System.TimeSpan.FromDays(System.Double)"/> that returns a value
            rounded to milliseconds. In .Net Core 3.0 the behavior has changed and timespan includes microseconds
            as well. As a result, the value 1:12:30 saved on one machine could become 1:12:29.999999 on another.
            </summary>
        </member>
        <member name="T:ClosedXML.PathHelper">
            <summary>
              Help methods for work with files
            </summary>
        </member>
        <member name="F:ClosedXML.PathHelper.ms_checkValidRelativePathRegEx">
            <summary>
              Can check only .\dfdfd\dfdf\dfdf or ..\..\gfhfgh\fghfh
            </summary>
        </member>
        <member name="M:ClosedXML.PathHelper.GetTimeStamp">
            <summary>
              Gets data and time string stamp for file name
            </summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.PathHelper.GetTimeStamp(System.DateTime)">
            <summary>
              Gets data and time string stamp for file name
            </summary>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.PathHelper.SafetyDeleteFile(System.String)">
            <summary>
              Safety delete file(with try block)
            </summary>
            <param name = "fileName">file name</param>
        </member>
        <member name="M:ClosedXML.PathHelper.NormalizeFileName(System.String)">
            <summary>
              Replace all not implemented symbols to '_'
            </summary>
            <param name = "fileName">input filename</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.PathHelper.ValidatePath(System.String,ClosedXML.PathHelper.PathTypes)">
            <summary>
              ValidatePath file or diretory path
            </summary>
            <param name = "path"></param>
            <param name = "type">path type</param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.PathHelper.ValidateFileName(System.String)">
            <summary>
              ValidatePath file or diretory path
            </summary>
            <param name = "fileName"></param>
            <returns></returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.FromClosedXMLColor``1(DocumentFormat.OpenXml.Spreadsheet.ColorType,ClosedXML.Excel.XLColor,System.Boolean)">
            <summary>
            Convert color in ClosedXML representation to specified OpenXML type.
            </summary>
            <typeparam name="T">The descendant of <see cref="T:DocumentFormat.OpenXml.Spreadsheet.ColorType"/>.</typeparam>
            <param name="openXMLColor">The existing instance of ColorType.</param>
            <param name="xlColor">Color in ClosedXML format.</param>
            <param name="isDifferential">Flag specifiying that the color should be saved in
            differential format (affects the transparent color processing).</param>
            <returns>The original color in OpenXML format.</returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.FromClosedXMLColor``1(DocumentFormat.OpenXml.Office2010.Excel.ColorType,ClosedXML.Excel.XLColor,System.Boolean)">
            <summary>
            Convert color in ClosedXML representation to specified OpenXML type.
            </summary>
            <typeparam name="T">The descendant of <see cref="T:DocumentFormat.OpenXml.Office2010.Excel.ColorType"/>.</typeparam>
            <param name="openXMLColor">The existing instance of ColorType.</param>
            <param name="xlColor">Color in ClosedXML format.</param>
            <param name="isDifferential">Flag specifiying that the color should be saved in
            differential format (affects the transparent color processing).</param>
            <returns>The original color in OpenXML format.</returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.ToClosedXMLColor(DocumentFormat.OpenXml.Spreadsheet.ColorType,System.Collections.Generic.IDictionary{System.String,System.Drawing.Color})">
            <summary>
            Convert color in OpenXML representation to ClosedXML type.
            </summary>
            <param name="openXMLColor">Color in OpenXML format.</param>
            <param name="colorCache">The dictionary containing parsed colors to optimize performance.</param>
            <returns>The color in ClosedXML format.</returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.ToClosedXMLColor(DocumentFormat.OpenXml.Office2010.Excel.ColorType,System.Collections.Generic.IDictionary{System.String,System.Drawing.Color})">
            <summary>
            Convert color in OpenXML representation to ClosedXML type.
            </summary>
            <param name="openXMLColor">Color in OpenXML format.</param>
            <param name="colorCache">The dictionary containing parsed colors to optimize performance.</param>
            <returns>The color in ClosedXML format.</returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.ConvertToClosedXMLColor(ClosedXML.Utils.IColorTypeAdapter,System.Collections.Generic.IDictionary{System.String,System.Drawing.Color})">
            <summary>
            Here we perform the actual convertion from OpenXML color to ClosedXML color.
            </summary>
            <param name="openXMLColor">OpenXML color. Must be either <see cref="T:DocumentFormat.OpenXml.Spreadsheet.ColorType"/> or <see cref="T:DocumentFormat.OpenXml.Office2010.Excel.ColorType"/>. 
            Since these types do not implement a common interface we use dynamic.</param>
            <param name="colorCache">The dictionary containing parsed colors to optimize performance.</param>
            <returns>The color in ClosedXML format.</returns>
        </member>
        <member name="M:ClosedXML.Utils.OpenXmlHelper.FillFromClosedXMLColor(ClosedXML.Utils.IColorTypeAdapter,ClosedXML.Excel.XLColor,System.Boolean)">
            <summary>
            Initialize properties of the existing instance of the color in OpenXML format basing on properties of the color 
            in ClosedXML format.
            </summary>
            <param name="openXMLColor">OpenXML color. Must be either <see cref="T:DocumentFormat.OpenXml.Spreadsheet.ColorType"/> or <see cref="T:DocumentFormat.OpenXml.Office2010.Excel.ColorType"/>. 
            Since these types do not implement a common interface we use dynamic.</param>
            <param name="xlColor">Color in ClosedXML format.</param>
            <param name="isDifferential">Flag specifiying that the color should be saved in
            differential format (affects the transparent color processing).</param>
        </member>
    </members>
</doc>
