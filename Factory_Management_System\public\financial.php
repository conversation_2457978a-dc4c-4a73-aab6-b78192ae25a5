<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Financial.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$financial = new Financial();
$db = new Database();

// معالجة الفلاتر
$period = $_GET['period'] ?? 'monthly';
$currency = $_GET['currency'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-t');

try {
    // مؤشرات الأداء المالي
    $kpis = $financial->getKPIs($date_from, $date_to, $currency ?: null);
    
    // الأرباح حسب الفترة
    if ($period == 'daily') {
        $profits = $financial->getDailyProfits($date_from, $currency ?: null);
    } elseif ($period == 'yearly') {
        $profits = $financial->getYearlyProfits(date('Y', strtotime($date_from)), $currency ?: null);
    } else {
        $profits = $financial->getMonthlyProfits(date('Y', strtotime($date_from)), date('m', strtotime($date_from)), $currency ?: null);
    }
    
    // تحليل المصروفات
    $expenseAnalysis = $financial->getExpenseAnalysis($date_from, $date_to, $currency ?: null);
    
    // تقرير التدفق النقدي
    $cashFlow = $financial->getCashFlowReport($date_from, $date_to);
    
    // العملات المتاحة
    $currencies = $db->fetchAll("SELECT * FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");
    
} catch (Exception $e) {
    $error = "خطأ في جلب البيانات المالية: " . $e->getMessage();
    $kpis = ['total_sales' => 0, 'total_purchases' => 0, 'total_expenses' => 0, 'gross_profit' => 0, 'net_profit' => 0, 'gross_profit_margin' => 0, 'net_profit_margin' => 0];
    $profits = [];
    $expenseAnalysis = [];
    $cashFlow = [];
    $currencies = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المالية - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .kpi-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .kpi-card.sales { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .kpi-card.purchases { background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%); }
        .kpi-card.expenses { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .kpi-card.profit { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .chart-container { position: relative; height: 300px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <br><a href="setup.php" class="btn btn-sm btn-primary mt-2">إعداد قاعدة البيانات</a>
            </div>
        <?php endif; ?>

        <!-- فلاتر التقارير -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>التقارير المالية</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label class="form-label">الفترة</label>
                        <select class="form-select" name="period">
                            <option value="daily" <?= $period == 'daily' ? 'selected' : '' ?>>يومي</option>
                            <option value="monthly" <?= $period == 'monthly' ? 'selected' : '' ?>>شهري</option>
                            <option value="yearly" <?= $period == 'yearly' ? 'selected' : '' ?>>سنوي</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">العملة</label>
                        <select class="form-select" name="currency">
                            <option value="">جميع العملات</option>
                            <?php foreach ($currencies as $curr): ?>
                                <option value="<?= $curr['id'] ?>" <?= $currency == $curr['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($curr['name']) ?> (<?= htmlspecialchars($curr['symbol']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block">
                            <i class="fas fa-search"></i> تحديث
                        </button>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="dropdown d-block">
                            <button class="btn btn-success dropdown-toggle w-100" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-download"></i> تصدير
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="export_financial.php?format=pdf&<?= http_build_query($_GET) ?>">PDF</a></li>
                                <li><a class="dropdown-item" href="export_financial.php?format=excel&<?= http_build_query($_GET) ?>">Excel</a></li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- مؤشرات الأداء المالي -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="card kpi-card sales">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المبيعات</h6>
                                <h3 class="mb-0"><?= formatMoney($kpis['total_sales']) ?></h3>
                            </div>
                            <div>
                                <i class="fas fa-chart-line fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card kpi-card purchases">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المشتريات</h6>
                                <h3 class="mb-0"><?= formatMoney($kpis['total_purchases']) ?></h3>
                            </div>
                            <div>
                                <i class="fas fa-truck fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card kpi-card expenses">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">إجمالي المصروفات</h6>
                                <h3 class="mb-0"><?= formatMoney($kpis['total_expenses']) ?></h3>
                            </div>
                            <div>
                                <i class="fas fa-credit-card fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card kpi-card profit">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title">صافي الربح</h6>
                                <h3 class="mb-0"><?= formatMoney($kpis['net_profit']) ?></h3>
                                <small>هامش الربح: <?= number_format($kpis['net_profit_margin'], 1) ?>%</small>
                            </div>
                            <div>
                                <i class="fas fa-coins fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تحليل الأرباح -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>تحليل الأرباح - <?= ucfirst($period) ?></h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="profitChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- هوامش الربح -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-percentage me-2"></i>هوامش الربح</h6>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="marginChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- تحليل المصروفات -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i>تحليل المصروفات</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($expenseAnalysis)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-chart-pie fa-3x mb-3"></i><br>
                                لا توجد مصروفات في الفترة المحددة
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>القسم</th>
                                            <th>المبلغ</th>
                                            <th>العدد</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($expenseAnalysis as $expense): ?>
                                            <tr>
                                                <td><?= htmlspecialchars($expense['category']) ?></td>
                                                <td><?= htmlspecialchars($expense['department'] ?? '-') ?></td>
                                                <td><?= formatMoney($expense['total_amount'], $expense['currency_symbol'] ?? 'IQD') ?></td>
                                                <td><?= $expense['expenses_count'] ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- التدفق النقدي -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>التدفق النقدي</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($cashFlow)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-exchange-alt fa-3x mb-3"></i><br>
                                لا توجد حركات نقدية في الفترة المحددة
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الصندوق</th>
                                            <th>الوارد</th>
                                            <th>الصادر</th>
                                            <th>صافي التدفق</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach (array_slice($cashFlow, 0, 10) as $flow): ?>
                                            <tr>
                                                <td><?= formatDate($flow['transaction_date']) ?></td>
                                                <td><?= htmlspecialchars($flow['cash_box_name']) ?></td>
                                                <td class="text-success"><?= formatMoney($flow['total_income'], $flow['currency_symbol'] ?? 'IQD') ?></td>
                                                <td class="text-danger"><?= formatMoney($flow['total_expense'], $flow['currency_symbol'] ?? 'IQD') ?></td>
                                                <td class="<?= $flow['net_flow'] >= 0 ? 'text-success' : 'text-danger' ?>">
                                                    <?= formatMoney($flow['net_flow'], $flow['currency_symbol'] ?? 'IQD') ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php if (count($cashFlow) > 10): ?>
                                <div class="text-center">
                                    <a href="cash_flow_report.php?<?= http_build_query($_GET) ?>" class="btn btn-sm btn-outline-primary">
                                        عرض التقرير الكامل
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- روابط سريعة -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-link me-2"></i>روابط سريعة</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="expenses.php" class="btn btn-outline-primary w-100 mb-2">
                                    <i class="fas fa-credit-card me-2"></i>إدارة المصروفات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="cash_boxes.php" class="btn btn-outline-success w-100 mb-2">
                                    <i class="fas fa-wallet me-2"></i>إدارة الصناديق
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="profit_analysis.php" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-chart-line me-2"></i>تحليل الأرباح التفصيلي
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="financial_reports.php" class="btn btn-outline-warning w-100 mb-2">
                                    <i class="fas fa-file-alt me-2"></i>التقارير المالية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني للأرباح
        const profitCtx = document.getElementById('profitChart').getContext('2d');
        new Chart(profitCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'إجمالي المبيعات',
                    data: [<?= $kpis['total_sales'] ?>, 0, 0, 0, 0, 0],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }, {
                    label: 'صافي الربح',
                    data: [<?= $kpis['net_profit'] ?>, 0, 0, 0, 0, 0],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // رسم بياني لهوامش الربح
        const marginCtx = document.getElementById('marginChart').getContext('2d');
        new Chart(marginCtx, {
            type: 'doughnut',
            data: {
                labels: ['هامش الربح الإجمالي', 'هامش الربح الصافي'],
                datasets: [{
                    data: [<?= $kpis['gross_profit_margin'] ?>, <?= $kpis['net_profit_margin'] ?>],
                    backgroundColor: ['#36A2EB', '#FF6384']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    </script>
</body>
</html>
