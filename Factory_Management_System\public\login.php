<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

$auth = new Auth();
$error = '';
$success = '';

// إذا كان المستخدم مسجل دخول بالفعل، توجيهه للوحة التحكم
if ($auth->isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = Helper::cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);

    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        $result = $auth->login($username, $password);

        if ($result['success']) {
            // إعداد تذكر المستخدم
            if ($remember) {
                setcookie('remember_user', $username, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
            }

            header('Location: dashboard.php');
            exit;
        } else {
            $error = $result['message'];
        }
    }
}

// استرجاع اسم المستخدم المحفوظ
$rememberedUser = $_COOKIE['remember_user'] ?? '';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة المعامل</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            margin: 20px;
        }

        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .login-right {
            padding: 60px 40px;
        }

        .login-logo {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .login-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            text-align: right;
        }

        .feature-list li {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-list i {
            margin-left: 10px;
            font-size: 1.2rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: bold;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }

        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }

        .version-info {
            position: absolute;
            bottom: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .login-left {
                display: none;
            }

            .login-right {
                padding: 40px 30px;
            }

            .login-container {
                margin: 10px;
            }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 10%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="row g-0">
            <!-- Left Side - Branding -->
            <div class="col-lg-6">
                <div class="login-left">
                    <div class="login-logo">
                        <i class="fas fa-industry"></i>
                    </div>
                    <h1 class="login-title">نظام إدارة المعامل</h1>
                    <p class="login-subtitle">حلول متكاملة لإدارة المعامل والإنتاج</p>

                    <ul class="feature-list">
                        <li>
                            <i class="fas fa-check-circle"></i>
                            إدارة شاملة للإنتاج والمخزون
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            تتبع المبيعات والمشتريات
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            تقارير مالية تفصيلية
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            دعم العملات المتعددة
                        </li>
                        <li>
                            <i class="fas fa-check-circle"></i>
                            إدارة الموظفين والرواتب
                        </li>
                    </ul>

                    <div class="version-info">
                        الإصدار <?= Config::APP_VERSION ?>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="col-lg-6">
                <div class="login-right">
                    <div class="text-center mb-4">
                        <h2 class="mb-2">مرحباً بك</h2>
                        <p class="text-muted">يرجى تسجيل الدخول للمتابعة</p>
                    </div>

                    <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= htmlspecialchars($success) ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                            <div class="input-group">
                                <input type="text"
                                       class="form-control with-icon"
                                       id="username"
                                       name="username"
                                       value="<?= htmlspecialchars($rememberedUser) ?>"
                                       placeholder="أدخل اسم المستخدم"
                                       required>
                                <span class="input-group-text">
                                    <i class="fas fa-user"></i>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <input type="password"
                                       class="form-control with-icon"
                                       id="password"
                                       name="password"
                                       placeholder="أدخل كلمة المرور"
                                       required>
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember" name="remember">
                            <label class="form-check-label" for="remember">
                                تذكرني
                            </label>
                        </div>

                        <button type="submit" class="btn btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </button>
                    </form>

                    <div class="text-center mt-4">
                        <p class="text-muted mb-2">بيانات تجريبية:</p>
                        <small class="text-muted">
                            <strong>المدير:</strong> admin / admin123<br>
                            <strong>المحاسب:</strong> accountant / 123456<br>
                            <strong>المخزن:</strong> warehouse / 123456
                        </small>
                    </div>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="text-muted mb-0">
                            <i class="fas fa-shield-alt me-1"></i>
                            نظام آمن ومحمي
                        </p>
                        <small class="text-muted">
                            جميع البيانات محمية بأحدث تقنيات الأمان
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تركيز على حقل اسم المستخدم عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('username');
            if (usernameField.value === '') {
                usernameField.focus();
            } else {
                document.getElementById('password').focus();
            }
        });

        // إظهار/إخفاء كلمة المرور
        document.addEventListener('DOMContentLoaded', function() {
            const passwordField = document.getElementById('password');
            const toggleButton = document.createElement('button');
            toggleButton.type = 'button';
            toggleButton.className = 'btn btn-outline-secondary';
            toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
            toggleButton.style.position = 'absolute';
            toggleButton.style.left = '10px';
            toggleButton.style.top = '50%';
            toggleButton.style.transform = 'translateY(-50%)';
            toggleButton.style.border = 'none';
            toggleButton.style.background = 'transparent';
            toggleButton.style.zIndex = '10';

            passwordField.parentElement.style.position = 'relative';
            passwordField.parentElement.appendChild(toggleButton);

            toggleButton.addEventListener('click', function() {
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleButton.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    passwordField.type = 'password';
                    toggleButton.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });

        // تأثير الحركة للأشكال
        document.addEventListener('DOMContentLoaded', function() {
            const shapes = document.querySelectorAll('.shape');
            shapes.forEach((shape, index) => {
                shape.style.animationDelay = `${index * 2}s`;
            });
        });
    </script>
</body>
</html>
