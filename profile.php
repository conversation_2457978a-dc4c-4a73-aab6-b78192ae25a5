<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $validation_rules = [
            'full_name' => ['required' => true, 'message' => 'الاسم الكامل مطلوب'],
            'email' => ['required' => true, 'type' => 'email', 'message' => 'البريد الإلكتروني مطلوب']
        ];
        
        $errors = validateInput($_POST, $validation_rules);
        
        if (empty($errors)) {
            try {
                $stmt = $pdo->prepare("UPDATE users SET full_name = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                $result = $stmt->execute([
                    trim($_POST['full_name']),
                    trim($_POST['email']),
                    $_SESSION['user_id']
                ]);
                
                if ($result) {
                    $_SESSION['full_name'] = trim($_POST['full_name']);
                    $message = 'تم تحديث الملف الشخصي بنجاح';
                    $message_type = 'success';
                    logActivity('تحديث الملف الشخصي', 'تم تحديث البيانات الشخصية');
                } else {
                    $message = 'حدث خطأ أثناء تحديث البيانات';
                    $message_type = 'danger';
                }
            } catch (PDOException $e) {
                $message = 'حدث خطأ في النظام';
                $message_type = 'danger';
                error_log("خطأ تحديث الملف الشخصي: " . $e->getMessage());
            }
        } else {
            $message = 'يرجى تصحيح الأخطاء في النموذج';
            $message_type = 'danger';
        }
    }
    
    elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $message = 'جميع حقول كلمة المرور مطلوبة';
            $message_type = 'danger';
        } elseif ($new_password !== $confirm_password) {
            $message = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
            $message_type = 'danger';
        } elseif (strlen($new_password) < 6) {
            $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            $message_type = 'danger';
        } else {
            try {
                // التحقق من كلمة المرور الحالية
                $stmt = $pdo->prepare("SELECT password FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user = $stmt->fetch();
                
                if ($user && password_verify($current_password, $user['password'])) {
                    // تحديث كلمة المرور
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
                    $result = $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                    
                    if ($result) {
                        $message = 'تم تغيير كلمة المرور بنجاح';
                        $message_type = 'success';
                        logActivity('تغيير كلمة المرور', 'تم تغيير كلمة المرور');
                    } else {
                        $message = 'حدث خطأ أثناء تغيير كلمة المرور';
                        $message_type = 'danger';
                    }
                } else {
                    $message = 'كلمة المرور الحالية غير صحيحة';
                    $message_type = 'danger';
                }
            } catch (PDOException $e) {
                $message = 'حدث خطأ في النظام';
                $message_type = 'danger';
                error_log("خطأ تغيير كلمة المرور: " . $e->getMessage());
            }
        }
    }
}

// جلب بيانات المستخدم
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
} catch (PDOException $e) {
    $user = null;
    error_log("خطأ جلب بيانات المستخدم: " . $e->getMessage());
}

// جلب إحصائيات المستخدم
$user_stats = [
    'total_transactions' => 0,
    'total_customers_added' => 0,
    'total_suppliers_added' => 0,
    'last_login' => null
];

try {
    // عدد المعاملات التي أنشأها المستخدم
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM transactions WHERE created_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user_stats['total_transactions'] = $stmt->fetch()['count'];
    
    // عدد العملاء المضافين
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM customers WHERE created_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user_stats['total_customers_added'] = $stmt->fetch()['count'];
    
    // عدد الموردين المضافين
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM suppliers WHERE created_by = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user_stats['total_suppliers_added'] = $stmt->fetch()['count'];
    
    // آخر تسجيل دخول
    $stmt = $pdo->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
    $stmt->execute(['last_login_' . $_SESSION['user_id']]);
    $last_login = $stmt->fetch();
    if ($last_login) {
        $user_stats['last_login'] = $last_login['setting_value'];
    }
} catch (PDOException $e) {
    error_log("خطأ جلب إحصائيات المستخدم: " . $e->getMessage());
}

// جلب إعدادات الشركة للشعار
$company_settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('company_name', 'company_logo')");
    while ($row = $stmt->fetch()) {
        $company_settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (PDOException $e) {
    error_log("خطأ جلب إعدادات الشركة: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--gradient-primary);">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <?php if (!empty($company_settings['company_logo']) && file_exists('assets/uploads/' . $company_settings['company_logo'])): ?>
                    <img src="assets/uploads/<?php echo htmlspecialchars($company_settings['company_logo']); ?>" alt="شعار الشركة" style="height: 40px; margin-left: 10px;">
                <?php else: ?>
                    <i class="fas fa-calculator me-2"></i>
                <?php endif; ?>
                <?php echo htmlspecialchars($company_settings['company_name'] ?? 'نظام إدارة الديون'); ?>
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <div class="avatar-circle me-2">
                            <i class="fas fa-user"></i>
                        </div>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.php">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2 arabic-text">الملف الشخصي</h1>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- معلومات المستخدم -->
                    <div class="col-lg-4">
                        <div class="card shadow-sm mb-4">
                            <div class="card-body text-center">
                                <div class="avatar-large mb-3">
                                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                                </div>
                                <h4 class="arabic-text"><?php echo htmlspecialchars($user['full_name'] ?? 'غير محدد'); ?></h4>
                                <p class="text-muted">@<?php echo htmlspecialchars($user['username'] ?? ''); ?></p>
                                <span class="badge bg-<?php echo $user['role'] === 'admin' ? 'danger' : 'primary'; ?> mb-3">
                                    <?php echo $user['role'] === 'admin' ? 'مدير النظام' : 'مستخدم'; ?>
                                </span>
                                
                                <div class="row text-center mt-4">
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="numbers text-primary"><?php echo $user_stats['total_transactions']; ?></h5>
                                            <small class="text-muted arabic-text">المعاملات</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="numbers text-success"><?php echo $user_stats['total_customers_added']; ?></h5>
                                            <small class="text-muted arabic-text">العملاء</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-item">
                                            <h5 class="numbers text-info"><?php echo $user_stats['total_suppliers_added']; ?></h5>
                                            <small class="text-muted arabic-text">الموردين</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($user_stats['last_login']): ?>
                                <div class="mt-3">
                                    <small class="text-muted arabic-text">
                                        <i class="fas fa-clock me-1"></i>
                                        آخر تسجيل دخول: <?php echo date('Y-m-d H:i', strtotime($user_stats['last_login'])); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- تحديث البيانات -->
                    <div class="col-lg-8">
                        <!-- تحديث المعلومات الشخصية -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-gradient text-white" style="background: var(--gradient-primary) !important;">
                                <h5 class="mb-0 arabic-text">
                                    <i class="fas fa-user-edit me-2"></i>
                                    تحديث المعلومات الشخصية
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="username" class="form-label arabic-text">اسم المستخدم</label>
                                            <input type="text" class="form-control" id="username" 
                                                   value="<?php echo htmlspecialchars($user['username'] ?? ''); ?>" disabled>
                                            <div class="form-text arabic-text">لا يمكن تغيير اسم المستخدم</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="full_name" class="form-label arabic-text">الاسم الكامل *</label>
                                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                                   value="<?php echo htmlspecialchars($user['full_name'] ?? ''); ?>" required>
                                            <div class="invalid-feedback">الاسم الكامل مطلوب</div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label arabic-text">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($user['email'] ?? ''); ?>" required>
                                        <div class="invalid-feedback">البريد الإلكتروني مطلوب وصحيح</div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>
                                            حفظ التغييرات
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- تغيير كلمة المرور -->
                        <div class="card shadow-sm">
                            <div class="card-header bg-gradient text-white" style="background: var(--gradient-warning) !important;">
                                <h5 class="mb-0 arabic-text">
                                    <i class="fas fa-lock me-2"></i>
                                    تغيير كلمة المرور
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="needs-validation" novalidate>
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="mb-3">
                                        <label for="current_password" class="form-label arabic-text">كلمة المرور الحالية *</label>
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <div class="invalid-feedback">كلمة المرور الحالية مطلوبة</div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="new_password" class="form-label arabic-text">كلمة المرور الجديدة *</label>
                                            <input type="password" class="form-control" id="new_password" name="new_password" 
                                                   minlength="6" required>
                                            <div class="invalid-feedback">كلمة المرور يجب أن تكون 6 أحرف على الأقل</div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="confirm_password" class="form-label arabic-text">تأكيد كلمة المرور *</label>
                                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                                   minlength="6" required>
                                            <div class="invalid-feedback">تأكيد كلمة المرور مطلوب</div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key me-1"></i>
                                            تغيير كلمة المرور
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // التحقق من تطابق كلمة المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;
            
            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
