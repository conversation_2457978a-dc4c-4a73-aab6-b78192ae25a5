<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$purchaseId = (int)($_GET['id'] ?? 0);
if (!$purchaseId) {
    header('Location: purchases.php');
    exit;
}

// جلب بيانات فاتورة المشتريات
try {
    $purchase = $db->fetchOne("
        SELECT p.*, s.name as supplier_name, s.phone as supplier_phone, 
               s.address as supplier_address, u.full_name as created_by_name,
               c.symbol as currency_symbol
        FROM purchases p
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN currencies c ON p.currency_id = c.id
        WHERE p.id = ?
    ", [$purchaseId]);
    
    if (!$purchase) {
        header('Location: purchases.php');
        exit;
    }
    
    // جلب تفاصيل الفاتورة
    $items = $db->fetchAll("
        SELECT pd.*, i.name as item_name, i.unit as item_unit, i.code as item_code
        FROM purchase_details pd
        LEFT JOIN items i ON pd.item_id = i.id
        WHERE pd.purchase_id = ?
        ORDER BY pd.id
    ", [$purchaseId]);
    
} catch (Exception $e) {
    header('Location: purchases.php');
    exit;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة فاتورة مشتريات - <?= htmlspecialchars($purchase['invoice_number']) ?></title>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            margin: 20px;
            background: white;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border: 2px solid #333;
            padding: 20px;
        }
        
        .invoice-header {
            text-align: center;
            border-bottom: 3px solid #333;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        
        .company-info {
            margin-bottom: 15px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .company-details {
            font-size: 14px;
            color: #666;
        }
        
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #d32f2f;
            margin: 15px 0;
        }
        
        .invoice-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            background: #f9f9f9;
        }
        
        .info-section {
            flex: 1;
        }
        
        .info-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .info-item {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 10px;
            text-align: center;
        }
        
        .items-table th {
            background: #333;
            color: white;
            font-weight: bold;
        }
        
        .items-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .totals-section {
            float: left;
            width: 300px;
            border: 2px solid #333;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .total-row.final {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 18px;
            color: #d32f2f;
        }
        
        .footer {
            clear: both;
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }
        
        .print-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .print-btn:hover {
            background: #218838;
        }
    </style>
</head>
<body>
    <div class="no-print" style="text-align: center; margin-bottom: 20px;">
        <button onclick="window.print()" class="print-btn">
            <i class="fas fa-print"></i> طباعة الفاتورة
        </button>
        <button onclick="window.close()" class="print-btn" style="background: #dc3545;">
            <i class="fas fa-times"></i> إغلاق
        </button>
    </div>

    <div class="invoice-container">
        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="company-info">
                <div class="company-name">شركة المعامل المتقدمة</div>
                <div class="company-details">
                    العراق - بغداد | هاتف: 07901234567 | البريد الإلكتروني: <EMAIL>
                </div>
            </div>
            <div class="invoice-title">فاتورة مشتريات</div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="invoice-info">
            <div class="info-section">
                <div class="info-title">معلومات الفاتورة</div>
                <div class="info-item"><strong>رقم الفاتورة:</strong> <?= htmlspecialchars($purchase['invoice_number']) ?></div>
                <div class="info-item"><strong>تاريخ الفاتورة:</strong> <?= date('d/m/Y', strtotime($purchase['purchase_date'])) ?></div>
                <div class="info-item"><strong>تاريخ الاستحقاق:</strong> <?= $purchase['due_date'] ? date('d/m/Y', strtotime($purchase['due_date'])) : 'فوري' ?></div>
                <div class="info-item"><strong>أنشأ بواسطة:</strong> <?= htmlspecialchars($purchase['created_by_name']) ?></div>
            </div>
            
            <div class="info-section">
                <div class="info-title">معلومات المورد</div>
                <div class="info-item"><strong>اسم المورد:</strong> <?= htmlspecialchars($purchase['supplier_name']) ?></div>
                <div class="info-item"><strong>الهاتف:</strong> <?= htmlspecialchars($purchase['supplier_phone']) ?></div>
                <div class="info-item"><strong>العنوان:</strong> <?= htmlspecialchars($purchase['supplier_address']) ?></div>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 15%;">كود الصنف</th>
                    <th style="width: 30%;">اسم الصنف</th>
                    <th style="width: 10%;">الوحدة</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">سعر الوحدة</th>
                    <th style="width: 15%;">المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php $counter = 1; ?>
                <?php foreach ($items as $item): ?>
                    <tr>
                        <td><?= $counter++ ?></td>
                        <td><?= htmlspecialchars($item['item_code']) ?></td>
                        <td style="text-align: right;"><?= htmlspecialchars($item['item_name']) ?></td>
                        <td><?= htmlspecialchars($item['item_unit']) ?></td>
                        <td><?= number_format($item['quantity'], 3) ?></td>
                        <td><?= number_format($item['unit_price'], 2) ?></td>
                        <td><?= number_format($item['total_price'], 2) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="total-row">
                <span>المجموع الفرعي:</span>
                <span><?= number_format($purchase['subtotal'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
            </div>
            
            <?php if ($purchase['tax_amount'] > 0): ?>
            <div class="total-row">
                <span>الضريبة (<?= $purchase['tax_rate'] ?>%):</span>
                <span><?= number_format($purchase['tax_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
            </div>
            <?php endif; ?>
            
            <?php if ($purchase['discount_amount'] > 0): ?>
            <div class="total-row">
                <span>الخصم:</span>
                <span><?= number_format($purchase['discount_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
            </div>
            <?php endif; ?>
            
            <div class="total-row final">
                <span>المجموع النهائي:</span>
                <span><?= number_format($purchase['total_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
            </div>
        </div>

        <div style="clear: both;"></div>

        <?php if ($purchase['notes']): ?>
        <div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9;">
            <strong>ملاحظات:</strong><br>
            <?= nl2br(htmlspecialchars($purchase['notes'])) ?>
        </div>
        <?php endif; ?>

        <!-- تذييل الفاتورة -->
        <div class="footer">
            <p>شكراً لتعاملكم معنا</p>
            <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة المعامل - <?= date('d/m/Y H:i') ?></p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
