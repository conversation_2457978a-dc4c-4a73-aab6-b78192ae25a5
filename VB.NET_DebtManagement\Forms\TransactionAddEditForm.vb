Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إضافة/تعديل المعاملة المالية - Transaction Add/Edit Form
''' </summary>
Public Class TransactionAddEditForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User
    Private _transaction As Transaction
    Private _isEditMode As Boolean

    ' عناصر التحكم
    Private _cmbTransactionType As ComboBox
    Private _cmbEntityType As ComboBox
    Private _cmbEntity As ComboBox
    Private _cmbCashBox As ComboBox
    Private _txtAmount As TextBox
    Private _cmbCurrency As ComboBox
    Private _txtExchangeRate As TextBox
    Private _txtDescription As TextBox
    Private _txtReferenceNumber As TextBox
    Private _dtpTransactionDate As DateTimePicker
    Private _cmbStatus As ComboBox
    Private _txtNotes As TextBox
    Private _btnSave As Button
    Private _btnCancel As Button

    ' تسميات الحقول
    Private _lblTransactionType As Label
    Private _lblEntityType As Label
    Private _lblEntity As Label
    Private _lblCashBox As Label
    Private _lblAmount As Label
    Private _lblCurrency As Label
    Private _lblExchangeRate As Label
    Private _lblDescription As Label
    Private _lblReferenceNumber As Label
    Private _lblTransactionDate As Label
    Private _lblStatus As Label
    Private _lblNotes As Label

    ' لوحات التجميع
    Private _panelMain As Panel
    Private _panelButtons As Panel

#End Region

#Region "Constructors"

    ''' <summary>
    ''' منشئ لإضافة معاملة جديدة
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        _transaction = New Transaction()
        _isEditMode = False

        InitializeComponent()
        SetupForm()
        SetupControls()
        LoadComboBoxData()
        Me.Text = "إضافة معاملة مالية جديدة"
    End Sub

    ''' <summary>
    ''' منشئ لتعديل معاملة موجودة
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User, transaction As Transaction)
        _context = context
        _currentUser = currentUser
        _transaction = transaction
        _isEditMode = True

        InitializeComponent()
        SetupForm()
        SetupControls()
        LoadComboBoxData()
        LoadTransactionData()
        Me.Text = $"تعديل المعاملة: {transaction.InvoiceNumber}"
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Size = New Size(600, 800)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.FormBorderStyle = FormBorderStyle.FixedDialog
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.TransactionIcon
    End Sub

    ''' <summary>
    ''' إعداد عناصر التحكم
    ''' </summary>
    Private Sub SetupControls()
        ' اللوحة الرئيسية
        _panelMain = New Panel With {
            .Size = New Size(560, 680),
            .Location = New Point(20, 20),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle,
            .Padding = New Padding(20),
            .AutoScroll = True
        }

        ' إنشاء الحقول
        CreateFormFields()

        ' لوحة الأزرار
        _panelButtons = New Panel With {
            .Size = New Size(560, 60),
            .Location = New Point(20, 720),
            .BackColor = Color.Transparent
        }

        ' أزرار الحفظ والإلغاء
        CreateButtons()

        Me.Controls.AddRange({_panelMain, _panelButtons})
    End Sub

    ''' <summary>
    ''' إنشاء حقول النموذج
    ''' </summary>
    Private Sub CreateFormFields()
        Dim yPosition As Integer = 20
        Dim fieldHeight As Integer = 55

        ' نوع المعاملة
        _lblTransactionType = CreateLabel("نوع المعاملة *:", yPosition)
        _cmbTransactionType = CreateComboBox(yPosition + 25)
        _cmbTransactionType.Items.AddRange({"وارد", "صادر"})
        _cmbTransactionType.SelectedIndex = 0
        yPosition += fieldHeight

        ' نوع الطرف
        _lblEntityType = CreateLabel("نوع الطرف *:", yPosition)
        _cmbEntityType = CreateComboBox(yPosition + 25)
        _cmbEntityType.Items.AddRange({"عميل", "مورد"})
        _cmbEntityType.SelectedIndex = 0
        yPosition += fieldHeight

        ' الطرف
        _lblEntity = CreateLabel("الطرف *:", yPosition)
        _cmbEntity = CreateComboBox(yPosition + 25)
        yPosition += fieldHeight

        ' الصندوق
        _lblCashBox = CreateLabel("الصندوق *:", yPosition)
        _cmbCashBox = CreateComboBox(yPosition + 25)
        yPosition += fieldHeight

        ' المبلغ
        _lblAmount = CreateLabel("المبلغ *:", yPosition)
        _txtAmount = CreateNumericTextBox(yPosition + 25, True)
        yPosition += fieldHeight

        ' العملة
        _lblCurrency = CreateLabel("العملة *:", yPosition)
        _cmbCurrency = CreateComboBox(yPosition + 25)
        _cmbCurrency.Items.AddRange({"IQD", "USD"})
        _cmbCurrency.SelectedIndex = 0
        yPosition += fieldHeight

        ' سعر الصرف
        _lblExchangeRate = CreateLabel("سعر الصرف:", yPosition)
        _txtExchangeRate = CreateNumericTextBox(yPosition + 25)
        _txtExchangeRate.Text = "1"
        yPosition += fieldHeight

        ' الوصف
        _lblDescription = CreateLabel("الوصف:", yPosition)
        _txtDescription = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight

        ' رقم المرجع
        _lblReferenceNumber = CreateLabel("رقم المرجع:", yPosition)
        _txtReferenceNumber = CreateTextBox(yPosition + 25)
        yPosition += fieldHeight

        ' تاريخ المعاملة
        _lblTransactionDate = CreateLabel("تاريخ المعاملة *:", yPosition)
        _dtpTransactionDate = New DateTimePicker With {
            .Size = New Size(500, 25),
            .Location = New Point(20, yPosition + 25),
            .Format = DateTimePickerFormat.Short,
            .Value = DateTime.Today
        }
        yPosition += fieldHeight

        ' الحالة
        _lblStatus = CreateLabel("الحالة:", yPosition)
        _cmbStatus = CreateComboBox(yPosition + 25)
        _cmbStatus.Items.AddRange({"مكتملة", "معلقة", "ملغية"})
        _cmbStatus.SelectedIndex = 0
        yPosition += fieldHeight

        ' الملاحظات
        _lblNotes = CreateLabel("ملاحظات:", yPosition)
        _txtNotes = New TextBox With {
            .Size = New Size(500, 80),
            .Location = New Point(20, yPosition + 25),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .TextAlign = HorizontalAlignment.Right
        }

        ' إضافة جميع العناصر للوحة الرئيسية
        _panelMain.Controls.AddRange({
            _lblTransactionType, _cmbTransactionType,
            _lblEntityType, _cmbEntityType,
            _lblEntity, _cmbEntity,
            _lblCashBox, _cmbCashBox,
            _lblAmount, _txtAmount,
            _lblCurrency, _cmbCurrency,
            _lblExchangeRate, _txtExchangeRate,
            _lblDescription, _txtDescription,
            _lblReferenceNumber, _txtReferenceNumber,
            _lblTransactionDate, _dtpTransactionDate,
            _lblStatus, _cmbStatus,
            _lblNotes, _txtNotes
        })

        ' ربط الأحداث
        AddHandler _cmbEntityType.SelectedIndexChanged, AddressOf CmbEntityType_SelectedIndexChanged
        AddHandler _cmbCurrency.SelectedIndexChanged, AddressOf CmbCurrency_SelectedIndexChanged
    End Sub

    ''' <summary>
    ''' إنشاء تسمية
    ''' </summary>
    Private Function CreateLabel(text As String, y As Integer) As Label
        Return New Label With {
            .Text = text,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .ForeColor = Color.FromArgb(73, 80, 87),
            .Size = New Size(500, 20),
            .Location = New Point(20, y),
            .TextAlign = ContentAlignment.MiddleRight
        }
    End Function

    ''' <summary>
    ''' إنشاء مربع نص
    ''' </summary>
    Private Function CreateTextBox(y As Integer, Optional isRequired As Boolean = False) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Right
        }

        If isRequired Then
            textBox.BackColor = Color.FromArgb(255, 248, 220)
        End If

        Return textBox
    End Function

    ''' <summary>
    ''' إنشاء قائمة منسدلة
    ''' </summary>
    Private Function CreateComboBox(y As Integer) As ComboBox
        Return New ComboBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
    End Function

    ''' <summary>
    ''' إنشاء مربع نص رقمي
    ''' </summary>
    Private Function CreateNumericTextBox(y As Integer, Optional isRequired As Boolean = False) As TextBox
        Dim textBox As New TextBox With {
            .Size = New Size(500, 25),
            .Location = New Point(20, y),
            .Font = New Font("Segoe UI", 10, FontStyle.Regular),
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = HorizontalAlignment.Center,
            .Text = "0"
        }

        If isRequired Then
            textBox.BackColor = Color.FromArgb(255, 248, 220)
        End If

        AddHandler textBox.KeyPress, AddressOf NumericTextBox_KeyPress

        Return textBox
    End Function

    ''' <summary>
    ''' إنشاء الأزرار
    ''' </summary>
    Private Sub CreateButtons()
        _btnSave = New Button With {
            .Text = If(_isEditMode, "حفظ التغييرات", "إضافة المعاملة"),
            .Size = New Size(140, 40),
            .Location = New Point(400, 10),
            .BackColor = Color.FromArgb(40, 167, 69),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnSave.FlatAppearance.BorderSize = 0

        _btnCancel = New Button With {
            .Text = "إلغاء",
            .Size = New Size(100, 40),
            .Location = New Point(290, 10),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Cursor = Cursors.Hand
        }
        _btnCancel.FlatAppearance.BorderSize = 0

        ' ربط الأحداث
        AddHandler _btnSave.Click, AddressOf BtnSave_Click
        AddHandler _btnCancel.Click, AddressOf BtnCancel_Click

        ' تأثيرات الماوس
        AddHandler _btnSave.MouseEnter, Sub() _btnSave.BackColor = Color.FromArgb(33, 136, 56)
        AddHandler _btnSave.MouseLeave, Sub() _btnSave.BackColor = Color.FromArgb(40, 167, 69)
        AddHandler _btnCancel.MouseEnter, Sub() _btnCancel.BackColor = Color.FromArgb(90, 98, 104)
        AddHandler _btnCancel.MouseLeave, Sub() _btnCancel.BackColor = Color.FromArgb(108, 117, 125)

        _panelButtons.Controls.AddRange({_btnSave, _btnCancel})
    End Sub

#End Region

#Region "Data Methods"

    ''' <summary>
    ''' تحميل بيانات القوائم المنسدلة
    ''' </summary>
    Private Sub LoadComboBoxData()
        ' تحميل الصناديق
        LoadCashBoxes()

        ' تحميل الأطراف حسب النوع المحدد
        LoadEntities()
    End Sub

    ''' <summary>
    ''' تحميل الصناديق
    ''' </summary>
    Private Sub LoadCashBoxes()
        Try
            _cmbCashBox.Items.Clear()

            Dim cashBoxes = _context.CashBoxes.Where(Function(cb) cb.IsActive).OrderBy(Function(cb) cb.Name).ToList()

            For Each cashBox In cashBoxes
                _cmbCashBox.Items.Add(New ComboBoxItem With {.Text = cashBox.Name, .Value = cashBox.Id})
            Next

            If _cmbCashBox.Items.Count > 0 Then
                _cmbCashBox.SelectedIndex = 0
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل الصناديق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحميل الأطراف (العملاء أو الموردين)
    ''' </summary>
    Private Sub LoadEntities()
        Try
            _cmbEntity.Items.Clear()

            If _cmbEntityType.Text = "عميل" Then
                Dim customers = _context.Customers.Where(Function(c) c.IsActive).OrderBy(Function(c) c.Name).ToList()
                For Each customer In customers
                    _cmbEntity.Items.Add(New ComboBoxItem With {.Text = customer.Name, .Value = customer.Id})
                Next
            Else
                Dim suppliers = _context.Suppliers.Where(Function(s) s.IsActive).OrderBy(Function(s) s.Name).ToList()
                For Each supplier In suppliers
                    _cmbEntity.Items.Add(New ComboBoxItem With {.Text = supplier.Name, .Value = supplier.Id})
                Next
            End If

            If _cmbEntity.Items.Count > 0 Then
                _cmbEntity.SelectedIndex = 0
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل الأطراف: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' تحميل بيانات المعاملة للتعديل
    ''' </summary>
    Private Sub LoadTransactionData()
        If _transaction IsNot Nothing Then
            _cmbTransactionType.Text = _transaction.TypeInArabic
            _cmbEntityType.Text = _transaction.EntityTypeInArabic

            ' تحميل الأطراف أولاً
            LoadEntities()

            ' تحديد الطرف
            For i As Integer = 0 To _cmbEntity.Items.Count - 1
                Dim item = DirectCast(_cmbEntity.Items(i), ComboBoxItem)
                If item.Value = _transaction.EntityId Then
                    _cmbEntity.SelectedIndex = i
                    Exit For
                End If
            Next

            ' تحديد الصندوق
            For i As Integer = 0 To _cmbCashBox.Items.Count - 1
                Dim item = DirectCast(_cmbCashBox.Items(i), ComboBoxItem)
                If item.Value = _transaction.CashBoxId Then
                    _cmbCashBox.SelectedIndex = i
                    Exit For
                End If
            Next

            _txtAmount.Text = _transaction.Amount.ToString("F2")
            _cmbCurrency.Text = _transaction.Currency
            _txtExchangeRate.Text = _transaction.ExchangeRate.ToString("F4")
            _txtDescription.Text = _transaction.Description
            _txtReferenceNumber.Text = _transaction.ReferenceNumber
            _dtpTransactionDate.Value = _transaction.TransactionDate
            _cmbStatus.Text = _transaction.StatusInArabic
            _txtNotes.Text = _transaction.Notes
        End If
    End Sub

    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    Private Function ValidateData() As Boolean
        Dim errors As New List(Of String)

        If _cmbEntity.SelectedItem Is Nothing Then
            errors.Add("يجب اختيار الطرف")
        End If

        If _cmbCashBox.SelectedItem Is Nothing Then
            errors.Add("يجب اختيار الصندوق")
        End If

        Dim amount As Decimal
        If Not Decimal.TryParse(_txtAmount.Text, amount) OrElse amount <= 0 Then
            errors.Add("المبلغ يجب أن يكون أكبر من صفر")
        End If

        Dim exchangeRate As Decimal
        If Not Decimal.TryParse(_txtExchangeRate.Text, exchangeRate) OrElse exchangeRate <= 0 Then
            errors.Add("سعر الصرف يجب أن يكون أكبر من صفر")
        End If

        If errors.Count > 0 Then
            MessageBox.Show(String.Join(Environment.NewLine, errors), "أخطاء في البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return False
        End If

        Return True
    End Function

    ''' <summary>
    ''' حفظ بيانات المعاملة
    ''' </summary>
    Private Function SaveTransaction() As Boolean
        Try
            ' تحديث بيانات المعاملة
            _transaction.Type = If(_cmbTransactionType.Text = "وارد", "Income", "Expense")
            _transaction.EntityType = If(_cmbEntityType.Text = "عميل", "Customer", "Supplier")
            _transaction.EntityId = DirectCast(_cmbEntity.SelectedItem, ComboBoxItem).Value
            _transaction.CashBoxId = DirectCast(_cmbCashBox.SelectedItem, ComboBoxItem).Value
            _transaction.Amount = Decimal.Parse(_txtAmount.Text)
            _transaction.Currency = _cmbCurrency.Text
            _transaction.ExchangeRate = Decimal.Parse(_txtExchangeRate.Text)
            _transaction.Description = If(String.IsNullOrWhiteSpace(_txtDescription.Text), Nothing, _txtDescription.Text.Trim())
            _transaction.ReferenceNumber = If(String.IsNullOrWhiteSpace(_txtReferenceNumber.Text), Nothing, _txtReferenceNumber.Text.Trim())
            _transaction.TransactionDate = _dtpTransactionDate.Value.Date
            _transaction.Status = GetStatusFromArabic(_cmbStatus.Text)
            _transaction.Notes = If(String.IsNullOrWhiteSpace(_txtNotes.Text), Nothing, _txtNotes.Text.Trim())

            If _isEditMode Then
                ' تحديث المعاملة الموجودة
                _transaction.UpdatedAt = DateTime.Now
                _transaction.UpdatedBy = _currentUser.Id
            Else
                ' إضافة معاملة جديدة
                _transaction.InvoiceNumber = GenerateInvoiceNumber()
                _transaction.CreatedAt = DateTime.Now
                _transaction.CreatedBy = _currentUser.Id

                _context.Transactions.Add(_transaction)
            End If

            _context.SaveChanges()
            Return True

        Catch ex As Exception
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Return False
        End Try
    End Function

    ''' <summary>
    ''' توليد رقم فاتورة جديد
    ''' </summary>
    Private Function GenerateInvoiceNumber() As String
        Dim today = DateTime.Today
        Dim prefix = $"INV-{today:yyyyMMdd}-"

        Dim lastInvoice = _context.Transactions _
            .Where(Function(t) t.InvoiceNumber.StartsWith(prefix)) _
            .OrderByDescending(Function(t) t.InvoiceNumber) _
            .FirstOrDefault()

        Dim sequence As Integer = 1
        If lastInvoice IsNot Nothing Then
            Dim lastSequence = lastInvoice.InvoiceNumber.Substring(prefix.Length)
            Integer.TryParse(lastSequence, sequence)
            sequence += 1
        End If

        Return prefix & sequence.ToString("D4")
    End Function

    ''' <summary>
    ''' تحويل الحالة من العربية إلى الإنجليزية
    ''' </summary>
    Private Function GetStatusFromArabic(arabicStatus As String) As String
        Select Case arabicStatus
            Case "مكتملة"
                Return "Completed"
            Case "معلقة"
                Return "Pending"
            Case "ملغية"
                Return "Cancelled"
            Case Else
                Return "Completed"
        End Select
    End Function

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' تغيير نوع الطرف
    ''' </summary>
    Private Sub CmbEntityType_SelectedIndexChanged(sender As Object, e As EventArgs)
        LoadEntities()
    End Sub

    ''' <summary>
    ''' تغيير العملة
    ''' </summary>
    Private Sub CmbCurrency_SelectedIndexChanged(sender As Object, e As EventArgs)
        ' تحديث سعر الصرف تلقائياً
        If _cmbCurrency.Text = "USD" Then
            Try
                Dim usdRate = _context.SystemSettings.FirstOrDefault(Function(s) s.SettingKey = "ExchangeRateUSDToIQD")
                If usdRate IsNot Nothing Then
                    _txtExchangeRate.Text = usdRate.GetDecimalValue(1320).ToString("F4")
                End If
            Catch
                _txtExchangeRate.Text = "1320.0000"
            End Try
        Else
            _txtExchangeRate.Text = "1.0000"
        End If
    End Sub

    ''' <summary>
    ''' حفظ البيانات
    ''' </summary>
    Private Sub BtnSave_Click(sender As Object, e As EventArgs)
        If ValidateData() Then
            If SaveTransaction() Then
                Me.DialogResult = DialogResult.OK
                Me.Close()
            End If
        End If
    End Sub

    ''' <summary>
    ''' إلغاء العملية
    ''' </summary>
    Private Sub BtnCancel_Click(sender As Object, e As EventArgs)
        Me.DialogResult = DialogResult.Cancel
        Me.Close()
    End Sub

    ''' <summary>
    ''' التحقق من الأرقام فقط
    ''' </summary>
    Private Sub NumericTextBox_KeyPress(sender As Object, e As KeyPressEventArgs)
        If Not Char.IsDigit(e.KeyChar) AndAlso e.KeyChar <> "."c AndAlso Not Char.IsControl(e.KeyChar) Then
            e.Handled = True
        End If

        Dim textBox = DirectCast(sender, TextBox)
        If e.KeyChar = "."c AndAlso textBox.Text.Contains(".") Then
            e.Handled = True
        End If
    End Sub

    ''' <summary>
    ''' التحقق عند إغلاق النموذج
    ''' </summary>
    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        If Me.DialogResult = DialogResult.None Then
            If MessageBox.Show("هل تريد إغلاق النموذج بدون حفظ؟", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.No Then
                e.Cancel = True
                Return
            End If
        End If

        MyBase.OnFormClosing(e)
    End Sub

#End Region

End Class

''' <summary>
''' فئة مساعدة لعناصر القائمة المنسدلة
''' </summary>
Public Class ComboBoxItem
    Public Property Text As String
    Public Property Value As Object

    Public Overrides Function ToString() As String
        Return Text
    End Function
End Class
