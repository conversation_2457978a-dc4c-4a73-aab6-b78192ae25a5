<?php
/**
 * الوظائف الأساسية لنظام إدارة الديون
 */

require_once __DIR__ . '/../config/database.php';

/**
 * جلب إحصائيات لوحة التحكم
 */
function getDashboardStats() {
    global $pdo;
    
    $stats = [
        'total_customers' => 0,
        'total_suppliers' => 0,
        'total_balance_iqd' => 0,
        'total_balance_usd' => 0,
        'total_transactions' => 0
    ];
    
    try {
        // عدد العملاء
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM customers WHERE is_active = 1");
        $stats['total_customers'] = $stmt->fetch()['count'];
        
        // عدد الموردين
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM suppliers WHERE is_active = 1");
        $stats['total_suppliers'] = $stmt->fetch()['count'];
        
        // إجمالي الأرصدة
        $stmt = $pdo->query("SELECT SUM(current_balance_iqd) as total_iqd, SUM(current_balance_usd) as total_usd FROM customers WHERE is_active = 1");
        $customer_balances = $stmt->fetch();
        
        $stmt = $pdo->query("SELECT SUM(current_balance_iqd) as total_iqd, SUM(current_balance_usd) as total_usd FROM suppliers WHERE is_active = 1");
        $supplier_balances = $stmt->fetch();
        
        $stats['total_balance_iqd'] = ($customer_balances['total_iqd'] ?? 0) + ($supplier_balances['total_iqd'] ?? 0);
        $stats['total_balance_usd'] = ($customer_balances['total_usd'] ?? 0) + ($supplier_balances['total_usd'] ?? 0);
        
        // عدد المعاملات
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM transactions");
        $stats['total_transactions'] = $stmt->fetch()['count'];
        
    } catch (PDOException $e) {
        error_log("خطأ في جلب الإحصائيات: " . $e->getMessage());
    }
    
    return $stats;
}

/**
 * جلب آخر المعاملات
 */
function getRecentTransactions($limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT t.*, 
                   CASE 
                       WHEN t.entity_type = 'customer' THEN c.name
                       WHEN t.entity_type = 'supplier' THEN s.name
                   END as entity_name,
                   cb.name as cash_box_name
            FROM transactions t
            LEFT JOIN customers c ON t.entity_type = 'customer' AND t.entity_id = c.id
            LEFT JOIN suppliers s ON t.entity_type = 'supplier' AND t.entity_id = s.id
            LEFT JOIN cash_boxes cb ON t.cash_box_id = cb.id
            ORDER BY t.created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("خطأ في جلب المعاملات الأخيرة: " . $e->getMessage());
        return [];
    }
}

/**
 * جلب أكبر الديون
 */
function getTopDebts($limit = 10) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT d.*, 
                   CASE 
                       WHEN d.entity_type = 'customer' THEN c.name
                       WHEN d.entity_type = 'supplier' THEN s.name
                   END as name,
                   d.entity_type as type
            FROM debts d
            LEFT JOIN customers c ON d.entity_type = 'customer' AND d.entity_id = c.id
            LEFT JOIN suppliers s ON d.entity_type = 'supplier' AND d.entity_id = s.id
            WHERE d.balance > 0
            ORDER BY d.balance DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll();
        
    } catch (PDOException $e) {
        error_log("خطأ في جلب أكبر الديون: " . $e->getMessage());
        return [];
    }
}

/**
 * إضافة عميل جديد
 */
function addCustomer($data) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO customers (name, phone, email, address, initial_balance_iqd, initial_balance_usd, 
                                 current_balance_iqd, current_balance_usd, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $data['name'],
            $data['phone'] ?? null,
            $data['email'] ?? null,
            $data['address'] ?? null,
            $data['initial_balance_iqd'] ?? 0,
            $data['initial_balance_usd'] ?? 0,
            $data['initial_balance_iqd'] ?? 0,
            $data['initial_balance_usd'] ?? 0,
            $data['notes'] ?? null,
            $_SESSION['user_id']
        ]);
        
        if ($result) {
            $customer_id = $pdo->lastInsertId();
            
            // تحديث جدول الديون
            updateDebtBalance('customer', $customer_id, 'IQD', $data['initial_balance_iqd'] ?? 0);
            updateDebtBalance('customer', $customer_id, 'USD', $data['initial_balance_usd'] ?? 0);
            
            return $customer_id;
        }
        
        return false;
        
    } catch (PDOException $e) {
        error_log("خطأ في إضافة العميل: " . $e->getMessage());
        return false;
    }
}

/**
 * إضافة مورد جديد
 */
function addSupplier($data) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO suppliers (name, phone, email, address, initial_balance_iqd, initial_balance_usd, 
                                 current_balance_iqd, current_balance_usd, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $data['name'],
            $data['phone'] ?? null,
            $data['email'] ?? null,
            $data['address'] ?? null,
            $data['initial_balance_iqd'] ?? 0,
            $data['initial_balance_usd'] ?? 0,
            $data['initial_balance_iqd'] ?? 0,
            $data['initial_balance_usd'] ?? 0,
            $data['notes'] ?? null,
            $_SESSION['user_id']
        ]);
        
        if ($result) {
            $supplier_id = $pdo->lastInsertId();
            
            // تحديث جدول الديون
            updateDebtBalance('supplier', $supplier_id, 'IQD', $data['initial_balance_iqd'] ?? 0);
            updateDebtBalance('supplier', $supplier_id, 'USD', $data['initial_balance_usd'] ?? 0);
            
            return $supplier_id;
        }
        
        return false;
        
    } catch (PDOException $e) {
        error_log("خطأ في إضافة المورد: " . $e->getMessage());
        return false;
    }
}

/**
 * إضافة معاملة جديدة
 */
function addTransaction($data) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // إضافة المعاملة
        $stmt = $pdo->prepare("
            INSERT INTO transactions (type, entity_type, entity_id, cash_box_id, amount, currency, 
                                    exchange_rate, description, reference_number, transaction_date, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            $data['type'],
            $data['entity_type'],
            $data['entity_id'],
            $data['cash_box_id'],
            $data['amount'],
            $data['currency'],
            $data['exchange_rate'] ?? 1,
            $data['description'] ?? null,
            $data['reference_number'] ?? null,
            $data['transaction_date'],
            $_SESSION['user_id']
        ]);
        
        if ($result) {
            $transaction_id = $pdo->lastInsertId();
            
            // تحديث رصيد العميل/المورد
            updateEntityBalance($data['entity_type'], $data['entity_id'], $data['currency'], $data['amount'], $data['type']);
            
            // تحديث رصيد الصندوق
            updateCashBoxBalance($data['cash_box_id'], $data['amount'], $data['type']);
            
            // تحديث جدول الديون
            $current_balance = getEntityBalance($data['entity_type'], $data['entity_id'], $data['currency']);
            updateDebtBalance($data['entity_type'], $data['entity_id'], $data['currency'], $current_balance);
            
            $pdo->commit();
            return $transaction_id;
        }
        
        $pdo->rollback();
        return false;
        
    } catch (PDOException $e) {
        $pdo->rollback();
        error_log("خطأ في إضافة المعاملة: " . $e->getMessage());
        return false;
    }
}

/**
 * تحديث رصيد العميل/المورد
 */
function updateEntityBalance($entity_type, $entity_id, $currency, $amount, $transaction_type) {
    global $pdo;
    
    $table = $entity_type === 'customer' ? 'customers' : 'suppliers';
    $balance_field = $currency === 'IQD' ? 'current_balance_iqd' : 'current_balance_usd';
    
    // تحديد إشارة المبلغ حسب نوع المعاملة
    $amount_sign = ($transaction_type === 'income') ? '+' : '-';
    
    $stmt = $pdo->prepare("
        UPDATE {$table} 
        SET {$balance_field} = {$balance_field} {$amount_sign} ?
        WHERE id = ?
    ");
    
    return $stmt->execute([$amount, $entity_id]);
}

/**
 * تحديث رصيد الصندوق
 */
function updateCashBoxBalance($cash_box_id, $amount, $transaction_type) {
    global $pdo;
    
    $amount_sign = ($transaction_type === 'income') ? '+' : '-';
    
    $stmt = $pdo->prepare("
        UPDATE cash_boxes 
        SET current_balance = current_balance {$amount_sign} ?
        WHERE id = ?
    ");
    
    return $stmt->execute([$amount, $cash_box_id]);
}

/**
 * جلب رصيد العميل/المورد
 */
function getEntityBalance($entity_type, $entity_id, $currency) {
    global $pdo;
    
    $table = $entity_type === 'customer' ? 'customers' : 'suppliers';
    $balance_field = $currency === 'IQD' ? 'current_balance_iqd' : 'current_balance_usd';
    
    $stmt = $pdo->prepare("SELECT {$balance_field} as balance FROM {$table} WHERE id = ?");
    $stmt->execute([$entity_id]);
    $result = $stmt->fetch();
    
    return $result ? $result['balance'] : 0;
}

/**
 * تحديث جدول الديون
 */
function updateDebtBalance($entity_type, $entity_id, $currency, $balance) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO debts (entity_type, entity_id, currency, balance, last_transaction_date)
        VALUES (?, ?, ?, ?, CURDATE())
        ON DUPLICATE KEY UPDATE 
        balance = VALUES(balance),
        last_transaction_date = VALUES(last_transaction_date)
    ");
    
    return $stmt->execute([$entity_type, $entity_id, $currency, $balance]);
}

/**
 * تنسيق المبلغ حسب العملة
 */
function formatCurrency($amount, $currency = 'IQD') {
    if ($currency === 'USD') {
        return '$' . number_format($amount, 2);
    } else {
        return number_format($amount, 0) . ' د.ع';
    }
}

/**
 * التحقق من صحة البيانات
 */
function validateInput($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $rule) {
        if (isset($rule['required']) && $rule['required'] && empty($data[$field])) {
            $errors[$field] = $rule['message'] ?? "الحقل {$field} مطلوب";
        }
        
        if (isset($data[$field]) && !empty($data[$field])) {
            if (isset($rule['type'])) {
                switch ($rule['type']) {
                    case 'email':
                        if (!filter_var($data[$field], FILTER_VALIDATE_EMAIL)) {
                            $errors[$field] = "البريد الإلكتروني غير صحيح";
                        }
                        break;
                    case 'numeric':
                        if (!is_numeric($data[$field])) {
                            $errors[$field] = "يجب أن يكون الحقل رقماً";
                        }
                        break;
                }
            }
            
            if (isset($rule['min_length']) && strlen($data[$field]) < $rule['min_length']) {
                $errors[$field] = "الحد الأدنى للطول هو {$rule['min_length']} أحرف";
            }
        }
    }
    
    return $errors;
}

/**
 * تسجيل العمليات في ملف السجل
 */
function logActivity($action, $details = '') {
    $log_file = __DIR__ . '/../logs/activity.log';
    $log_dir = dirname($log_file);
    
    if (!is_dir($log_dir)) {
        mkdir($log_dir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $user_id = $_SESSION['user_id'] ?? 'غير معروف';
    $username = $_SESSION['username'] ?? 'غير معروف';
    
    $log_entry = "[{$timestamp}] المستخدم: {$username} (ID: {$user_id}) - العملية: {$action}";
    if ($details) {
        $log_entry .= " - التفاصيل: {$details}";
    }
    $log_entry .= PHP_EOL;
    
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}
?>
