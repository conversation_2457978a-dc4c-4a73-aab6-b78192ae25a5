<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جميع الجداول الأساسية
        
        // جدول العملات
        $db->query("CREATE TABLE IF NOT EXISTS currencies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الأقسام
        $db->query("CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول فئات الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول المخازن
        $db->query("CREATE TABLE IF NOT EXISTS warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول العملاء
        $db->query("CREATE TABLE IF NOT EXISTS customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الموردين
        $db->query("CREATE TABLE IF NOT EXISTS suppliers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'company',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول فواتير المبيعات
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول فواتير المشتريات
        $db->query("CREATE TABLE IF NOT EXISTS purchase_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            supplier_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'received', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إدراج البيانات الأساسية
        
        // العملات
        $currencyExists = $db->fetchOne("SELECT COUNT(*) as count FROM currencies");
        if ($currencyExists['count'] == 0) {
            $currencies = [
                ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
                ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
                ['اليورو', '€', 'EUR', 0.00061, 0]
            ];
            
            foreach ($currencies as $currency) {
                $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
            }
        }
        
        // الأقسام
        $deptExists = $db->fetchOne("SELECT COUNT(*) as count FROM departments");
        if ($deptExists['count'] == 0) {
            $departments = [
                ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
                ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
                ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
                ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
                ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
                ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
                ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
                ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
            ];
            
            foreach ($departments as $dept) {
                $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
            }
        }
        
        $db->commit();
        $success = 'تم الإعداد الفوري بنجاح! جميع الجداول والبيانات الأساسية جاهزة.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في الإعداد: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعداد الفوري - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-instant {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1 class="pulse"><i class="fas fa-bolt fa-2x mb-3"></i><br>الإعداد الفوري</h1>
            <p class="mb-0">إعداد سريع وفوري لجميع أنظمة المعمل</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                
                <div class="text-center">
                    <h3 class="mb-4">🚀 النظام جاهز للاستخدام!</h3>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="employees.php" class="btn btn-primary w-100">
                                <i class="fas fa-users me-2"></i>الموظفين
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="purchases.php" class="btn btn-warning w-100">
                                <i class="fas fa-shopping-bag me-2"></i>المشتريات
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="sales.php" class="btn btn-success w-100">
                                <i class="fas fa-shopping-cart me-2"></i>المبيعات
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="customers.php" class="btn btn-info w-100">
                                <i class="fas fa-users-cog me-2"></i>العملاء
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="suppliers.php" class="btn btn-secondary w-100">
                                <i class="fas fa-truck me-2"></i>الموردين
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="inventory.php" class="btn btn-dark w-100">
                                <i class="fas fa-warehouse me-2"></i>المخزون
                            </a>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>لوحة التحكم الرئيسية
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="alert alert-warning">
                    <h5><i class="fas fa-bolt me-2"></i>إعداد فوري وسريع</h5>
                    <p>هذا الإعداد سيقوم بإنشاء جميع الجداول والبيانات الأساسية المطلوبة لتشغيل النظام فوراً.</p>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ما سيتم إنشاؤه:</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الجداول الأساسية:</h6>
                                <ul>
                                    <li>العملات (3 عملات)</li>
                                    <li>الأقسام (8 أقسام)</li>
                                    <li>الموظفين</li>
                                    <li>فئات الأصناف</li>
                                    <li>الأصناف</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>جداول المعاملات:</h6>
                                <ul>
                                    <li>المخازن</li>
                                    <li>العملاء</li>
                                    <li>الموردين</li>
                                    <li>فواتير المبيعات</li>
                                    <li>فواتير المشتريات</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-instant btn-lg me-3 pulse">
                            <i class="fas fa-bolt me-2"></i>تشغيل الإعداد الفوري
                        </button>
                    </form>
                    
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
