<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$purchaseId = (int)($_GET['id'] ?? 0);
if (!$purchaseId) {
    header('Location: purchases.php');
    exit;
}

// جلب بيانات فاتورة المشتريات
try {
    $purchase = $db->fetchOne("
        SELECT p.*, s.name as supplier_name, s.phone as supplier_phone, 
               s.address as supplier_address, u.full_name as created_by_name,
               c.symbol as currency_symbol
        FROM purchases p
        LEFT JOIN suppliers s ON p.supplier_id = s.id
        LEFT JOIN users u ON p.created_by = u.id
        LEFT JOIN currencies c ON p.currency_id = c.id
        WHERE p.id = ?
    ", [$purchaseId]);
    
    if (!$purchase) {
        header('Location: purchases.php');
        exit;
    }
    
    // جلب تفاصيل الفاتورة
    $items = $db->fetchAll("
        SELECT pd.*, i.name as item_name, i.unit as item_unit, i.code as item_code
        FROM purchase_details pd
        LEFT JOIN items i ON pd.item_id = i.id
        WHERE pd.purchase_id = ?
        ORDER BY pd.id
    ", [$purchaseId]);
    
} catch (Exception $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
    $purchase = null;
    $items = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض فاتورة مشتريات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .info-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        .info-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <?php if (!$purchase): ?>
        <div class="container mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                فاتورة المشتريات غير موجودة أو حدث خطأ في جلب البيانات
                <br><a href="purchases.php" class="btn btn-sm btn-primary mt-2">العودة إلى قائمة المشتريات</a>
            </div>
        </div>
    <?php else: ?>
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1><i class="fas fa-eye me-3"></i>فاتورة مشتريات: <?= htmlspecialchars($purchase['invoice_number']) ?></h1>
                        <p class="mb-0"><?= htmlspecialchars($purchase['supplier_name']) ?></p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="print_purchase.php?id=<?= $purchase['id'] ?>" class="btn btn-light me-2" target="_blank">
                            <i class="fas fa-print me-2"></i>طباعة
                        </a>
                        <a href="purchases.php" class="btn btn-outline-light">
                            <i class="fas fa-list me-2"></i>قائمة المشتريات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>رقم الفاتورة:</strong>
                                        <span class="float-end"><?= htmlspecialchars($purchase['invoice_number']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>تاريخ الفاتورة:</strong>
                                        <span class="float-end"><?= date('d/m/Y', strtotime($purchase['purchase_date'])) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>تاريخ الاستحقاق:</strong>
                                        <span class="float-end"><?= $purchase['due_date'] ? date('d/m/Y', strtotime($purchase['due_date'])) : 'فوري' ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>المورد:</strong>
                                        <span class="float-end"><?= htmlspecialchars($purchase['supplier_name']) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>المجموع الفرعي:</strong>
                                        <span class="float-end"><?= number_format($purchase['subtotal'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الضريبة:</strong>
                                        <span class="float-end"><?= number_format($purchase['tax_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الخصم:</strong>
                                        <span class="float-end"><?= number_format($purchase['discount_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>المجموع النهائي:</strong>
                                        <span class="float-end text-success"><strong><?= number_format($purchase['total_amount'], 2) ?> <?= $purchase['currency_symbol'] ?></strong></span>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($purchase['notes']): ?>
                            <div class="mt-3">
                                <strong>ملاحظات:</strong>
                                <div class="bg-light p-3 rounded mt-2">
                                    <?= nl2br(htmlspecialchars($purchase['notes'])) ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- تفاصيل الأصناف -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>تفاصيل الأصناف</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($items)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا توجد أصناف في هذه الفاتورة</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>كود الصنف</th>
                                                <th>اسم الصنف</th>
                                                <th>الوحدة</th>
                                                <th>الكمية</th>
                                                <th>سعر الوحدة</th>
                                                <th>المجموع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($items as $item): ?>
                                                <tr>
                                                    <td><?= htmlspecialchars($item['item_code']) ?></td>
                                                    <td><?= htmlspecialchars($item['item_name']) ?></td>
                                                    <td><?= htmlspecialchars($item['item_unit']) ?></td>
                                                    <td><?= number_format($item['quantity'], 3) ?></td>
                                                    <td><?= number_format($item['unit_price'], 2) ?> <?= $purchase['currency_symbol'] ?></td>
                                                    <td><?= number_format($item['total_price'], 2) ?> <?= $purchase['currency_symbol'] ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-lg-4">
                    <!-- حالة الفاتورة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>حالة الفاتورة</h5>
                        </div>
                        <div class="card-body text-center">
                            <?php
                            $statusColors = [
                                'draft' => 'secondary',
                                'confirmed' => 'info',
                                'received' => 'success',
                                'paid' => 'success',
                                'cancelled' => 'danger'
                            ];
                            $statusNames = [
                                'draft' => 'مسودة',
                                'confirmed' => 'مؤكدة',
                                'received' => 'مستلمة',
                                'paid' => 'مدفوعة',
                                'cancelled' => 'ملغية'
                            ];
                            ?>
                            <span class="badge bg-<?= $statusColors[$purchase['status']] ?> mb-3 d-inline-block fs-6">
                                <?= $statusNames[$purchase['status']] ?>
                            </span>
                        </div>
                    </div>

                    <!-- معلومات المورد -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-truck me-2"></i>معلومات المورد</h5>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <strong>اسم المورد:</strong>
                                <div><?= htmlspecialchars($purchase['supplier_name']) ?></div>
                            </div>
                            <?php if ($purchase['supplier_phone']): ?>
                            <div class="info-item">
                                <strong>الهاتف:</strong>
                                <div><?= htmlspecialchars($purchase['supplier_phone']) ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($purchase['supplier_address']): ?>
                            <div class="info-item">
                                <strong>العنوان:</strong>
                                <div><?= htmlspecialchars($purchase['supplier_address']) ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- إجراءات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="print_purchase.php?id=<?= $purchase['id'] ?>" class="btn btn-success" target="_blank">
                                    <i class="fas fa-print me-2"></i>طباعة الفاتورة
                                </a>
                                <?php if ($purchase['status'] == 'draft'): ?>
                                <a href="edit_purchase.php?id=<?= $purchase['id'] ?>" class="btn btn-warning">
                                    <i class="fas fa-edit me-2"></i>تعديل الفاتورة
                                </a>
                                <?php endif; ?>
                                <a href="purchases.php" class="btn btn-secondary">
                                    <i class="fas fa-list me-2"></i>قائمة المشتريات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
