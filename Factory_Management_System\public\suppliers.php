<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة مورد جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_supplier') {
    try {
        // توليد كود المورد
        $prefix = 'SUP';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM suppliers");
        $sequence = ($countResult['count'] ?? 0) + 1;
        $supplierCode = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);
        
        $supplierData = [
            'code' => $supplierCode,
            'name' => trim($_POST['name']),
            'type' => $_POST['type'],
            'phone' => trim($_POST['phone'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'tax_number' => trim($_POST['tax_number'] ?? ''),
            'credit_limit' => (float)($_POST['credit_limit'] ?? 0),
            'currency_id' => (int)$_POST['currency_id'],
            'payment_terms' => (int)($_POST['payment_terms'] ?? 0),
            'discount_percentage' => (float)($_POST['discount_percentage'] ?? 0)
        ];
        
        $db->insert('suppliers', $supplierData);
        $success = "تم إضافة المورد بنجاح برقم: $supplierCode";
        
    } catch (Exception $e) {
        $error = "خطأ في إضافة المورد: " . $e->getMessage();
    }
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$type = $_GET['type'] ?? '';
$status = $_GET['status'] ?? '';

// بناء استعلام البحث
$whereConditions = ["1=1"];
$params = [];

if ($search) {
    $whereConditions[] = "(s.name LIKE ? OR s.code LIKE ? OR s.phone LIKE ? OR s.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($type) {
    $whereConditions[] = "s.type = ?";
    $params[] = $type;
}

if ($status !== '') {
    $whereConditions[] = "s.is_active = ?";
    $params[] = $status;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب الموردين
try {
    $suppliers = $db->fetchAll("
        SELECT s.*, c.symbol as currency_symbol,
               COUNT(pi.id) as purchase_count,
               SUM(pi.total_amount) as total_purchases
        FROM suppliers s
        LEFT JOIN currencies c ON s.currency_id = c.id
        LEFT JOIN purchase_invoices pi ON s.id = pi.supplier_id
        WHERE $whereClause
        GROUP BY s.id
        ORDER BY s.name
    ", $params);
} catch (Exception $e) {
    $suppliers = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// جلب العملات
try {
    $currencies = $db->fetchAll("SELECT id, name, symbol FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");
} catch (Exception $e) {
    $currencies = [];
}

// إحصائيات الموردين
try {
    $stats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_suppliers,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_suppliers,
            COUNT(CASE WHEN type = 'company' THEN 1 END) as companies,
            COUNT(CASE WHEN type = 'individual' THEN 1 END) as individuals
        FROM suppliers
    ");
} catch (Exception $e) {
    $stats = [
        'total_suppliers' => 0,
        'active_suppliers' => 0,
        'companies' => 0,
        'individuals' => 0
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموردين - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .stats-card.total { background: linear-gradient(135deg, #17a2b8 0%, #138496 100%); }
        .stats-card.active { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .stats-card.companies { background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%); }
        .stats-card.individuals { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); }
        .supplier-row {
            transition: all 0.3s ease;
        }
        .supplier-row:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-truck me-3"></i>إدارة الموردين</h1>
                    <p class="mb-0">إدارة الموردين والشركات المورّدة</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="add_purchase.php" class="btn btn-light me-2">
                        <i class="fas fa-plus me-2"></i>فاتورة مشتريات
                    </a>
                    <a href="purchases.php" class="btn btn-outline-light">
                        <i class="fas fa-shopping-bag me-2"></i>المشتريات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <?php if (strpos($error, 'Table') !== false): ?>
                    <br><a href="final_setup.php" class="btn btn-sm btn-primary mt-2">إعداد النظام</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الموردين -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card total">
                    <h3><?= $stats['total_suppliers'] ?></h3>
                    <p class="mb-0">إجمالي الموردين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card active">
                    <h3><?= $stats['active_suppliers'] ?></h3>
                    <p class="mb-0">موردين نشطين</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card companies">
                    <h3><?= $stats['companies'] ?></h3>
                    <p class="mb-0">شركات</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card individuals">
                    <h3><?= $stats['individuals'] ?></h3>
                    <p class="mb-0">أفراد</p>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-filter me-2"></i>البحث والفلترة</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">البحث</label>
                                <input type="text" name="search" class="form-control" 
                                       value="<?= htmlspecialchars($search) ?>" 
                                       placeholder="الاسم، الكود، الهاتف، البريد">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">النوع</label>
                                <select name="type" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual" <?= $type == 'individual' ? 'selected' : '' ?>>فرد</option>
                                    <option value="company" <?= $type == 'company' ? 'selected' : '' ?>>شركة</option>
                                    <option value="institution" <?= $type == 'institution' ? 'selected' : '' ?>>مؤسسة</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="1" <?= $status === '1' ? 'selected' : '' ?>>نشط</option>
                                    <option value="0" <?= $status === '0' ? 'selected' : '' ?>>غير نشط</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </form>
                    </div>
                </div>

                <!-- إضافة مورد جديد -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>مورد جديد</h5>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-primary w-100" 
                                data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                            <i class="fas fa-plus me-2"></i>إضافة مورد
                        </button>
                    </div>
                </div>
            </div>

            <!-- قائمة الموردين -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الموردين (<?= count($suppliers) ?>)</h5>
                            <a href="suppliers_report.php" class="btn btn-success">
                                <i class="fas fa-file-excel me-2"></i>تصدير
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($suppliers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-truck fa-4x text-muted mb-3"></i>
                                <h5>لا توجد موردين</h5>
                                <p class="text-muted">ابدأ بإضافة مورد جديد</p>
                                <button type="button" class="btn btn-primary" 
                                        data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                                    <i class="fas fa-plus me-2"></i>إضافة مورد
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الكود</th>
                                            <th>الاسم</th>
                                            <th>النوع</th>
                                            <th>الهاتف</th>
                                            <th>البريد الإلكتروني</th>
                                            <th>عدد المشتريات</th>
                                            <th>إجمالي المشتريات</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <tr class="supplier-row">
                                                <td>
                                                    <strong><?= htmlspecialchars($supplier['code']) ?></strong>
                                                </td>
                                                <td>
                                                    <strong><?= htmlspecialchars($supplier['name']) ?></strong>
                                                    <?php if ($supplier['tax_number']): ?>
                                                        <br><small class="text-muted">ض.ر: <?= htmlspecialchars($supplier['tax_number']) ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $typeNames = [
                                                        'individual' => 'فرد',
                                                        'company' => 'شركة',
                                                        'institution' => 'مؤسسة'
                                                    ];
                                                    ?>
                                                    <span class="badge bg-info"><?= $typeNames[$supplier['type']] ?></span>
                                                </td>
                                                <td><?= htmlspecialchars($supplier['phone']) ?></td>
                                                <td><?= htmlspecialchars($supplier['email']) ?></td>
                                                <td>
                                                    <span class="badge bg-primary"><?= $supplier['purchase_count'] ?></span>
                                                </td>
                                                <td>
                                                    <strong><?= number_format($supplier['total_purchases'] ?? 0, 0) ?></strong>
                                                    <small class="text-muted"><?= $supplier['currency_symbol'] ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($supplier['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="view_supplier.php?id=<?= $supplier['id'] ?>" 
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_supplier.php?id=<?= $supplier['id'] ?>" 
                                                           class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="add_purchase.php?supplier_id=<?= $supplier['id'] ?>" 
                                                           class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-shopping-bag"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مورد -->
    <div class="modal fade" id="addSupplierModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مورد جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_supplier">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" required 
                                       placeholder="اسم المورد أو الشركة">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">النوع</label>
                                <select name="type" class="form-select">
                                    <option value="company">شركة</option>
                                    <option value="individual">فرد</option>
                                    <option value="institution">مؤسسة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" name="phone" class="form-control" 
                                       placeholder="رقم الهاتف">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control" 
                                       placeholder="البريد الإلكتروني">
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="2" 
                                          placeholder="العنوان الكامل"></textarea>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الرقم الضريبي</label>
                                <input type="text" name="tax_number" class="form-control" 
                                       placeholder="الرقم الضريبي">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">العملة</label>
                                <select name="currency_id" class="form-select">
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?= $currency['id'] ?>" <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الحد الائتماني</label>
                                <input type="number" name="credit_limit" class="form-control" 
                                       step="0.01" min="0" value="0" placeholder="0.00">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label class="form-label">شروط الدفع (أيام)</label>
                                <input type="number" name="payment_terms" class="form-control" 
                                       min="0" value="0" placeholder="0">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ المورد</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
