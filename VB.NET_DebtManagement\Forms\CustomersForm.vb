Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إدارة العملاء - Customers Management Form
''' </summary>
Public Class CustomersForm
    Inherits Form
    
#Region "Fields"
    
    Private _context As DebtContext
    Private _currentUser As User
    
    ' عناصر التحكم الرئيسية
    Private _toolStrip As ToolStrip
    Private _searchPanel As Panel
    Private _dataGridView As DataGridView
    Private _statusStrip As StatusStrip
    
    ' أزرار شريط الأدوات
    Private _btnAdd As ToolStripButton
    Private _btnEdit As ToolStripButton
    Private _btnDelete As ToolStripButton
    Private _btnRefresh As ToolStripButton
    Private _btnExport As ToolStripButton
    Private _btnPrint As ToolStripButton
    
    ' عناصر البحث
    Private _txtSearch As TextBox
    Private _cmbSearchType As ComboBox
    Private _btnSearch As Button
    Private _btnClearSearch As Button
    
    ' شريط الحالة
    Private _lblStatus As ToolStripStatusLabel
    Private _lblCount As ToolStripStatusLabel
    
    ' متغيرات أخرى
    Private _selectedCustomer As Customer
    
#End Region

#Region "Constructor"
    
    ''' <summary>
    ''' منشئ نموذج إدارة العملاء
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        
        InitializeComponent()
        SetupForm()
        SetupToolStrip()
        SetupSearchPanel()
        SetupDataGridView()
        SetupStatusStrip()
        LoadCustomers()
    End Sub
    
#End Region

#Region "Form Setup"
    
    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "إدارة العملاء"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.CustomersIcon
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolStrip()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }
        
        ' إنشاء الأزرار
        _btnAdd = New ToolStripButton With {
            .Text = "إضافة عميل",
            .Image = My.Resources.AddIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "إضافة عميل جديد"
        }
        
        _btnEdit = New ToolStripButton With {
            .Text = "تعديل",
            .Image = My.Resources.EditIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تعديل العميل المحدد",
            .Enabled = False
        }
        
        _btnDelete = New ToolStripButton With {
            .Text = "حذف",
            .Image = My.Resources.DeleteIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "حذف العميل المحدد",
            .Enabled = False
        }
        
        _btnRefresh = New ToolStripButton With {
            .Text = "تحديث",
            .Image = My.Resources.RefreshIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحديث قائمة العملاء"
        }
        
        _btnExport = New ToolStripButton With {
            .Text = "تصدير",
            .Image = My.Resources.ExportIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تصدير قائمة العملاء"
        }
        
        _btnPrint = New ToolStripButton With {
            .Text = "طباعة",
            .Image = My.Resources.PrintIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "طباعة قائمة العملاء"
        }
        
        ' إضافة الأزرار لشريط الأدوات
        _toolStrip.Items.AddRange({
            _btnAdd,
            New ToolStripSeparator(),
            _btnEdit,
            _btnDelete,
            New ToolStripSeparator(),
            _btnRefresh,
            New ToolStripSeparator(),
            _btnExport,
            _btnPrint
        })
        
        ' ربط الأحداث
        AddHandler _btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler _btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler _btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler _btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler _btnExport.Click, AddressOf BtnExport_Click
        AddHandler _btnPrint.Click, AddressOf BtnPrint_Click
        
        Me.Controls.Add(_toolStrip)
    End Sub
    
    ''' <summary>
    ''' إعداد لوحة البحث
    ''' </summary>
    Private Sub SetupSearchPanel()
        _searchPanel = New Panel With {
            .Height = 60,
            .Dock = DockStyle.Top,
            .BackColor = Color.White,
            .Padding = New Padding(10)
        }
        
        ' تسمية البحث
        Dim lblSearch As New Label With {
            .Text = "البحث:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 25),
            .Location = New Point(Me.Width - 70, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        ' نوع البحث
        _cmbSearchType = New ComboBox With {
            .Size = New Size(120, 25),
            .Location = New Point(Me.Width - 200, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular)
        }
        _cmbSearchType.Items.AddRange({"الاسم", "الهاتف", "البريد الإلكتروني", "العنوان"})
        _cmbSearchType.SelectedIndex = 0
        
        ' مربع البحث
        _txtSearch = New TextBox With {
            .Size = New Size(250, 25),
            .Location = New Point(Me.Width - 460, 15),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .PlaceholderText = "ادخل كلمة البحث..."
        }
        
        ' زر البحث
        _btnSearch = New Button With {
            .Text = "بحث",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 540, 13),
            .BackColor = Color.FromArgb(0, 123, 255),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnSearch.FlatAppearance.BorderSize = 0
        
        ' زر مسح البحث
        _btnClearSearch = New Button With {
            .Text = "مسح",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 620, 13),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnClearSearch.FlatAppearance.BorderSize = 0
        
        ' إضافة العناصر للوحة
        _searchPanel.Controls.AddRange({lblSearch, _cmbSearchType, _txtSearch, _btnSearch, _btnClearSearch})
        
        ' ربط الأحداث
        AddHandler _txtSearch.KeyDown, AddressOf TxtSearch_KeyDown
        AddHandler _btnSearch.Click, AddressOf BtnSearch_Click
        AddHandler _btnClearSearch.Click, AddressOf BtnClearSearch_Click
        
        Me.Controls.Add(_searchPanel)
    End Sub
    
    ''' <summary>
    ''' إعداد شبكة البيانات
    ''' </summary>
    Private Sub SetupDataGridView()
        _dataGridView = New DataGridView With {
            .Dock = DockStyle.Fill,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            .ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.FromArgb(52, 58, 64),
                .ForeColor = Color.White,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .ColumnHeadersHeight = 40,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.White,
                .ForeColor = Color.FromArgb(33, 37, 41),
                .Font = New Font("Segoe UI", 9, FontStyle.Regular),
                .SelectionBackColor = Color.FromArgb(0, 123, 255),
                .SelectionForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .EnableHeadersVisualStyles = False,
            .GridColor = Color.FromArgb(222, 226, 230),
            .RowHeadersVisible = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .MultiSelect = False,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .RightToLeft = RightToLeft.Yes
        }
        
        ' إضافة الأعمدة
        _dataGridView.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Id", .HeaderText = "المعرف", .Visible = False},
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم العميل", .FillWeight = 25},
            New DataGridViewTextBoxColumn With {.Name = "Phone", .HeaderText = "رقم الهاتف", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Email", .HeaderText = "البريد الإلكتروني", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "Address", .HeaderText = "العنوان", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "BalanceIQD", .HeaderText = "الرصيد (د.ع)", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "BalanceUSD", .HeaderText = "الرصيد ($)", .FillWeight = 10}
        })
        
        ' ربط الأحداث
        AddHandler _dataGridView.SelectionChanged, AddressOf DataGridView_SelectionChanged
        AddHandler _dataGridView.CellDoubleClick, AddressOf DataGridView_CellDoubleClick
        AddHandler _dataGridView.CellFormatting, AddressOf DataGridView_CellFormatting
        
        Me.Controls.Add(_dataGridView)
    End Sub
    
    ''' <summary>
    ''' إعداد شريط الحالة
    ''' </summary>
    Private Sub SetupStatusStrip()
        _statusStrip = New StatusStrip With {
            .BackColor = Color.FromArgb(248, 249, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }
        
        _lblStatus = New ToolStripStatusLabel With {
            .Text = "جاهز",
            .Spring = True,
            .TextAlign = ContentAlignment.MiddleLeft
        }
        
        _lblCount = New ToolStripStatusLabel With {
            .Text = "عدد العملاء: 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }
        
        _statusStrip.Items.AddRange({_lblStatus, _lblCount})
        Me.Controls.Add(_statusStrip)
    End Sub
    
#End Region

#Region "Data Methods"
    
    ''' <summary>
    ''' تحميل قائمة العملاء
    ''' </summary>
    Private Sub LoadCustomers(Optional searchText As String = "", Optional searchType As String = "الاسم")
        Try
            _lblStatus.Text = "جاري تحميل البيانات..."
            Application.DoEvents()
            
            Dim query = _context.Customers.Where(Function(c) c.IsActive)
            
            ' تطبيق البحث
            If Not String.IsNullOrWhiteSpace(searchText) Then
                Select Case searchType
                    Case "الاسم"
                        query = query.Where(Function(c) c.Name.Contains(searchText))
                    Case "الهاتف"
                        query = query.Where(Function(c) c.Phone.Contains(searchText))
                    Case "البريد الإلكتروني"
                        query = query.Where(Function(c) c.Email.Contains(searchText))
                    Case "العنوان"
                        query = query.Where(Function(c) c.Address.Contains(searchText))
                End Select
            End If
            
            Dim customers = query.OrderBy(Function(c) c.Name).ToList()
            
            ' مسح البيانات الحالية
            _dataGridView.Rows.Clear()
            
            ' إضافة البيانات الجديدة
            For Each customer In customers
                Dim row As New DataGridViewRow()
                row.CreateCells(_dataGridView)
                
                row.Cells("Id").Value = customer.Id
                row.Cells("Name").Value = customer.Name
                row.Cells("Phone").Value = customer.Phone
                row.Cells("Email").Value = customer.Email
                row.Cells("Address").Value = customer.Address
                row.Cells("BalanceIQD").Value = customer.CurrentBalanceIQD
                row.Cells("BalanceUSD").Value = customer.CurrentBalanceUSD
                
                row.Tag = customer
                _dataGridView.Rows.Add(row)
            Next
            
            ' تحديث شريط الحالة
            _lblCount.Text = $"عدد العملاء: {customers.Count}"
            _lblStatus.Text = "تم تحميل البيانات بنجاح"
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            _lblStatus.Text = "خطأ في تحميل البيانات"
        End Try
    End Sub
    
    ''' <summary>
    ''' حذف عميل
    ''' </summary>
    Private Sub DeleteCustomer(customer As Customer)
        Try
            ' التحقق من وجود معاملات مرتبطة
            Dim hasTransactions = _context.Transactions.Any(Function(t) t.EntityType = "Customer" AndAlso t.EntityId = customer.Id)
            
            If hasTransactions Then
                If MessageBox.Show("هذا العميل لديه معاملات مرتبطة. هل تريد إلغاء تفعيله بدلاً من حذفه؟", 
                                 "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    customer.IsActive = False
                    customer.UpdatedAt = DateTime.Now
                    customer.UpdatedBy = _currentUser.Id
                    _context.SaveChanges()
                    
                    MessageBox.Show("تم إلغاء تفعيل العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCustomers()
                End If
            Else
                If MessageBox.Show($"هل أنت متأكد من حذف العميل '{customer.Name}'؟", 
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    _context.Customers.Remove(customer)
                    _context.SaveChanges()
                    
                    MessageBox.Show("تم حذف العميل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCustomers()
                End If
            End If
            
        Catch ex As Exception
            MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
#End Region

#Region "Event Handlers"
    
    ''' <summary>
    ''' إضافة عميل جديد
    ''' </summary>
    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim addForm As New CustomerAddEditForm(_context, _currentUser)
        If addForm.ShowDialog() = DialogResult.OK Then
            LoadCustomers()
        End If
    End Sub
    
    ''' <summary>
    ''' تعديل العميل المحدد
    ''' </summary>
    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If _selectedCustomer IsNot Nothing Then
            Dim editForm As New CustomerAddEditForm(_context, _currentUser, _selectedCustomer)
            If editForm.ShowDialog() = DialogResult.OK Then
                LoadCustomers()
            End If
        End If
    End Sub
    
    ''' <summary>
    ''' حذف العميل المحدد
    ''' </summary>
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If _selectedCustomer IsNot Nothing Then
            DeleteCustomer(_selectedCustomer)
        End If
    End Sub
    
    ''' <summary>
    ''' تحديث قائمة العملاء
    ''' </summary>
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadCustomers()
    End Sub
    
    ''' <summary>
    ''' تصدير قائمة العملاء
    ''' </summary>
    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        ' سيتم تنفيذ التصدير لاحقاً
        MessageBox.Show("سيتم تنفيذ ميزة التصدير قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    ''' <summary>
    ''' طباعة قائمة العملاء
    ''' </summary>
    Private Sub BtnPrint_Click(sender As Object, e As EventArgs)
        ' سيتم تنفيذ الطباعة لاحقاً
        MessageBox.Show("سيتم تنفيذ ميزة الطباعة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    ''' <summary>
    ''' البحث في قائمة العملاء
    ''' </summary>
    Private Sub BtnSearch_Click(sender As Object, e As EventArgs)
        LoadCustomers(_txtSearch.Text.Trim(), _cmbSearchType.Text)
    End Sub
    
    ''' <summary>
    ''' مسح البحث
    ''' </summary>
    Private Sub BtnClearSearch_Click(sender As Object, e As EventArgs)
        _txtSearch.Clear()
        _cmbSearchType.SelectedIndex = 0
        LoadCustomers()
    End Sub
    
    ''' <summary>
    ''' البحث عند الضغط على Enter
    ''' </summary>
    Private Sub TxtSearch_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            BtnSearch_Click(sender, e)
        End If
    End Sub
    
    ''' <summary>
    ''' تغيير التحديد في الشبكة
    ''' </summary>
    Private Sub DataGridView_SelectionChanged(sender As Object, e As EventArgs)
        If _dataGridView.SelectedRows.Count > 0 Then
            _selectedCustomer = DirectCast(_dataGridView.SelectedRows(0).Tag, Customer)
            _btnEdit.Enabled = True
            _btnDelete.Enabled = True
        Else
            _selectedCustomer = Nothing
            _btnEdit.Enabled = False
            _btnDelete.Enabled = False
        End If
    End Sub
    
    ''' <summary>
    ''' النقر المزدوج لتعديل العميل
    ''' </summary>
    Private Sub DataGridView_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(sender, e)
        End If
    End Sub
    
    ''' <summary>
    ''' تنسيق خلايا الشبكة
    ''' </summary>
    Private Sub DataGridView_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs)
        If e.ColumnIndex = _dataGridView.Columns("BalanceIQD").Index AndAlso e.Value IsNot Nothing Then
            Dim balance As Decimal = Convert.ToDecimal(e.Value)
            e.Value = $"{balance:N0} د.ع"
            
            ' تلوين الرصيد
            If balance > 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69) ' أخضر
            ElseIf balance < 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
            End If
            
        ElseIf e.ColumnIndex = _dataGridView.Columns("BalanceUSD").Index AndAlso e.Value IsNot Nothing Then
            Dim balance As Decimal = Convert.ToDecimal(e.Value)
            e.Value = $"${balance:N2}"
            
            ' تلوين الرصيد
            If balance > 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69) ' أخضر
            ElseIf balance < 0 Then
                e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
            End If
        End If
    End Sub
    
#End Region

End Class
