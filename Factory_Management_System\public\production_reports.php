<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// جلب تقارير الإنتاج
try {
    // إحصائيات عامة
    $generalStats = $db->fetchOne("
        SELECT 
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            SUM(quantity_required) as total_quantity_required,
            SUM(quantity_produced) as total_quantity_produced,
            SUM(estimated_cost) as total_estimated_cost,
            SUM(actual_cost) as total_actual_cost
        FROM production_orders
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ");
    
    // أداء الإنتاج اليومي
    $dailyProduction = $db->fetchAll("
        SELECT 
            DATE(created_at) as production_date,
            COUNT(*) as orders_count,
            SUM(quantity_produced) as total_produced,
            SUM(actual_cost) as total_cost
        FROM production_orders
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY production_date DESC
    ");
    
    // أكثر المنتجات إنتاجاً
    $topProducts = $db->fetchAll("
        SELECT 
            i.name as product_name,
            i.code as product_code,
            COUNT(po.id) as orders_count,
            SUM(po.quantity_produced) as total_produced,
            SUM(po.actual_cost) as total_cost
        FROM production_orders po
        JOIN items i ON po.product_id = i.id
        WHERE po.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY po.product_id, i.name, i.code
        ORDER BY total_produced DESC
        LIMIT 10
    ");
    
    // كفاءة الإنتاج
    $efficiency = $db->fetchAll("
        SELECT 
            po.order_number,
            i.name as product_name,
            po.quantity_required,
            po.quantity_produced,
            ROUND((po.quantity_produced / po.quantity_required) * 100, 2) as efficiency_percentage,
            po.estimated_cost,
            po.actual_cost,
            CASE 
                WHEN po.actual_cost > 0 THEN ROUND(((po.actual_cost - po.estimated_cost) / po.estimated_cost) * 100, 2)
                ELSE 0
            END as cost_variance_percentage
        FROM production_orders po
        JOIN items i ON po.product_id = i.id
        WHERE po.status = 'completed' AND po.created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ORDER BY efficiency_percentage DESC
        LIMIT 10
    ");
    
} catch (Exception $e) {
    $generalStats = [];
    $dailyProduction = [];
    $topProducts = [];
    $efficiency = [];
    $error = "خطأ في جلب التقارير: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #6f42c1;
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-label {
            color: #6c757d;
            font-weight: 600;
            margin-top: 0.5rem;
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 2rem 0;
        }
        
        .efficiency-bar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            height: 20px;
            border-radius: 10px;
            margin: 0.5rem 0;
        }
        
        .efficiency-bar.low {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .efficiency-bar.medium {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-chart-line me-3"></i>تقارير الإنتاج</h1>
                    <p class="mb-0">تحليلات شاملة لأداء الإنتاج والكفاءة</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-light me-2" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <a href="production.php" class="btn btn-outline-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- الإحصائيات العامة -->
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $generalStats['total_orders'] ?: 0 ?></div>
                    <div class="stats-label">إجمالي الأوامر</div>
                    <small class="text-muted">(30 يوم)</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= $generalStats['completed_orders'] ?: 0 ?></div>
                    <div class="stats-label">أوامر مكتملة</div>
                    <small class="text-success">
                        <?php 
                        $completionRate = $generalStats['total_orders'] > 0 ? 
                                         round(($generalStats['completed_orders'] / $generalStats['total_orders']) * 100, 1) : 0;
                        echo $completionRate . '%';
                        ?>
                    </small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= number_format($generalStats['total_quantity_produced'] ?: 0, 0) ?></div>
                    <div class="stats-label">إجمالي الإنتاج</div>
                    <small class="text-muted">وحدة</small>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?= number_format(($generalStats['total_actual_cost'] ?: 0) / 1000, 0) ?>K</div>
                    <div class="stats-label">التكلفة الفعلية</div>
                    <small class="text-muted">د.ع</small>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الإنتاج اليومي -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calendar-day me-2"></i>الإنتاج اليومي (7 أيام)</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyProductionChart"></canvas>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>عدد الأوامر</th>
                                        <th>الكمية المنتجة</th>
                                        <th>التكلفة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($dailyProduction as $day): ?>
                                        <tr>
                                            <td><?= date('d/m/Y', strtotime($day['production_date'])) ?></td>
                                            <td><?= $day['orders_count'] ?></td>
                                            <td><?= number_format($day['total_produced'], 2) ?></td>
                                            <td><?= number_format($day['total_cost'], 0) ?> د.ع</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أكثر المنتجات إنتاجاً -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>أكثر المنتجات إنتاجاً</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="topProductsChart"></canvas>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>عدد الأوامر</th>
                                        <th>الكمية المنتجة</th>
                                        <th>التكلفة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($topProducts, 0, 5) as $product): ?>
                                        <tr>
                                            <td>
                                                <?= htmlspecialchars($product['product_name']) ?>
                                                <small class="text-muted d-block"><?= htmlspecialchars($product['product_code']) ?></small>
                                            </td>
                                            <td><?= $product['orders_count'] ?></td>
                                            <td><?= number_format($product['total_produced'], 2) ?></td>
                                            <td><?= number_format($product['total_cost'], 0) ?> د.ع</td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- كفاءة الإنتاج -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tachometer-alt me-2"></i>كفاءة الإنتاج</h5>
            </div>
            <div class="card-body">
                <?php if (empty($efficiency)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                        <h5>لا توجد بيانات كفاءة</h5>
                        <p class="text-muted">لا توجد أوامر إنتاج مكتملة في الفترة المحددة</p>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($efficiency as $order): ?>
                            <div class="col-lg-6 mb-4">
                                <div class="border rounded p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0"><?= htmlspecialchars($order['order_number']) ?></h6>
                                        <span class="badge bg-<?= $order['efficiency_percentage'] >= 90 ? 'success' : ($order['efficiency_percentage'] >= 70 ? 'warning' : 'danger') ?>">
                                            <?= $order['efficiency_percentage'] ?>%
                                        </span>
                                    </div>
                                    
                                    <p class="text-muted mb-2"><?= htmlspecialchars($order['product_name']) ?></p>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">كفاءة الإنتاج:</small>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-<?= $order['efficiency_percentage'] >= 90 ? 'success' : ($order['efficiency_percentage'] >= 70 ? 'warning' : 'danger') ?>" 
                                                 style="width: <?= $order['efficiency_percentage'] ?>%"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <small class="text-muted">مطلوب</small>
                                            <div><?= number_format($order['quantity_required'], 2) ?></div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">منتج</small>
                                            <div><?= number_format($order['quantity_produced'], 2) ?></div>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">انحراف التكلفة</small>
                                            <div class="<?= $order['cost_variance_percentage'] > 0 ? 'text-danger' : 'text-success' ?>">
                                                <?= $order['cost_variance_percentage'] ?>%
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- ملخص الأداء -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>ملخص الأداء</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>معدل الإنجاز:</h6>
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar" style="width: <?= $completionRate ?>%">
                                <?= $completionRate ?>%
                            </div>
                        </div>
                        
                        <h6>كفاءة الإنتاج:</h6>
                        <?php
                        $totalRequired = $generalStats['total_quantity_required'] ?: 1;
                        $totalProduced = $generalStats['total_quantity_produced'] ?: 0;
                        $productionEfficiency = round(($totalProduced / $totalRequired) * 100, 1);
                        ?>
                        <div class="progress mb-3" style="height: 25px;">
                            <div class="progress-bar bg-success" style="width: <?= $productionEfficiency ?>%">
                                <?= $productionEfficiency ?>%
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>انحراف التكلفة:</h6>
                        <?php
                        $estimatedCost = $generalStats['total_estimated_cost'] ?: 1;
                        $actualCost = $generalStats['total_actual_cost'] ?: 0;
                        $costVariance = round((($actualCost - $estimatedCost) / $estimatedCost) * 100, 1);
                        ?>
                        <div class="alert alert-<?= $costVariance > 0 ? 'warning' : 'success' ?>">
                            <strong><?= abs($costVariance) ?>%</strong>
                            <?= $costVariance > 0 ? 'زيادة في التكلفة' : 'توفير في التكلفة' ?>
                        </div>
                        
                        <div class="text-center">
                            <h6>التوصيات:</h6>
                            <ul class="list-unstyled text-start">
                                <?php if ($completionRate < 80): ?>
                                    <li><i class="fas fa-exclamation-triangle text-warning me-2"></i>تحسين معدل إنجاز الأوامر</li>
                                <?php endif; ?>
                                <?php if ($productionEfficiency < 90): ?>
                                    <li><i class="fas fa-cogs text-info me-2"></i>تحسين كفاءة عملية الإنتاج</li>
                                <?php endif; ?>
                                <?php if ($costVariance > 10): ?>
                                    <li><i class="fas fa-dollar-sign text-danger me-2"></i>مراجعة تقديرات التكلفة</li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // رسم بياني للإنتاج اليومي
        const dailyCtx = document.getElementById('dailyProductionChart').getContext('2d');
        new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: [<?php echo implode(',', array_map(function($d) { return "'" . date('d/m', strtotime($d['production_date'])) . "'"; }, array_reverse($dailyProduction))); ?>],
                datasets: [{
                    label: 'الكمية المنتجة',
                    data: [<?php echo implode(',', array_map(function($d) { return $d['total_produced']; }, array_reverse($dailyProduction))); ?>],
                    borderColor: '#6f42c1',
                    backgroundColor: 'rgba(111, 66, 193, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // رسم بياني للمنتجات الأكثر إنتاجاً
        const productsCtx = document.getElementById('topProductsChart').getContext('2d');
        new Chart(productsCtx, {
            type: 'doughnut',
            data: {
                labels: [<?php echo implode(',', array_map(function($p) { return "'" . htmlspecialchars($p['product_name']) . "'"; }, array_slice($topProducts, 0, 5))); ?>],
                datasets: [{
                    data: [<?php echo implode(',', array_map(function($p) { return $p['total_produced']; }, array_slice($topProducts, 0, 5))); ?>],
                    backgroundColor: [
                        '#6f42c1',
                        '#e83e8c',
                        '#28a745',
                        '#ffc107',
                        '#17a2b8'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
