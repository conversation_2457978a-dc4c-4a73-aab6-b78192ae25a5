Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' لوحة التحكم الرئيسية - Dashboard Control
''' </summary>
Public Class DashboardControl
    Inherits UserControl
    
#Region "Fields"
    
    Private _context As DebtContext
    Private _currentUser As User
    
    ' البطاقات الإحصائية
    Private _cardCustomers As Panel
    Private _cardSuppliers As Panel
    Private _cardTransactions As Panel
    Private _cardDebts As Panel
    Private _cardCashBoxes As Panel
    Private _cardBalance As Panel
    
    ' الرسوم البيانية
    Private _chartPanel As Panel
    Private _recentTransactionsPanel As Panel
    Private _overdueDebtsPanel As Panel
    
    ' التحديث التلقائي
    Private _refreshTimer As Timer
    
#End Region

#Region "Constructor"
    
    ''' <summary>
    ''' منشئ لوحة التحكم
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser
        
        InitializeComponent()
        SetupDashboard()
        LoadDashboardData()
        SetupAutoRefresh()
    End Sub
    
#End Region

#Region "Setup Methods"
    
    ''' <summary>
    ''' إعداد لوحة التحكم
    ''' </summary>
    Private Sub SetupDashboard()
        ' إعدادات التحكم الأساسية
        Me.Size = New Size(1200, 800)
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.AutoScroll = True
        
        ' إنشاء العنوان الرئيسي
        CreateMainTitle()
        
        ' إنشاء البطاقات الإحصائية
        CreateStatisticsCards()
        
        ' إنشاء الرسوم البيانية
        CreateChartsSection()
        
        ' إنشاء قسم المعاملات الأخيرة
        CreateRecentTransactionsSection()
        
        ' إنشاء قسم الديون المستحقة
        CreateOverdueDebtsSection()
    End Sub
    
    ''' <summary>
    ''' إنشاء العنوان الرئيسي
    ''' </summary>
    Private Sub CreateMainTitle()
        Dim titlePanel As New Panel With {
            .Size = New Size(Me.Width - 40, 80),
            .Location = New Point(20, 20),
            .BackColor = Color.Transparent
        }
        
        Dim lblWelcome As New Label With {
            .Text = $"مرحباً بك، {_currentUser.FullName}",
            .Font = New Font("Segoe UI", 24, FontStyle.Bold),
            .ForeColor = Color.FromArgb(33, 37, 41),
            .Size = New Size(600, 40),
            .Location = New Point(0, 0),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        Dim lblDate As New Label With {
            .Text = DateTime.Now.ToString("dddd، dd MMMM yyyy", New Globalization.CultureInfo("ar-SA")),
            .Font = New Font("Segoe UI", 12, FontStyle.Regular),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Size = New Size(400, 25),
            .Location = New Point(0, 45),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        titlePanel.Controls.AddRange({lblWelcome, lblDate})
        Me.Controls.Add(titlePanel)
    End Sub
    
    ''' <summary>
    ''' إنشاء البطاقات الإحصائية
    ''' </summary>
    Private Sub CreateStatisticsCards()
        Dim cardsPanel As New Panel With {
            .Size = New Size(Me.Width - 40, 200),
            .Location = New Point(20, 120),
            .BackColor = Color.Transparent
        }
        
        ' بطاقة العملاء
        _cardCustomers = CreateStatCard("العملاء", "0", Color.FromArgb(40, 167, 69), "fas fa-users", 0)
        
        ' بطاقة الموردين
        _cardSuppliers = CreateStatCard("الموردين", "0", Color.FromArgb(23, 162, 184), "fas fa-truck", 200)
        
        ' بطاقة المعاملات
        _cardTransactions = CreateStatCard("المعاملات", "0", Color.FromArgb(255, 193, 7), "fas fa-exchange-alt", 400)
        
        ' بطاقة الديون
        _cardDebts = CreateStatCard("الديون", "0", Color.FromArgb(220, 53, 69), "fas fa-file-invoice-dollar", 600)
        
        ' بطاقة الصناديق
        _cardCashBoxes = CreateStatCard("الصناديق", "0", Color.FromArgb(102, 16, 242), "fas fa-cash-register", 800)
        
        ' بطاقة الرصيد الإجمالي
        _cardBalance = CreateStatCard("الرصيد الإجمالي", "0", Color.FromArgb(13, 110, 253), "fas fa-wallet", 1000)
        
        cardsPanel.Controls.AddRange({_cardCustomers, _cardSuppliers, _cardTransactions, _cardDebts, _cardCashBoxes, _cardBalance})
        Me.Controls.Add(cardsPanel)
    End Sub
    
    ''' <summary>
    ''' إنشاء بطاقة إحصائية
    ''' </summary>
    Private Function CreateStatCard(title As String, value As String, color As Color, icon As String, x As Integer) As Panel
        Dim card As New Panel With {
            .Size = New Size(180, 160),
            .Location = New Point(x, 20),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.None
        }
        
        ' إضافة ظل للبطاقة
        AddHandler card.Paint, Sub(sender, e)
                                   Dim shadowBrush As New SolidBrush(Color.FromArgb(30, 0, 0, 0))
                                   e.Graphics.FillRectangle(shadowBrush, 3, 3, card.Width, card.Height)
                                   
                                   Dim cardBrush As New SolidBrush(card.BackColor)
                                   e.Graphics.FillRectangle(cardBrush, 0, 0, card.Width - 3, card.Height - 3)
                                   
                                   shadowBrush.Dispose()
                                   cardBrush.Dispose()
                               End Sub
        
        ' أيقونة البطاقة
        Dim iconPanel As New Panel With {
            .Size = New Size(60, 60),
            .Location = New Point(60, 20),
            .BackColor = color
        }
        
        Dim lblIcon As New Label With {
            .Text = "📊", ' يمكن استبدالها بأيقونة FontAwesome
            .Font = New Font("Segoe UI", 24, FontStyle.Regular),
            .ForeColor = Color.White,
            .Size = New Size(60, 60),
            .TextAlign = ContentAlignment.MiddleCenter
        }
        iconPanel.Controls.Add(lblIcon)
        
        ' عنوان البطاقة
        Dim lblTitle As New Label With {
            .Text = title,
            .Font = New Font("Segoe UI", 12, FontStyle.Bold),
            .ForeColor = Color.FromArgb(73, 80, 87),
            .Size = New Size(160, 25),
            .Location = New Point(10, 90),
            .TextAlign = ContentAlignment.MiddleCenter
        }
        
        ' قيمة البطاقة
        Dim lblValue As New Label With {
            .Text = value,
            .Font = New Font("Segoe UI", 18, FontStyle.Bold),
            .ForeColor = color,
            .Size = New Size(160, 30),
            .Location = New Point(10, 115),
            .TextAlign = ContentAlignment.MiddleCenter,
            .Name = "Value"
        }
        
        card.Controls.AddRange({iconPanel, lblTitle, lblValue})
        
        ' إضافة تأثير الماوس
        AddHandler card.MouseEnter, Sub() card.BackColor = Color.FromArgb(248, 249, 250)
        AddHandler card.MouseLeave, Sub() card.BackColor = Color.White
        
        Return card
    End Function
    
    ''' <summary>
    ''' إنشاء قسم الرسوم البيانية
    ''' </summary>
    Private Sub CreateChartsSection()
        _chartPanel = New Panel With {
            .Size = New Size(Me.Width - 40, 300),
            .Location = New Point(20, 340),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim titleLabel As New Label With {
            .Text = "الرسم البياني للمعاملات الشهرية",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(33, 37, 41),
            .Size = New Size(400, 30),
            .Location = New Point(20, 20),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        ' يمكن إضافة رسم بياني هنا باستخدام مكتبة مثل Chart.js أو DevExpress Charts
        Dim chartPlaceholder As New Label With {
            .Text = "سيتم إضافة الرسم البياني هنا",
            .Font = New Font("Segoe UI", 12, FontStyle.Regular),
            .ForeColor = Color.FromArgb(108, 117, 125),
            .Size = New Size(Me.Width - 80, 200),
            .Location = New Point(20, 60),
            .TextAlign = ContentAlignment.MiddleCenter,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        _chartPanel.Controls.AddRange({titleLabel, chartPlaceholder})
        Me.Controls.Add(_chartPanel)
    End Sub
    
    ''' <summary>
    ''' إنشاء قسم المعاملات الأخيرة
    ''' </summary>
    Private Sub CreateRecentTransactionsSection()
        _recentTransactionsPanel = New Panel With {
            .Size = New Size((Me.Width - 60) / 2, 300),
            .Location = New Point(20, 660),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim titleLabel As New Label With {
            .Text = "المعاملات الأخيرة",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(33, 37, 41),
            .Size = New Size(200, 30),
            .Location = New Point(20, 20),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        Dim transactionsList As New ListView With {
            .Size = New Size(_recentTransactionsPanel.Width - 40, 220),
            .Location = New Point(20, 60),
            .View = View.Details,
            .FullRowSelect = True,
            .GridLines = True,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes,
            .RightToLeftLayout = True
        }
        
        ' إضافة أعمدة القائمة
        transactionsList.Columns.AddRange({
            New ColumnHeader With {.Text = "التاريخ", .Width = 80},
            New ColumnHeader With {.Text = "النوع", .Width = 60},
            New ColumnHeader With {.Text = "المبلغ", .Width = 80},
            New ColumnHeader With {.Text = "الوصف", .Width = 150}
        })
        
        _recentTransactionsPanel.Controls.AddRange({titleLabel, transactionsList})
        Me.Controls.Add(_recentTransactionsPanel)
    End Sub
    
    ''' <summary>
    ''' إنشاء قسم الديون المستحقة
    ''' </summary>
    Private Sub CreateOverdueDebtsSection()
        _overdueDebtsPanel = New Panel With {
            .Size = New Size((Me.Width - 60) / 2, 300),
            .Location = New Point((Me.Width - 40) / 2 + 40, 660),
            .BackColor = Color.White,
            .BorderStyle = BorderStyle.FixedSingle
        }
        
        Dim titleLabel As New Label With {
            .Text = "الديون المستحقة",
            .Font = New Font("Segoe UI", 14, FontStyle.Bold),
            .ForeColor = Color.FromArgb(220, 53, 69),
            .Size = New Size(200, 30),
            .Location = New Point(20, 20),
            .TextAlign = ContentAlignment.MiddleRight
        }
        
        Dim debtsList As New ListView With {
            .Size = New Size(_overdueDebtsPanel.Width - 40, 220),
            .Location = New Point(20, 60),
            .View = View.Details,
            .FullRowSelect = True,
            .GridLines = True,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes,
            .RightToLeftLayout = True
        }
        
        ' إضافة أعمدة القائمة
        debtsList.Columns.AddRange({
            New ColumnHeader With {.Text = "العميل/المورد", .Width = 120},
            New ColumnHeader With {.Text = "المبلغ", .Width = 80},
            New ColumnHeader With {.Text = "تاريخ الاستحقاق", .Width = 90},
            New ColumnHeader With {.Text = "الأيام المتأخرة", .Width = 80}
        })
        
        _overdueDebtsPanel.Controls.AddRange({titleLabel, debtsList})
        Me.Controls.Add(_overdueDebtsPanel)
    End Sub
    
    ''' <summary>
    ''' إعداد التحديث التلقائي
    ''' </summary>
    Private Sub SetupAutoRefresh()
        _refreshTimer = New Timer With {
            .Interval = 30000, ' 30 ثانية
            .Enabled = True
        }
        AddHandler _refreshTimer.Tick, AddressOf RefreshTimer_Tick
    End Sub
    
#End Region

#Region "Data Loading Methods"
    
    ''' <summary>
    ''' تحميل بيانات لوحة التحكم
    ''' </summary>
    Private Sub LoadDashboardData()
        Try
            LoadStatisticsData()
            LoadRecentTransactions()
            LoadOverdueDebts()
        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ''' <summary>
    ''' تحميل البيانات الإحصائية
    ''' </summary>
    Private Sub LoadStatisticsData()
        ' عدد العملاء
        Dim customersCount = _context.Customers.Count(Function(c) c.IsActive)
        UpdateCardValue(_cardCustomers, customersCount.ToString())
        
        ' عدد الموردين
        Dim suppliersCount = _context.Suppliers.Count(Function(s) s.IsActive)
        UpdateCardValue(_cardSuppliers, suppliersCount.ToString())
        
        ' عدد المعاملات هذا الشهر
        Dim thisMonth = DateTime.Now.Month
        Dim thisYear = DateTime.Now.Year
        Dim transactionsCount = _context.Transactions.Count(Function(t) t.TransactionDate.Month = thisMonth AndAlso t.TransactionDate.Year = thisYear)
        UpdateCardValue(_cardTransactions, transactionsCount.ToString())
        
        ' عدد الديون النشطة
        Dim activeDebtsCount = _context.Debts.Count(Function(d) d.Status = "Active")
        UpdateCardValue(_cardDebts, activeDebtsCount.ToString())
        
        ' عدد الصناديق
        Dim cashBoxesCount = _context.CashBoxes.Count(Function(cb) cb.IsActive)
        UpdateCardValue(_cardCashBoxes, cashBoxesCount.ToString())
        
        ' الرصيد الإجمالي
        Dim totalBalanceIQD = _context.CashBoxes.Where(Function(cb) cb.Currency.Code = "IQD").Sum(Function(cb) cb.CurrentBalance)
        Dim totalBalanceUSD = _context.CashBoxes.Where(Function(cb) cb.Currency.Code = "USD").Sum(Function(cb) cb.CurrentBalance)
        Dim balanceText = $"{totalBalanceIQD:N0} د.ع" & Environment.NewLine & $"${totalBalanceUSD:N2}"
        UpdateCardValue(_cardBalance, balanceText)
    End Sub
    
    ''' <summary>
    ''' تحديث قيمة البطاقة
    ''' </summary>
    Private Sub UpdateCardValue(card As Panel, value As String)
        Dim valueLabel = card.Controls.OfType(Of Label)().FirstOrDefault(Function(l) l.Name = "Value")
        If valueLabel IsNot Nothing Then
            valueLabel.Text = value
        End If
    End Sub
    
    ''' <summary>
    ''' تحميل المعاملات الأخيرة
    ''' </summary>
    Private Sub LoadRecentTransactions()
        Dim transactionsList = DirectCast(_recentTransactionsPanel.Controls.OfType(Of ListView)().FirstOrDefault(), ListView)
        If transactionsList IsNot Nothing Then
            transactionsList.Items.Clear()
            
            Dim recentTransactions = _context.Transactions _
                .OrderByDescending(Function(t) t.CreatedAt) _
                .Take(10) _
                .ToList()
            
            For Each transaction In recentTransactions
                Dim item As New ListViewItem(transaction.TransactionDate.ToString("dd/MM"))
                item.SubItems.Add(transaction.TypeInArabic)
                item.SubItems.Add(transaction.GetFormattedAmount())
                item.SubItems.Add(If(transaction.Description?.Length > 30, transaction.Description.Substring(0, 30) & "...", transaction.Description))
                
                ' تلوين الصفوف حسب نوع المعاملة
                If transaction.Type = "Income" Then
                    item.ForeColor = Color.FromArgb(40, 167, 69)
                Else
                    item.ForeColor = Color.FromArgb(220, 53, 69)
                End If
                
                transactionsList.Items.Add(item)
            Next
        End If
    End Sub
    
    ''' <summary>
    ''' تحميل الديون المستحقة
    ''' </summary>
    Private Sub LoadOverdueDebts()
        Dim debtsList = DirectCast(_overdueDebtsPanel.Controls.OfType(Of ListView)().FirstOrDefault(), ListView)
        If debtsList IsNot Nothing Then
            debtsList.Items.Clear()
            
            Dim overdueDebts = _context.Debts _
                .Where(Function(d) d.DueDate.HasValue AndAlso d.DueDate.Value < DateTime.Today AndAlso d.Status = "Active") _
                .OrderBy(Function(d) d.DueDate) _
                .Take(10) _
                .ToList()
            
            For Each debt In overdueDebts
                Dim entityName = GetEntityName(debt.EntityType, debt.EntityId)
                Dim daysOverdue = (DateTime.Today - debt.DueDate.Value).Days
                
                Dim item As New ListViewItem(entityName)
                item.SubItems.Add(debt.GetFormattedRemainingAmount())
                item.SubItems.Add(debt.DueDate.Value.ToString("dd/MM/yyyy"))
                item.SubItems.Add($"{daysOverdue} يوم")
                
                ' تلوين حسب شدة التأخير
                If daysOverdue > 30 Then
                    item.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
                ElseIf daysOverdue > 7 Then
                    item.ForeColor = Color.FromArgb(255, 193, 7) ' أصفر
                Else
                    item.ForeColor = Color.FromArgb(255, 152, 0) ' برتقالي
                End If
                
                debtsList.Items.Add(item)
            Next
        End If
    End Sub
    
    ''' <summary>
    ''' الحصول على اسم الكيان (عميل أو مورد)
    ''' </summary>
    Private Function GetEntityName(entityType As String, entityId As Integer) As String
        Try
            If entityType = "Customer" Then
                Dim customer = _context.Customers.Find(entityId)
                Return customer?.Name ?? "عميل غير معروف"
            ElseIf entityType = "Supplier" Then
                Dim supplier = _context.Suppliers.Find(entityId)
                Return supplier?.Name ?? "مورد غير معروف"
            End If
        Catch
            ' تجاهل الأخطاء
        End Try
        
        Return "غير محدد"
    End Function
    
#End Region

#Region "Event Handlers"
    
    ''' <summary>
    ''' تحديث البيانات تلقائياً
    ''' </summary>
    Private Sub RefreshTimer_Tick(sender As Object, e As EventArgs)
        LoadDashboardData()
    End Sub
    
    ''' <summary>
    ''' تنظيف الموارد
    ''' </summary>
    Protected Overrides Sub Dispose(disposing As Boolean)
        If disposing Then
            _refreshTimer?.Stop()
            _refreshTimer?.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub
    
#End Region

End Class
