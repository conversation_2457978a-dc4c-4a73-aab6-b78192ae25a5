<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();

// قائمة الصفحات المطلوب فحصها
$pages = [
    'dashboard.php' => 'لوحة التحكم',
    'employees.php' => 'الموظفين',
    'attendance.php' => 'الحضور والانصراف',
    'payroll.php' => 'الرواتب',
    'expenses.php' => 'المصاريف',
    'inventory.php' => 'المخزون',
    'add_item.php' => 'إضافة صنف',
    'sales.php' => 'المبيعات',
    'add_sale.php' => 'فاتورة مبيعات جديدة',
    'customers.php' => 'العملاء',
    'production.php' => 'الإنتاج',
    'financial.php' => 'التقارير المالية',
    'settings.php' => 'الإعدادات'
];

// فحص الجداول المطلوبة
$requiredTables = [
    'users' => 'المستخدمين',
    'departments' => 'الأقسام',
    'employees' => 'الموظفين',
    'attendance' => 'الحضور',
    'payroll' => 'الرواتب',
    'general_expenses' => 'المصاريف العامة',
    'production_expenses' => 'مصاريف الإنتاج',
    'items' => 'الأصناف',
    'item_categories' => 'فئات الأصناف',
    'warehouses' => 'المخازن',
    'customers' => 'العملاء',
    'sales_invoices' => 'فواتير المبيعات',
    'sales_invoice_details' => 'تفاصيل فواتير المبيعات',
    'currencies' => 'العملات'
];

$diagnosis = [];

// فحص الملفات
foreach ($pages as $file => $name) {
    $filePath = __DIR__ . '/' . $file;
    $diagnosis['files'][$file] = [
        'name' => $name,
        'exists' => file_exists($filePath),
        'readable' => file_exists($filePath) && is_readable($filePath),
        'size' => file_exists($filePath) ? filesize($filePath) : 0
    ];
}

// فحص الجداول
foreach ($requiredTables as $table => $name) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        $exists = !empty($result);

        $count = 0;
        if ($exists) {
            try {
                $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM $table");
                $count = $countResult['count'] ?? 0;
            } catch (Exception $e) {
                $count = 'خطأ';
            }
        }

        $diagnosis['tables'][$table] = [
            'name' => $name,
            'exists' => $exists,
            'count' => $count
        ];
    } catch (Exception $e) {
        $diagnosis['tables'][$table] = [
            'name' => $name,
            'exists' => false,
            'count' => 'خطأ',
            'error' => $e->getMessage()
        ];
    }
}

// فحص الكلاسات المطلوبة
$requiredClasses = [
    '../classes/Auth.php' => 'نظام المصادقة',
    '../classes/Helper.php' => 'الوظائف المساعدة',
    '../config/database.php' => 'قاعدة البيانات'
];

foreach ($requiredClasses as $file => $name) {
    $filePath = __DIR__ . '/' . $file;
    $diagnosis['classes'][$file] = [
        'name' => $name,
        'exists' => file_exists($filePath),
        'readable' => file_exists($filePath) && is_readable($filePath)
    ];
}

// فحص إعدادات قاعدة البيانات
try {
    $dbTest = $db->fetchOne("SELECT 1 as test");
    $diagnosis['database'] = [
        'connected' => true,
        'error' => null
    ];
} catch (Exception $e) {
    $diagnosis['database'] = [
        'connected' => false,
        'error' => $e->getMessage()
    ];
}

// إحصائيات عامة
$stats = [
    'files_working' => count(array_filter($diagnosis['files'], function($f) { return $f['exists'] && $f['readable']; })),
    'files_total' => count($diagnosis['files']),
    'tables_working' => count(array_filter($diagnosis['tables'], function($t) { return $t['exists']; })),
    'tables_total' => count($diagnosis['tables']),
    'classes_working' => count(array_filter($diagnosis['classes'], function($c) { return $c['exists'] && $c['readable']; })),
    'classes_total' => count($diagnosis['classes'])
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .diagnosis-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .diagnosis-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .diagnosis-body {
            padding: 40px;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .diagnostic-card {
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
        .progress-good { background: #28a745; }
        .progress-warning { background: #ffc107; }
        .progress-bad { background: #dc3545; }
        .btn-purple {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            border: none;
            color: white;
        }
    </style>
</head>
<body>
    <div class="diagnosis-container">
        <div class="diagnosis-header">
            <h1><i class="fas fa-stethoscope fa-2x mb-3"></i><br>تشخيص شامل للنظام</h1>
            <p class="mb-0">فحص حالة جميع مكونات النظام والصفحات</p>
        </div>

        <div class="diagnosis-body">
            <!-- إحصائيات عامة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="progress-circle <?= $stats['files_working'] == $stats['files_total'] ? 'progress-good' : ($stats['files_working'] > $stats['files_total']/2 ? 'progress-warning' : 'progress-bad') ?> mx-auto mb-3">
                                <?= round(($stats['files_working']/$stats['files_total'])*100) ?>%
                            </div>
                            <h5>الملفات</h5>
                            <p class="mb-0"><?= $stats['files_working'] ?> من <?= $stats['files_total'] ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="progress-circle <?= $stats['tables_working'] == $stats['tables_total'] ? 'progress-good' : ($stats['tables_working'] > $stats['tables_total']/2 ? 'progress-warning' : 'progress-bad') ?> mx-auto mb-3">
                                <?= round(($stats['tables_working']/$stats['tables_total'])*100) ?>%
                            </div>
                            <h5>الجداول</h5>
                            <p class="mb-0"><?= $stats['tables_working'] ?> من <?= $stats['tables_total'] ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="progress-circle <?= $stats['classes_working'] == $stats['classes_total'] ? 'progress-good' : ($stats['classes_working'] > $stats['classes_total']/2 ? 'progress-warning' : 'progress-bad') ?> mx-auto mb-3">
                                <?= round(($stats['classes_working']/$stats['classes_total'])*100) ?>%
                            </div>
                            <h5>الكلاسات</h5>
                            <p class="mb-0"><?= $stats['classes_working'] ?> من <?= $stats['classes_total'] ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <div class="progress-circle <?= $diagnosis['database']['connected'] ? 'progress-good' : 'progress-bad' ?> mx-auto mb-3">
                                <?= $diagnosis['database']['connected'] ? '100' : '0' ?>%
                            </div>
                            <h5>قاعدة البيانات</h5>
                            <p class="mb-0"><?= $diagnosis['database']['connected'] ? 'متصلة' : 'غير متصلة' ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- حالة الملفات -->
            <div class="card diagnostic-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-file-code me-2"></i>حالة الملفات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الملف</th>
                                    <th>الاسم</th>
                                    <th>موجود</th>
                                    <th>قابل للقراءة</th>
                                    <th>الحجم</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diagnosis['files'] as $file => $info): ?>
                                    <tr>
                                        <td><code><?= $file ?></code></td>
                                        <td><?= $info['name'] ?></td>
                                        <td>
                                            <i class="fas fa-<?= $info['exists'] ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        </td>
                                        <td>
                                            <i class="fas fa-<?= $info['readable'] ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        </td>
                                        <td><?= $info['exists'] ? number_format($info['size']) . ' بايت' : '-' ?></td>
                                        <td>
                                            <?php if ($info['exists'] && $info['readable']): ?>
                                                <span class="badge bg-success">يعمل</span>
                                                <a href="<?= $file ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="badge bg-danger">لا يعمل</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- حالة الجداول -->
            <div class="card diagnostic-card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة الجداول</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الاسم</th>
                                    <th>موجود</th>
                                    <th>عدد السجلات</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diagnosis['tables'] as $table => $info): ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td><?= $info['name'] ?></td>
                                        <td>
                                            <i class="fas fa-<?= $info['exists'] ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        </td>
                                        <td><?= $info['exists'] ? $info['count'] : '-' ?></td>
                                        <td>
                                            <?php if ($info['exists']): ?>
                                                <span class="badge bg-success">موجود</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">مفقود</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- حالة الكلاسات -->
            <div class="card diagnostic-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-code me-2"></i>حالة الكلاسات</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>الملف</th>
                                    <th>الاسم</th>
                                    <th>موجود</th>
                                    <th>قابل للقراءة</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diagnosis['classes'] as $file => $info): ?>
                                    <tr>
                                        <td><code><?= $file ?></code></td>
                                        <td><?= $info['name'] ?></td>
                                        <td>
                                            <i class="fas fa-<?= $info['exists'] ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        </td>
                                        <td>
                                            <i class="fas fa-<?= $info['readable'] ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        </td>
                                        <td>
                                            <?php if ($info['exists'] && $info['readable']): ?>
                                                <span class="badge bg-success">يعمل</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">لا يعمل</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- أزرار الإصلاح -->
            <div class="text-center">
                <h5 class="mb-3">أدوات الإصلاح</h5>
                <div class="btn-group-vertical gap-2">
                    <a href="setup_employees.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-users me-2"></i>إعداد نظام الموظفين
                    </a>
                    <a href="setup_inventory.php" class="btn btn-success btn-lg">
                        <i class="fas fa-warehouse me-2"></i>إعداد نظام المخزون
                    </a>
                    <a href="setup_sales.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-shopping-cart me-2"></i>إعداد نظام المبيعات
                    </a>
                    <a href="setup_production.php" class="btn btn-info btn-lg">
                        <i class="fas fa-industry me-2"></i>إعداد نظام الإنتاج
                    </a>
                    <a href="fix_sales_constraints.php" class="btn btn-danger btn-lg">
                        <i class="fas fa-tools me-2"></i>إصلاح مشاكل المبيعات
                    </a>
                    <a href="complete_setup.php" class="btn btn-success btn-lg">
                        <i class="fas fa-rocket me-2"></i>الإعداد الكامل
                    </a>
                    <a href="create_missing_files.php" class="btn btn-purple btn-lg">
                        <i class="fas fa-file-medical me-2"></i>إنشاء الملفات المفقودة
                    </a>
                    <a href="emergency_setup.php" class="btn btn-dark btn-lg">
                        <i class="fas fa-first-aid me-2"></i>الإعداد الطارئ
                    </a>
                </div>
            </div>

            <?php if (!$diagnosis['database']['connected']): ?>
                <div class="alert alert-danger mt-4">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>مشكلة في قاعدة البيانات</h5>
                    <p><strong>الخطأ:</strong> <?= htmlspecialchars($diagnosis['database']['error']) ?></p>
                    <p>يرجى التحقق من إعدادات قاعدة البيانات في ملف <code>config/database.php</code></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
