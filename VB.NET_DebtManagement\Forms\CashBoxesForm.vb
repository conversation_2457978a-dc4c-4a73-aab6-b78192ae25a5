Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إدارة الصناديق - Cash Boxes Management Form
''' </summary>
Public Class CashBoxesForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User

    ' عناصر التحكم الرئيسية
    Private _toolStrip As ToolStrip
    Private _filterPanel As Panel
    Private _dataGridView As DataGridView
    Private _statusStrip As StatusStrip

    ' أزرار شريط الأدوات
    Private _btnAdd As ToolStripButton
    Private _btnEdit As ToolStripButton
    Private _btnDelete As ToolStripButton
    Private _btnRefresh As ToolStripButton
    Private _btnTransfer As ToolStripButton
    Private _btnStatement As ToolStripButton

    ' عناصر الفلترة
    Private _cmbCashBoxType As ComboBox
    Private _cmbCurrency As ComboBox
    Private _chkActiveOnly As CheckBox
    Private _btnFilter As Button
    Private _btnClearFilter As Button

    ' شريط الحالة
    Private _lblStatus As ToolStripStatusLabel
    Private _lblCount As ToolStripStatusLabel
    Private _lblTotalIQD As ToolStripStatusLabel
    Private _lblTotalUSD As ToolStripStatusLabel

    ' متغيرات أخرى
    Private _selectedCashBox As CashBox

#End Region

#Region "Constructor"

    ''' <summary>
    ''' منشئ نموذج إدارة الصناديق
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser

        InitializeComponent()
        SetupForm()
        SetupToolStrip()
        SetupFilterPanel()
        SetupDataGridView()
        SetupStatusStrip()
        LoadCashBoxes()
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "إدارة الصناديق"
        Me.Size = New Size(1200, 700)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.CashBoxIcon
    End Sub

    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolStrip()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }

        ' إنشاء الأزرار
        _btnAdd = New ToolStripButton With {
            .Text = "إضافة صندوق",
            .Image = My.Resources.AddIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "إضافة صندوق جديد"
        }

        _btnEdit = New ToolStripButton With {
            .Text = "تعديل",
            .Image = My.Resources.EditIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تعديل الصندوق المحدد",
            .Enabled = False
        }

        _btnDelete = New ToolStripButton With {
            .Text = "حذف",
            .Image = My.Resources.DeleteIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "حذف الصندوق المحدد",
            .Enabled = False
        }

        _btnRefresh = New ToolStripButton With {
            .Text = "تحديث",
            .Image = My.Resources.RefreshIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحديث قائمة الصناديق"
        }

        _btnTransfer = New ToolStripButton With {
            .Text = "تحويل أموال",
            .Image = My.Resources.TransferIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحويل أموال بين الصناديق",
            .Enabled = False
        }

        _btnStatement = New ToolStripButton With {
            .Text = "كشف حساب",
            .Image = My.Resources.StatementIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "عرض كشف حساب الصندوق",
            .Enabled = False
        }

        ' إضافة الأزرار لشريط الأدوات
        _toolStrip.Items.AddRange({
            _btnAdd,
            New ToolStripSeparator(),
            _btnEdit,
            _btnDelete,
            New ToolStripSeparator(),
            _btnRefresh,
            New ToolStripSeparator(),
            _btnTransfer,
            _btnStatement
        })

        ' ربط الأحداث
        AddHandler _btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler _btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler _btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler _btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler _btnTransfer.Click, AddressOf BtnTransfer_Click
        AddHandler _btnStatement.Click, AddressOf BtnStatement_Click

        Me.Controls.Add(_toolStrip)
    End Sub

    ''' <summary>
    ''' إعداد لوحة الفلترة
    ''' </summary>
    Private Sub SetupFilterPanel()
        _filterPanel = New Panel With {
            .Height = 60,
            .Dock = DockStyle.Top,
            .BackColor = Color.White,
            .Padding = New Padding(10)
        }

        ' نوع الصندوق
        Dim lblCashBoxType As New Label With {
            .Text = "نوع الصندوق:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(80, 25),
            .Location = New Point(Me.Width - 100, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbCashBoxType = New ComboBox With {
            .Size = New Size(120, 25),
            .Location = New Point(Me.Width - 230, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbCashBoxType.Items.AddRange({"الكل", "نقدي", "بنكي", "رقمي"})
        _cmbCashBoxType.SelectedIndex = 0

        ' العملة
        Dim lblCurrency As New Label With {
            .Text = "العملة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 25),
            .Location = New Point(Me.Width - 300, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbCurrency = New ComboBox With {
            .Size = New Size(100, 25),
            .Location = New Point(Me.Width - 410, 15),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbCurrency.Items.AddRange({"الكل", "دينار", "دولار"})
        _cmbCurrency.SelectedIndex = 0

        ' النشطة فقط
        _chkActiveOnly = New CheckBox With {
            .Text = "النشطة فقط",
            .Size = New Size(100, 25),
            .Location = New Point(Me.Width - 520, 15),
            .Checked = True,
            .Font = New Font("Segoe UI", 9, FontStyle.Regular)
        }

        ' أزرار الفلترة
        _btnFilter = New Button With {
            .Text = "فلترة",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 600, 13),
            .BackColor = Color.FromArgb(0, 123, 255),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnFilter.FlatAppearance.BorderSize = 0

        _btnClearFilter = New Button With {
            .Text = "مسح",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 680, 13),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnClearFilter.FlatAppearance.BorderSize = 0

        ' إضافة العناصر للوحة
        _filterPanel.Controls.AddRange({
            lblCashBoxType, _cmbCashBoxType,
            lblCurrency, _cmbCurrency,
            _chkActiveOnly,
            _btnFilter, _btnClearFilter
        })

        ' ربط الأحداث
        AddHandler _btnFilter.Click, AddressOf BtnFilter_Click
        AddHandler _btnClearFilter.Click, AddressOf BtnClearFilter_Click
        AddHandler _cmbCashBoxType.SelectedIndexChanged, AddressOf Filter_Changed
        AddHandler _cmbCurrency.SelectedIndexChanged, AddressOf Filter_Changed
        AddHandler _chkActiveOnly.CheckedChanged, AddressOf Filter_Changed

        Me.Controls.Add(_filterPanel)
    End Sub

    ''' <summary>
    ''' إعداد شبكة البيانات
    ''' </summary>
    Private Sub SetupDataGridView()
        _dataGridView = New DataGridView With {
            .Dock = DockStyle.Fill,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            .ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.FromArgb(52, 58, 64),
                .ForeColor = Color.White,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .ColumnHeadersHeight = 40,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.White,
                .ForeColor = Color.FromArgb(33, 37, 41),
                .Font = New Font("Segoe UI", 9, FontStyle.Regular),
                .SelectionBackColor = Color.FromArgb(0, 123, 255),
                .SelectionForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .EnableHeadersVisualStyles = False,
            .GridColor = Color.FromArgb(222, 226, 230),
            .RowHeadersVisible = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .MultiSelect = False,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .RightToLeft = RightToLeft.Yes
        }

        ' إضافة الأعمدة
        _dataGridView.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Id", .HeaderText = "المعرف", .Visible = False},
            New DataGridViewTextBoxColumn With {.Name = "Name", .HeaderText = "اسم الصندوق", .FillWeight = 20},
            New DataGridViewTextBoxColumn With {.Name = "CashBoxType", .HeaderText = "النوع", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Currency", .HeaderText = "العملة", .FillWeight = 8},
            New DataGridViewTextBoxColumn With {.Name = "InitialBalance", .HeaderText = "الرصيد الافتتاحي", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "CurrentBalance", .HeaderText = "الرصيد الحالي", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "MinimumBalance", .HeaderText = "الحد الأدنى", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "MaximumBalance", .HeaderText = "الحد الأقصى", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "BankName", .HeaderText = "البنك", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "AccountNumber", .HeaderText = "رقم الحساب", .FillWeight = 10}
        })

        ' ربط الأحداث
        AddHandler _dataGridView.SelectionChanged, AddressOf DataGridView_SelectionChanged
        AddHandler _dataGridView.CellDoubleClick, AddressOf DataGridView_CellDoubleClick
        AddHandler _dataGridView.CellFormatting, AddressOf DataGridView_CellFormatting

        Me.Controls.Add(_dataGridView)
    End Sub

    ''' <summary>
    ''' إعداد شريط الحالة
    ''' </summary>
    Private Sub SetupStatusStrip()
        _statusStrip = New StatusStrip With {
            .BackColor = Color.FromArgb(248, 249, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }

        _lblStatus = New ToolStripStatusLabel With {
            .Text = "جاهز",
            .Spring = True,
            .TextAlign = ContentAlignment.MiddleLeft
        }

        _lblCount = New ToolStripStatusLabel With {
            .Text = "عدد الصناديق: 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _lblTotalIQD = New ToolStripStatusLabel With {
            .Text = "الإجمالي (د.ع): 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _lblTotalUSD = New ToolStripStatusLabel With {
            .Text = "الإجمالي ($): 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _statusStrip.Items.AddRange({_lblStatus, _lblCount, _lblTotalIQD, _lblTotalUSD})
        Me.Controls.Add(_statusStrip)
    End Sub

#End Region

#Region "Data Methods"

    ''' <summary>
    ''' تحميل قائمة الصناديق
    ''' </summary>
    Private Sub LoadCashBoxes()
        Try
            _lblStatus.Text = "جاري تحميل البيانات..."
            Application.DoEvents()

            Dim query = _context.CashBoxes.Include("Currency").AsQueryable()

            ' تطبيق الفلاتر
            If _chkActiveOnly.Checked Then
                query = query.Where(Function(cb) cb.IsActive)
            End If

            If _cmbCashBoxType.Text <> "الكل" Then
                Dim typeFilter = GetCashBoxTypeFromArabic(_cmbCashBoxType.Text)
                query = query.Where(Function(cb) cb.CashBoxType = typeFilter)
            End If

            If _cmbCurrency.Text <> "الكل" Then
                Dim currencyFilter = If(_cmbCurrency.Text = "دينار", "IQD", "USD")
                query = query.Where(Function(cb) cb.Currency.Code = currencyFilter)
            End If

            Dim cashBoxes = query.OrderBy(Function(cb) cb.Name).ToList()

            ' مسح البيانات الحالية
            _dataGridView.Rows.Clear()

            ' متغيرات الإجماليات
            Dim totalIQD As Decimal = 0
            Dim totalUSD As Decimal = 0

            ' إضافة البيانات الجديدة
            For Each cashBox In cashBoxes
                Dim row As New DataGridViewRow()
                row.CreateCells(_dataGridView)

                row.Cells("Id").Value = cashBox.Id
                row.Cells("Name").Value = cashBox.Name
                row.Cells("CashBoxType").Value = cashBox.CashBoxTypeInArabic
                row.Cells("Currency").Value = cashBox.Currency?.Code
                row.Cells("InitialBalance").Value = cashBox.InitialBalance
                row.Cells("CurrentBalance").Value = cashBox.CurrentBalance
                row.Cells("MinimumBalance").Value = cashBox.MinimumBalance
                row.Cells("MaximumBalance").Value = cashBox.MaximumBalance
                row.Cells("BankName").Value = cashBox.BankName
                row.Cells("AccountNumber").Value = cashBox.AccountNumber

                row.Tag = cashBox
                _dataGridView.Rows.Add(row)

                ' حساب الإجماليات
                If cashBox.Currency?.Code = "IQD" Then
                    totalIQD += cashBox.CurrentBalance
                ElseIf cashBox.Currency?.Code = "USD" Then
                    totalUSD += cashBox.CurrentBalance
                End If
            Next

            ' تحديث شريط الحالة
            _lblCount.Text = $"عدد الصناديق: {cashBoxes.Count}"
            _lblTotalIQD.Text = $"الإجمالي (د.ع): {totalIQD:N0}"
            _lblTotalUSD.Text = $"الإجمالي ($): {totalUSD:N2}"
            _lblStatus.Text = "تم تحميل البيانات بنجاح"

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            _lblStatus.Text = "خطأ في تحميل البيانات"
        End Try
    End Sub

    ''' <summary>
    ''' تحويل نوع الصندوق من العربية
    ''' </summary>
    Private Function GetCashBoxTypeFromArabic(arabicType As String) As String
        Select Case arabicType
            Case "نقدي"
                Return "Cash"
            Case "بنكي"
                Return "Bank"
            Case "رقمي"
                Return "Digital"
            Case Else
                Return arabicType
        End Select
    End Function

    ''' <summary>
    ''' حذف صندوق
    ''' </summary>
    Private Sub DeleteCashBox(cashBox As CashBox)
        Try
            ' التحقق من وجود معاملات مرتبطة
            Dim hasTransactions = _context.Transactions.Any(Function(t) t.CashBoxId = cashBox.Id)

            If hasTransactions Then
                If MessageBox.Show("هذا الصندوق لديه معاملات مرتبطة. هل تريد إلغاء تفعيله بدلاً من حذفه؟",
                                 "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    cashBox.IsActive = False
                    cashBox.UpdatedAt = DateTime.Now
                    _context.SaveChanges()

                    MessageBox.Show("تم إلغاء تفعيل الصندوق بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCashBoxes()
                End If
            Else
                If MessageBox.Show($"هل أنت متأكد من حذف الصندوق '{cashBox.Name}'؟",
                                 "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                    _context.CashBoxes.Remove(cashBox)
                    _context.SaveChanges()

                    MessageBox.Show("تم حذف الصندوق بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadCashBoxes()
                End If
            End If

        Catch ex As Exception
            MessageBox.Show($"خطأ في حذف الصندوق: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' إضافة صندوق جديد
    ''' </summary>
    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim addForm As New CashBoxAddEditForm(_context, _currentUser)
        If addForm.ShowDialog() = DialogResult.OK Then
            LoadCashBoxes()
        End If
    End Sub

    ''' <summary>
    ''' تعديل الصندوق المحدد
    ''' </summary>
    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If _selectedCashBox IsNot Nothing Then
            Dim editForm As New CashBoxAddEditForm(_context, _currentUser, _selectedCashBox)
            If editForm.ShowDialog() = DialogResult.OK Then
                LoadCashBoxes()
            End If
        End If
    End Sub

    ''' <summary>
    ''' حذف الصندوق المحدد
    ''' </summary>
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If _selectedCashBox IsNot Nothing Then
            DeleteCashBox(_selectedCashBox)
        End If
    End Sub

    ''' <summary>
    ''' تحديث قائمة الصناديق
    ''' </summary>
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadCashBoxes()
    End Sub

    ''' <summary>
    ''' تحويل أموال بين الصناديق
    ''' </summary>
    Private Sub BtnTransfer_Click(sender As Object, e As EventArgs)
        If _selectedCashBox IsNot Nothing Then
            MessageBox.Show("سيتم تنفيذ ميزة تحويل الأموال قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    ''' <summary>
    ''' عرض كشف حساب الصندوق
    ''' </summary>
    Private Sub BtnStatement_Click(sender As Object, e As EventArgs)
        If _selectedCashBox IsNot Nothing Then
            MessageBox.Show("سيتم تنفيذ ميزة كشف الحساب قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    ''' <summary>
    ''' تطبيق الفلاتر
    ''' </summary>
    Private Sub BtnFilter_Click(sender As Object, e As EventArgs)
        LoadCashBoxes()
    End Sub

    ''' <summary>
    ''' مسح الفلاتر
    ''' </summary>
    Private Sub BtnClearFilter_Click(sender As Object, e As EventArgs)
        _cmbCashBoxType.SelectedIndex = 0
        _cmbCurrency.SelectedIndex = 0
        _chkActiveOnly.Checked = True
        LoadCashBoxes()
    End Sub

    ''' <summary>
    ''' تغيير الفلاتر
    ''' </summary>
    Private Sub Filter_Changed(sender As Object, e As EventArgs)
        LoadCashBoxes()
    End Sub

    ''' <summary>
    ''' تغيير التحديد في الشبكة
    ''' </summary>
    Private Sub DataGridView_SelectionChanged(sender As Object, e As EventArgs)
        If _dataGridView.SelectedRows.Count > 0 Then
            _selectedCashBox = DirectCast(_dataGridView.SelectedRows(0).Tag, CashBox)
            _btnEdit.Enabled = True
            _btnDelete.Enabled = True
            _btnTransfer.Enabled = True
            _btnStatement.Enabled = True
        Else
            _selectedCashBox = Nothing
            _btnEdit.Enabled = False
            _btnDelete.Enabled = False
            _btnTransfer.Enabled = False
            _btnStatement.Enabled = False
        End If
    End Sub

    ''' <summary>
    ''' النقر المزدوج لتعديل الصندوق
    ''' </summary>
    Private Sub DataGridView_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    ''' <summary>
    ''' تنسيق خلايا الشبكة
    ''' </summary>
    Private Sub DataGridView_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs)
        If e.Value IsNot Nothing Then
            Dim columnName = _dataGridView.Columns(e.ColumnIndex).Name
            Dim currency = _dataGridView.Rows(e.RowIndex).Cells("Currency").Value?.ToString()

            If {"InitialBalance", "CurrentBalance", "MinimumBalance", "MaximumBalance"}.Contains(columnName) Then
                Dim amount As Decimal = Convert.ToDecimal(e.Value)

                If currency = "IQD" Then
                    e.Value = $"{amount:N0} د.ع"
                ElseIf currency = "USD" Then
                    e.Value = $"${amount:N2}"
                Else
                    e.Value = amount.ToString("N2")
                End If

                ' تلوين الرصيد الحالي
                If columnName = "CurrentBalance" Then
                    If amount > 0 Then
                        e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69) ' أخضر
                    ElseIf amount < 0 Then
                        e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
                    End If
                End If
            End If
        End If
    End Sub

#End Region

End Class
