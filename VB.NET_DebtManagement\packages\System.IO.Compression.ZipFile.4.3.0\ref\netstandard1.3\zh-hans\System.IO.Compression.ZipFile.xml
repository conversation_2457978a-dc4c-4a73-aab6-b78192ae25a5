﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression.ZipFile</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.ZipFile">
      <summary>提供创建、解压缩和打开 zip 存档的静态方法。</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String)">
      <summary>创建 zip 存档，该存档包含指定目录的文件和目录。</summary>
      <param name="sourceDirectoryName">要存档的目录的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="destinationArchiveFileName">要生成的存档路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>创建 zip 存档，该存档包括指定目录的文件和目录，使用指定压缩级别，以及可以选择包含基目录。</summary>
      <param name="sourceDirectoryName">要存档的目录的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="destinationArchiveFileName">要生成的存档路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="compressionLevel">指示创建项时是否强调速度或压缩有效性的枚举值之一。</param>
      <param name="includeBaseDirectory">包括从在存档的根的 <paramref name="sourceDirectoryName" /> 的目录名称，则为 true；仅包含目录中的内容，则为 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean,System.Text.Encoding)">
      <summary>创建 zip 存档，该存档包括文件和指定目录的目录，使用指定压缩级别和条目名称的字符编码，以及可以选择包含基目录。</summary>
      <param name="sourceDirectoryName">要存档的目录的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="destinationArchiveFileName">要生成的存档路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="compressionLevel">指示创建项时是否强调速度或压缩有效性的枚举值之一。</param>
      <param name="includeBaseDirectory">包括从在存档的根的 <paramref name="sourceDirectoryName" /> 的目录名称，则为 true；仅包含目录中的内容，则为 false。</param>
      <param name="entryNameEncoding">在存档中读取或写入项名时使用的编码。仅当需要针对具有不支持项名的 UTF-8 编码的 zip 归档工具和库的互操作性进行编码时，为此参数指定一个值。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String)">
      <summary>将指定 zip 存档中的所有文件都解压缩到文件系统的一个目录下。</summary>
      <param name="sourceArchiveFileName">要解压缩存档的路径。</param>
      <param name="destinationDirectoryName">放置解压缩文件的目录的路径，指定为相对或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String,System.Text.Encoding)">
      <summary>将指定 zip 存档中的所有文件解压缩到文件系统的一目录下，并使用项名称的指定字符编码。</summary>
      <param name="sourceArchiveFileName">要解压缩存档的路径。</param>
      <param name="destinationDirectoryName">放置解压缩文件的目录的路径，指定为相对或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="entryNameEncoding">在存档中读取或写入项名时使用的编码。仅当需要针对具有不支持项名的 UTF-8 编码的 zip 归档工具和库的互操作性进行编码时，为此参数指定一个值。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode)">
      <summary>以指定的模式打开指定路径上的 zip 存档。</summary>
      <returns>打开的 zip 存档。</returns>
      <param name="archiveFileName">要打开的存档的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="mode">指定允许对打开的存档中的项进行的操作的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode,System.Text.Encoding)">
      <summary>在指定模式下，在指定路径中，使用项名称的指定字符编码打开 zip 存档。</summary>
      <returns>打开的 zip 存档。</returns>
      <param name="archiveFileName">要打开的存档的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <param name="mode">指定允许对打开的存档中的项进行的操作的枚举值之一。</param>
      <param name="entryNameEncoding">在存档中读取或写入项名时使用的编码。仅当需要针对具有不支持项名的 UTF-8 编码的 zip 归档工具和库的互操作性进行编码时，为此参数指定一个值。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.OpenRead(System.String)">
      <summary>打开在指定路径用于读取的 zip 存档。</summary>
      <returns>打开的 zip 存档。</returns>
      <param name="archiveFileName">要打开的存档的路径，指定为相对路径或绝对路径。相对路径是指相对于当前工作目录的路径。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipFileExtensions">
      <summary>为 <see cref="T:System.IO.Compression.ZipArchive" /> 和 <see cref="T:System.IO.Compression.ZipArchiveEntry" /> 类提供扩展方法。</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String)">
      <summary>通过压缩并将其添加到邮编存档的存档文件。</summary>
      <returns>zip 存档中新项的包装。</returns>
      <param name="destination">要添加该文件的 zip 存档。</param>
      <param name="sourceFileName">待存档的文件的路径。可以指定相对或绝对路径。相对路径被解释为相对于当前工作目录。</param>
      <param name="entryName">要在 zip 存档中生成的输入的名称。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 为 <see cref="F:System.String.Empty" />，仅包含空白，或包含至少一个无效字符。- 或 -<paramref name="entryName" /> 为 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="entryName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">在<paramref name="sourceFileName" />内，指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径不得超过 248 个字符，文件名不得超过 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">无法打开由 <paramref name="sourceFileName" /> 指定的文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> 指定目录。- 或 -调用方没有访问 <paramref name="sourceFileName" /> 指定的文件的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="sourceFileName" /> 指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 参数的格式无效。- 或 -zip 归档不支持写入。</exception>
      <exception cref="T:System.ObjectDisposedException">zip 存档已释放。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String,System.IO.Compression.CompressionLevel)">
      <summary>通过使用指定压缩级别压缩并将其添加到邮编存档的存档文件。</summary>
      <returns>zip 存档中新项的包装。</returns>
      <param name="destination">要添加该文件的 zip 存档。</param>
      <param name="sourceFileName">待存档的文件的路径。可以指定相对或绝对路径。相对路径被解释为相对于当前工作目录。</param>
      <param name="entryName">要在 zip 存档中生成的输入的名称。</param>
      <param name="compressionLevel">创建项时，指示是否要强调速度或压缩有效性的枚举值之一。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceFileName" /> 为 <see cref="F:System.String.Empty" />，仅包含空白，或包含至少一个无效字符。- 或 -<paramref name="entryName" /> 为 <see cref="F:System.String.Empty" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceFileName" /> 或 <paramref name="entryName" /> 为 null。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceFileName" /> 无效（例如，在未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.PathTooLongException">在<paramref name="sourceFileName" />内，指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径不得超过 248 个字符，文件名不得超过 260 个字符。</exception>
      <exception cref="T:System.IO.IOException">无法打开由 <paramref name="sourceFileName" /> 指定的文件。</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="sourceFileName" /> 指定目录。- 或 -调用方没有访问 <paramref name="sourceFileName" /> 指定的文件的权限。</exception>
      <exception cref="T:System.IO.FileNotFoundException">未找到 <paramref name="sourceFileName" /> 指定的文件。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceFileName" /> 参数的格式无效。- 或 -zip 归档不支持写入。</exception>
      <exception cref="T:System.ObjectDisposedException">zip 存档已释放。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToDirectory(System.IO.Compression.ZipArchive,System.String)">
      <summary>将 zip 存档中的所有文件都解压到文件系统的一目录下。</summary>
      <param name="source">从 zip 归档解压文件。</param>
      <param name="destinationDirectoryName">到放置解压缩文件的目录的路径。可以指定相对或绝对路径。相对路径被解释为相对于当前工作目录。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> 为 <see cref="F:System.String.Empty" />，仅包含空白，或包含至少一个无效字符。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定路径超过了系统定义的最大长度。例如，在基于 Windows 的平台上，路径不得超过 248 个字符，文件名不得超过 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationDirectoryName" /> 指定的目录已存在。- 或 -一个在存档中的输入名称是 <see cref="F:System.String.Empty" />，仅包含空白或包含至少一个无效字符。- 或 -从存档中提取条目将生成在 <paramref name="destinationDirectoryName" /> 指定的目录之外的一个文件。（例如，如果该输入名称包括父目录访问器，则这可能发生。）- 或 -存档中具有相同名称的两个或多个项。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方不具有所需权限以便写入到目标目录。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> 包含无效格式。</exception>
      <exception cref="T:System.IO.InvalidDataException">未能找到归档项或已损坏。- 或 -通过一种不支持的压缩方法，对一个存档条目进行压缩。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String)">
      <summary>将 zip 存档中的条目解压到文件下。</summary>
      <param name="source">从 zip 归档项解压文件。</param>
      <param name="destinationFileName">从输入的内容生成的文件的路径。可以指定相对或绝对路径。相对路径被解释为相对于当前工作目录。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 -<paramref name="destinationFileName" /> 指定目录。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径不得超过 248 个字符，文件名不得超过 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> 已存在。- 或 -发生了 I/O 错误。- 或 -该项当前为编写开放。- 或 -已从存档中删除的项。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有创建新文件所需的权限。</exception>
      <exception cref="T:System.IO.InvalidDataException">该输入从存档中缺失，或损坏且无法读取。- 或 -该输入已经通过使用一种不被支持的压缩方法压缩。</exception>
      <exception cref="T:System.ObjectDisposedException">已释放属于 zip 归档项。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> 的格式无效。- 或 -该 zip 归档项在 <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> 模式中打开的，不允许项检索。</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String,System.Boolean)">
      <summary>将 zip 存档中的条目解压到文件下，并可选择覆盖具有相同名称的现存文件。</summary>
      <param name="source">从 zip 归档项解压文件。</param>
      <param name="destinationFileName">从输入的内容生成的文件的路径。可以指定相对或绝对路径。相对路径被解释为相对于当前工作目录。</param>
      <param name="overwrite">如果覆盖带与目标文件同名的名称的现有文件，则为 true；否则为 false。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> 是一个零长度字符串，仅包含空白或者包含一个或多个由 <see cref="F:System.IO.Path.InvalidPathChars" /> 定义的无效字符。- 或 -<paramref name="destinationFileName" /> 指定目录。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationFileName" /> 为 null。</exception>
      <exception cref="T:System.IO.PathTooLongException">指定的路径、文件名或者两者都超出了系统定义的最大长度。例如，在基于 Windows 的平台上，路径不得超过 248 个字符，文件名不得超过 260 个字符。</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">指定的路径无效（例如，它位于未映射的驱动器上）。</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> 已存在，且 <paramref name="overwrite" /> 是 false。- 或 -发生了 I/O 错误。- 或 -该项当前为编写开放。- 或 -已从存档中删除的项。</exception>
      <exception cref="T:System.UnauthorizedAccessException">调用方没有创建新文件所需的权限。</exception>
      <exception cref="T:System.IO.InvalidDataException">该输入从存档中缺失或损坏且无法读取。- 或 -该输入已经通过使用一种不被支持的压缩方法压缩。</exception>
      <exception cref="T:System.ObjectDisposedException">已释放属于 zip 归档项。</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> 的格式无效。- 或 -该 zip 归档项在 <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> 模式中打开的，不允许项检索。</exception>
    </member>
  </members>
</doc>