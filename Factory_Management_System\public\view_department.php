<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$deptId = (int)($_GET['id'] ?? 0);
if (!$deptId) {
    header('Location: departments.php');
    exit;
}

// جلب بيانات القسم
try {
    $department = $db->fetchOne("
        SELECT d.*, 
               m.full_name as manager_name, m.phone as manager_phone, m.email as manager_email,
               p.name as parent_name,
               u.full_name as created_by_name
        FROM departments d
        LEFT JOIN employees m ON d.manager_id = m.id
        LEFT JOIN departments p ON d.parent_id = p.id
        LEFT JOIN users u ON d.created_by = u.id
        WHERE d.id = ?
    ", [$deptId]);
    
    if (!$department) {
        header('Location: departments.php');
        exit;
    }
    
    // جلب موظفي القسم
    $employees = $db->fetchAll("
        SELECT id, full_name, position, phone, email, hire_date, status
        FROM employees 
        WHERE department_id = ? 
        ORDER BY full_name
    ", [$deptId]);
    
    // جلب الأقسام الفرعية
    $subDepartments = $db->fetchAll("
        SELECT d.*, m.full_name as manager_name,
               (SELECT COUNT(*) FROM employees WHERE department_id = d.id AND status = 'active') as employee_count
        FROM departments d
        LEFT JOIN employees m ON d.manager_id = m.id
        WHERE d.parent_id = ?
        ORDER BY d.name
    ", [$deptId]);
    
    // إحصائيات القسم
    $stats = $db->fetchOne("
        SELECT 
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_employees,
            COUNT(CASE WHEN status = 'inactive' THEN 1 END) as inactive_employees,
            COUNT(CASE WHEN status = 'terminated' THEN 1 END) as terminated_employees,
            AVG(salary) as avg_salary,
            SUM(salary) as total_salary
        FROM employees 
        WHERE department_id = ?
    ", [$deptId]);
    
} catch (Exception $e) {
    $error = "خطأ في جلب بيانات القسم: " . $e->getMessage();
    $department = null;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل القسم - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .department-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin: 0 auto 1rem;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .employee-card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .status-badge {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #fff3cd; color: #856404; }
        .status-terminated { background: #f8d7da; color: #721c24; }
        
        .info-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <?php if (!$department): ?>
        <div class="container mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                القسم غير موجود أو حدث خطأ في جلب البيانات
                <br><a href="departments.php" class="btn btn-sm btn-primary mt-2">العودة إلى قائمة الأقسام</a>
            </div>
        </div>
    <?php else: ?>
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1><i class="fas fa-building me-3"></i><?= htmlspecialchars($department['name']) ?></h1>
                        <p class="mb-0"><?= htmlspecialchars($department['code']) ?></p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="departments.php" class="btn btn-light">
                            <i class="fas fa-arrow-right me-2"></i>العودة للأقسام
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <!-- معلومات القسم -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <div class="department-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            
                            <h4><?= htmlspecialchars($department['name']) ?></h4>
                            <p class="text-muted"><?= htmlspecialchars($department['code']) ?></p>
                            
                            <span class="badge bg-<?= $department['is_active'] ? 'success' : 'danger' ?> fs-6 mb-3">
                                <?= $department['is_active'] ? 'نشط' : 'غير نشط' ?>
                            </span>
                            
                            <?php if ($department['description']): ?>
                                <p class="text-muted"><?= htmlspecialchars($department['description']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="stats-card">
                        <h5>الموظفين النشطين</h5>
                        <div class="stats-number"><?= $stats['active_employees'] ?: 0 ?></div>
                    </div>

                    <div class="stats-card">
                        <h5>متوسط الراتب</h5>
                        <div class="stats-number"><?= number_format($stats['avg_salary'] ?: 0, 0) ?></div>
                        <small class="text-muted">د.ع</small>
                    </div>

                    <div class="stats-card">
                        <h5>إجمالي الرواتب</h5>
                        <div class="stats-number"><?= number_format($stats['total_salary'] ?: 0, 0) ?></div>
                        <small class="text-muted">د.ع</small>
                    </div>
                </div>

                <!-- تفاصيل القسم -->
                <div class="col-lg-8">
                    <!-- المعلومات التفصيلية -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات التفصيلية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>اسم القسم:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['name']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>كود القسم:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['code']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>المدير:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['manager_name'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>القسم الرئيسي:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['parent_name'] ?: 'قسم رئيسي') ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>الميزانية:</strong>
                                        <span class="float-end"><?= number_format($department['budget'], 2) ?> د.ع</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الموقع:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['location'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الهاتف:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['phone'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>البريد الإلكتروني:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['email'] ?: 'غير محدد') ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- موظفي القسم -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0"><i class="fas fa-users me-2"></i>موظفي القسم</h5>
                                <span class="badge bg-light text-dark"><?= count($employees) ?> موظف</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($employees)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">لا يوجد موظفين في هذا القسم</p>
                                </div>
                            <?php else: ?>
                                <div class="row">
                                    <?php foreach ($employees as $emp): ?>
                                        <div class="col-md-6">
                                            <div class="employee-card">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?= htmlspecialchars($emp['full_name']) ?></h6>
                                                        <p class="text-muted mb-1"><?= htmlspecialchars($emp['position']) ?></p>
                                                        <small class="text-muted">
                                                            <i class="fas fa-calendar me-1"></i>
                                                            <?= date('d/m/Y', strtotime($emp['hire_date'])) ?>
                                                        </small>
                                                    </div>
                                                    <span class="status-badge status-<?= $emp['status'] ?>">
                                                        <?php
                                                        $statusNames = ['active' => 'نشط', 'inactive' => 'غير نشط', 'terminated' => 'منتهي'];
                                                        echo $statusNames[$emp['status']];
                                                        ?>
                                                    </span>
                                                </div>
                                                <?php if ($emp['phone'] || $emp['email']): ?>
                                                    <div class="mt-2">
                                                        <?php if ($emp['phone']): ?>
                                                            <small class="text-muted me-3">
                                                                <i class="fas fa-phone me-1"></i><?= htmlspecialchars($emp['phone']) ?>
                                                            </small>
                                                        <?php endif; ?>
                                                        <?php if ($emp['email']): ?>
                                                            <small class="text-muted">
                                                                <i class="fas fa-envelope me-1"></i><?= htmlspecialchars($emp['email']) ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- الأقسام الفرعية -->
                    <?php if (!empty($subDepartments)): ?>
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-sitemap me-2"></i>الأقسام الفرعية</h5>
                                    <span class="badge bg-light text-dark"><?= count($subDepartments) ?> قسم</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($subDepartments as $subDept): ?>
                                        <div class="col-md-6">
                                            <div class="employee-card">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <a href="view_department.php?id=<?= $subDept['id'] ?>" class="text-decoration-none">
                                                                <?= htmlspecialchars($subDept['name']) ?>
                                                            </a>
                                                        </h6>
                                                        <p class="text-muted mb-1"><?= htmlspecialchars($subDept['code']) ?></p>
                                                        <?php if ($subDept['manager_name']): ?>
                                                            <small class="text-muted">
                                                                <i class="fas fa-user-tie me-1"></i>
                                                                <?= htmlspecialchars($subDept['manager_name']) ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <span class="badge bg-primary">
                                                        <?= $subDept['employee_count'] ?> موظف
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- معلومات النظام -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-cog me-2"></i>معلومات النظام</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>تاريخ الإنشاء:</strong>
                                        <span class="float-end"><?= date('d/m/Y H:i', strtotime($department['created_at'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>أنشئ بواسطة:</strong>
                                        <span class="float-end"><?= htmlspecialchars($department['created_by_name'] ?: 'غير محدد') ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
