# 🔧 حلول سريعة للمشاكل الشائعة

## ❌ مشكلة: "Could not find file .resx"

### الحل السريع:
```vb
' في جميع النماذج، استبدل مراجع الموارد بـ Nothing:

' في MainForm.vb:
Me.Icon = Nothing

' في جميع الأزرار:
_btnAdd.Image = Nothing
_btnEdit.Image = Nothing
_btnDelete.Image = Nothing
' ... إلخ
```

### أو إنشاء أيقونات بسيطة:
```vb
' أضف هذه الدالة في كل نموذج:
Private Function CreateSimpleIcon(color As Color, size As Size) As Bitmap
    Dim bmp As New Bitmap(size.Width, size.Height)
    Using g As Graphics = Graphics.FromImage(bmp)
        g.FillRectangle(New SolidBrush(color), 0, 0, size.Width, size.Height)
        g.DrawRectangle(Pens.Black, 0, 0, size.Width - 1, size.Height - 1)
    End Using
    Return bmp
End Function

' ثم استخدمها:
_btnAdd.Image = CreateSimpleIcon(Color.Green, New Size(16, 16))
_btnEdit.Image = CreateSimpleIcon(Color.Blue, New Size(16, 16))
_btnDelete.Image = CreateSimpleIcon(Color.Red, New Size(16, 16))
```

## ❌ مشكلة: "My.Resources not found"

### الحل:
```vb
' استبدل جميع مراجع My.Resources بـ:

' بدلاً من:
' Me.Icon = My.Resources.AppIcon

' استخدم:
Me.Icon = Nothing

' أو:
Try
    Me.Icon = My.Resources.AppIcon
Catch
    Me.Icon = Nothing
End Try
```

## ❌ مشكلة: "Entity Framework not found"

### الحل:
```powershell
# في Package Manager Console:
Install-Package EntityFramework -Version 6.4.4
Update-Package EntityFramework -Reinstall
```

## ❌ مشكلة: "Database connection failed"

### الحل:
```xml
<!-- في App.config، استبدل سلسلة الاتصال بـ: -->
<connectionStrings>
  <add name="DebtContext" 
       connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementSystem;Integrated Security=True;MultipleActiveResultSets=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

## ❌ مشكلة: "Namespace not found"

### الحل:
```vb
' أضف في بداية كل ملف:
Imports System.Data.Entity
Imports System.Linq
Imports System.Windows.Forms
Imports System.Drawing
Imports System.ComponentModel
```

## 🚀 تشغيل سريع بدون أخطاء:

### 1. إنشاء مشروع جديد:
```
File > New > Project > Visual Basic > Windows Forms App
اسم المشروع: DebtManagementSystem
Framework: .NET Framework 4.7.2
```

### 2. تثبيت Entity Framework:
```powershell
Install-Package EntityFramework -Version 6.4.4
```

### 3. إضافة الملفات:
- انسخ جميع ملفات .vb
- أضفها للمشروع: Add > Existing Item

### 4. تحديث App.config:
```xml
<connectionStrings>
  <add name="DebtContext" 
       connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=DebtManagementSystem;Integrated Security=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 5. إنشاء قاعدة البيانات:
- شغل ملف Database/CreateDatabase.sql في SQL Server

### 6. إزالة مراجع الموارد:
```vb
' في جميع النماذج، استبدل:
Me.Icon = My.Resources.SomeIcon
' بـ:
Me.Icon = Nothing
```

### 7. بناء وتشغيل:
```
Build > Build Solution
Debug > Start Debugging (F5)
```

## 🔑 بيانات الدخول:
```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 📞 إذا استمرت المشاكل:

### تحقق من:
1. ✅ تثبيت .NET Framework 4.7.2
2. ✅ تثبيت SQL Server أو SQL Server Express
3. ✅ تثبيت Entity Framework 6.4.4
4. ✅ إنشاء قاعدة البيانات
5. ✅ سلسلة الاتصال صحيحة

### رسائل خطأ شائعة وحلولها:

**"The type initializer for 'System.Data.Entity.Internal.AppConfig' threw an exception"**
```xml
<!-- أضف في App.config: -->
<runtime>
  <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
    <dependentAssembly>
      <assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
      <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
    </dependentAssembly>
  </assemblyBinding>
</runtime>
```

**"Login failed for user"**
```xml
<!-- استخدم Windows Authentication: -->
<add name="DebtContext" 
     connectionString="Data Source=.;Initial Catalog=DebtManagementSystem;Integrated Security=True" 
     providerName="System.Data.SqlClient" />
```

**"Cannot open database"**
```sql
-- أنشئ قاعدة البيانات أولاً:
CREATE DATABASE DebtManagementSystem;
```

---

**🎯 هدف: تشغيل النظام في أقل من 10 دقائق!**
