﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression.ZipFile</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.ZipFile">
      <summary>Предоставляет статические методы для создания, извлечения и открытия ZIP-архивов. </summary>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String)">
      <summary>Создает ZIP архив, содержащий файлы и каталоги из указанного каталога.</summary>
      <param name="sourceDirectoryName">Путь к архивируемому каталогу, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="destinationArchiveFileName">Путь создаваемого архива, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Создает ZIP-архив, содержащий файлы и каталоги из указанного каталога, использует указанный уровень сжатия и необязательно включает базовый каталог.</summary>
      <param name="sourceDirectoryName">Путь к архивируемому каталогу, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="destinationArchiveFileName">Путь создаваемого архива, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="compressionLevel">Одно из значений перечисления, указывающее, акцентировать ли внимание на скорости или эффективности сжатия при создании записи.</param>
      <param name="includeBaseDirectory">Значение true, чтобы включить имя каталога из параметра <paramref name="sourceDirectoryName" /> в корень архива; значение false, чтобы включать только содержимое этого каталога.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.CreateFromDirectory(System.String,System.String,System.IO.Compression.CompressionLevel,System.Boolean,System.Text.Encoding)">
      <summary>Создает ZIP-архив, содержащий файлы и каталоги из указанного каталога, использует указанный уровень сжатия и кодировку символов для имен записей и необязательно включает базовый каталог.</summary>
      <param name="sourceDirectoryName">Путь к архивируемому каталогу, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="destinationArchiveFileName">Путь создаваемого архива, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="compressionLevel">Одно из значений перечисления, указывающее, акцентировать ли внимание на скорости или эффективности сжатия при создании записи.</param>
      <param name="includeBaseDirectory">Значение true, чтобы включить имя каталога из параметра <paramref name="sourceDirectoryName" /> в корень архива; false — для включения только содержимого этого каталога.</param>
      <param name="entryNameEncoding">Кодирование, используемое при чтении или записи имен записей в этом архиве.Задайте значение для этого параметра, только если кодирование требуется для взаимодействия с инструментами и библиотеками ZIP-архива, которые не поддерживают кодирование UTF-8 для имен записей.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="sourceDirectoryName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationArchiveFileName" /> already exists.-or-A file in the specified directory could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="destinationArchiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the directory specified in <paramref name="sourceDirectoryName" /> or the file specified in <paramref name="destinationArchiveFileName" />.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="sourceDirectoryName" /> or <paramref name="destinationArchiveFileName" /> contains an invalid format.-or-The zip archive does not support writing.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String)">
      <summary>Извлекает все файлы в указанном ZIP-архиве в каталогу в файловой системе.</summary>
      <param name="sourceArchiveFileName">Путь к архиву, который требуется извлечь.</param>
      <param name="destinationDirectoryName">Путь к каталогу, в котором следует поместить извлеченные файлы, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.ExtractToDirectory(System.String,System.String,System.Text.Encoding)">
      <summary>Извлекает все файлы в указанном ZIP-архиве к каталог в файловой системе и использует указанную кодировку для имен записей.</summary>
      <param name="sourceArchiveFileName">Путь к архиву, который требуется извлечь.</param>
      <param name="destinationDirectoryName">Путь к каталогу, в котором следует поместить извлеченные файлы, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="entryNameEncoding">Кодирование, используемое при чтении или записи имен записей в этом архиве.Задайте значение для этого параметра, только если кодирование требуется для взаимодействия с инструментами и библиотеками ZIP-архива, которые не поддерживают кодирование UTF-8 для имен записей.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">The specified path in <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> exceeds the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">The specified path is invalid (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">The directory specified by <paramref name="destinationDirectoryName" /> already exists.-or-The name of an entry in the archive is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-Extracting an archive entry would create a file that is outside the directory specified by <paramref name="destinationDirectoryName" />.(For example, this might happen if the entry name contains parent directory accessors.)-or-An archive entry to extract has the same name as an entry that has already been extracted from the same archive.</exception>
      <exception cref="T:System.UnauthorizedAccessException">The caller does not have the required permission to access the archive or the destination directory.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> or <paramref name="sourceArchiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="sourceArchiveFileName" /> was not found.</exception>
      <exception cref="T:System.IO.InvalidDataException">The archive specified by <paramref name="sourceArchiveFileName" /> is not a valid zip archive.-or-An archive entry was not found or was corrupt.-or-An archive entry was compressed by using a compression method that is not supported.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode)">
      <summary>Открывает ZIP-архив по указанному пути и в заданном режиме.</summary>
      <returns>Открытый ZIP-архив.</returns>
      <param name="archiveFileName">Путь к открываемому архиву, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="mode">Одно из значений перечисления, указывающее действия, которые разрешены над записями в открытом архиве.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.Open(System.String,System.IO.Compression.ZipArchiveMode,System.Text.Encoding)">
      <summary>Открывает ZIP-архив по указанному пути в указанном режиме и с использованием указанной кодировки символов для имен записей.</summary>
      <returns>Открытый ZIP-архив.</returns>
      <param name="archiveFileName">Путь к открываемому архиву, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="mode">Одно из значений перечисления, указывающее действия, которые разрешены над записями в открытом архиве.</param>
      <param name="entryNameEncoding">Кодирование, используемое при чтении или записи имен записей в этом архиве.Задайте значение для этого параметра, только если кодирование требуется для взаимодействия с инструментами и библиотеками ZIP-архива, которые не поддерживают кодирование UTF-8 для имен записей.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.-or-<paramref name="entryNameEncoding" /> is set to a Unicode encoding other than UTF-8.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.-or-<paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, but the file specified in <paramref name="archiveFileName" /> already exists.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> specifies an invalid value.</exception>
      <exception cref="T:System.IO.FileNotFoundException">
        <paramref name="mode" /> is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Read" />, but the file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is missing or corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />, but an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFile.OpenRead(System.String)">
      <summary>Открывает для чтения ZIP-архив по указанному пути.</summary>
      <returns>Открытый ZIP-архив.</returns>
      <param name="archiveFileName">Путь к открываемому архиву, заданный как относительный или абсолютный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="archiveFileName" /> is <see cref="F:System.String.Empty" />, contains only white space, or contains at least one invalid character.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="archiveFileName" /> is null.</exception>
      <exception cref="T:System.IO.PathTooLongException">In <paramref name="archiveFileName" />, the specified path, file name, or both exceed the system-defined maximum length.For example, on Windows-based platforms, paths must not exceed 248 characters, and file names must not exceed 260 characters.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">
        <paramref name="archiveFileName" /> is invalid or does not exist (for example, it is on an unmapped drive).</exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="archiveFileName" /> could not be opened.</exception>
      <exception cref="T:System.UnauthorizedAccessException">
        <paramref name="archiveFileName" /> specifies a directory.-or-The caller does not have the required permission to access the file specified in <paramref name="archiveFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">The file specified in <paramref name="archiveFileName" /> is not found.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="archiveFileName" /> contains an invalid format.</exception>
      <exception cref="T:System.IO.InvalidDataException">
        <paramref name="archiveFileName" /> could not be interpreted as a zip archive.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipFileExtensions">
      <summary>Предоставляет методы расширения для классов<see cref="T:System.IO.Compression.ZipArchive" /> и <see cref="T:System.IO.Compression.ZipArchiveEntry" />.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String)">
      <summary>Архивирует файл, сжимая его и добавляя его в ZIP-архив.</summary>
      <returns>Программа-оболочка для новой записи в ZIP-архиве.</returns>
      <param name="destination">ZIP-архив, в который добавляется файл.</param>
      <param name="sourceFileName">Путь к файлу, который необходимо заархивировать.Можно задавать абсолютный или относительный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="entryName">Имя записи, которую требуется создать в ZIP-архиве.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="sourceFileName" /> является <see cref="F:System.String.Empty" />, содержит только пробелы или хотя бы один недопустимый символ.-или-Параметр <paramref name="entryName" /> имеет значение <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="sourceFileName" /> или <paramref name="entryName" /> — null.</exception>
      <exception cref="T:System.IO.PathTooLongException">В <paramref name="sourceFileName" /> длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="sourceFileName" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.IOException">Не удается открыть файл, заданный параметром <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="sourceFileName" /> указывает каталог.-или-У вызывающего оператора отсутствует разрешение на доступ к файлу, указанному параметром <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="sourceFileName" />, не найден.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="sourceFileName" /> имеет недопустимый формат.-или-ZIP-архив не поддерживает запись.</exception>
      <exception cref="T:System.ObjectDisposedException">ZIP-архив был удален.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.CreateEntryFromFile(System.IO.Compression.ZipArchive,System.String,System.String,System.IO.Compression.CompressionLevel)">
      <summary>Архивирует файл, сжимая его с использованием заданного уровня сжатия и добавляя его в ZIP-архив.</summary>
      <returns>Программа-оболочка для новой записи в ZIP-архиве.</returns>
      <param name="destination">ZIP-архив, в который добавляется файл.</param>
      <param name="sourceFileName">Путь к файлу, который необходимо заархивировать.Можно задавать абсолютный или относительный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="entryName">Имя записи, которую требуется создать в ZIP-архиве.</param>
      <param name="compressionLevel">Одно из значений перечисления, указывающее, акцентировать ли внимание на скорости или эффективности сжатия при создании записи.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="sourceFileName" /> является <see cref="F:System.String.Empty" />, содержит только пробелы или хотя бы один недопустимый символ.-или-Параметр <paramref name="entryName" /> имеет значение <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="sourceFileName" /> или <paramref name="entryName" /> — null.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Параметр <paramref name="sourceFileName" /> недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.PathTooLongException">В <paramref name="sourceFileName" /> длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.IOException">Не удается открыть файл, заданный параметром <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Параметр <paramref name="sourceFileName" /> указывает каталог.-или-У вызывающего оператора отсутствует разрешение на доступ к файлу, указанному параметром <paramref name="sourceFileName" />.</exception>
      <exception cref="T:System.IO.FileNotFoundException">Файл, заданный параметром <paramref name="sourceFileName" />, не найден.</exception>
      <exception cref="T:System.NotSupportedException">Параметр <paramref name="sourceFileName" /> имеет недопустимый формат.-или-ZIP-архив не поддерживает запись.</exception>
      <exception cref="T:System.ObjectDisposedException">ZIP-архив был удален.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToDirectory(System.IO.Compression.ZipArchive,System.String)">
      <summary>Извлекает все файлы в ZIP-архиве в каталогу в файловой системе.</summary>
      <param name="source">ZIP-архив, из которого требуется извлечь файлы.</param>
      <param name="destinationDirectoryName">Путь к каталогу, в который требуется поместить извлеченные файлы.Можно задавать абсолютный или относительный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="destinationDirectoryName" /> является <see cref="F:System.String.Empty" />, содержит только пробелы или хотя бы один недопустимый символ.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="destinationDirectoryName" /> имеет значение null.</exception>
      <exception cref="T:System.IO.PathTooLongException">Указанная длина пути превышает максимальную длину, определенную в системе.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску).</exception>
      <exception cref="T:System.IO.IOException">Каталог, заданный параметром <paramref name="destinationDirectoryName" />, уже существует.-или-Имя записи в архиве имеет значение <see cref="F:System.String.Empty" />, содержит только пробелы или содержит по крайней мере один недопустимый символ.-или-Извлечение записи из архива создаст файл, который находится вне каталога, заданного <paramref name="destinationDirectoryName" />. (Например, это может произойти, если имя записи содержит методы доступа родительского каталога.) -или-Две или более записей в архиве имеют одинаковые имена.</exception>
      <exception cref="T:System.UnauthorizedAccessException">Вызывающий код не имеет необходимого разрешения на запись в целевом каталоге.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationDirectoryName" /> содержит недопустимый формат.</exception>
      <exception cref="T:System.IO.InvalidDataException">Не удалось найти запись архива или она повреждена.-или-Запись архива была сжата с помощью неподдерживаемого метода сжатия.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String)">
      <summary>Извлекает запись в ZIP-архиве в файлу.</summary>
      <param name="source">Запись ZIP-архива, из которой требуется извлечь файл.</param>
      <param name="destinationFileName">Путь к файлу, создаваемому из содержимого записи.Можно задавать абсолютный или относительный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или-Параметр <paramref name="destinationFileName" /> указывает каталог.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="destinationFileName" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> уже существует.-или- Произошла ошибка ввода-вывода.-или-Сущность открыта для записи в данный момент.-или-Сущность была удалена из архива.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего оператора отсутствует разрешение на создание нового файла.</exception>
      <exception cref="T:System.IO.InvalidDataException">Запись отсутствует в архиве или повреждена и не может быть прочитана.-или-Запись сжата с помощью неподдерживаемого метода сжатия.</exception>
      <exception cref="T:System.ObjectDisposedException">ZIP-архив, которому принадлежит запись, был удален.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> имеет недопустимый формат. -или-ZIP-архив для данной записи был открыт в режиме <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, который не допускает извлечение записей.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipFileExtensions.ExtractToFile(System.IO.Compression.ZipArchiveEntry,System.String,System.Boolean)">
      <summary>Извлекает запись в ZIP-архиве в файлу и при необходимости перезаписывает существующий файл с тем же именем.</summary>
      <param name="source">Запись ZIP-архива, из которой требуется извлечь файл.</param>
      <param name="destinationFileName">Путь к файлу, создаваемому из содержимого записи.Можно задавать абсолютный или относительный путь.Относительный путь интерпретируется относительно текущего рабочего каталога.</param>
      <param name="overwrite">Значение true, чтобы перезаписать существующий файл с таким же именем, что и конечный файл; в противном случае — значение false.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destinationFileName" /> представляет собой строку нулевой длины, содержащую только пробелы или один или несколько недопустимых символов, как указано <see cref="F:System.IO.Path.InvalidPathChars" />.-или-Параметр <paramref name="destinationFileName" /> указывает каталог.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="destinationFileName" /> имеет значение null. </exception>
      <exception cref="T:System.IO.PathTooLongException">Длина указанного пути, имени файла или обоих параметров превышает установленное в системе максимальное значение.Например, для платформ на основе Windows длина пути не должна превышать 248 символов, а имена файлов не должны содержать более 260 символов.</exception>
      <exception cref="T:System.IO.DirectoryNotFoundException">Указанный путь недопустим (например, он соответствует неподключенному диску). </exception>
      <exception cref="T:System.IO.IOException">
        <paramref name="destinationFileName" /> уже существует, и <paramref name="overwrite" /> имеет значение false.-или- Произошла ошибка ввода-вывода.-или-Сущность открыта для записи в данный момент.-или-Сущность была удалена из архива.</exception>
      <exception cref="T:System.UnauthorizedAccessException">У вызывающего оператора отсутствует разрешение на создание нового файла.</exception>
      <exception cref="T:System.IO.InvalidDataException">Запись отсутствует в архиве или повреждена и не может быть прочитана.-или-Запись сжата с помощью неподдерживаемого метода сжатия.</exception>
      <exception cref="T:System.ObjectDisposedException">ZIP-архив, которому принадлежит запись, был удален.</exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="destinationFileName" /> имеет недопустимый формат. -или-ZIP-архив для данной записи был открыт в режиме <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />, который не допускает извлечение записей.</exception>
    </member>
  </members>
</doc>