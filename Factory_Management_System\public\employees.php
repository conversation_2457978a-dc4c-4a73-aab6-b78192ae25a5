<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$department = $_GET['department'] ?? '';
$position = $_GET['position'] ?? '';
$status = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$sql = "SELECT e.*, d.name as department_name
        FROM employees e
        LEFT JOIN departments d ON e.department_id = d.id
        WHERE 1=1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (e.full_name LIKE :search OR e.employee_code LIKE :search OR e.email LIKE :search OR e.phone LIKE :search)";
    $params['search'] = "%$search%";
}

if (!empty($department)) {
    $sql .= " AND e.department_id = :department";
    $params['department'] = $department;
}

if (!empty($position)) {
    $sql .= " AND e.position LIKE :position";
    $params['position'] = "%$position%";
}

if (!empty($status)) {
    $sql .= " AND e.status = :status";
    $params['status'] = $status;
}

$sql .= " ORDER BY e.full_name LIMIT :limit OFFSET :offset";
$params['limit'] = $limit;
$params['offset'] = $offset;

try {
    // التحقق من وجود الجداول
    if (!$db->fetchOne("SHOW TABLES LIKE 'employees'")) {
        $employees = [];
        $totalEmployees = 0;
        $totalPages = 0;
        $departments = [];
        $error = "جداول الموظفين غير موجودة. يرجى إعداد قاعدة البيانات أولاً.";
    } else {
        $employees = $db->fetchAll($sql, $params);
        
        // عدد النتائج الإجمالي
        $countSql = str_replace("SELECT e.*, d.name as department_name", "SELECT COUNT(*) as count", $sql);
        $countSql = str_replace("ORDER BY e.full_name LIMIT :limit OFFSET :offset", "", $countSql);
        unset($params['limit'], $params['offset']);
        $totalEmployees = $db->fetchOne($countSql, $params)['count'] ?? 0;
        $totalPages = ceil($totalEmployees / $limit);
        
        // الأقسام للفلترة
        $departments = $db->fetchAll("SELECT * FROM departments WHERE is_active = 1 ORDER BY name");
    }
} catch (Exception $e) {
    $employees = [];
    $totalEmployees = 0;
    $totalPages = 0;
    $departments = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}

// إحصائيات سريعة
$stats = [
    'total_employees' => $totalEmployees,
    'active_employees' => count(array_filter($employees, function($emp) { return $emp['status'] == 'active'; })),
    'departments_count' => count($departments),
    'avg_salary' => $totalEmployees > 0 ? array_sum(array_column($employees, 'salary')) / $totalEmployees : 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموظفين - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .table th { background-color: #f8f9fa; }
        .status-active { background-color: #198754; }
        .status-inactive { background-color: #6c757d; }
        .status-suspended { background-color: #dc3545; }
        .employee-avatar { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($error)): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <br><a href="setup.php" class="btn btn-sm btn-primary mt-2">إعداد قاعدة البيانات</a>
            </div>
        <?php endif; ?>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>إجمالي الموظفين</h6>
                                <h3><?= number_format($stats['total_employees']) ?></h3>
                            </div>
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>الموظفين النشطين</h6>
                                <h3><?= number_format($stats['active_employees']) ?></h3>
                            </div>
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>عدد الأقسام</h6>
                                <h3><?= number_format($stats['departments_count']) ?></h3>
                            </div>
                            <i class="fas fa-building fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card bg-warning text-dark">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>متوسط الراتب</h6>
                                <h3><?= formatMoney($stats['avg_salary']) ?></h3>
                            </div>
                            <i class="fas fa-money-bill fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>إدارة الموظفين</h5>
                <div>
                    <a href="add_employee.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                    </a>
                    <a href="departments.php" class="btn btn-info">
                        <i class="fas fa-building me-2"></i>إدارة الأقسام
                    </a>
                    <a href="payroll.php" class="btn btn-success">
                        <i class="fas fa-money-bill me-2"></i>الرواتب
                    </a>
                    <a href="attendance.php" class="btn btn-warning">
                        <i class="fas fa-clock me-2"></i>الحضور والانصراف
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- فلاتر البحث -->
                <form method="GET" class="row g-3 mb-4">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="الاسم، الكود، البريد، الهاتف...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">القسم</label>
                        <select class="form-select" name="department">
                            <option value="">جميع الأقسام</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?= $dept['id'] ?>" <?= $department == $dept['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dept['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">المنصب</label>
                        <input type="text" class="form-control" name="position" value="<?= htmlspecialchars($position) ?>" placeholder="المنصب...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="active" <?= $status == 'active' ? 'selected' : '' ?>>نشط</option>
                            <option value="inactive" <?= $status == 'inactive' ? 'selected' : '' ?>>غير نشط</option>
                            <option value="suspended" <?= $status == 'suspended' ? 'selected' : '' ?>>موقوف</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="employees.php" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح
                            </a>
                        </div>
                    </div>
                </form>

                <!-- جدول الموظفين -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>كود الموظف</th>
                                <th>الاسم الكامل</th>
                                <th>القسم</th>
                                <th>المنصب</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>تاريخ التوظيف</th>
                                <th>الراتب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($employees)): ?>
                                <tr>
                                    <td colspan="11" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-3x mb-3"></i><br>
                                        لا توجد موظفين مطابقين للبحث
                                        <?php if (!isset($error)): ?>
                                            <br><a href="add_employee.php" class="btn btn-primary mt-2">إضافة أول موظف</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($employees as $employee): ?>
                                    <tr>
                                        <td>
                                            <?php if ($employee['photo']): ?>
                                                <img src="<?= htmlspecialchars($employee['photo']) ?>" class="employee-avatar" alt="صورة الموظف">
                                            <?php else: ?>
                                                <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center text-white">
                                                    <i class="fas fa-user"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><code><?= htmlspecialchars($employee['employee_code']) ?></code></td>
                                        <td>
                                            <strong><?= htmlspecialchars($employee['full_name']) ?></strong>
                                            <?php if ($employee['national_id']): ?>
                                                <br><small class="text-muted">هوية: <?= htmlspecialchars($employee['national_id']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= htmlspecialchars($employee['department_name'] ?? 'غير محدد') ?></td>
                                        <td><?= htmlspecialchars($employee['position']) ?></td>
                                        <td>
                                            <?php if ($employee['email']): ?>
                                                <a href="mailto:<?= htmlspecialchars($employee['email']) ?>">
                                                    <?= htmlspecialchars($employee['email']) ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($employee['phone']): ?>
                                                <a href="tel:<?= htmlspecialchars($employee['phone']) ?>">
                                                    <?= htmlspecialchars($employee['phone']) ?>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= formatDate($employee['hire_date']) ?></td>
                                        <td><?= formatMoney($employee['salary']) ?></td>
                                        <td>
                                            <?php
                                            $statusLabels = [
                                                'active' => ['نشط', 'status-active'],
                                                'inactive' => ['غير نشط', 'status-inactive'],
                                                'suspended' => ['موقوف', 'status-suspended']
                                            ];
                                            $statusInfo = $statusLabels[$employee['status']] ?? [$employee['status'], 'bg-secondary'];
                                            ?>
                                            <span class="badge <?= $statusInfo[1] ?>"><?= $statusInfo[0] ?></span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="view_employee.php?id=<?= $employee['id'] ?>" class="btn btn-outline-info" title="عرض">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_employee.php?id=<?= $employee['id'] ?>" class="btn btn-outline-primary" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="employee_salary.php?id=<?= $employee['id'] ?>" class="btn btn-outline-success" title="الراتب">
                                                    <i class="fas fa-money-bill"></i>
                                                </a>
                                                <a href="employee_attendance.php?id=<?= $employee['id'] ?>" class="btn btn-outline-warning" title="الحضور">
                                                    <i class="fas fa-clock"></i>
                                                </a>
                                                <div class="dropdown">
                                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="employee_documents.php?id=<?= $employee['id'] ?>">
                                                            <i class="fas fa-file me-2"></i>المستندات
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="employee_leaves.php?id=<?= $employee['id'] ?>">
                                                            <i class="fas fa-calendar-times me-2"></i>الإجازات
                                                        </a></li>
                                                        <li><a class="dropdown-item" href="employee_evaluations.php?id=<?= $employee['id'] ?>">
                                                            <i class="fas fa-star me-2"></i>التقييمات
                                                        </a></li>
                                                        <li><hr class="dropdown-divider"></li>
                                                        <li><a class="dropdown-item text-danger" href="deactivate_employee.php?id=<?= $employee['id'] ?>">
                                                            <i class="fas fa-user-times me-2"></i>إلغاء التفعيل
                                                        </a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&department=<?= urlencode($department) ?>&position=<?= urlencode($position) ?>&status=<?= urlencode($status) ?>">السابق</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&department=<?= urlencode($department) ?>&position=<?= urlencode($position) ?>&status=<?= urlencode($status) ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&department=<?= urlencode($department) ?>&position=<?= urlencode($position) ?>&status=<?= urlencode($status) ?>">التالي</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
