<?php
session_start();
require_once '../config/database.php';

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();

        // إنشاء جدول المستخدمين إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS users (
            id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) NOT NULL UNIQUE,
            email VARCHAR(100) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(200) NOT NULL,
            role ENUM('admin', 'manager', 'employee', 'accountant') DEFAULT 'employee',
            permissions JSON,
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // حذف المستخدم الافتراضي إذا كان موجوداً
        $db->query("DELETE FROM users WHERE username = 'admin'");

        // إضافة عمود permissions إذا لم يكن موجوداً
        try {
            $db->query("ALTER TABLE users ADD COLUMN permissions JSON");
        } catch (Exception $e) {
            // العمود موجود بالفعل
        }

        // إنشاء مستخدم افتراضي جديد
        $hashedPassword = password_hash('123456', PASSWORD_DEFAULT);
        $permissions = json_encode(['all' => true]); // صلاحيات كاملة للمدير

        $userData = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => $hashedPassword,
            'full_name' => 'مدير النظام',
            'role' => 'admin',
            'permissions' => $permissions,
            'is_active' => 1
        ];

        $db->insert('users', $userData);

        $db->commit();
        $success = 'تم إنشاء المستخدم الافتراضي بنجاح!';

    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إنشاء المستخدم: ' . $e->getMessage();
    }
}

// فحص حالة جدول المستخدمين
$tableExists = false;
$userCount = 0;
try {
    $result = $db->fetchOne("SHOW TABLES LIKE 'users'");
    $tableExists = !empty($result);

    if ($tableExists) {
        $count = $db->fetchOne("SELECT COUNT(*) as count FROM users");
        $userCount = $count['count'];
    }
} catch (Exception $e) {
    $tableExists = false;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشكلة تسجيل الدخول - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .fix-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .fix-body {
            padding: 40px;
        }
        .btn-fix {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
        .emergency {
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1 class="emergency"><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><br>إصلاح طارئ</h1>
            <p class="mb-0">إصلاح مشكلة تسجيل الدخول</p>
        </div>

        <div class="fix-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">بيانات تسجيل الدخول</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <strong>اسم المستخدم:</strong>
                                <div class="bg-light p-2 rounded mt-1">
                                    <code>admin</code>
                                </div>
                            </div>
                            <div class="col-6">
                                <strong>كلمة المرور:</strong>
                                <div class="bg-light p-2 rounded mt-1">
                                    <code>123456</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <a href="login.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                    <a href="dashboard.php" class="btn btn-success btn-lg">
                        <i class="fas fa-home me-2"></i>لوحة التحكم
                    </a>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <!-- حالة النظام -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>حالة النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <i class="fas fa-<?= $tableExists ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-2x mb-2"></i>
                                <h6>جدول المستخدمين</h6>
                                <small><?= $tableExists ? 'موجود' : 'مفقود' ?></small>
                            </div>
                            <div class="col-6 text-center">
                                <i class="fas fa-<?= $userCount > 0 ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-2x mb-2"></i>
                                <h6>المستخدمين</h6>
                                <small><?= $userCount ?> مستخدم</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <h5><i class="fas fa-tools me-2"></i>ما سيتم إصلاحه:</h5>
                    <ul class="mb-0">
                        <li>إنشاء جدول المستخدمين إذا لم يكن موجوداً</li>
                        <li>إنشاء مستخدم افتراضي للدخول</li>
                        <li>اسم المستخدم: <strong>admin</strong></li>
                        <li>كلمة المرور: <strong>123456</strong></li>
                        <li>الدور: <strong>مدير النظام</strong></li>
                    </ul>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-fix btn-lg me-3">
                            <i class="fas fa-tools me-2"></i>إصلاح مشكلة تسجيل الدخول
                        </button>
                    </form>

                    <a href="login.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>صفحة تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>

            <!-- معلومات إضافية -->
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-lightbulb me-2"></i>نصائح مهمة:</h6>
                <ul class="mb-0">
                    <li>تأكد من تشغيل خادم قاعدة البيانات (MySQL/MariaDB)</li>
                    <li>تأكد من صحة إعدادات قاعدة البيانات في ملف config.php</li>
                    <li>بعد الإصلاح، يمكنك تغيير كلمة المرور من إعدادات الملف الشخصي</li>
                    <li>يمكنك إنشاء مستخدمين جدد من لوحة التحكم</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
