<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول فئات الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES item_categories(id)
        )");
        
        // إنشاء جدول الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES item_categories(id),
            FOREIGN KEY (currency_id) REFERENCES currencies(id)
        )");
        
        // إنشاء جدول المخازن
        $db->query("CREATE TABLE IF NOT EXISTS warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (manager_id) REFERENCES users(id)
        )");
        
        // إنشاء جدول حركات المخزون
        $db->query("CREATE TABLE IF NOT EXISTS stock_movements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            movement_type ENUM('in', 'out', 'transfer_in', 'transfer_out', 'adjustment') NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_cost DECIMAL(10,2) DEFAULT 0.00,
            reference_type ENUM('sales_invoice', 'purchase_invoice', 'production_order', 'adjustment', 'transfer'),
            reference_id INT,
            movement_date DATE NOT NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (item_id) REFERENCES items(id),
            FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
            FOREIGN KEY (created_by) REFERENCES users(id)
        )");
        
        // إنشاء جدول أرصدة المخزون
        $db->query("CREATE TABLE IF NOT EXISTS stock_balances (
            id INT PRIMARY KEY AUTO_INCREMENT,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) DEFAULT 0.000,
            average_cost DECIMAL(10,2) DEFAULT 0.00,
            last_movement_date DATE,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_item_warehouse (item_id, warehouse_id),
            FOREIGN KEY (item_id) REFERENCES items(id),
            FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
        )");
        
        // إضافة فئات الأصناف الافتراضية
        $categories = [
            ['مواد خام', 'المواد الأولية المستخدمة في الإنتاج'],
            ['منتجات نهائية', 'المنتجات الجاهزة للبيع'],
            ['منتجات نصف مصنعة', 'منتجات في مراحل التصنيع'],
            ['مواد استهلاكية', 'مواد مساعدة ومكتبية'],
            ['قطع غيار', 'قطع الغيار والصيانة'],
            ['مواد تعبئة وتغليف', 'مواد التعبئة والتغليف']
        ];
        
        foreach ($categories as $category) {
            $existing = $db->fetchOne("SELECT id FROM item_categories WHERE name = ?", [$category[0]]);
            if (!$existing) {
                $db->query("INSERT INTO item_categories (name, description) VALUES (?, ?)", $category);
            }
        }
        
        // إضافة المخازن الافتراضية
        $warehouses = [
            ['المخزن الرئيسي', 'المبنى الرئيسي - الطابق الأرضي'],
            ['مخزن المواد الخام', 'المبنى الإنتاجي - الطابق الأول'],
            ['مخزن المنتجات النهائية', 'المبنى الرئيسي - الطابق الثاني'],
            ['مخزن قطع الغيار', 'المبنى الخدمي - الطابق الأرضي'],
            ['مخزن المواد الاستهلاكية', 'المبنى الإداري - الطابق الأول']
        ];
        
        foreach ($warehouses as $warehouse) {
            $existing = $db->fetchOne("SELECT id FROM warehouses WHERE name = ?", [$warehouse[0]]);
            if (!$existing) {
                $db->query("INSERT INTO warehouses (name, location) VALUES (?, ?)", $warehouse);
            }
        }
        
        // إضافة أصناف تجريبية
        $items = [
            // مواد خام
            ['RAW001', 'حديد خام', 'حديد خام للتصنيع', 1, 'كيلو', 'raw_material', 50.00, 0.00],
            ['RAW002', 'ألمنيوم خام', 'ألمنيوم خام للتصنيع', 1, 'كيلو', 'raw_material', 80.00, 0.00],
            ['RAW003', 'بلاستيك خام', 'بلاستيك خام للتصنيع', 1, 'كيلو', 'raw_material', 30.00, 0.00],
            
            // منتجات نهائية
            ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100.00, 150.00],
            ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80.00, 120.00],
            ['PROD003', 'منتج نهائي ج', 'منتج جاهز للبيع', 2, 'علبة', 'finished_product', 200.00, 280.00],
            
            // منتجات نصف مصنعة
            ['SEMI001', 'منتج نصف مصنع أ', 'منتج في مرحلة التصنيع', 3, 'قطعة', 'semi_finished', 60.00, 90.00],
            ['SEMI002', 'منتج نصف مصنع ب', 'منتج في مرحلة التصنيع', 3, 'كيلو', 'semi_finished', 45.00, 70.00],
            
            // مواد استهلاكية
            ['CONS001', 'ورق طباعة', 'ورق طباعة A4', 4, 'علبة', 'consumable', 25.00, 35.00],
            ['CONS002', 'أقلام', 'أقلام مكتبية', 4, 'قطعة', 'consumable', 2.00, 3.00],
            
            // قطع غيار
            ['SPARE001', 'محرك صغير', 'محرك صغير للآلات', 5, 'قطعة', 'consumable', 500.00, 750.00],
            ['SPARE002', 'حزام نقل', 'حزام نقل للآلات', 5, 'متر', 'consumable', 15.00, 25.00]
        ];
        
        foreach ($items as $item) {
            $existing = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$item[0]]);
            if (!$existing) {
                $db->query("INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id, min_stock_level, reorder_level) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 10, 5)", $item);
            }
        }
        
        $db->commit();
        $success = 'تم إعداد نظام المخزون بنجاح! تم إنشاء الجداول وإضافة البيانات التجريبية.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إعداد نظام المخزون: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tables = ['item_categories', 'items', 'warehouses', 'stock_movements', 'stock_balances'];
$tableStatus = [];

foreach ($tables as $table) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
        $tableStatus[$table] = !empty($result);
    } catch (Exception $e) {
        $tableStatus[$table] = false;
    }
}

$allTablesExist = !in_array(false, $tableStatus);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام المخزون - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .table-status {
            font-size: 0.9rem;
        }
        .status-icon {
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-warehouse fa-2x mb-3"></i><br>إعداد نظام المخزون</h1>
            <p class="mb-0">إعداد جداول وبيانات نظام إدارة المخزون</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- حالة الجداول -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة جداول المخزون</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-status">
                            <thead>
                                <tr>
                                    <th>الجدول</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $tableDescriptions = [
                                    'item_categories' => 'جدول فئات الأصناف',
                                    'items' => 'جدول الأصناف',
                                    'warehouses' => 'جدول المخازن',
                                    'stock_movements' => 'جدول حركات المخزون',
                                    'stock_balances' => 'جدول أرصدة المخزون'
                                ];
                                
                                foreach ($tableDescriptions as $table => $description):
                                    $exists = $tableStatus[$table] ?? false;
                                ?>
                                    <tr>
                                        <td><code><?= $table ?></code></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <i class="fas fa-check-circle text-success status-icon"></i> موجود
                                            <?php else: ?>
                                                <i class="fas fa-times-circle text-danger status-icon"></i> غير موجود
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $description ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- البيانات التجريبية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-box me-2"></i>البيانات التجريبية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>فئات الأصناف:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-tag text-primary me-2"></i>مواد خام</li>
                                <li><i class="fas fa-tag text-success me-2"></i>منتجات نهائية</li>
                                <li><i class="fas fa-tag text-warning me-2"></i>منتجات نصف مصنعة</li>
                                <li><i class="fas fa-tag text-info me-2"></i>مواد استهلاكية</li>
                                <li><i class="fas fa-tag text-secondary me-2"></i>قطع غيار</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>المخازن:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-warehouse text-primary me-2"></i>المخزن الرئيسي</li>
                                <li><i class="fas fa-warehouse text-success me-2"></i>مخزن المواد الخام</li>
                                <li><i class="fas fa-warehouse text-warning me-2"></i>مخزن المنتجات النهائية</li>
                                <li><i class="fas fa-warehouse text-info me-2"></i>مخزن قطع الغيار</li>
                                <li><i class="fas fa-warehouse text-secondary me-2"></i>مخزن المواد الاستهلاكية</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!$allTablesExist): ?>
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-setup btn-lg me-3">
                            <i class="fas fa-play me-2"></i>إعداد نظام المخزون
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="add_item.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>إضافة صنف
                </a>
                
                <a href="inventory.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>المخزون
                </a>
            </div>

            <?php if ($allTablesExist): ?>
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>نظام المخزون جاهز!</strong> يمكنك الآن إضافة أصناف جديدة وإدارة المخزون.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
