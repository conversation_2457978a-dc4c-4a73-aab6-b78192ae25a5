<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// جلب قائمة الديون
try {
    $stmt = $pdo->query("
        SELECT d.*, 
               CASE 
                   WHEN d.entity_type = 'customer' THEN c.name
                   WHEN d.entity_type = 'supplier' THEN s.name
               END as entity_name,
               CASE 
                   WHEN d.entity_type = 'customer' THEN c.phone
                   WHEN d.entity_type = 'supplier' THEN s.phone
               END as entity_phone,
               CASE 
                   WHEN d.entity_type = 'customer' THEN c.email
                   WHEN d.entity_type = 'supplier' THEN s.email
               END as entity_email
        FROM debts d
        LEFT JOIN customers c ON d.entity_type = 'customer' AND d.entity_id = c.id
        LEFT JOIN suppliers s ON d.entity_type = 'supplier' AND d.entity_id = s.id
        WHERE d.balance != 0
        ORDER BY ABS(d.balance) DESC
    ");
    $debts = $stmt->fetchAll();
} catch (PDOException $e) {
    $debts = [];
    error_log("خطأ جلب الديون: " . $e->getMessage());
}

// حساب الإحصائيات
$stats = [
    'total_customer_debts_iqd' => 0,
    'total_customer_debts_usd' => 0,
    'total_supplier_debts_iqd' => 0,
    'total_supplier_debts_usd' => 0,
    'customer_count' => 0,
    'supplier_count' => 0
];

foreach ($debts as $debt) {
    if ($debt['entity_type'] === 'customer') {
        if ($debt['currency'] === 'IQD') {
            $stats['total_customer_debts_iqd'] += $debt['balance'];
        } else {
            $stats['total_customer_debts_usd'] += $debt['balance'];
        }
        $stats['customer_count']++;
    } else {
        if ($debt['currency'] === 'IQD') {
            $stats['total_supplier_debts_iqd'] += $debt['balance'];
        } else {
            $stats['total_supplier_debts_usd'] += $debt['balance'];
        }
        $stats['supplier_count']++;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الديون - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calculator me-2"></i>
                نظام إدارة الديون
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الديون</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>
                                تصدير
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقات الإحصائيات -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            ديون العملاء (دينار)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatCurrency($stats['total_customer_debts_iqd'], 'IQD'); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            ديون العملاء (دولار)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatCurrency($stats['total_customer_debts_usd'], 'USD'); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            ديون الموردين (دينار)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatCurrency($stats['total_supplier_debts_iqd'], 'IQD'); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-truck fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            ديون الموردين (دولار)
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo formatCurrency($stats['total_supplier_debts_usd'], 'USD'); ?>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-coins fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول الديون -->
                <div class="card shadow">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="m-0 font-weight-bold text-primary">قائمة الديون</h6>
                            </div>
                            <div class="col-auto">
                                <div class="btn-group" role="group">
                                    <input type="radio" class="btn-check" name="debtFilter" id="allDebts" autocomplete="off" checked>
                                    <label class="btn btn-outline-primary btn-sm" for="allDebts">الكل</label>

                                    <input type="radio" class="btn-check" name="debtFilter" id="customerDebts" autocomplete="off">
                                    <label class="btn btn-outline-primary btn-sm" for="customerDebts">العملاء</label>

                                    <input type="radio" class="btn-check" name="debtFilter" id="supplierDebts" autocomplete="off">
                                    <label class="btn btn-outline-primary btn-sm" for="supplierDebts">الموردين</label>
                                </div>
                            </div>
                            <div class="col-auto">
                                <input type="text" class="form-control table-search" placeholder="البحث..." data-table="#debtsTable">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="debtsTable">
                                <thead>
                                    <tr>
                                        <th class="sortable">الاسم</th>
                                        <th>النوع</th>
                                        <th>الهاتف</th>
                                        <th>البريد الإلكتروني</th>
                                        <th class="sortable">المبلغ</th>
                                        <th>العملة</th>
                                        <th>آخر معاملة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($debts as $debt): ?>
                                    <tr data-entity-type="<?php echo $debt['entity_type']; ?>">
                                        <td>
                                            <strong><?php echo htmlspecialchars($debt['entity_name'] ?? 'غير محدد'); ?></strong>
                                        </td>
                                        <td>
                                            <span class="badge <?php echo $debt['entity_type'] == 'customer' ? 'bg-primary' : 'bg-secondary'; ?>">
                                                <?php echo $debt['entity_type'] == 'customer' ? 'عميل' : 'مورد'; ?>
                                            </span>
                                        </td>
                                        <td><?php echo htmlspecialchars($debt['entity_phone'] ?: '-'); ?></td>
                                        <td><?php echo htmlspecialchars($debt['entity_email'] ?: '-'); ?></td>
                                        <td>
                                            <span class="<?php echo $debt['balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <strong><?php echo formatCurrency($debt['balance'], $debt['currency']); ?></strong>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo $debt['currency']; ?></span>
                                        </td>
                                        <td>
                                            <?php if ($debt['last_transaction_date']): ?>
                                                <?php echo date('Y-m-d', strtotime($debt['last_transaction_date'])); ?>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <?php if ($debt['entity_type'] == 'customer'): ?>
                                                    <a href="customer_details.php?id=<?php echo $debt['entity_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                <?php else: ?>
                                                    <a href="supplier_details.php?id=<?php echo $debt['entity_id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <a href="transactions.php?entity_type=<?php echo $debt['entity_type']; ?>&entity_id=<?php echo $debt['entity_id']; ?>" class="btn btn-sm btn-success" title="إضافة معاملة">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    
    <script>
        // فلترة الديون حسب النوع
        document.querySelectorAll('input[name="debtFilter"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const table = document.getElementById('debtsTable');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const entityType = row.getAttribute('data-entity-type');
                    
                    if (this.id === 'allDebts') {
                        row.style.display = '';
                    } else if (this.id === 'customerDebts' && entityType === 'customer') {
                        row.style.display = '';
                    } else if (this.id === 'supplierDebts' && entityType === 'supplier') {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
    </script>
</body>
</html>
