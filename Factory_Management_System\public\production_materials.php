<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة صرف المواد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['allocate_material'])) {
    try {
        $db->beginTransaction();
        
        $materialId = (int)$_POST['material_id'];
        $quantityToAllocate = (float)$_POST['quantity_allocated'];
        $warehouseId = (int)$_POST['warehouse_id'];
        
        // التحقق من توفر الكمية في المخزن
        $inventoryItem = $db->fetchOne("
            SELECT quantity FROM inventory 
            WHERE item_id = ? AND warehouse_id = ?
        ", [$materialId, $warehouseId]);
        
        if (!$inventoryItem || $inventoryItem['quantity'] < $quantityToAllocate) {
            throw new Exception("الكمية المطلوبة غير متوفرة في المخزن");
        }
        
        // تحديث كمية المادة المستخدمة
        $db->update('production_materials', [
            'quantity_used' => $quantityToAllocate,
            'status' => 'allocated'
        ], ['id' => $materialId]);
        
        // تحديث المخزون (صرف المواد)
        $db->update('inventory', [
            'quantity' => $inventoryItem['quantity'] - $quantityToAllocate
        ], ['item_id' => $materialId, 'warehouse_id' => $warehouseId]);
        
        // تسجيل حركة المخزون
        $productionOrderId = $db->fetchOne("SELECT production_order_id FROM production_materials WHERE id = ?", [$materialId])['production_order_id'];
        
        $db->insert('production_inventory_movements', [
            'production_order_id' => $productionOrderId,
            'item_id' => $materialId,
            'movement_type' => 'raw_material_out',
            'quantity' => $quantityToAllocate,
            'warehouse_id' => $warehouseId,
            'reference_number' => 'MAT-' . date('YmdHis'),
            'notes' => 'صرف مواد للإنتاج',
            'created_by' => $user['id']
        ]);
        
        $db->commit();
        $success = "تم صرف المواد بنجاح";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في صرف المواد: " . $e->getMessage();
    }
}

// معالجة إدخال المنتج النهائي
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_finished_product'])) {
    try {
        $db->beginTransaction();
        
        $orderId = (int)$_POST['order_id'];
        $quantityProduced = (float)$_POST['quantity_produced'];
        $warehouseId = (int)$_POST['warehouse_id'];
        
        // الحصول على معلومات الأمر
        $order = $db->fetchOne("SELECT * FROM production_orders WHERE id = ?", [$orderId]);
        
        if (!$order) {
            throw new Exception("أمر الإنتاج غير موجود");
        }
        
        // تحديث كمية الإنتاج في الأمر
        $newQuantityProduced = $order['quantity_produced'] + $quantityProduced;
        $db->update('production_orders', [
            'quantity_produced' => $newQuantityProduced
        ], ['id' => $orderId]);
        
        // إضافة المنتج للمخزون
        $existingInventory = $db->fetchOne("
            SELECT * FROM inventory 
            WHERE item_id = ? AND warehouse_id = ?
        ", [$order['product_id'], $warehouseId]);
        
        if ($existingInventory) {
            $db->update('inventory', [
                'quantity' => $existingInventory['quantity'] + $quantityProduced
            ], ['item_id' => $order['product_id'], 'warehouse_id' => $warehouseId]);
        } else {
            $db->insert('inventory', [
                'item_id' => $order['product_id'],
                'warehouse_id' => $warehouseId,
                'quantity' => $quantityProduced,
                'unit_cost' => 0,
                'reorder_level' => 10
            ]);
        }
        
        // تسجيل حركة المخزون
        $db->insert('production_inventory_movements', [
            'production_order_id' => $orderId,
            'item_id' => $order['product_id'],
            'movement_type' => 'finished_product_in',
            'quantity' => $quantityProduced,
            'warehouse_id' => $warehouseId,
            'reference_number' => 'FIN-' . date('YmdHis'),
            'notes' => 'إدخال منتج نهائي من الإنتاج',
            'created_by' => $user['id']
        ]);
        
        // تحديث حالة الأمر إذا اكتمل
        if ($newQuantityProduced >= $order['quantity_required']) {
            $db->update('production_orders', [
                'status' => 'completed',
                'completion_date' => date('Y-m-d')
            ], ['id' => $orderId]);
        }
        
        $db->commit();
        $success = "تم إدخال المنتج النهائي بنجاح";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إدخال المنتج النهائي: " . $e->getMessage();
    }
}

// جلب المواد المطلوبة للإنتاج
try {
    $materials = $db->fetchAll("
        SELECT pm.*, po.order_number, i.name as material_name, i.code as material_code, 
               i.unit as material_unit, w.name as warehouse_name,
               inv.quantity as available_quantity
        FROM production_materials pm
        JOIN production_orders po ON pm.production_order_id = po.id
        JOIN items i ON pm.material_id = i.id
        LEFT JOIN warehouses w ON pm.warehouse_id = w.id
        LEFT JOIN inventory inv ON pm.material_id = inv.item_id AND pm.warehouse_id = inv.warehouse_id
        WHERE po.status IN ('pending', 'in_progress')
        ORDER BY po.created_at DESC, pm.id
    ");
    
    // جلب أوامر الإنتاج النشطة
    $activeOrders = $db->fetchAll("
        SELECT po.*, i.name as product_name, i.code as product_code
        FROM production_orders po
        JOIN items i ON po.product_id = i.id
        WHERE po.status IN ('pending', 'in_progress')
        ORDER BY po.created_at DESC
    ");
    
    // جلب المخازن
    $warehouses = $db->fetchAll("SELECT id, name FROM warehouses WHERE is_active = 1 ORDER BY name");
    
} catch (Exception $e) {
    $materials = [];
    $activeOrders = [];
    $warehouses = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة مواد الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .material-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #28a745;
            transition: all 0.3s ease;
        }
        
        .material-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-allocated { background: #d1ecf1; color: #0c5460; }
        .status-consumed { background: #d4edda; color: #155724; }
        
        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
        }
        
        .quantity-display {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            margin: 0.5rem 0;
        }
        
        .available-quantity {
            color: #28a745;
            font-weight: bold;
        }
        
        .required-quantity {
            color: #dc3545;
            font-weight: bold;
        }
        
        .workflow-step {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 5px solid #28a745;
            text-align: center;
        }
        
        .workflow-arrow {
            text-align: center;
            color: #28a745;
            font-size: 2rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-boxes me-3"></i>إدارة مواد الإنتاج</h1>
                    <p class="mb-0">صرف المواد الأولية وإدخال المنتجات النهائية</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="production.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- سير العمل -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-project-diagram me-2"></i>سير العمل</h5>
                    </div>
                    <div class="card-body">
                        <div class="workflow-step">
                            <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
                            <h6>1. أمر الإنتاج</h6>
                            <small>تحديد المواد المطلوبة</small>
                        </div>
                        
                        <div class="workflow-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        
                        <div class="workflow-step">
                            <i class="fas fa-box-open fa-2x text-warning mb-2"></i>
                            <h6>2. صرف المواد</h6>
                            <small>سحب من المخزن</small>
                        </div>
                        
                        <div class="workflow-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        
                        <div class="workflow-step">
                            <i class="fas fa-cogs fa-2x text-info mb-2"></i>
                            <h6>3. التصنيع</h6>
                            <small>عملية الإنتاج</small>
                        </div>
                        
                        <div class="workflow-arrow">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        
                        <div class="workflow-step">
                            <i class="fas fa-warehouse fa-2x text-success mb-2"></i>
                            <h6>4. المنتج النهائي</h6>
                            <small>إدخال للمخزن</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المواد المطلوبة للصرف -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-box-open me-2"></i>المواد المطلوبة للصرف</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($materials)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                                <h5>لا توجد مواد مطلوبة</h5>
                                <p class="text-muted">جميع المواد تم صرفها أو لا توجد أوامر إنتاج نشطة</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($materials as $material): ?>
                                <div class="material-card">
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <h6 class="mb-1">
                                                <?= htmlspecialchars($material['material_name']) ?>
                                            </h6>
                                            <small class="text-muted">
                                                <?= htmlspecialchars($material['material_code']) ?> | 
                                                أمر: <?= htmlspecialchars($material['order_number']) ?>
                                            </small>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <div class="quantity-display">
                                                <div class="required-quantity">
                                                    مطلوب: <?= number_format($material['quantity_required'], 2) ?>
                                                </div>
                                                <div class="available-quantity">
                                                    متوفر: <?= number_format($material['available_quantity'] ?: 0, 2) ?>
                                                </div>
                                                <small class="text-muted"><?= htmlspecialchars($material['material_unit']) ?></small>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-2">
                                            <?php
                                            $statusNames = [
                                                'pending' => 'في الانتظار',
                                                'allocated' => 'تم الصرف',
                                                'consumed' => 'مستهلك'
                                            ];
                                            ?>
                                            <span class="status-badge status-<?= $material['status'] ?>">
                                                <?= $statusNames[$material['status']] ?>
                                            </span>
                                        </div>
                                        
                                        <div class="col-md-3">
                                            <?php if ($material['status'] == 'pending' && $material['available_quantity'] >= $material['quantity_required']): ?>
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="material_id" value="<?= $material['id'] ?>">
                                                    <input type="hidden" name="quantity_allocated" value="<?= $material['quantity_required'] ?>">
                                                    <input type="hidden" name="warehouse_id" value="<?= $material['warehouse_id'] ?>">
                                                    <button type="submit" name="allocate_material" class="btn btn-primary btn-sm w-100">
                                                        <i class="fas fa-check me-1"></i>صرف المادة
                                                    </button>
                                                </form>
                                            <?php elseif ($material['status'] == 'pending'): ?>
                                                <button class="btn btn-warning btn-sm w-100" disabled>
                                                    <i class="fas fa-exclamation-triangle me-1"></i>غير متوفر
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm w-100" disabled>
                                                    <i class="fas fa-check-circle me-1"></i>تم الصرف
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- إدخال المنتج النهائي -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-warehouse me-2"></i>إدخال منتج نهائي</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($activeOrders)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد أوامر إنتاج نشطة</p>
                            </div>
                        <?php else: ?>
                            <form method="POST">
                                <div class="mb-3">
                                    <label class="form-label">أمر الإنتاج</label>
                                    <select name="order_id" class="form-select" required>
                                        <option value="">اختر الأمر</option>
                                        <?php foreach ($activeOrders as $order): ?>
                                            <option value="<?= $order['id'] ?>">
                                                <?= htmlspecialchars($order['order_number']) ?> - 
                                                <?= htmlspecialchars($order['product_name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الكمية المنتجة</label>
                                    <input type="number" name="quantity_produced" class="form-control" 
                                           step="0.001" min="0.001" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">المخزن</label>
                                    <select name="warehouse_id" class="form-select" required>
                                        <option value="">اختر المخزن</option>
                                        <?php foreach ($warehouses as $warehouse): ?>
                                            <option value="<?= $warehouse['id'] ?>">
                                                <?= htmlspecialchars($warehouse['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <button type="submit" name="add_finished_product" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>إدخال المنتج
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>إحصائيات</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $pendingMaterials = count(array_filter($materials, function($m) { return $m['status'] == 'pending'; }));
                        $allocatedMaterials = count(array_filter($materials, function($m) { return $m['status'] == 'allocated'; }));
                        ?>
                        <div class="text-center mb-3">
                            <h4 class="text-warning"><?= $pendingMaterials ?></h4>
                            <small>مواد في الانتظار</small>
                        </div>
                        
                        <div class="text-center mb-3">
                            <h4 class="text-success"><?= $allocatedMaterials ?></h4>
                            <small>مواد تم صرفها</small>
                        </div>
                        
                        <div class="text-center">
                            <h4 class="text-primary"><?= count($activeOrders) ?></h4>
                            <small>أوامر نشطة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
