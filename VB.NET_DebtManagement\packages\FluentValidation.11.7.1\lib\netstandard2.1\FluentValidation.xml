<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentValidation</name>
    </assembly>
    <members>
        <member name="T:FluentValidation.AbstractValidator`1">
            <summary>
            Base class for object validators.
            </summary>
            <typeparam name="T">The type of the object being validated</typeparam>
        </member>
        <member name="P:FluentValidation.AbstractValidator`1.CascadeMode">
            <summary>
            <para>
            Gets a single <see cref="P:FluentValidation.AbstractValidator`1.CascadeMode"/> mode value representing the default values of
            <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>., based on the same logic as used when setting
            this property as described below.
            </para>
            <para>
            Sets the values of <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Continue"/> or <see cref="F:FluentValidation.CascadeMode.Stop"/>, then both properties are set
            to that value.
            </para>
            <para>
            If set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>,
            then <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            is set to <see cref="F:FluentValidation.CascadeMode.Continue"/>, and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>
            is set to <see cref="F:FluentValidation.CascadeMode.Stop"/>.
            This results in the same behaviour as before this property was deprecated.
            </para>
            </summary>
        </member>
        <member name="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode">
            <summary>
            <para>
            Sets the cascade behaviour <i>in between</i> rules in this validator.
            This overrides the default value set in <see cref="P:FluentValidation.ValidatorConfiguration.DefaultClassLevelCascadeMode"/>.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Continue"/> then all rules in the class will execute regardless of failures.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Stop"/> then execution of the validator will stop after any rule fails.
            </para>
            <para>
            Note that cascade behaviour <i>within</i> individual rules is controlled by
            <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>.
            </para>
            <para>
            This cannot be set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>.
            <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>. Attempting to do so it will actually
            result in <see cref="F:FluentValidation.CascadeMode.Stop"/> being used.
            </para>
            </summary>
        </member>
        <member name="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode">
            <summary>
            <para>
            Sets the default cascade behaviour <i>within</i> each rule in this validator.
            </para>
            <para>
            This overrides the default value set in <see cref="P:FluentValidation.ValidatorConfiguration.DefaultRuleLevelCascadeMode"/>.
            </para>
            <para>
            It can be further overridden for specific rules by calling
            <see cref="M:FluentValidation.DefaultValidatorOptions.Cascade``2(FluentValidation.IRuleBuilderInitial{``0,``1},FluentValidation.CascadeMode)"/>.
            <seealso cref="P:FluentValidation.Internal.RuleBase`3.CascadeMode"/>.
            </para>
            <para>
            Note that cascade behaviour <i>between</i> rules is controlled by <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>.
            </para>
            <para>
            This cannot be set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>.
            <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>. Attempting to do so it will actually
            result in <see cref="F:FluentValidation.CascadeMode.Stop"/> being used.
            </para>
            </summary>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Validate(`0)">
            <summary>
            Validates the specified instance
            </summary>
            <param name="instance">The object to validate</param>
            <returns>A ValidationResult object containing any validation failures</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.ValidateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Validates the specified instance asynchronously
            </summary>
            <param name="instance">The object to validate</param>
            <param name="cancellation">Cancellation token</param>
            <returns>A ValidationResult object containing any validation failures</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Validate(FluentValidation.ValidationContext{`0})">
            <summary>
            Validates the specified instance.
            </summary>
            <param name="context">Validation Context</param>
            <returns>A ValidationResult object containing any validation failures.</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.ValidateAsync(FluentValidation.ValidationContext{`0},System.Threading.CancellationToken)">
            <summary>
            Validates the specified instance asynchronously.
            </summary>
            <param name="context">Validation Context</param>
            <param name="cancellation">Cancellation token</param>
            <returns>A ValidationResult object containing any validation failures.</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.CreateDescriptor">
            <summary>
            Creates a <see cref="T:FluentValidation.IValidatorDescriptor" /> that can be used to obtain metadata about the current validator.
            </summary>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.RuleFor``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
            <summary>
            Defines a validation rule for a specific property.
            </summary>
            <example>
            RuleFor(x => x.Surname)...
            </example>
            <typeparam name="TProperty">The type of property being validated</typeparam>
            <param name="expression">The expression representing the property to validate</param>
            <returns>an IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Transform``2(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{``0,``1})">
            <summary>
            Defines a validation rule for a specify property and transform it to a different type.
            </summary>
            <example>
            Transform(x => x.OrderNumber, to: orderNumber => orderNumber.ToString())...
            </example>
            <typeparam name="TProperty">The type of property being validated</typeparam>
            <typeparam name="TTransformed">The type after the transformer has been applied</typeparam>
            <param name="from">The expression representing the property to transform</param>
            <param name="to">Function to transform the property value into a different type</param>
            <returns>an IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Transform``2(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{`0,``0,``1})">
            <summary>
            Defines a validation rule for a specify property and transform it to a different type.
            </summary>
            <example>
            Transform(x => x.OrderNumber, to: orderNumber => orderNumber.ToString())...
            </example>
            <typeparam name="TProperty">The type of property being validated</typeparam>
            <typeparam name="TTransformed">The type after the transformer has been applied</typeparam>
            <param name="from">The expression representing the property to transform</param>
            <param name="to">Function to transform the property value into a different type</param>
            <returns>an IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.RuleForEach``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}})">
            <summary>
            Invokes a rule for each item in the collection.
            </summary>
            <typeparam name="TElement">Type of property</typeparam>
            <param name="expression">Expression representing the collection to validate</param>
            <returns>An IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.TransformForEach``2(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{``0,``1})">
            <summary>
            Invokes a rule for each item in the collection, transforming the element from one type to another.
            </summary>
            <typeparam name="TElement">Type of property</typeparam>
            <typeparam name="TTransformed">The type after the transformer has been applied</typeparam>
            <param name="expression">Expression representing the collection to validate</param>
            <param name="to">Function to transform the collection element into a different type</param>
            <returns>An IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.TransformForEach``2(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{`0,``0,``1})">
            <summary>
            Invokes a rule for each item in the collection, transforming the element from one type to another.
            </summary>
            <typeparam name="TElement">Type of property</typeparam>
            <typeparam name="TTransformed">The type after the transformer has been applied</typeparam>
            <param name="expression">Expression representing the collection to validate</param>
            <param name="to">Function to transform the collection element into a different type</param>
            <returns>An IRuleBuilder instance on which validators can be defined</returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.RuleSet(System.String,System.Action)">
            <summary>
            Defines a RuleSet that can be used to group together several validators.
            </summary>
            <param name="ruleSetName">The name of the ruleset.</param>
            <param name="action">Action that encapsulates the rules in the ruleset.</param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.When(System.Func{`0,System.Boolean},System.Action)">
            <summary>
            Defines a condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.When(System.Func{`0,FluentValidation.ValidationContext{`0},System.Boolean},System.Action)">
            <summary>
            Defines a condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Unless(System.Func{`0,System.Boolean},System.Action)">
            <summary>
            Defines an inverse condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Unless(System.Func{`0,FluentValidation.ValidationContext{`0},System.Boolean},System.Action)">
            <summary>
            Defines an inverse condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.WhenAsync(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.WhenAsync(System.Func{`0,FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.UnlessAsync(System.Func{`0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an inverse asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.UnlessAsync(System.Func{`0,FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an inverse asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Include(FluentValidation.IValidator{`0})">
            <summary>
            Includes the rules from the specified validator
            </summary>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.Include``1(System.Func{`0,``0})">
            <summary>
            Includes the rules from the specified validator
            </summary>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection of validation rules.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.EnsureInstanceNotNull(System.Object)">
            <summary>
            Throws an exception if the instance being validated is null.
            </summary>
            <param name="instanceToValidate"></param>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.PreValidate(FluentValidation.ValidationContext{`0},FluentValidation.Results.ValidationResult)">
            <summary>
            Determines if validation should occur and provides a means to modify the context and ValidationResult prior to execution.
            If this method returns false, then the ValidationResult is immediately returned from Validate/ValidateAsync.
            </summary>
            <param name="context"></param>
            <param name="result"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.AbstractValidator`1.RaiseValidationException(FluentValidation.ValidationContext{`0},FluentValidation.Results.ValidationResult)">
            <summary>
            Throws a ValidationException. This method will only be called if the validator has been configured
            to throw exceptions if validation fails. The default behaviour is not to throw an exception.
            </summary>
            <param name="context"></param>
            <param name="result"></param>
            <exception cref="T:FluentValidation.ValidationException"></exception>
        </member>
        <member name="T:FluentValidation.AssemblyScanner">
            <summary>
            Class that can be used to find all the validators from a collection of types.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.#ctor(System.Collections.Generic.IEnumerable{System.Type})">
            <summary>
            Creates a scanner that works on a sequence of types.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.FindValidatorsInAssembly(System.Reflection.Assembly,System.Boolean)">
            <summary>
            Finds all the validators in the specified assembly.
            </summary>
            <param name="assembly">The assembly to scan</param>
            <param name="includeInternalTypes">Whether to include internal validators in the search as well as public validators. The default is false.</param>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.FindValidatorsInAssemblies(System.Collections.Generic.IEnumerable{System.Reflection.Assembly},System.Boolean)">
            <summary>
            Finds all the validators in the specified assemblies.
            </summary>
            <param name="assemblies">The assemblies to scan</param>
            <param name="includeInternalTypes">Whether to include internal validators as well as public validators. The default is false.</param>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.FindValidatorsInAssemblyContaining``1">
            <summary>
            Finds all the validators in the assembly containing the specified type.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.FindValidatorsInAssemblyContaining(System.Type)">
            <summary>
            Finds all the validators in the assembly containing the specified type.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.ForEach(System.Action{FluentValidation.AssemblyScanner.AssemblyScanResult})">
            <summary>
            Performs the specified action to all of the assembly scan results.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1"/> that can be used to iterate through the collection.
            </returns>
            <filterpriority>1</filterpriority>
        </member>
        <member name="T:FluentValidation.AssemblyScanner.AssemblyScanResult">
            <summary>
            Result of performing a scan.
            </summary>
        </member>
        <member name="M:FluentValidation.AssemblyScanner.AssemblyScanResult.#ctor(System.Type,System.Type)">
            <summary>
            Creates an instance of an AssemblyScanResult.
            </summary>
        </member>
        <member name="P:FluentValidation.AssemblyScanner.AssemblyScanResult.InterfaceType">
            <summary>
            Validator interface type, eg IValidator&lt;Foo&gt;
            </summary>
        </member>
        <member name="P:FluentValidation.AssemblyScanner.AssemblyScanResult.ValidatorType">
            <summary>
            Concrete type that implements the InterfaceType, eg FooValidator.
            </summary>
        </member>
        <member name="T:FluentValidation.AsyncValidatorInvokedSynchronouslyException">
            <summary>
            This exception is thrown when an asynchronous validator is executed synchronously.
            </summary>
        </member>
        <member name="T:FluentValidation.DefaultValidatorExtensions">
            <summary>
            Extension methods that provide the default set of validators.
            </summary>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.SetValidator``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},FluentValidation.Validators.IPropertyValidator{``0,``1})">
            <summary>
            Associates a validator with this the property for this rule builder.
            This overload handles type conversion for nullable value types, allowing a validator for TProperty to be applied to a property of type Nullable&lt;TProperty&gt;
            </summary>
            <param name="ruleBuilder"></param>
            <param name="validator">The validator to set</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.SetAsyncValidator``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},FluentValidation.Validators.IAsyncPropertyValidator{``0,``1})">
            <summary>
            Associates an async validator with this the property for this rule builder.
            This overload handles type conversion for nullable value types, allowing a validator for TProperty to be applied to a property of type Nullable&lt;TProperty&gt;
            </summary>
            <param name="ruleBuilder"></param>
            <param name="validator">The validator to set</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotNull``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Defines a 'not null' validator on the current rule builder.
            Validation will fail if the property is null.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Null``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Defines a 'null' validator on the current rule builder.
            Validation will fail if the property is not null.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotEmpty``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Defines a 'not empty' validator on the current rule builder.
            Validation will fail if the property is null, an empty string, whitespace, an empty collection or the default value for the type (for example, 0 for integers but null for nullable integers)
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Empty``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Defines a 'empty' validator on the current rule builder.
            Validation will fail if the property is not null, an empty or the default value for the type (for example, 0 for integers)
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Length``1(FluentValidation.IRuleBuilder{``0,System.String},System.Int32,System.Int32)">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is outside of the specified range. The range is inclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="min"></param>
            <param name="max"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Length``1(FluentValidation.IRuleBuilder{``0,System.String},System.Func{``0,System.Int32},System.Func{``0,System.Int32})">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is outside of the specified range. The range is inclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="min"></param>
            <param name="max"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Length``1(FluentValidation.IRuleBuilder{``0,System.String},System.Int32)">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is not equal to the length specified.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="exactLength"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Length``1(FluentValidation.IRuleBuilder{``0,System.String},System.Func{``0,System.Int32})">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is not equal to the length specified.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="exactLength"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.String)">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The regular expression to check the value against.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.MaximumLength``1(FluentValidation.IRuleBuilder{``0,System.String},System.Int32)">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is larger than the length specified.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="maximumLength"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.MinimumLength``1(FluentValidation.IRuleBuilder{``0,System.String},System.Int32)">
            <summary>
            Defines a length validator on the current rule builder, but only for string properties.
            Validation will fail if the length of the string is less than the length specified.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="minimumLength"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.Func{``0,System.String})">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The regular expression to check the value against.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.Text.RegularExpressions.Regex)">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="regex">The regular expression to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.Func{``0,System.Text.RegularExpressions.Regex})">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="regex">The regular expression to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The regular expression to check the value against.</param>
            <param name="options">Regex options</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Matches``1(FluentValidation.IRuleBuilder{``0,System.String},System.Func{``0,System.String},System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Defines a regular expression validator on the current rule builder, but only for string properties.
            Validation will fail if the value returned by the lambda does not match the regular expression.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The regular expression to check the value against.</param>
            <param name="options">Regex options</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.EmailAddress``1(FluentValidation.IRuleBuilder{``0,System.String},FluentValidation.Validators.EmailValidationMode)">
            <summary>
            Defines an email validator on the current rule builder for string properties.
            Validation will fail if the value returned by the lambda is not a valid email address.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="mode">The mode to use for email validation. If set to <see cref="F:FluentValidation.Validators.EmailValidationMode.Net4xRegex"/>, then a regular expression will be used. This is the same regex used by the EmailAddressAttribute in .NET 4.x. If set to <see cref="F:FluentValidation.Validators.EmailValidationMode.AspNetCoreCompatible"/> then this uses the simplified ASP.NET Core logic for checking an email address, which just checks for the presence of an @ sign.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotEqual``2(FluentValidation.IRuleBuilder{``0,``1},``1,System.Collections.Generic.IEqualityComparer{``1})">
            <summary>
            Defines a 'not equal' validator on the current rule builder.
            Validation will fail if the specified value is equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="toCompare">The value to compare</param>
            <param name="comparer">Equality comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotEqual``1(FluentValidation.IRuleBuilder{``0,System.String},System.String,System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>
            Defines a 'not equal' validator on the current rule builder.
            Validation will fail if the specified value is equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="toCompare">The value to compare</param>
            <param name="comparer">Equality comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotEqual``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
            <summary>
            Defines a 'not equal' validator on the current rule builder using a lambda to specify the value.
            Validation will fail if the value returned by the lambda is equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda expression to provide the comparison value</param>
            <param name="comparer">Equality Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.NotEqual``1(FluentValidation.IRuleBuilder{``0,System.String},System.Linq.Expressions.Expression{System.Func{``0,System.String}},System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>
            Defines a 'not equal' validator on the current rule builder using a lambda to specify the value.
            Validation will fail if the value returned by the lambda is equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda expression to provide the comparison value</param>
            <param name="comparer">Equality Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Equal``2(FluentValidation.IRuleBuilder{``0,``1},``1,System.Collections.Generic.IEqualityComparer{``1})">
            <summary>
            Defines an 'equals' validator on the current rule builder.
            Validation will fail if the specified value is not equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="toCompare">The value to compare</param>
            <param name="comparer">Equality Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Equal``1(FluentValidation.IRuleBuilder{``0,System.String},System.String,System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>
            Defines an 'equals' validator on the current rule builder.
            Validation will fail if the specified value is not equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="toCompare">The value to compare</param>
            <param name="comparer">Equality Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Equal``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
            <summary>
            Defines an 'equals' validator on the current rule builder using a lambda to specify the comparison value.
            Validation will fail if the value returned by the lambda is not equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">The type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda expression to provide the comparison value</param>
            <param name="comparer">Equality comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Equal``1(FluentValidation.IRuleBuilder{``0,System.String},System.Linq.Expressions.Expression{System.Func{``0,System.String}},System.Collections.Generic.IEqualityComparer{System.String})">
            <summary>
            Defines an 'equals' validator on the current rule builder using a lambda to specify the comparison value.
            Validation will fail if the value returned by the lambda is not equal to the value of the property.
            For strings, this performs an ordinal comparison unless you specify a different comparer.
            </summary>
            <typeparam name="T">The type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda expression to provide the comparison value</param>
            <param name="comparer">Equality comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Must``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``1,System.Boolean})">
            <summary>
            Defines a predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Must``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``0,``1,System.Boolean})">
            <summary>
            Defines a predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            This overload accepts the object being validated in addition to the property being validated.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Must``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``0,``1,FluentValidation.ValidationContext{``0},System.Boolean})">
            <summary>
            Defines a predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            This overload accepts the object being validated in addition to the property being validated.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.MustAsync``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``1,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Defines an asynchronous predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.MustAsync``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``0,``1,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Defines an asynchronous predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            This overload accepts the object being validated in addition to the property being validated.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.MustAsync``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``0,``1,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Defines an asynchronous predicate validator on the current rule builder using a lambda expression to specify the predicate.
            Validation will fail if the specified lambda returns false.
            Validation will succeed if the specified lambda returns true.
            This overload accepts the object being validated in addition to the property being validated.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="predicate">A lambda expression specifying the predicate</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,``1},``1)">
            <summary>
            Defines a 'less than' validator on the current rule builder.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1)">
            <summary>
            Defines a 'less than' validator on the current rule builder.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},``1)">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1)">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,``1},``1)">
            <summary>
            Defines a 'greater than' validator on the current rule builder.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1)">
            <summary>
            Defines a 'greater than' validator on the current rule builder.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},``1)">
            <summary>
            Defines a 'greater than or equal' validator on the current rule builder.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1)">
            <summary>
            Defines a 'greater than or equal' validator on the current rule builder.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'less than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda that should return the value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'less than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda that should return the value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'less than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda that should return the value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'less than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than the specified value.
            The validation will fail if the property value is greater than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">A lambda that should return the value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.LessThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'less than or equal' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is less than or equal to the specified value.
            The validation will fail if the property value is greater than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'greater than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'greater than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'greater than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThan``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'greater than' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than the specified value.
            The validation will fail if the property value is less than or equal to the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="expression">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'greater than or equal to' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'greater than or equal to' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{``1}}})">
            <summary>
            Defines a 'greater than or equal to' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.GreaterThanOrEqualTo``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Defines a 'greater than or equal to' validator on the current rule builder using a lambda expression.
            The validation will succeed if the property value is greater than or equal the specified value.
            The validation will fail if the property value is less than the specified value.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="valueToCompare">The value being compared</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.InclusiveBetween``2(FluentValidation.IRuleBuilder{``0,``1},``1,``1)">
            <summary>
            Defines an 'inclusive between' validator on the current rule builder, but only for properties of types that implement IComparable.
            Validation will fail if the value of the property is outside of the specified range. The range is inclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.InclusiveBetween``2(FluentValidation.IRuleBuilder{``0,``1},``1,``1,System.Collections.Generic.IComparer{``1})">
            <summary>
            Defines an 'inclusive between' validator on the current rule builder.
            Validation will fail if the value of the property is outside of the specified range. The range is inclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <param name="comparer">Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.InclusiveBetween``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1,``1)">
            <summary>
            Defines an 'inclusive between' validator on the current rule builder, but only for properties of types that implement IComparable.
            Validation will fail if the value of the property is outside of the specified range. The range is inclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ExclusiveBetween``2(FluentValidation.IRuleBuilder{``0,``1},``1,``1)">
            <summary>
            Defines an 'exclusive between' validator on the current rule builder, but only for properties of types that implement IComparable.
            Validation will fail if the value of the property is outside of the specified range. The range is exclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ExclusiveBetween``2(FluentValidation.IRuleBuilder{``0,``1},``1,``1,System.Collections.Generic.IComparer{``1})">
            <summary>
            Defines an 'exclusive between' validator on the current rule builder.
            Validation will fail if the value of the property is outside of the specified range. The range is exclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <param name="comparer">Comparer to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ExclusiveBetween``2(FluentValidation.IRuleBuilder{``0,System.Nullable{``1}},``1,``1)">
            <summary>
            Defines an 'exclusive between' validator on the current rule builder, but only for properties of types that implement IComparable.
            Validation will fail if the value of the property is outside of the specified range. The range is exclusive.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="from">The lowest allowed value</param>
            <param name="to">The highest allowed value</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.CreditCard``1(FluentValidation.IRuleBuilder{``0,System.String})">
            <summary>
            Defines a credit card validator for the current rule builder that ensures that the specified string is a valid credit card number.
            </summary>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.IsInEnum``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Defines a enum value validator on the current rule builder that ensures that the specific value is a valid enum value.
            </summary>
            <typeparam name="T">Type of Enum being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ScalePrecision``1(FluentValidation.IRuleBuilder{``0,System.Decimal},System.Int32,System.Int32,System.Boolean)">
            <summary>
            Defines a scale precision validator on the current rule builder that ensures that the specific value has a certain scale and precision
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="scale">Allowed scale of the value</param>
            <param name="precision">Allowed precision of the value</param>
            <param name="ignoreTrailingZeros">Whether the validator will ignore trailing zeros.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ScalePrecision``1(FluentValidation.IRuleBuilder{``0,System.Nullable{System.Decimal}},System.Int32,System.Int32,System.Boolean)">
            <summary>
            Defines a scale precision validator on the current rule builder that ensures that the specific value has a certain scale and precision
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="scale">Allowed scale of the value</param>
            <param name="precision">Allowed precision of the value</param>
            <param name="ignoreTrailingZeros">Whether the validator will ignore trailing zeros.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.PrecisionScale``1(FluentValidation.IRuleBuilder{``0,System.Decimal},System.Int32,System.Int32,System.Boolean)">
            <summary>
            Defines a scale precision validator on the current rule builder that ensures a decimal the specified precision and scale.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="scale">Allowed scale of the value</param>
            <param name="precision">Allowed precision of the value</param>
            <param name="ignoreTrailingZeros">Whether the validator will ignore trailing zeros after the decimal point. For example, when set to true the decimal 123.4500 will be considered to have a precision of 5 and scale of 2. When set to false, it will be considered to have a precision of 7 and scale of 4.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.PrecisionScale``1(FluentValidation.IRuleBuilder{``0,System.Nullable{System.Decimal}},System.Int32,System.Int32,System.Boolean)">
            <summary>
            Defines a scale precision validator on the current rule builder that ensures a decimal the specified precision and scale.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="scale">Allowed scale of the value</param>
            <param name="precision">Allowed precision of the value</param>
            <param name="ignoreTrailingZeros">Whether the validator will ignore trailing zeros after the decimal point. For example, when set to true the decimal 123.4500 will be considered to have a precision of 5 and scale of 2. When set to false, it will be considered to have a precision of 7 and scale of 4.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Custom``2(FluentValidation.IRuleBuilder{``0,``1},System.Action{``1,FluentValidation.ValidationContext{``0}})">
            <summary>
            Defines a custom validation rule
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="ruleBuilder"></param>
            <param name="action"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.CustomAsync``2(FluentValidation.IRuleBuilder{``0,``1},System.Func{``1,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task})">
            <summary>
            Defines a custom validation rule
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="ruleBuilder"></param>
            <param name="action"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ForEach``2(FluentValidation.IRuleBuilder{``0,System.Collections.Generic.IEnumerable{``1}},System.Action{FluentValidation.IRuleBuilderInitialCollection{System.Collections.Generic.IEnumerable{``1},``1}})">
            <summary>
            Allows rules to be built against individual elements in the collection.
            </summary>
            <param name="ruleBuilder"></param>
            <param name="action"></param>
            <typeparam name="T"></typeparam>
            <typeparam name="TElement"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.IsEnumName``1(FluentValidation.IRuleBuilder{``0,System.String},System.Type,System.Boolean)">
            <summary>
            Defines a enum value validator on the current rule builder that ensures that the specific value is a valid enum name.
            </summary>
            <typeparam name="T">Type of Enum being validated</typeparam>
            <param name="ruleBuilder">The rule builder on which the validator should be defined</param>
            <param name="enumType">The enum whose the string should match any name</param>
            <param name="caseSensitive">If the comparison between the string and the enum names should be case sensitive</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ChildRules``2(FluentValidation.IRuleBuilder{``0,``1},System.Action{FluentValidation.InlineValidator{``1}})">
            <summary>
            Defines child rules for a nested property.
            </summary>
            <param name="ruleBuilder">The rule builder.</param>
            <param name="action">Callback that will be invoked to build the rules.</param>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.SetInheritanceValidator``2(FluentValidation.IRuleBuilder{``0,``1},System.Action{FluentValidation.Validators.PolymorphicValidator{``0,``1}})">
            <summary>
            Defines one or more validators that can be used to validate sub-classes or implementors
            in an inheritance hierarchy. This is useful when the property being validated is an interface
            or base-class, but you want to define rules for properties of a specific subclass.
            </summary>
            <param name="ruleBuilder"></param>
            <param name="validatorConfiguration">Callback for setting up the inheritance validators.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.Validate``1(FluentValidation.IValidator{``0},``0,System.Action{FluentValidation.Internal.ValidationStrategy{``0}})">
            <summary>
            Validates the specified instance using a combination of extra options
            </summary>
            <param name="validator">The validator</param>
            <param name="instance">The instance to validate</param>
            <param name="options">Callback to configure additional options</param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ValidateAsync``1(FluentValidation.IValidator{``0},``0,System.Action{FluentValidation.Internal.ValidationStrategy{``0}},System.Threading.CancellationToken)">
            <summary>
            Validates the specified instance using a combination of extra options
            </summary>
            <param name="validator">The validator</param>
            <param name="instance">The instance to validate</param>
            <param name="cancellation">Cancellation token</param>
            <param name="options">Callback to configure additional options</param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ValidateAndThrow``1(FluentValidation.IValidator{``0},``0)">
            <summary>
            Performs validation and then throws an exception if validation fails.
            This method is a shortcut for: Validate(instance, options => options.ThrowOnFailures());
            </summary>
            <param name="validator">The validator this method is extending.</param>
            <param name="instance">The instance of the type we are validating.</param>
        </member>
        <member name="M:FluentValidation.DefaultValidatorExtensions.ValidateAndThrowAsync``1(FluentValidation.IValidator{``0},``0,System.Threading.CancellationToken)">
            <summary>
            Performs validation asynchronously and then throws an exception if validation fails.
            This method is a shortcut for: ValidateAsync(instance, options => options.ThrowOnFailures());
            </summary>
            <param name="validator">The validator this method is extending.</param>
            <param name="instance">The instance of the type we are validating.</param>
            <param name="cancellationToken"></param>
        </member>
        <member name="T:FluentValidation.DefaultValidatorOptions">
            <summary>
            Default options that can be used to configure a validator.
            </summary>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Configure``2(FluentValidation.IRuleBuilderInitial{``0,``1},System.Action{FluentValidation.IValidationRule{``0,``1}})">
            <summary>
            Configures the rule.
            </summary>
            <param name="ruleBuilder"></param>
            <param name="configurator">Action to configure the object.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Configure``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Action{FluentValidation.IValidationRule{``0,``1}})">
            <summary>
            Configures the current object.
            </summary>
            <param name="ruleBuilder"></param>
            <param name="configurator">Action to configure the object.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Configure``2(FluentValidation.IRuleBuilderInitialCollection{``0,``1},System.Action{FluentValidation.ICollectionRule{``0,``1}})">
            <summary>
            Configures the rule object.
            </summary>
            <param name="ruleBuilder"></param>
            <param name="configurator">Action to configure the object.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Configurable``2(FluentValidation.IRuleBuilder{``0,``1})">
            <summary>
            Gets the configurable rule instance from a rule builder.
            </summary>
            <param name="ruleBuilder">The rule builder.</param>
            <returns>A configurable IValidationRule instance.</returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Configurable``2(FluentValidation.IRuleBuilderInitialCollection{``0,``1})">
            <summary>
            Gets the configurable rule instance from a rule builder.
            </summary>
            <param name="ruleBuilder">The rule builder.</param>
            <returns>A configurable IValidationRule instance.</returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Cascade``2(FluentValidation.IRuleBuilderInitial{``0,``1},FluentValidation.CascadeMode)">
            <summary>
            <para>
            Specifies the cascade mode for failures.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Stop"/> then execution of the rule will stop once the first validator in the chain fails.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Continue"/> then all validators in the chain will execute regardless of failures.
            </para>
            <para>
            If set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>, behavior is as with <see cref="F:FluentValidation.CascadeMode.Stop"/>.
            </para>
            </summary>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Cascade``2(FluentValidation.IRuleBuilderInitialCollection{``0,``1},FluentValidation.CascadeMode)">
            <summary>
            <para>
            Specifies the cascade mode for failures.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Stop"/> then execution of the rule will stop once the first validator in the chain fails.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Continue"/> then all validators in the chain will execute regardless of failures.
            </para>
            <para>
            If set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>, behaviour is as with <see cref="F:FluentValidation.CascadeMode.Stop"/>.
            </para>
            </summary>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithMessage``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.String)">
            <summary>
            Specifies a custom error message to use when validation fails. Only applies to the rule that directly precedes it.
            </summary>
            <param name="rule">The current rule</param>
            <param name="errorMessage">The error message to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithMessage``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.String})">
            <summary>
            Specifies a custom error message to use when validation fails. Only applies to the rule that directly precedes it.
            </summary>
            <param name="rule">The current rule</param>
            <param name="messageProvider">Delegate that will be invoked to retrieve the localized message. </param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithMessage``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,``1,System.String})">
            <summary>
            Specifies a custom error message to use when validation fails. Only applies to the rule that directly precedes it.
            </summary>
            <param name="rule">The current rule</param>
            <param name="messageProvider">Delegate that will be invoked.Uses_localized_name to retrieve the localized message. </param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithErrorCode``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.String)">
            <summary>
            Specifies a custom error code to use if validation fails.
            </summary>
            <param name="rule">The current rule</param>
            <param name="errorCode">The error code to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.When``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.When``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.When``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.When``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Unless``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Unless``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Unless``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Unless``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies a condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WhenAsync``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WhenAsync``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WhenAsync``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WhenAsync``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should run.
            The validator will only be executed if the result of the lambda returns true.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.UnlessAsync``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.UnlessAsync``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.UnlessAsync``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.UnlessAsync``2(FluentValidation.IRuleBuilderOptionsConditions{``0,``1},System.Func{``0,FluentValidation.ValidationContext{``0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Specifies an asynchronous condition limiting when the validator should not run.
            The validator will only be executed if the result of the lambda returns false.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">A lambda expression that specifies a condition for when the validator should not run</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current rule or all rules in the chain</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.Where``2(FluentValidation.IRuleBuilderInitialCollection{``0,``1},System.Func{``1,System.Boolean})">
            <summary>
            Applies a filter to a collection property.
            </summary>
            <param name="rule">The current rule</param>
            <param name="predicate">The condition</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithName``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.String)">
            <summary>
            Specifies a custom property name to use within the error message.
            </summary>
            <param name="rule">The current rule</param>
            <param name="overridePropertyName">The property name to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithName``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.String})">
            <summary>
            Specifies a custom property name to use within the error message.
            </summary>
            <param name="rule">The current rule</param>
            <param name="nameProvider">Func used to retrieve the property's display name</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.OverridePropertyName``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.String)">
            <summary>
            Overrides the name of the property associated with this rule.
            NOTE: This is a considered to be an advanced feature. Most of the time that you use this, you actually meant to use WithName.
            </summary>
            <param name="rule">The current rule</param>
            <param name="propertyName">The property name to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.OverridePropertyName``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Linq.Expressions.Expression{System.Func{``0,System.Object}})">
            <summary>
            Overrides the name of the property associated with this rule.
            NOTE: This is a considered to be an advanced feature. Most of the time that you use this, you actually meant to use WithName.
            </summary>
            <param name="rule">The current rule</param>
            <param name="expr">An expression referencing another property</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithState``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,System.Object})">
            <summary>
            Specifies custom state that should be stored alongside the validation message when validation fails for this rule.
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="rule"></param>
            <param name="stateProvider"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithState``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,``1,System.Object})">
            <summary>
            Specifies custom state that should be stored alongside the validation message when validation fails for this rule.
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="rule"></param>
            <param name="stateProvider"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithSeverity``2(FluentValidation.IRuleBuilderOptions{``0,``1},FluentValidation.Severity)">
            <summary>
             Specifies custom severity that should be stored alongside the validation message when validation fails for this rule.
             </summary>
             <typeparam name="T"></typeparam>
             <typeparam name="TProperty"></typeparam>
             <param name="rule"></param>
             <param name="severity"></param>
             <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithSeverity``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,FluentValidation.Severity})">
            <summary>
            Specifies custom severity that should be stored alongside the validation message when validation fails for this rule.
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="rule"></param>
            <param name="severityProvider"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithSeverity``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,``1,FluentValidation.Severity})">
            <summary>
            Specifies custom severity that should be stored alongside the validation message when validation fails for this rule.
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="rule"></param>
            <param name="severityProvider"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.WithSeverity``2(FluentValidation.IRuleBuilderOptions{``0,``1},System.Func{``0,``1,FluentValidation.ValidationContext{``0},FluentValidation.Severity})">
            <summary>
            Specifies custom severity that should be stored alongside the validation message when validation fails for this rule.
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
            <param name="rule"></param>
            <param name="severityProvider"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.DefaultValidatorOptions.OverrideIndexer``2(FluentValidation.IRuleBuilderInitialCollection{``0,``1},System.Func{``0,System.Collections.Generic.IEnumerable{``1},``1,System.Int32,System.String})">
            <summary>
            Allows the generated indexer to be overridden for collection rules.
            </summary>
            <param name="rule">The current rule</param>
            <param name="callback">The callback. Receives the model, the collection, the current element and the current index as parameters. Should return a string representation of the indexer. The default is "[" + index + "]"</param>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.CascadeMode">
            <summary>
            Specifies how rules should cascade when one fails.
            </summary>
        </member>
        <member name="F:FluentValidation.CascadeMode.Continue">
            <summary>
            When a rule/validator fails, execution continues to the next rule/validator.
            For more information, see the methods/properties that accept this enum as a parameter.
            </summary>
        </member>
        <member name="F:FluentValidation.CascadeMode.StopOnFirstFailure">
            <summary>
            For more information, see the methods/properties that accept this enum as a parameter.
            </summary>
        </member>
        <member name="F:FluentValidation.CascadeMode.Stop">
            <summary>
            When a rule/validator fails, validation is stopped for the current rule/validator.
            For more information, see the methods/properties that accept this enum as a parameter.
            </summary>
        </member>
        <member name="T:FluentValidation.ApplyConditionTo">
            <summary>
            Specifies where a When/Unless condition should be applied
            </summary>
        </member>
        <member name="F:FluentValidation.ApplyConditionTo.AllValidators">
            <summary>
            Applies the condition to all validators declared so far in the chain.
            </summary>
        </member>
        <member name="F:FluentValidation.ApplyConditionTo.CurrentValidator">
            <summary>
            Applies the condition to the current validator only.
            </summary>
        </member>
        <member name="T:FluentValidation.Severity">
            <summary>
            Specifies the severity of a rule.
            </summary>
        </member>
        <member name="F:FluentValidation.Severity.Error">
            <summary>
            Error
            </summary>
        </member>
        <member name="F:FluentValidation.Severity.Warning">
            <summary>
            Warning
            </summary>
        </member>
        <member name="F:FluentValidation.Severity.Info">
            <summary>
            Info
            </summary>
        </member>
        <member name="T:FluentValidation.ICollectionRule`2">
            <summary>
            Represents a rule defined against a collection with RuleForEach.
            </summary>
            <typeparam name="T">Root object</typeparam>
            <typeparam name="TElement">Type of each element in the collection</typeparam>
        </member>
        <member name="P:FluentValidation.ICollectionRule`2.Filter">
            <summary>
            Filter that should include/exclude items in the collection.
            </summary>
        </member>
        <member name="P:FluentValidation.ICollectionRule`2.IndexBuilder">
            <summary>
            Constructs the indexer in the property name associated with the error message.
            By default this is "[" + index + "]"
            </summary>
        </member>
        <member name="T:FluentValidation.InlineValidator`1">
             <summary>
             Validator implementation that allows rules to be defined without inheriting from AbstractValidator.
             </summary>
             <example>
             <code>
             public class Customer {
               public int Id { get; set; }
               public string Name { get; set; }
            
               public static readonly InlineValidator&lt;Customer&gt; Validator = new InlineValidator&lt;Customer&gt; {
                 v =&gt; v.RuleFor(x =&gt; x.Name).NotNull(),
                 v =&gt; v.RuleFor(x =&gt; x.Id).NotEqual(0),
               }
             }
             </code>
             </example>
             <typeparam name="T"></typeparam>
        </member>
        <member name="M:FluentValidation.InlineValidator`1.Add``1(System.Func{FluentValidation.InlineValidator{`0},FluentValidation.IRuleBuilderOptions{`0,``0}})">
            <summary>
            Allows configuration of the validator.
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.AccessorCache`1">
            <summary>
            Member accessor cache.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:FluentValidation.Internal.AccessorCache`1.GetCachedAccessor``1(System.Reflection.MemberInfo,System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Boolean,System.String)">
            <summary>
            Gets an accessor func based on an expression
            </summary>
            <typeparam name="TProperty"></typeparam>
            <param name="member">The member represented by the expression</param>
            <param name="expression"></param>
            <param name="bypassCache"></param>
            <param name="cachePrefix">Cache prefix</param>
            <returns>Accessor func</returns>
        </member>
        <member name="T:FluentValidation.Internal.ChildRulesContainer`1">
            <summary>
            AbstractValidator implementation for containing child rules.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="F:FluentValidation.Internal.ChildRulesContainer`1.RuleSetsToApplyToChildRules">
            <summary>
            Used to keep track of rulesets from parent that need to be applied
            to child rules in the case of multiple nested child rules.
            </summary>
            <see cref="M:FluentValidation.DefaultValidatorExtensions.ChildRules``2(FluentValidation.IRuleBuilder{``0,``1},System.Action{FluentValidation.InlineValidator{``1}})"/>
        </member>
        <member name="T:FluentValidation.Internal.CollectionPropertyRule`2">
            <summary>
            Rule definition for collection properties
            </summary>
            <typeparam name="TElement"></typeparam>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:FluentValidation.Internal.CollectionPropertyRule`2.#ctor(System.Reflection.MemberInfo,System.Func{`0,System.Collections.Generic.IEnumerable{`1}},System.Linq.Expressions.LambdaExpression,System.Func{FluentValidation.CascadeMode},System.Type)">
            <summary>
            Initializes new instance of the CollectionPropertyRule class
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.CollectionPropertyRule`2.Filter">
            <summary>
            Filter that should include/exclude items in the collection.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.CollectionPropertyRule`2.IndexBuilder">
            <summary>
            Constructs the indexer in the property name associated with the error message.
            By default this is "[" + index + "]"
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.CollectionPropertyRule`2.Create(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{`1}}},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.CollectionPropertyRule`2.CreateTransformed``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{``0,`1},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.CollectionPropertyRule`2.CreateTransformed``1(System.Linq.Expressions.Expression{System.Func{`0,System.Collections.Generic.IEnumerable{``0}}},System.Func{`0,``0,`1},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.ConditionBuilder`1.When(System.Func{`0,FluentValidation.ValidationContext{`0},System.Boolean},System.Action)">
            <summary>
            Defines a condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ConditionBuilder`1.Unless(System.Func{`0,FluentValidation.ValidationContext{`0},System.Boolean},System.Action)">
            <summary>
            Defines an inverse condition that applies to several rules
            </summary>
            <param name="predicate">The condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="M:FluentValidation.Internal.AsyncConditionBuilder`1.WhenAsync(System.Func{`0,FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should apply to multiple rules</param>
            <param name="action">Action that encapsulates the rules.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.AsyncConditionBuilder`1.UnlessAsync(System.Func{`0,FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},System.Action)">
            <summary>
            Defines an inverse asynchronous condition that applies to several rules
            </summary>
            <param name="predicate">The asynchronous condition that should be applied to multiple rules</param>
            <param name="action">Action that encapsulates the rules</param>
        </member>
        <member name="T:FluentValidation.Internal.DefaultValidatorSelector">
            <summary>
            Default validator selector that will execute all rules that do not belong to a RuleSet.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.DefaultValidatorSelector.CanExecute(FluentValidation.IValidationRule,System.String,FluentValidation.IValidationContext)">
            <summary>
            Determines whether or not a rule should execute.
            </summary>
            <param name="rule">The rule</param>
            <param name="propertyPath">Property path (eg Customer.Address.Line1)</param>
            <param name="context">Contextual information</param>
            <returns>Whether or not the validator can execute.</returns>
        </member>
        <member name="T:FluentValidation.Internal.Extensions">
            <summary>
            Useful extensions
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.Extensions.GetMember``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Gets a MemberInfo from a member expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.ExtensionsInternal.IsParameterExpression(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Checks if the expression is a parameter expression
            </summary>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ExtensionsInternal.SplitPascalCase(System.String)">
            <summary>
            Splits pascal case, so "FooBar" would become "Foo Bar".
            </summary>
            <remarks>
            Pascal case strings with periods delimiting the upper case letters,
            such as "Address.Line1", will have the periods removed.
            </remarks>
        </member>
        <member name="T:FluentValidation.Internal.IIncludeRule">
            <summary>
            Marker interface indicating an include rule.
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.IncludeRule`1">
            <summary>
            Include rule
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IncludeRule`1.#ctor(FluentValidation.IValidator{`0},System.Func{FluentValidation.CascadeMode},System.Type)">
            <summary>
            Creates a new IncludeRule
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IncludeRule`1.#ctor(System.Func{FluentValidation.ValidationContext{`0},`0,FluentValidation.IValidator{`0}},System.Func{FluentValidation.CascadeMode},System.Type,System.Type)">
            <summary>
            Creates a new IncludeRule
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IncludeRule`1.Create(FluentValidation.IValidator{`0},System.Func{FluentValidation.CascadeMode})">
            <summary>
            Creates a new include rule from an existing validator
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IncludeRule`1.Create``1(System.Func{`0,``0},System.Func{FluentValidation.CascadeMode})">
            <summary>
            Creates a new include rule from an existing validator
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.IRuleComponent`2">
            <summary>
            An individual component within a rule with a validator attached.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent`2.ErrorCode">
            <summary>
            The error code associated with this rule component.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent`2.CustomStateProvider">
            <summary>
            Function used to retrieve custom state for the validator
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent`2.SeverityProvider">
            <summary>
            Function used to retrieve the severity for the validator
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IRuleComponent`2.ApplyCondition(System.Func{FluentValidation.ValidationContext{`0},System.Boolean})">
            <summary>
            Adds a condition for this validator. If there's already a condition, they're combined together with an AND.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:FluentValidation.Internal.IRuleComponent`2.ApplyAsyncCondition(System.Func{FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Adds a condition for this validator. If there's already a condition, they're combined together with an AND.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:FluentValidation.Internal.IRuleComponent`2.SetErrorMessage(System.Func{FluentValidation.ValidationContext{`0},`1,System.String})">
            <summary>
            Sets the overridden error message template for this validator.
            </summary>
            <param name="errorFactory">A function for retrieving the error message template.</param>
        </member>
        <member name="M:FluentValidation.Internal.IRuleComponent`2.SetErrorMessage(System.String)">
            <summary>
            Sets the overridden error message template for this validator.
            </summary>
            <param name="errorMessage">The error message to set</param>
        </member>
        <member name="T:FluentValidation.Internal.IRuleComponent">
            <summary>
            An individual component within a rule with a validator attached.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent.HasCondition">
            <summary>
            Whether or not this validator has a condition associated with it.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent.HasAsyncCondition">
            <summary>
            Whether or not this validator has an async condition associated with it.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent.Validator">
            <summary>
            The validator associated with this component.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IRuleComponent.GetUnformattedErrorMessage">
            <summary>
            Gets the raw unformatted error message. Placeholders will not have been rewritten.
            </summary>
            <returns></returns>
        </member>
        <member name="P:FluentValidation.Internal.IRuleComponent.ErrorCode">
            <summary>
            The error code associated with this rule component.
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.IValidatorSelector">
            <summary>
            Determines whether or not a rule should execute.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.IValidatorSelector.CanExecute(FluentValidation.IValidationRule,System.String,FluentValidation.IValidationContext)">
            <summary>
            Determines whether or not a rule should execute.
            </summary>
            <param name="rule">The rule</param>
            <param name="propertyPath">Property path (eg Customer.Address.Line1)</param>
            <param name="context">Contextual information</param>
            <returns>Whether or not the validator can execute.</returns>
        </member>
        <member name="T:FluentValidation.Internal.MemberNameValidatorSelector">
            <summary>
            Selects validators that are associated with a particular property.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.MemberNameValidatorSelector.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of MemberNameValidatorSelector.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.MemberNameValidatorSelector.MemberNames">
            <summary>
            Member names that are validated.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.MemberNameValidatorSelector.CanExecute(FluentValidation.IValidationRule,System.String,FluentValidation.IValidationContext)">
            <summary>
            Determines whether or not a rule should execute.
            </summary>
            <param name="rule">The rule</param>
            <param name="propertyPath">Property path (eg Customer.Address.Line1)</param>
            <param name="context">Contextual information</param>
            <returns>Whether or not the validator can execute.</returns>
        </member>
        <member name="M:FluentValidation.Internal.MemberNameValidatorSelector.MemberNamesFromExpressions``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}}[])">
            <summary>
            Gets member names from expressions
            </summary>
            <param name="propertyExpressions"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.Internal.MessageFormatter">
            <summary>
            Assists in the construction of validation messages.
            </summary>
        </member>
        <member name="F:FluentValidation.Internal.MessageFormatter.PropertyName">
            <summary>
            Default Property Name placeholder.
            </summary>
        </member>
        <member name="F:FluentValidation.Internal.MessageFormatter.PropertyValue">
            <summary>
            Default Property Value placeholder.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.MessageFormatter.AppendArgument(System.String,System.Object)">
            <summary>
            Adds a value for a validation message placeholder.
            </summary>
            <param name="name"></param>
            <param name="value"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.MessageFormatter.AppendPropertyName(System.String)">
            <summary>
            Appends a property name to the message.
            </summary>
            <param name="name">The name of the property</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.MessageFormatter.AppendPropertyValue(System.Object)">
            <summary>
            Appends a property value to the message.
            </summary>
            <param name="value">The value of the property</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.MessageFormatter.BuildMessage(System.String)">
            <summary>
            Constructs the final message from the specified template.
            </summary>
            <param name="messageTemplate">Message template</param>
            <returns>The message with placeholders replaced with their appropriate values</returns>
        </member>
        <member name="P:FluentValidation.Internal.MessageFormatter.PlaceholderValues">
            <summary>
            Additional placeholder values
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.PropertyChain">
            <summary>
            Represents a chain of properties
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.#ctor">
            <summary>
            Creates a new PropertyChain.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.#ctor(FluentValidation.Internal.PropertyChain)">
            <summary>
            Creates a new PropertyChain based on another.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new PropertyChain
            </summary>
            <param name="memberNames"></param>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.FromExpression(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Creates a PropertyChain from a lambda expression
            </summary>
            <param name="expression"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.Add(System.Reflection.MemberInfo)">
            <summary>
            Adds a MemberInfo instance to the chain
            </summary>
            <param name="member">Member to add</param>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.Add(System.String)">
            <summary>
            Adds a property name to the chain
            </summary>
            <param name="propertyName">Name of the property to add</param>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.AddIndexer(System.Object,System.Boolean)">
            <summary>
            Adds an indexer to the property chain. For example, if the following chain has been constructed:
            Parent.Child
            then calling AddIndexer(0) would convert this to:
            Parent.Child[0]
            </summary>
            <param name="indexer"></param>
            <param name="surroundWithBrackets">Whether square brackets should be applied before and after the indexer. Default true.</param>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.ToString">
            <summary>
            Creates a string representation of a property chain.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.IsChildChainOf(FluentValidation.Internal.PropertyChain)">
            <summary>
            Checks if the current chain is the child of another chain.
            For example, if chain1 were for "Parent.Child" and chain2 were for "Parent.Child.GrandChild" then
            chain2.IsChildChainOf(chain1) would be true.
            </summary>
            <param name="parentChain">The parent chain to compare</param>
            <returns>True if the current chain is the child of the other chain, otherwise false</returns>
        </member>
        <member name="M:FluentValidation.Internal.PropertyChain.BuildPropertyPath(System.String)">
            <summary>
            Builds a property path.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.PropertyChain.Count">
            <summary>
            Number of member names in the chain
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.PropertyRule`2">
            <summary>
            Defines a rule associated with a property.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyRule`2.Create(System.Linq.Expressions.Expression{System.Func{`0,`1}},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyRule`2.Create``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{``0,`1},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyRule`2.Create``1(System.Linq.Expressions.Expression{System.Func{`0,``0}},System.Func{`0,``0,`1},System.Func{FluentValidation.CascadeMode},System.Boolean)">
            <summary>
            Creates a new property rule from a lambda expression.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.PropertyRule`2.ValidateAsync(FluentValidation.ValidationContext{`0},System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Performs validation using a validation context and adds collected validation failures to the Context.
            </summary>
            <param name="context">Validation Context</param>
            <param name="useAsync">
            Whether asynchronous components are allowed to execute.
            This will be set to True when ValidateAsync is called on the root validator.
            This will be set to False when Validate is called on the root validator.
            When set to True, asynchronous components and asynchronous conditions will be executed.
            When set to False, an exception will be thrown if a component can only be executed asynchronously or if a component has an async condition associated with it.
            </param>
            <param name="cancellation"></param>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.FluentValidation#IValidationRule#Components">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.Condition">
            <summary>
            Condition for all validators in this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.AsyncCondition">
            <summary>
            Asynchronous condition for all validators in this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.Member">
            <summary>
            Property associated with this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.PropertyFunc">
            <summary>
            Function that can be invoked to retrieve the value of the property.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.Expression">
            <summary>
            Expression that was used to create the rule.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.SetDisplayName(System.String)">
            <summary>
            Sets the display name for the property.
            </summary>
            <param name="name">The property's display name</param>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.SetDisplayName(System.Func{FluentValidation.ValidationContext{`0},System.String})">
            <summary>
            Sets the display name for the property using a function.
            </summary>
            <param name="factory">The function for building the display name</param>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.RuleSets">
            <summary>
            Rule set that this rule belongs to (if specified)
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.Current">
            <summary>
            The current rule component.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.TypeToValidate">
            <summary>
            Type of the property being validated
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.HasCondition">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.HasAsyncCondition">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.CascadeMode">
            <summary>
            Cascade mode for this rule.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.#ctor(System.Reflection.MemberInfo,System.Func{`0,`1},System.Linq.Expressions.LambdaExpression,System.Func{FluentValidation.CascadeMode},System.Type)">
            <summary>
            Creates a new property rule.
            </summary>
            <param name="member">Property</param>
            <param name="propertyFunc">Function to get the property value</param>
            <param name="expression">Lambda expression used to create the rule</param>
            <param name="cascadeModeThunk">Function to get the cascade mode.</param>
            <param name="typeToValidate">Type to validate</param>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.ClearValidators">
            <summary>
            Clear all validators from this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.PropertyName">
            <summary>
            Returns the property name for the property being validated.
            Returns null if it is not a property being validated (eg a method call)
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.MessageBuilder">
            <summary>
            Allows custom creation of an error message
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBase`3.DependentRules">
            <summary>
            Dependent rules
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.GetDisplayName(FluentValidation.ValidationContext{`0})">
            <summary>
            Display name for the property.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.ApplyCondition(System.Func{FluentValidation.ValidationContext{`0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Applies a condition to the rule
            </summary>
            <param name="predicate"></param>
            <param name="applyConditionTo"></param>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.ApplyAsyncCondition(System.Func{FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Applies the condition to the rule asynchronously
            </summary>
            <param name="predicate"></param>
            <param name="applyConditionTo"></param>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.PrepareMessageFormatterForValidationError(FluentValidation.ValidationContext{`0},`2)">
            <summary>
            Prepares the <see cref="T:FluentValidation.Internal.MessageFormatter"/> of <paramref name="context"/> for an upcoming <see cref="T:FluentValidation.Results.ValidationFailure"/>.
            </summary>
            <param name="context">The validator context</param>
            <param name="value">Property value.</param>
        </member>
        <member name="M:FluentValidation.Internal.RuleBase`3.CreateValidationError(FluentValidation.ValidationContext{`0},`2,FluentValidation.Internal.RuleComponent{`0,`2})">
            <summary>
            Creates an error validation result for this validator.
            </summary>
            <param name="context">The validator context</param>
            <param name="value">The property value</param>
            <param name="component">The current rule component.</param>
            <returns>Returns an error validation result.</returns>
        </member>
        <member name="T:FluentValidation.Internal.RuleBuilder`2">
            <summary>
            Builds a validation rule and constructs a validator.
            </summary>
            <typeparam name="T">Type of object being validated</typeparam>
            <typeparam name="TProperty">Type of property being validated</typeparam>
        </member>
        <member name="P:FluentValidation.Internal.RuleBuilder`2.Rule">
            <summary>
            The rule being created by this RuleBuilder.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleBuilder`2.ParentValidator">
            <summary>
            Parent validator
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleBuilder`2.#ctor(FluentValidation.IValidationRuleInternal{`0,`1},FluentValidation.AbstractValidator{`0})">
            <summary>
            Creates a new instance of the <see cref="T:FluentValidation.Internal.RuleBuilder`2">RuleBuilder</see> class.
            </summary>
        </member>
        <member name="T:FluentValidation.Internal.RuleComponent`2">
            <summary>
            An individual component within a rule.
            In a rule definition such as RuleFor(x => x.Name).NotNull().NotEqual("Foo")
            the NotNull and the NotEqual are both rule components.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.HasCondition">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.HasAsyncCondition">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.Validator">
            <inheritdoc />
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.ApplyCondition(System.Func{FluentValidation.ValidationContext{`0},System.Boolean})">
            <summary>
            Adds a condition for this validator. If there's already a condition, they're combined together with an AND.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.ApplyAsyncCondition(System.Func{FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Adds a condition for this validator. If there's already a condition, they're combined together with an AND.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.CustomStateProvider">
            <summary>
            Function used to retrieve custom state for the validator
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.SeverityProvider">
            <summary>
            Function used to retrieve the severity for the validator
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RuleComponent`2.ErrorCode">
            <summary>
            Retrieves the error code.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.GetErrorMessage(FluentValidation.ValidationContext{`0},`1)">
            <summary>
            Gets the error message. If a context is supplied, it will be used to format the message if it has placeholders.
            If no context is supplied, the raw unformatted message will be returned, containing placeholders.
            </summary>
            <param name="context">The validation context.</param>
            <param name="value">The current property value.</param>
            <returns>Either the formatted or unformatted error message.</returns>
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.GetUnformattedErrorMessage">
            <summary>
            Gets the raw unformatted error message. Placeholders will not have been rewritten.
            </summary>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.SetErrorMessage(System.Func{FluentValidation.ValidationContext{`0},`1,System.String})">
            <summary>
            Sets the overridden error message template for this validator.
            </summary>
            <param name="errorFactory">A function for retrieving the error message template.</param>
        </member>
        <member name="M:FluentValidation.Internal.RuleComponent`2.SetErrorMessage(System.String)">
            <summary>
            Sets the overridden error message template for this validator.
            </summary>
            <param name="errorMessage">The error message to set</param>
        </member>
        <member name="T:FluentValidation.Internal.RulesetValidatorSelector">
            <summary>
            Selects validators that belong to the specified rulesets.
            </summary>
        </member>
        <member name="P:FluentValidation.Internal.RulesetValidatorSelector.RuleSets">
            <summary>
            Rule sets
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RulesetValidatorSelector.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Creates a new instance of the RulesetValidatorSelector.
            </summary>
        </member>
        <member name="M:FluentValidation.Internal.RulesetValidatorSelector.CanExecute(FluentValidation.IValidationRule,System.String,FluentValidation.IValidationContext)">
            <summary>
            Determines whether or not a rule should execute.
            </summary>
            <param name="rule">The rule</param>
            <param name="propertyPath">Property path (eg Customer.Address.Line1)</param>
            <param name="context">Contextual information</param>
            <returns>Whether or not the validator can execute.</returns>
        </member>
        <member name="M:FluentValidation.Internal.RulesetValidatorSelector.IsIncludeRule(FluentValidation.IValidationRule)">
            <summary>
            Checks if the rule is an IncludeRule
            </summary>
            <param name="rule"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.IncludeProperties(System.String[])">
            <summary>
            Indicates that only the specified properties should be validated.
            </summary>
            <param name="properties">The property names to validate.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.IncludeProperties(System.Linq.Expressions.Expression{System.Func{`0,System.Object}}[])">
            <summary>
            Indicates that only the specified properties should be validated.
            </summary>
            <param name="propertyExpressions">The properties to validate, defined as expressions.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.IncludeRulesNotInRuleSet">
            <summary>
            Indicates that all rules not in a rule-set should be included for validation (the equivalent of calling IncludeRuleSets("default")).
            This method can be combined with IncludeRuleSets.
            </summary>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.IncludeAllRuleSets">
            <summary>
            Indicates that all rules should be executed, regardless of whether or not they're in a ruleset.
            This is the equivalent of IncludeRuleSets("*").
            </summary>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.IncludeRuleSets(System.String[])">
            <summary>
            Indicates that only the specified rule sets should be validated.
            </summary>
            <param name="ruleSets">The names of the rulesets to validate.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.UseCustomSelector(FluentValidation.Internal.IValidatorSelector)">
            <summary>
            Indicates that the specified selector should be used to control which rules are executed.
            </summary>
            <param name="selector">The custom selector to use</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Internal.ValidationStrategy`1.ThrowOnFailures">
            <summary>
            Indicates that the validator should throw an exception if it fails, rather than return a validation result.
            </summary>
            <returns></returns>
        </member>
        <member name="P:FluentValidation.IValidationContext.InstanceToValidate">
            <summary>
            The object currently being validated.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.RootContextData">
            <summary>
            Additional data associated with the validation request.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.PropertyChain">
            <summary>
            Property chain
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.Selector">
            <summary>
            Selector
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.IsChildContext">
            <summary>
            Whether this is a child context
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.IsChildCollectionContext">
            <summary>
            Whether this is a child collection context.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.ParentContext">
            <summary>
            Parent validation context.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.IsAsync">
            <summary>
            Whether this context is async.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationContext.ThrowOnFailures">
            <summary>
            Whether the validator should throw an exception if validation fails.
            The default is false.
            </summary>
        </member>
        <member name="T:FluentValidation.ValidationContext`1">
            <summary>
            Validation context
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.MessageFormatter">
            <summary>
            The message formatter used to construct error messages.
            </summary>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.#ctor(`0)">
            <summary>
            Creates a new validation context
            </summary>
            <param name="instanceToValidate"></param>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.#ctor(`0,FluentValidation.Internal.PropertyChain,FluentValidation.Internal.IValidatorSelector)">
            <summary>
            Creates a new validation context with a custom property chain and selector
            </summary>
            <param name="instanceToValidate"></param>
            <param name="propertyChain"></param>
            <param name="validatorSelector"></param>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.CreateWithOptions(`0,System.Action{FluentValidation.Internal.ValidationStrategy{`0}})">
            <summary>
            Creates a new validation context using the specified options.
            </summary>
            <param name="instanceToValidate">The instance to validate</param>
            <param name="options">Callback that allows extra options to be configured.</param>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.InstanceToValidate">
            <summary>
            The object to validate
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.RootContextData">
            <summary>
            Additional data associated with the validation request.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.PropertyChain">
            <summary>
            Property chain
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.FluentValidation#IValidationContext#InstanceToValidate">
            <summary>
            Object being validated
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.Selector">
            <summary>
            Selector
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.IsChildContext">
            <summary>
            Whether this is a child context
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.IsChildCollectionContext">
            <summary>
            Whether this is a child collection context.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.IsAsync">
            <inheritdoc />
        </member>
        <member name="P:FluentValidation.ValidationContext`1.ThrowOnFailures">
            <summary>
            Whether the root validator should throw an exception when validation fails.
            Defaults to false.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.SharedConditionCache">
            <summary>
            Shared condition results cache.
            The key of the outer dictionary is the ID of the condition, and its value is the cache for that condition.
            The key of the inner dictionary is the instance being validated, and the value is the condition result.
            </summary>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.GetFromNonGenericContext(FluentValidation.IValidationContext)">
            <summary>
            Gets or creates generic validation context from non-generic validation context.
            </summary>
            <param name="context"></param>
            <returns></returns>
            <exception cref="T:System.ArgumentNullException"></exception>
            <exception cref="T:System.NotSupportedException"></exception>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.CloneForChildValidator``1(``0,System.Boolean,FluentValidation.Internal.IValidatorSelector)">
            <summary>
            Creates a new validation context for use with a child validator
            </summary>
            <param name="instanceToValidate"></param>
            <param name="preserveParentContext"></param>
            <param name="selector"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.AddFailure(FluentValidation.Results.ValidationFailure)">
            <summary>
            Adds a new validation failure.
            </summary>
            <param name="failure">The failure to add.</param>
            <exception cref="T:System.ArgumentNullException"></exception>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.AddFailure(System.String,System.String)">
            <summary>
            Adds a new validation failure for the specified property.
            </summary>
            <param name="propertyName">The property name</param>
            <param name="errorMessage">The error message</param>
        </member>
        <member name="M:FluentValidation.ValidationContext`1.AddFailure(System.String)">
            <summary>
            Adds a new validation failure for the specified message.
            The failure will be associated with the current property being validated.
            </summary>
            <param name="errorMessage">The error message</param>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.DisplayName">
            <summary>
            Gets the display name for the current property being validated.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationContext`1.PropertyPath">
            <summary>
            The full path of the current property being validated.
            If accessed inside a child validator, this will include the parent's path too.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule`2.CascadeMode">
            <summary>
            Cascade mode for this rule.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidationRule`2.SetDisplayName(System.String)">
            <summary>
            Sets the display name for the property.
            </summary>
            <param name="name">The property's display name</param>
        </member>
        <member name="M:FluentValidation.IValidationRule`2.SetDisplayName(System.Func{FluentValidation.ValidationContext{`0},System.String})">
            <summary>
            Sets the display name for the property using a function.
            </summary>
            <param name="factory">The function for building the display name</param>
        </member>
        <member name="M:FluentValidation.IValidationRule`2.AddValidator(FluentValidation.Validators.IPropertyValidator{`0,`1})">
            <summary>
            Adds a validator to this rule.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidationRule`2.AddAsyncValidator(FluentValidation.Validators.IAsyncPropertyValidator{`0,`1},FluentValidation.Validators.IPropertyValidator{`0,`1})">
            <summary>
            Adds an async validator to this rule.
            </summary>
            <param name="asyncValidator">The async property validator to invoke</param>
            <param name="fallback">A synchronous property validator to use as a fallback if executed synchronously. This parameter is optional. If omitted, the async validator will be called synchronously if needed.</param>
        </member>
        <member name="P:FluentValidation.IValidationRule`2.Current">
            <summary>
            The current rule component.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule`2.MessageBuilder">
            <summary>
            Allows custom creation of an error message
            </summary>
        </member>
        <member name="M:FluentValidation.IValidationRule`1.ApplyCondition(System.Func{FluentValidation.ValidationContext{`0},System.Boolean},FluentValidation.ApplyConditionTo)">
            <summary>
            Applies a condition to a single rule chain.
            The condition can be applied to either the current property validator in the chain,
            or all preceding property validators in the chain (the default).
            </summary>
            <param name="predicate">The condition to apply</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current property validator in the chain, or all preceding property validators in the chain.</param>
        </member>
        <member name="M:FluentValidation.IValidationRule`1.ApplyAsyncCondition(System.Func{FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}},FluentValidation.ApplyConditionTo)">
            <summary>
            Applies an async condition to a single rule chain.
            The condition can be applied to either the current property validator in the chain,
            or all preceding property validators in the chain (the default).
            </summary>
            <param name="predicate">The condition to apply</param>
            <param name="applyConditionTo">Whether the condition should be applied to the current property validator in the chain, or all preceding property validators in the chain.</param>
        </member>
        <member name="M:FluentValidation.IValidationRule`1.ApplySharedCondition(System.Func{FluentValidation.ValidationContext{`0},System.Boolean})">
            <summary>
            Applies a pre-condition to this rule.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:FluentValidation.IValidationRule`1.ApplySharedAsyncCondition(System.Func{FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Applies an async pre-condition to this rule.
            </summary>
            <param name="condition"></param>
        </member>
        <member name="M:FluentValidation.IValidationRule`1.GetPropertyValue(`0)">
            <summary>
            Gets the property value for this rule. Note that this bypasses all conditions.
            </summary>
            <param name="instance">The model from which the property value should be retrieved.</param>
            <returns>The property value.</returns>
        </member>
        <member name="T:FluentValidation.IValidationRule">
            <summary>
            Defines a rule associated with a property which can have multiple validators.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.Components">
            <summary>
            The components in this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.RuleSets">
            <summary>
            Name of the rule-set to which this rule belongs.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidationRule.GetDisplayName(FluentValidation.IValidationContext)">
            <summary>
            Gets the display name for the property.
            </summary>
            <param name="context">Current context</param>
            <returns>Display name</returns>
        </member>
        <member name="P:FluentValidation.IValidationRule.PropertyName">
            <summary>
            Returns the property name for the property being validated.
            Returns null if it is not a property being validated (eg a method call)
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.Member">
            <summary>
            Property associated with this rule.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.TypeToValidate">
            <summary>
            Type of the property being validated
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.HasCondition">
            <summary>
            Whether the rule has a condition defined.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.HasAsyncCondition">
            <summary>
            Whether the rule has an async condition defined.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.Expression">
            <summary>
            Expression that was used to create the rule.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidationRule.DependentRules">
            <summary>
            Dependent rules.
            </summary>
        </member>
        <member name="T:FluentValidation.IValidator`1">
            <summary>
            Defines a validator for a particular type.
            </summary>
            <typeparam name="T"></typeparam>
        </member>
        <member name="M:FluentValidation.IValidator`1.Validate(`0)">
            <summary>
            Validates the specified instance.
            </summary>
            <param name="instance">The instance to validate</param>
            <returns>A ValidationResult object containing any validation failures.</returns>
        </member>
        <member name="M:FluentValidation.IValidator`1.ValidateAsync(`0,System.Threading.CancellationToken)">
            <summary>
            Validate the specified instance asynchronously
            </summary>
            <param name="instance">The instance to validate</param>
            <param name="cancellation"></param>
            <returns>A ValidationResult object containing any validation failures.</returns>
        </member>
        <member name="T:FluentValidation.IValidator">
            <summary>
            Defines a validator for a particular type.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidator.Validate(FluentValidation.IValidationContext)">
            <summary>
            Validates the specified instance.
            </summary>
            <param name="context">A ValidationContext</param>
            <returns>A ValidationResult object contains any validation failures.</returns>
        </member>
        <member name="M:FluentValidation.IValidator.ValidateAsync(FluentValidation.IValidationContext,System.Threading.CancellationToken)">
            <summary>
            Validates the specified instance asynchronously.
            </summary>
            <param name="context">A ValidationContext</param>
            <param name="cancellation">Cancellation token</param>
            <returns>A ValidationResult object contains any validation failures.</returns>
        </member>
        <member name="M:FluentValidation.IValidator.CreateDescriptor">
            <summary>
            Creates a hook to access various meta data properties
            </summary>
            <returns>A IValidatorDescriptor object which contains methods to access metadata</returns>
        </member>
        <member name="M:FluentValidation.IValidator.CanValidateInstancesOfType(System.Type)">
            <summary>
            Checks to see whether the validator can validate objects of the specified type
            </summary>
        </member>
        <member name="T:FluentValidation.IValidatorDescriptor">
            <summary>
            Provides metadata about a validator.
            </summary>
        </member>
        <member name="P:FluentValidation.IValidatorDescriptor.Rules">
            <summary>
            All rules defined in the validator.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorDescriptor.GetName(System.String)">
            <summary>
            Gets the name display name for a property.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorDescriptor.GetMembersWithValidators">
            <summary>
            Gets a collection of validators grouped by property.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorDescriptor.GetValidatorsForMember(System.String)">
            <summary>
            Gets validators for a particular property.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorDescriptor.GetRulesForMember(System.String)">
            <summary>
            Gets rules for a property.
            </summary>
        </member>
        <member name="T:FluentValidation.IValidatorFactory">
            <summary>
            Gets validators for a particular type.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorFactory.GetValidator``1">
            <summary>
            Gets the validator for the specified type.
            </summary>
        </member>
        <member name="M:FluentValidation.IValidatorFactory.GetValidator(System.Type)">
            <summary>
            Gets the validator for the specified type.
            </summary>
        </member>
        <member name="T:FluentValidation.Resources.ILanguageManager">
            <summary>
            Allows the default error message translations to be managed.
            </summary>
        </member>
        <member name="P:FluentValidation.Resources.ILanguageManager.Enabled">
            <summary>
            Whether localization is enabled.
            </summary>
        </member>
        <member name="P:FluentValidation.Resources.ILanguageManager.Culture">
            <summary>
            Default culture to use for all requests to the LanguageManager. If not specified, uses the current UI culture.
            </summary>
        </member>
        <member name="M:FluentValidation.Resources.ILanguageManager.GetString(System.String,System.Globalization.CultureInfo)">
            <summary>
            Gets a translated string based on its key. If the culture is specific and it isn't registered, we try the neutral culture instead.
            If no matching culture is found  to be registered we use English.
            </summary>
            <param name="key">The key</param>
            <param name="culture">The culture to translate into</param>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.Resources.LanguageManager">
            <summary>
            Allows the default error message translations to be managed.
            </summary>
        </member>
        <member name="M:FluentValidation.Resources.LanguageManager.GetTranslation(System.String,System.String)">
            <summary>
            Language factory.
            </summary>
            <param name="culture">The culture code.</param>
            <param name="key">The key to load</param>
            <returns>The corresponding Language instance or null.</returns>
        </member>
        <member name="P:FluentValidation.Resources.LanguageManager.Enabled">
            <summary>
            Whether localization is enabled.
            </summary>
        </member>
        <member name="P:FluentValidation.Resources.LanguageManager.Culture">
            <summary>
            Default culture to use for all requests to the LanguageManager. If not specified, uses the current UI culture.
            </summary>
        </member>
        <member name="M:FluentValidation.Resources.LanguageManager.Clear">
            <summary>
            Removes all languages except the default.
            </summary>
        </member>
        <member name="M:FluentValidation.Resources.LanguageManager.GetString(System.String,System.Globalization.CultureInfo)">
            <summary>
            Gets a translated string based on its key. If the culture is specific and it isn't registered, we try the neutral culture instead.
            If no matching culture is found  to be registered we use English.
            </summary>
            <param name="key">The key</param>
            <param name="culture">The culture to translate into</param>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.Results.ValidationFailure">
            <summary>
            Defines a validation failure
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationFailure.#ctor">
            <summary>
            Creates a new validation failure.
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationFailure.#ctor(System.String,System.String)">
            <summary>
            Creates a new validation failure.
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationFailure.#ctor(System.String,System.String,System.Object)">
            <summary>
            Creates a new ValidationFailure.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.PropertyName">
            <summary>
            The name of the property.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.ErrorMessage">
            <summary>
            The error message
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.AttemptedValue">
            <summary>
            The property value that caused the failure.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.CustomState">
            <summary>
            Custom state associated with the failure.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.Severity">
            <summary>
            Custom severity level associated with the failure.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.ErrorCode">
            <summary>
            Gets or sets the error code.
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationFailure.FormattedMessagePlaceholderValues">
            <summary>
            Gets or sets the formatted message placeholder values.
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationFailure.ToString">
            <summary>
            Creates a textual representation of the failure.
            </summary>
        </member>
        <member name="T:FluentValidation.Results.ValidationResult">
            <summary>
            The result of running a validator
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationResult.IsValid">
            <summary>
            Whether validation succeeded
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationResult.Errors">
            <summary>
            A collection of errors
            </summary>
        </member>
        <member name="P:FluentValidation.Results.ValidationResult.RuleSetsExecuted">
            <summary>
            The RuleSets that were executed during the validation run.
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.#ctor">
            <summary>
            Creates a new ValidationResult
            </summary>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.#ctor(System.Collections.Generic.IEnumerable{FluentValidation.Results.ValidationFailure})">
            <summary>
            Creates a new ValidationResult from a collection of failures
            </summary>
            <param name="failures">Collection of <see cref="T:FluentValidation.Results.ValidationFailure"/> instances which is later available through the <see cref="P:FluentValidation.Results.ValidationResult.Errors"/> property.</param>
            <remarks>
            Any nulls will be excluded.
            The list is copied.
            </remarks>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.#ctor(System.Collections.Generic.IEnumerable{FluentValidation.Results.ValidationResult})">
            <summary>
            Creates a new ValidationResult by combining several other ValidationResults.
            </summary>
            <param name="otherResults"></param>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.ToString">
            <summary>
            Generates a string representation of the error messages separated by new lines.
            </summary>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.ToString(System.String)">
            <summary>
            Generates a string representation of the error messages separated by the specified character.
            </summary>
            <param name="separator">The character to separate the error messages.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Results.ValidationResult.ToDictionary">
            <summary>
            Converts the ValidationResult's errors collection into a simple dictionary representation.
            </summary>
            <returns>A dictionary keyed by property name
            where each value is an array of error messages associated with that property.
            </returns>
        </member>
        <member name="T:FluentValidation.IRuleBuilderInitial`2">
            <summary>
            Rule builder that starts the chain
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
        </member>
        <member name="T:FluentValidation.IRuleBuilder`2">
            <summary>
            Rule builder
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
        </member>
        <member name="M:FluentValidation.IRuleBuilder`2.SetValidator(FluentValidation.Validators.IPropertyValidator{`0,`1})">
            <summary>
            Associates a validator with this the property for this rule builder.
            </summary>
            <param name="validator">The validator to set</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.IRuleBuilder`2.SetAsyncValidator(FluentValidation.Validators.IAsyncPropertyValidator{`0,`1})">
            <summary>
            Associates an async validator with this the property for this rule builder.
            </summary>
            <param name="validator">The validator to set</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.IRuleBuilder`2.SetValidator(FluentValidation.IValidator{`1},System.String[])">
            <summary>
            Associates an instance of IValidator with the current property rule.
            </summary>
            <param name="validator">The validator to use</param>
            <param name="ruleSets"></param>
        </member>
        <member name="M:FluentValidation.IRuleBuilder`2.SetValidator``1(System.Func{`0,``0},System.String[])">
            <summary>
            Associates a validator provider with the current property rule.
            </summary>
            <param name="validatorProvider">The validator provider to use</param>
            <param name="ruleSets"></param>
        </member>
        <member name="M:FluentValidation.IRuleBuilder`2.SetValidator``1(System.Func{`0,`1,``0},System.String[])">
            <summary>
            Associates a validator provider with the current property rule.
            </summary>
            <param name="validatorProvider">The validator provider to use</param>
            <param name="ruleSets"></param>
        </member>
        <member name="T:FluentValidation.IRuleBuilderOptions`2">
            <summary>
            Rule builder
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
        </member>
        <member name="M:FluentValidation.IRuleBuilderOptions`2.DependentRules(System.Action)">
            <summary>
            Creates a scope for declaring dependent rules.
            </summary>
        </member>
        <member name="T:FluentValidation.IRuleBuilderOptionsConditions`2">
            <summary>
            Rule builder (for validators that only support conditions, but no other options)
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TProperty"></typeparam>
        </member>
        <member name="T:FluentValidation.IRuleBuilderInitialCollection`2">
            <summary>
            Rule builder that starts the chain for a child collection
            </summary>
            <typeparam name="T"></typeparam>
            <typeparam name="TElement"></typeparam>
        </member>
        <member name="T:FluentValidation.IConditionBuilder">
            <summary>
            Fluent interface for conditions (When/Unless/WhenAsync/UnlessAsync)
            </summary>
        </member>
        <member name="M:FluentValidation.IConditionBuilder.Otherwise(System.Action)">
            <summary>
            Rules to be invoked if the condition fails.
            </summary>
            <param name="action"></param>
        </member>
        <member name="M:FluentValidation.TestHelper.ValidationTestExtension.TestValidate``1(FluentValidation.IValidator{``0},``0,System.Action{FluentValidation.Internal.ValidationStrategy{``0}})">
            <summary>
            Performs validation, returning a TestValidationResult which allows assertions to be performed.
            </summary>
        </member>
        <member name="M:FluentValidation.TestHelper.ValidationTestExtension.TestValidate``1(FluentValidation.IValidator{``0},FluentValidation.ValidationContext{``0})">
            <summary>
            Performs validation, returning a TestValidationResult which allows assertions to be performed.
            </summary>
        </member>
        <member name="M:FluentValidation.TestHelper.ValidationTestExtension.TestValidateAsync``1(FluentValidation.IValidator{``0},``0,System.Action{FluentValidation.Internal.ValidationStrategy{``0}},System.Threading.CancellationToken)">
            <summary>
            Performs async validation, returning a TestValidationResult which allows assertions to be performed.
            </summary>
        </member>
        <member name="M:FluentValidation.TestHelper.ValidationTestExtension.TestValidateAsync``1(FluentValidation.IValidator{``0},FluentValidation.ValidationContext{``0},System.Threading.CancellationToken)">
            <summary>
            Performs async validation, returning a TestValidationResult which allows assertions to be performed.
            </summary>
        </member>
        <member name="T:FluentValidation.ValidationException">
            <summary>
            An exception that represents failed validation
            </summary>
        </member>
        <member name="P:FluentValidation.ValidationException.Errors">
            <summary>
            Validation errors
            </summary>
        </member>
        <member name="M:FluentValidation.ValidationException.#ctor(System.String)">
            <summary>
            Creates a new ValidationException
            </summary>
            <param name="message"></param>
        </member>
        <member name="M:FluentValidation.ValidationException.#ctor(System.String,System.Collections.Generic.IEnumerable{FluentValidation.Results.ValidationFailure})">
            <summary>
            Creates a new ValidationException
            </summary>
            <param name="message"></param>
            <param name="errors"></param>
        </member>
        <member name="M:FluentValidation.ValidationException.#ctor(System.String,System.Collections.Generic.IEnumerable{FluentValidation.Results.ValidationFailure},System.Boolean)">
            <summary>
            Creates a new ValidationException
            </summary>
            <param name="message"></param>
            <param name="errors"></param>
            <param name="appendDefaultMessage">appends default validation error message to message</param>
        </member>
        <member name="M:FluentValidation.ValidationException.#ctor(System.Collections.Generic.IEnumerable{FluentValidation.Results.ValidationFailure})">
            <summary>
            Creates a new ValidationException
            </summary>
            <param name="errors"></param>
        </member>
        <member name="T:FluentValidation.ValidatorDescriptor`1">
            <summary>
            Used for providing metadata about a validator.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorDescriptor`1.Rules">
            <summary>
            Rules associated with the validator
            </summary>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.#ctor(System.Collections.Generic.IEnumerable{FluentValidation.IValidationRule})">
            <summary>
            Creates a ValidatorDescriptor
            </summary>
            <param name="rules"></param>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetName(System.String)">
            <summary>
            Gets the display name or a property property
            </summary>
            <param name="property"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetMembersWithValidators">
            <summary>
            Gets all members with their associated validators
            </summary>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetValidatorsForMember(System.String)">
            <summary>
            Gets validators for a specific member
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetRulesForMember(System.String)">
            <summary>
            Gets rules for a specific member
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetName(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
            <summary>
            Gets the member name from an expression
            </summary>
            <param name="propertyExpression"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.GetRulesByRuleset">
            <summary>
            Gets rules grouped by ruleset
            </summary>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.ValidatorDescriptor`1.RulesetMetadata">
            <summary>
            Information about rulesets
            </summary>
        </member>
        <member name="M:FluentValidation.ValidatorDescriptor`1.RulesetMetadata.#ctor(System.String,System.Collections.Generic.IEnumerable{FluentValidation.IValidationRule})">
            <summary>
            Creates a new RulesetMetadata
            </summary>
            <param name="name"></param>
            <param name="rules"></param>
        </member>
        <member name="P:FluentValidation.ValidatorDescriptor`1.RulesetMetadata.Name">
            <summary>
            Ruleset name
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorDescriptor`1.RulesetMetadata.Rules">
            <summary>
            Rules in the ruleset
            </summary>
        </member>
        <member name="T:FluentValidation.ValidatorFactoryBase">
            <summary>
            Factory for creating validators
            </summary>
        </member>
        <member name="M:FluentValidation.ValidatorFactoryBase.GetValidator``1">
            <summary>
            Gets a validator for a type
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorFactoryBase.GetValidator(System.Type)">
            <summary>
            Gets a validator for a type
            </summary>
            <param name="type"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.ValidatorFactoryBase.CreateInstance(System.Type)">
            <summary>
            Instantiates the validator
            </summary>
            <param name="validatorType"></param>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.ValidatorConfiguration">
            <summary>
            Configuration options for validators.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.CascadeMode">
            <summary>
            <para>
            Gets a single <see cref="P:FluentValidation.ValidatorConfiguration.CascadeMode"/> mode value representing the default values of
            <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>., based on the same logic as used when setting
            this property as described below.
            </para>
            <para>
            Sets the default values of <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>.
            </para>
            <para>
            If set to <see cref="F:FluentValidation.CascadeMode.Continue"/> or <see cref="F:FluentValidation.CascadeMode.Stop"/>, then both properties are set
            to that value by default.
            </para>
            <para>
            If set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>,
            then <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>
            is set to <see cref="F:FluentValidation.CascadeMode.Continue"/> by default, and <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>
            is set to <see cref="F:FluentValidation.CascadeMode.Stop"/> by default.
            This results in the same behaviour as before this property was deprecated.
            </para>
            <para>
            Note that cascade mode behaviour <i>within</i> individual rules is controlled by
            <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>.
            </para>
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.DefaultClassLevelCascadeMode">
            <summary>
            <para>
            Sets the default value for <see cref="P:FluentValidation.AbstractValidator`1.ClassLevelCascadeMode"/>.
            Defaults to <see cref="F:FluentValidation.CascadeMode.Continue"/> if not set.
            </para>
            <para>
            This cannot be set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>.
            <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>. Attempting to do so it will actually
            result in <see cref="F:FluentValidation.CascadeMode.Stop"/> being used.
            </para>
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.DefaultRuleLevelCascadeMode">
            <summary>
            <para>
            Sets the default value for <see cref="P:FluentValidation.AbstractValidator`1.RuleLevelCascadeMode"/>
            Defaults to <see cref="F:FluentValidation.CascadeMode.Continue"/> if not set.
            </para>
            <para>
            This cannot be set to the deprecated <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>.
            <see cref="F:FluentValidation.CascadeMode.StopOnFirstFailure"/>. Attempting to do so it will actually
            result in <see cref="F:FluentValidation.CascadeMode.Stop"/> being used.
            </para>
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.Severity">
            <summary>
            Default severity level
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.PropertyChainSeparator">
            <summary>
            Default property chain separator
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.LanguageManager">
            <summary>
            Default language manager
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.ValidatorSelectors">
            <summary>
            Customizations of validator selector
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.MessageFormatterFactory">
            <summary>
            Specifies a factory for creating MessageFormatter instances.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.PropertyNameResolver">
            <summary>
            Pluggable logic for resolving property names
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.DisplayNameResolver">
            <summary>
            Pluggable logic for resolving display names
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.DisableAccessorCache">
            <summary>
            Disables the expression accessor cache. Not recommended.
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.ErrorCodeResolver">
            <summary>
            Pluggable resolver for default error codes
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorConfiguration.OnFailureCreated">
            <summary>
            Defines a hook that runs when a <see cref="T:FluentValidation.Results.ValidationFailure"/> is created.
            </summary>
        </member>
        <member name="T:FluentValidation.ValidatorOptions">
            <summary>
            Validator runtime options
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorOptions.Global">
            <summary>
            Global configuration for all validators.
            </summary>
        </member>
        <member name="T:FluentValidation.ValidatorSelectorOptions">
            <summary>
            ValidatorSelector options
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorSelectorOptions.DefaultValidatorSelectorFactory">
            <summary>
            Factory func for creating the default validator selector
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorSelectorOptions.MemberNameValidatorSelectorFactory">
            <summary>
            Factory func for creating the member validator selector
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorSelectorOptions.RulesetValidatorSelectorFactory">
            <summary>
            Factory func for creating the ruleset validator selector
            </summary>
        </member>
        <member name="P:FluentValidation.ValidatorSelectorOptions.CompositeValidatorSelectorFactory">
            <summary>
            Factory func for creating the composite validator selector
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.AbstractComparisonValidator`2">
            <summary>
            Base class for all comparison validators
            </summary>
        </member>
        <member name="M:FluentValidation.Validators.AbstractComparisonValidator`2.#ctor(`1)">
            <summary>
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:FluentValidation.Validators.AbstractComparisonValidator`2.#ctor(System.Func{`0,System.ValueTuple{System.Boolean,`1}},System.Reflection.MemberInfo,System.String)">
            <summary>
            </summary>
            <param name="valueToCompareFunc"></param>
            <param name="member"></param>
            <param name="memberDisplayName"></param>
        </member>
        <member name="M:FluentValidation.Validators.AbstractComparisonValidator`2.#ctor(System.Func{`0,`1},System.Reflection.MemberInfo,System.String)">
            <summary>
            </summary>
            <param name="valueToCompareFunc"></param>
            <param name="member"></param>
            <param name="memberDisplayName"></param>
        </member>
        <member name="M:FluentValidation.Validators.AbstractComparisonValidator`2.IsValid(FluentValidation.ValidationContext{`0},`1)">
            <summary>
            Performs the comparison
            </summary>
            <param name="context"></param>
            <param name="propertyValue"></param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.AbstractComparisonValidator`2.IsValid(`1,`1)">
            <summary>
            Override to perform the comparison
            </summary>
            <param name="value"></param>
            <param name="valueToCompare"></param>
            <returns></returns>
        </member>
        <member name="P:FluentValidation.Validators.AbstractComparisonValidator`2.Comparison">
            <summary>
            Metadata- the comparison type
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.AbstractComparisonValidator`2.MemberToCompare">
            <summary>
            Metadata- the member being compared
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.AbstractComparisonValidator`2.ValueToCompare">
            <summary>
            The value being compared
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.AbstractComparisonValidator`2.FluentValidation#Validators#IComparisonValidator#ValueToCompare">
            <summary>
            Comparison value as non-generic for metadata.
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.IComparisonValidator">
            <summary>
            Defines a comparison validator
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.IComparisonValidator.Comparison">
            <summary>
            Metadata- the comparison type
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.IComparisonValidator.MemberToCompare">
            <summary>
            Metadata- the member being compared
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.IComparisonValidator.ValueToCompare">
            <summary>
            Metadata- the value being compared
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.AsyncPredicateValidator`2">
            <summary>
            Asynchronous custom validator
            </summary>
        </member>
        <member name="M:FluentValidation.Validators.AsyncPredicateValidator`2.#ctor(System.Func{`0,`1,FluentValidation.ValidationContext{`0},System.Threading.CancellationToken,System.Threading.Tasks.Task{System.Boolean}})">
            <summary>
            Creates a new AsyncPredicateValidator
            </summary>
            <param name="predicate"></param>
        </member>
        <member name="P:FluentValidation.Validators.AsyncPropertyValidator`2.Name">
            <inheritdoc />
        </member>
        <member name="M:FluentValidation.Validators.AsyncPropertyValidator`2.GetDefaultMessageTemplate(System.String)">
            <summary>
            Returns the default error message template for this validator, when not overridden.
            </summary>
            <param name="errorCode">The currently configured error code for the validator.</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.AsyncPropertyValidator`2.IsValidAsync(FluentValidation.ValidationContext{`0},`1,System.Threading.CancellationToken)">
            <inheritdoc />
        </member>
        <member name="M:FluentValidation.Validators.AsyncPropertyValidator`2.Localized(System.String,System.String)">
            <summary>
            Retrieves a localized string from the LanguageManager.
            If an ErrorCode is defined for this validator, the error code is used as the key.
            If no ErrorCode is defined (or the language manager doesn't have a translation for the error code)
            then the fallback key is used instead.
            </summary>
            <param name="errorCode">The currently configured error code for the validator.</param>
            <param name="fallbackKey">The fallback key to use for translation, if no ErrorCode is available.</param>
            <returns>The translated error message template.</returns>
        </member>
        <member name="T:FluentValidation.Validators.IChildValidatorAdaptor">
            <summary>
            Indicates that this validator wraps another validator.
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.IChildValidatorAdaptor.ValidatorType">
            <summary>
            The type of the underlying validator
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.CreditCardValidator`1">
            <summary>
            Ensures that the property value is a valid credit card number.
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.EmailValidationMode">
            <summary>
            Defines which mode should be used for email validation.
            </summary>
        </member>
        <member name="F:FluentValidation.Validators.EmailValidationMode.Net4xRegex">
            <summary>
            Uses a regular expression for email validation. This is the same regex used by the EmailAddressAttribute in .NET 4.x.
            </summary>
        </member>
        <member name="F:FluentValidation.Validators.EmailValidationMode.AspNetCoreCompatible">
            <summary>
            Uses the simplified ASP.NET Core logic for checking an email address, which just checks for the presence of an @ sign.
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.ExclusiveBetweenValidator`2">
            <summary>
            Performs range validation where the property value must be between the two specified values (exclusive).
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.InclusiveBetweenValidator`2">
            <summary>
            Performs range validation where the property value must be between the two specified values (inclusive).
            </summary>
        </member>
        <member name="M:FluentValidation.Validators.IAsyncPropertyValidator`2.IsValidAsync(FluentValidation.ValidationContext{`0},`1,System.Threading.CancellationToken)">
            <summary>
            Validates a specific property value asynchronously.
            </summary>
            <param name="context">The validation context. The parent object can be obtained from here.</param>
            <param name="value">The current property value to validate</param>
            <param name="cancellation">Cancellation token</param>
            <returns>True if valid, otherwise false.</returns>
        </member>
        <member name="M:FluentValidation.Validators.IPropertyValidator`2.IsValid(FluentValidation.ValidationContext{`0},`1)">
            <summary>
            Validates a specific property value.
            </summary>
            <param name="context">The validation context. The parent object can be obtained from here.</param>
            <param name="value">The current property value to validate</param>
            <returns>True if valid, otherwise false.</returns>
        </member>
        <member name="T:FluentValidation.Validators.IPropertyValidator">
            <summary>
            A custom property validator.
            This interface should not be implemented directly in your code as it is subject to change.
            Please inherit from <see cref="T:FluentValidation.Validators.PropertyValidator`2">PropertyValidator</see> instead.
            </summary>
        </member>
        <member name="P:FluentValidation.Validators.IPropertyValidator.Name">
            <summary>
            The name of the validator. This is usually the type name without any generic parameters.
            This is used as the default Error Code for the validator.
            </summary>
        </member>
        <member name="M:FluentValidation.Validators.IPropertyValidator.GetDefaultMessageTemplate(System.String)">
            <summary>
            Returns the default error message template for this validator, when not overridden.
            </summary>
            <param name="errorCode"></param>
            <returns></returns>
        </member>
        <member name="T:FluentValidation.Validators.PolymorphicValidator`2">
            <summary>
            Performs runtime checking of the value being validated, and passes validation off to a subclass validator.
            </summary>
            <typeparam name="T">Root model type</typeparam>
            <typeparam name="TProperty">Base type of property being validated.</typeparam>
        </member>
        <member name="M:FluentValidation.Validators.PolymorphicValidator`2.Add``1(FluentValidation.IValidator{``0},System.String[])">
            <summary>
            Adds a validator to handle a specific subclass.
            </summary>
            <param name="derivedValidator">The derived validator</param>
            <param name="ruleSets">Optionally specify rulesets to execute. If set, rules not in these rulesets will not be run</param>
            <typeparam name="TDerived"></typeparam>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.PolymorphicValidator`2.Add``1(System.Func{`0,FluentValidation.IValidator{``0}},System.String[])">
            <summary>
            Adds a validator to handle a specific subclass.
            </summary>
            <param name="validatorFactory">The derived validator</param>
            <typeparam name="TDerived"></typeparam>
            <param name="ruleSets">Optionally specify rulesets to execute. If set, rules not in these rulesets will not be run</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.PolymorphicValidator`2.Add``1(System.Func{`0,``0,FluentValidation.IValidator{``0}},System.String[])">
            <summary>
            Adds a validator to handle a specific subclass.
            </summary>
            <param name="validatorFactory">The derived validator</param>
            <typeparam name="TDerived"></typeparam>
            <param name="ruleSets">Optionally specify rulesets to execute. If set, rules not in these rulesets will not be run</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.PolymorphicValidator`2.Add(System.Type,FluentValidation.IValidator,System.String[])">
            <summary>
            Adds a validator to handle a specific subclass. This method is not publicly exposed as it
            takes a non-generic IValidator instance which could result in a type-unsafe validation operation.
            It allows derived validaors more flexibility in handling type conversion. If you make use of this method, you
            should ensure that the validator can correctly handle the type being validated.
            </summary>
            <param name="subclassType"></param>
            <param name="validator"></param>
            <param name="ruleSets">Optionally specify rulesets to execute. If set, rules not in these rulesets will not be run</param>
            <returns></returns>
        </member>
        <member name="M:FluentValidation.Validators.PropertyValidator`2.GetDefaultMessageTemplate(System.String)">
            <summary>
            Returns the default error message template for this validator, when not overridden.
            </summary>
            <param name="errorCode">The currently configured error code for the validator.</param>
            <returns></returns>
        </member>
        <member name="P:FluentValidation.Validators.PropertyValidator`2.Name">
            <inheritdoc />
        </member>
        <member name="M:FluentValidation.Validators.PropertyValidator`2.Localized(System.String,System.String)">
            <summary>
            Retrieves a localized string from the LanguageManager.
            If an ErrorCode is defined for this validator, the error code is used as the key.
            If no ErrorCode is defined (or the language manager doesn't have a translation for the error code)
            then the fallback key is used instead.
            </summary>
            <param name="errorCode">The currently configured error code for the validator.</param>
            <param name="fallbackKey">The fallback key to use for translation, if no ErrorCode is available.</param>
            <returns>The translated error message template.</returns>
        </member>
        <member name="M:FluentValidation.Validators.PropertyValidator`2.IsValid(FluentValidation.ValidationContext{`0},`1)">
            <summary>
            Validates a specific property value.
            </summary>
            <param name="context">The validation context. The parent object can be obtained from here.</param>
            <param name="value">The current property value to validate</param>
            <returns>True if valid, otherwise false.</returns>
        </member>
        <member name="T:FluentValidation.Validators.RangeValidator`2">
            <summary>
            Base class for range validation.
            </summary>
        </member>
        <member name="T:FluentValidation.Validators.ScalePrecisionValidator`1">
             <summary>
             Allows a decimal to be validated for scale and precision.
             Scale would be the number of digits to the right of the decimal point.
             Precision would be the number of digits. This number includes both the left and the right sides of the decimal point.
            
             It can be configured to use the effective scale and precision
             (i.e. ignore trailing zeros) if required.
            
             123.4500 has an scale of 4 and a precision of 7, but an effective scale
             and precision of 2 and 5 respectively.
             </summary>
        </member>
    </members>
</doc>
