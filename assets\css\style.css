/* نظام إدارة الديون - ملف التصميم الرئيسي */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-info: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

/* إعدادات عامة */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: var(--gray-800);
    line-height: 1.7;
    direction: rtl;
    text-align: right;
    font-weight: 400;
    min-height: 100vh;
    overflow-x: hidden;
}

/* تحسين الخطوط العربية */
.arabic-text {
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
}

/* تحسين الأرقام */
.numbers {
    font-family: 'Cairo', monospace;
    font-variant-numeric: tabular-nums;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    right: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset 1px 0 0 rgba(0, 0, 0, .1);
    background: linear-gradient(180deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-left: 1px solid var(--gray-200);
    backdrop-filter: blur(10px);
}

.sidebar .nav-link {
    color: var(--gray-700);
    padding: 0.875rem 1.25rem;
    margin: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
    font-family: 'Cairo', sans-serif;
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%);
    color: var(--primary-color);
    transform: translateX(-5px);
    box-shadow: var(--box-shadow);
}

.sidebar .nav-link:hover::before {
    transform: scaleY(1);
}

.sidebar .nav-link.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--box-shadow-md);
}

.sidebar .nav-link.active::before {
    transform: scaleY(1);
    background: rgba(255, 255, 255, 0.3);
}

.sidebar .nav-link i {
    width: 24px;
    text-align: center;
    font-size: 1.1rem;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-4px);
}

.card-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-800);
    font-family: 'Cairo', sans-serif;
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* بطاقات الإحصائيات المحسنة */
.stats-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--box-shadow-lg);
}

.stats-card .card-body {
    position: relative;
    overflow: hidden;
}

.stats-card .card-body::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-primary);
}

/* أفاتار المستخدم */
.avatar-circle {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.avatar-large {
    width: 120px;
    height: 120px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* شعار الشركة */
.logo-placeholder {
    padding: 2rem;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    text-align: center;
    background: var(--gray-50);
}

.logo-preview img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* بطاقات الإحصائيات */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* الجداول */
.table {
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
    padding: 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* الشارات */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 50px;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* المحتوى الرئيسي */
main {
    margin-top: 20px;
}

.border-bottom {
    border-bottom: 2px solid #e9ecef !important;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }

    main {
        margin-top: 0;
    }

    .card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية */
.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-uppercase {
    text-transform: uppercase;
}

/* تأثيرات التحويم */
.hover-shadow:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transition: var(--transition);
}

/* أيقونات ملونة */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }

    main {
        margin: 0 !important;
        padding: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
