/* نظام إدارة الديون - ملف التصميم الرئيسي */

/* الخطوط والألوان الأساسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --transition: all 0.3s ease;
}

/* إعدادات عامة */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: #333;
    padding: 0.75rem 1rem;
    margin: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: white;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: var(--dark-color);
}

/* بطاقات الإحصائيات */
.border-left-primary {
    border-left: 4px solid var(--primary-color) !important;
}

.border-left-success {
    border-left: 4px solid var(--success-color) !important;
}

.border-left-info {
    border-left: 4px solid var(--info-color) !important;
}

.border-left-warning {
    border-left: 4px solid var(--warning-color) !important;
}

/* الجداول */
.table {
    background-color: white;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: var(--dark-color);
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* الأزرار */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

/* النماذج */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
    padding: 0.75rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: var(--transition);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* الشارات */
.badge {
    font-size: 0.75em;
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 50px;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid var(--info-color);
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* المحتوى الرئيسي */
main {
    margin-top: 20px;
}

.border-bottom {
    border-bottom: 2px solid #e9ecef !important;
}

/* الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sidebar {
        position: static;
        height: auto;
        padding: 0;
    }
    
    main {
        margin-top: 0;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات إضافية */
.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-xs {
    font-size: 0.7rem;
}

.text-uppercase {
    text-transform: uppercase;
}

/* تأثيرات التحويم */
.hover-shadow:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transition: var(--transition);
}

/* أيقونات ملونة */
.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .no-print {
        display: none !important;
    }
    
    main {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
