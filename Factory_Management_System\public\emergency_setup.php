<?php
/**
 * إعداد طوارئ - Emergency Setup
 * ملف إعداد مبسط للحالات الطارئة
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // إعدادات قاعدة البيانات
        $host = 'localhost';
        $username = 'root';
        $password = '';
        $dbname = 'factory_management';
        
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $message .= "✅ تم إنشاء قاعدة البيانات '$dbname'<br>";
        
        // الاتصال بقاعدة البيانات
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // إنشاء جدول المستخدمين
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'manager', 'accountant', 'warehouse', 'employee') DEFAULT 'employee',
            permissions TEXT,
            is_active TINYINT(1) DEFAULT 1,
            last_login DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        $message .= "✅ تم إنشاء جدول المستخدمين<br>";
        
        // إنشاء جدول الإعدادات
        $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE,
            setting_value TEXT,
            setting_group VARCHAR(50) DEFAULT 'general',
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        $message .= "✅ تم إنشاء جدول الإعدادات<br>";
        
        // إنشاء جدول العملات
        $pdo->exec("CREATE TABLE IF NOT EXISTS currencies (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base TINYINT(1) DEFAULT 0,
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        $message .= "✅ تم إنشاء جدول العملات<br>";
        
        // إنشاء المستخدم الافتراضي
        $adminExists = $pdo->query("SELECT COUNT(*) FROM users WHERE username = 'admin'")->fetchColumn();
        if (!$adminExists) {
            $passwordHash = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password_hash, full_name, role, permissions) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $passwordHash, 'مدير النظام', 'admin', '[]']);
            $message .= "✅ تم إنشاء المستخدم الافتراضي (admin/admin123)<br>";
        } else {
            $message .= "ℹ️ المستخدم الافتراضي موجود بالفعل<br>";
        }
        
        // إنشاء العملات الافتراضية
        $iqd = $pdo->query("SELECT COUNT(*) FROM currencies WHERE code = 'IQD'")->fetchColumn();
        if (!$iqd) {
            $stmt = $pdo->prepare("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute(['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1, 1]);
            $message .= "✅ تم إضافة الدينار العراقي<br>";
        }
        
        $usd = $pdo->query("SELECT COUNT(*) FROM currencies WHERE code = 'USD'")->fetchColumn();
        if (!$usd) {
            $stmt = $pdo->prepare("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute(['الدولار الأمريكي', '$', 'USD', 0.00068, 0, 1]);
            $message .= "✅ تم إضافة الدولار الأمريكي<br>";
        }
        
        $message .= "<br>🎉 <strong>تم الإعداد بنجاح! يمكنك الآن تسجيل الدخول.</strong>";
        $success = true;
        
    } catch (Exception $e) {
        $message = "❌ خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد طوارئ - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .emergency-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        .emergency-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .emergency-body {
            padding: 30px;
        }
        .btn-emergency {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            width: 100%;
        }
        .message-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            font-family: monospace;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="emergency-card">
        <div class="emergency-header">
            <h2><i class="fas fa-exclamation-triangle fa-2x mb-3"></i><br>إعداد طوارئ</h2>
            <p class="mb-0">إعداد مبسط لحل مشاكل قاعدة البيانات</p>
        </div>
        
        <div class="emergency-body">
            <?php if ($message): ?>
                <div class="message-box">
                    <?= $message ?>
                </div>
                
                <?php if ($success): ?>
                    <div class="text-center">
                        <a href="login.php" class="btn btn-success btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                <?php else: ?>
                    <div class="text-center">
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-redo me-2"></i>إعادة المحاولة
                        </button>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا إعداد طوارئ مبسط. استخدمه فقط إذا فشل الإعداد العادي.
                </div>
                
                <div class="mb-4">
                    <h6>ما سيتم عمله:</h6>
                    <ul>
                        <li>إنشاء قاعدة بيانات <code>factory_management</code></li>
                        <li>إنشاء الجداول الأساسية (users, settings, currencies)</li>
                        <li>إنشاء مستخدم افتراضي: <code>admin</code> / <code>admin123</code></li>
                        <li>إضافة العملات الأساسية (دينار عراقي، دولار)</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <button type="submit" class="btn btn-emergency btn-lg">
                        <i class="fas fa-rocket me-2"></i>بدء الإعداد الطارئ
                    </button>
                </form>
                
                <div class="mt-4 text-center">
                    <small class="text-muted">
                        <a href="db_test.php">اختبار قاعدة البيانات</a> |
                        <a href="quick_setup.php">الإعداد العادي</a> |
                        <a href="system_check.php">فحص النظام</a>
                    </small>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
