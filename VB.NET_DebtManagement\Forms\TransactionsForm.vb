Imports System.Windows.Forms
Imports System.Drawing
Imports System.Linq

''' <summary>
''' نموذج إدارة المعاملات المالية - Transactions Management Form
''' </summary>
Public Class TransactionsForm
    Inherits Form

#Region "Fields"

    Private _context As DebtContext
    Private _currentUser As User

    ' عناصر التحكم الرئيسية
    Private _toolStrip As ToolStrip
    Private _filterPanel As Panel
    Private _dataGridView As DataGridView
    Private _statusStrip As StatusStrip

    ' أزرار شريط الأدوات
    Private _btnAdd As ToolStripButton
    Private _btnEdit As ToolStripButton
    Private _btnDelete As ToolStripButton
    Private _btnRefresh As ToolStripButton
    Private _btnPrintInvoice As ToolStripButton
    Private _btnExport As ToolStripButton

    ' عناصر الفلترة
    Private _dtpFromDate As DateTimePicker
    Private _dtpToDate As DateTimePicker
    Private _cmbTransactionType As ComboBox
    Private _cmbEntityType As ComboBox
    Private _cmbCurrency As ComboBox
    Private _btnFilter As Button
    Private _btnClearFilter As Button

    ' شريط الحالة
    Private _lblStatus As ToolStripStatusLabel
    Private _lblCount As ToolStripStatusLabel
    Private _lblTotalIQD As ToolStripStatusLabel
    Private _lblTotalUSD As ToolStripStatusLabel

    ' متغيرات أخرى
    Private _selectedTransaction As Transaction

#End Region

#Region "Constructor"

    ''' <summary>
    ''' منشئ نموذج إدارة المعاملات
    ''' </summary>
    Public Sub New(context As DebtContext, currentUser As User)
        _context = context
        _currentUser = currentUser

        InitializeComponent()
        SetupForm()
        SetupToolStrip()
        SetupFilterPanel()
        SetupDataGridView()
        SetupStatusStrip()
        LoadTransactions()
    End Sub

#End Region

#Region "Form Setup"

    ''' <summary>
    ''' إعداد النموذج الأساسي
    ''' </summary>
    Private Sub SetupForm()
        Me.Text = "إدارة المعاملات المالية"
        Me.Size = New Size(1400, 800)
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Font = New Font("Segoe UI", 9, FontStyle.Regular)
        Me.RightToLeft = RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.BackColor = Color.FromArgb(248, 249, 250)
        Me.Icon = My.Resources.TransactionsIcon
    End Sub

    ''' <summary>
    ''' إعداد شريط الأدوات
    ''' </summary>
    Private Sub SetupToolStrip()
        _toolStrip = New ToolStrip With {
            .BackColor = Color.FromArgb(245, 246, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .ImageScalingSize = New Size(24, 24),
            .RightToLeft = RightToLeft.Yes
        }

        ' إنشاء الأزرار
        _btnAdd = New ToolStripButton With {
            .Text = "معاملة جديدة",
            .Image = My.Resources.AddIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "إضافة معاملة مالية جديدة"
        }

        _btnEdit = New ToolStripButton With {
            .Text = "تعديل",
            .Image = My.Resources.EditIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تعديل المعاملة المحددة",
            .Enabled = False
        }

        _btnDelete = New ToolStripButton With {
            .Text = "حذف",
            .Image = My.Resources.DeleteIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "حذف المعاملة المحددة",
            .Enabled = False
        }

        _btnRefresh = New ToolStripButton With {
            .Text = "تحديث",
            .Image = My.Resources.RefreshIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تحديث قائمة المعاملات"
        }

        _btnPrintInvoice = New ToolStripButton With {
            .Text = "طباعة فاتورة",
            .Image = My.Resources.PrintIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "طباعة فاتورة المعاملة",
            .Enabled = False
        }

        _btnExport = New ToolStripButton With {
            .Text = "تصدير",
            .Image = My.Resources.ExportIcon,
            .ImageTransparentColor = Color.Magenta,
            .ToolTipText = "تصدير قائمة المعاملات"
        }

        ' إضافة الأزرار لشريط الأدوات
        _toolStrip.Items.AddRange({
            _btnAdd,
            New ToolStripSeparator(),
            _btnEdit,
            _btnDelete,
            New ToolStripSeparator(),
            _btnRefresh,
            New ToolStripSeparator(),
            _btnPrintInvoice,
            _btnExport
        })

        ' ربط الأحداث
        AddHandler _btnAdd.Click, AddressOf BtnAdd_Click
        AddHandler _btnEdit.Click, AddressOf BtnEdit_Click
        AddHandler _btnDelete.Click, AddressOf BtnDelete_Click
        AddHandler _btnRefresh.Click, AddressOf BtnRefresh_Click
        AddHandler _btnPrintInvoice.Click, AddressOf BtnPrintInvoice_Click
        AddHandler _btnExport.Click, AddressOf BtnExport_Click

        Me.Controls.Add(_toolStrip)
    End Sub

    ''' <summary>
    ''' إعداد لوحة الفلترة
    ''' </summary>
    Private Sub SetupFilterPanel()
        _filterPanel = New Panel With {
            .Height = 100,
            .Dock = DockStyle.Top,
            .BackColor = Color.White,
            .Padding = New Padding(10)
        }

        ' الصف الأول - التواريخ
        Dim lblFromDate As New Label With {
            .Text = "من تاريخ:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(60, 25),
            .Location = New Point(Me.Width - 80, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _dtpFromDate = New DateTimePicker With {
            .Size = New Size(150, 25),
            .Location = New Point(Me.Width - 240, 15),
            .Format = DateTimePickerFormat.Short,
            .Value = DateTime.Today.AddDays(-30)
        }

        Dim lblToDate As New Label With {
            .Text = "إلى تاريخ:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(60, 25),
            .Location = New Point(Me.Width - 320, 15),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _dtpToDate = New DateTimePicker With {
            .Size = New Size(150, 25),
            .Location = New Point(Me.Width - 480, 15),
            .Format = DateTimePickerFormat.Short,
            .Value = DateTime.Today
        }

        ' الصف الثاني - الفلاتر
        Dim lblTransactionType As New Label With {
            .Text = "نوع المعاملة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(80, 25),
            .Location = New Point(Me.Width - 100, 50),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbTransactionType = New ComboBox With {
            .Size = New Size(120, 25),
            .Location = New Point(Me.Width - 230, 50),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbTransactionType.Items.AddRange({"الكل", "وارد", "صادر"})
        _cmbTransactionType.SelectedIndex = 0

        Dim lblEntityType As New Label With {
            .Text = "نوع الطرف:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(70, 25),
            .Location = New Point(Me.Width - 320, 50),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbEntityType = New ComboBox With {
            .Size = New Size(120, 25),
            .Location = New Point(Me.Width - 450, 50),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbEntityType.Items.AddRange({"الكل", "عميل", "مورد"})
        _cmbEntityType.SelectedIndex = 0

        Dim lblCurrency As New Label With {
            .Text = "العملة:",
            .Font = New Font("Segoe UI", 10, FontStyle.Bold),
            .Size = New Size(50, 25),
            .Location = New Point(Me.Width - 520, 50),
            .TextAlign = ContentAlignment.MiddleRight
        }

        _cmbCurrency = New ComboBox With {
            .Size = New Size(100, 25),
            .Location = New Point(Me.Width - 630, 50),
            .DropDownStyle = ComboBoxStyle.DropDownList
        }
        _cmbCurrency.Items.AddRange({"الكل", "دينار", "دولار"})
        _cmbCurrency.SelectedIndex = 0

        ' أزرار الفلترة
        _btnFilter = New Button With {
            .Text = "فلترة",
            .Size = New Size(80, 30),
            .Location = New Point(Me.Width - 720, 48),
            .BackColor = Color.FromArgb(0, 123, 255),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnFilter.FlatAppearance.BorderSize = 0

        _btnClearFilter = New Button With {
            .Text = "مسح",
            .Size = New Size(70, 30),
            .Location = New Point(Me.Width - 810, 48),
            .BackColor = Color.FromArgb(108, 117, 125),
            .ForeColor = Color.White,
            .FlatStyle = FlatStyle.Flat,
            .Font = New Font("Segoe UI", 9, FontStyle.Bold)
        }
        _btnClearFilter.FlatAppearance.BorderSize = 0

        ' إضافة العناصر للوحة
        _filterPanel.Controls.AddRange({
            lblFromDate, _dtpFromDate, lblToDate, _dtpToDate,
            lblTransactionType, _cmbTransactionType,
            lblEntityType, _cmbEntityType,
            lblCurrency, _cmbCurrency,
            _btnFilter, _btnClearFilter
        })

        ' ربط الأحداث
        AddHandler _btnFilter.Click, AddressOf BtnFilter_Click
        AddHandler _btnClearFilter.Click, AddressOf BtnClearFilter_Click

        Me.Controls.Add(_filterPanel)
    End Sub

    ''' <summary>
    ''' إعداد شبكة البيانات
    ''' </summary>
    Private Sub SetupDataGridView()
        _dataGridView = New DataGridView With {
            .Dock = DockStyle.Fill,
            .BackgroundColor = Color.White,
            .BorderStyle = BorderStyle.None,
            .CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal,
            .ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.None,
            .ColumnHeadersDefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.FromArgb(52, 58, 64),
                .ForeColor = Color.White,
                .Font = New Font("Segoe UI", 10, FontStyle.Bold),
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .ColumnHeadersHeight = 40,
            .DefaultCellStyle = New DataGridViewCellStyle With {
                .BackColor = Color.White,
                .ForeColor = Color.FromArgb(33, 37, 41),
                .Font = New Font("Segoe UI", 9, FontStyle.Regular),
                .SelectionBackColor = Color.FromArgb(0, 123, 255),
                .SelectionForeColor = Color.White,
                .Alignment = DataGridViewContentAlignment.MiddleCenter
            },
            .EnableHeadersVisualStyles = False,
            .GridColor = Color.FromArgb(222, 226, 230),
            .RowHeadersVisible = False,
            .SelectionMode = DataGridViewSelectionMode.FullRowSelect,
            .MultiSelect = False,
            .AllowUserToAddRows = False,
            .AllowUserToDeleteRows = False,
            .ReadOnly = True,
            .AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
            .RightToLeft = RightToLeft.Yes
        }

        ' إضافة الأعمدة
        _dataGridView.Columns.AddRange({
            New DataGridViewTextBoxColumn With {.Name = "Id", .HeaderText = "المعرف", .Visible = False},
            New DataGridViewTextBoxColumn With {.Name = "InvoiceNumber", .HeaderText = "رقم الفاتورة", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "TransactionDate", .HeaderText = "التاريخ", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Type", .HeaderText = "النوع", .FillWeight = 8},
            New DataGridViewTextBoxColumn With {.Name = "EntityType", .HeaderText = "نوع الطرف", .FillWeight = 8},
            New DataGridViewTextBoxColumn With {.Name = "EntityName", .HeaderText = "اسم الطرف", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Amount", .HeaderText = "المبلغ", .FillWeight = 10},
            New DataGridViewTextBoxColumn With {.Name = "Currency", .HeaderText = "العملة", .FillWeight = 6},
            New DataGridViewTextBoxColumn With {.Name = "CashBoxName", .HeaderText = "الصندوق", .FillWeight = 12},
            New DataGridViewTextBoxColumn With {.Name = "Description", .HeaderText = "الوصف", .FillWeight = 15},
            New DataGridViewTextBoxColumn With {.Name = "Status", .HeaderText = "الحالة", .FillWeight = 8}
        })

        ' ربط الأحداث
        AddHandler _dataGridView.SelectionChanged, AddressOf DataGridView_SelectionChanged
        AddHandler _dataGridView.CellDoubleClick, AddressOf DataGridView_CellDoubleClick
        AddHandler _dataGridView.CellFormatting, AddressOf DataGridView_CellFormatting

        Me.Controls.Add(_dataGridView)
    End Sub

    ''' <summary>
    ''' إعداد شريط الحالة
    ''' </summary>
    Private Sub SetupStatusStrip()
        _statusStrip = New StatusStrip With {
            .BackColor = Color.FromArgb(248, 249, 250),
            .Font = New Font("Segoe UI", 9, FontStyle.Regular),
            .RightToLeft = RightToLeft.Yes
        }

        _lblStatus = New ToolStripStatusLabel With {
            .Text = "جاهز",
            .Spring = True,
            .TextAlign = ContentAlignment.MiddleLeft
        }

        _lblCount = New ToolStripStatusLabel With {
            .Text = "عدد المعاملات: 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _lblTotalIQD = New ToolStripStatusLabel With {
            .Text = "الإجمالي (د.ع): 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _lblTotalUSD = New ToolStripStatusLabel With {
            .Text = "الإجمالي ($): 0",
            .BorderSides = ToolStripStatusLabelBorderSides.Left
        }

        _statusStrip.Items.AddRange({_lblStatus, _lblCount, _lblTotalIQD, _lblTotalUSD})
        Me.Controls.Add(_statusStrip)
    End Sub

#End Region

#Region "Data Methods"

    ''' <summary>
    ''' تحميل قائمة المعاملات
    ''' </summary>
    Private Sub LoadTransactions()
        Try
            _lblStatus.Text = "جاري تحميل البيانات..."
            Application.DoEvents()

            Dim query = _context.Transactions.AsQueryable()

            ' تطبيق الفلاتر
            query = query.Where(Function(t) t.TransactionDate >= _dtpFromDate.Value.Date AndAlso t.TransactionDate <= _dtpToDate.Value.Date)

            If _cmbTransactionType.Text <> "الكل" Then
                Dim typeFilter = If(_cmbTransactionType.Text = "وارد", "Income", "Expense")
                query = query.Where(Function(t) t.Type = typeFilter)
            End If

            If _cmbEntityType.Text <> "الكل" Then
                Dim entityFilter = If(_cmbEntityType.Text = "عميل", "Customer", "Supplier")
                query = query.Where(Function(t) t.EntityType = entityFilter)
            End If

            If _cmbCurrency.Text <> "الكل" Then
                Dim currencyFilter = If(_cmbCurrency.Text = "دينار", "IQD", "USD")
                query = query.Where(Function(t) t.Currency = currencyFilter)
            End If

            Dim transactions = query.OrderByDescending(Function(t) t.CreatedAt).ToList()

            ' مسح البيانات الحالية
            _dataGridView.Rows.Clear()

            ' متغيرات الإجماليات
            Dim totalIQD As Decimal = 0
            Dim totalUSD As Decimal = 0

            ' إضافة البيانات الجديدة
            For Each transaction In transactions
                Dim row As New DataGridViewRow()
                row.CreateCells(_dataGridView)

                row.Cells("Id").Value = transaction.Id
                row.Cells("InvoiceNumber").Value = transaction.InvoiceNumber
                row.Cells("TransactionDate").Value = transaction.TransactionDate.ToString("dd/MM/yyyy")
                row.Cells("Type").Value = transaction.TypeInArabic
                row.Cells("EntityType").Value = transaction.EntityTypeInArabic
                row.Cells("EntityName").Value = GetEntityName(transaction.EntityType, transaction.EntityId)
                row.Cells("Amount").Value = transaction.Amount
                row.Cells("Currency").Value = transaction.Currency
                row.Cells("CashBoxName").Value = GetCashBoxName(transaction.CashBoxId)
                row.Cells("Description").Value = transaction.Description
                row.Cells("Status").Value = transaction.StatusInArabic

                row.Tag = transaction
                _dataGridView.Rows.Add(row)

                ' حساب الإجماليات
                If transaction.Currency = "IQD" Then
                    totalIQD += If(transaction.Type = "Income", transaction.Amount, -transaction.Amount)
                Else
                    totalUSD += If(transaction.Type = "Income", transaction.Amount, -transaction.Amount)
                End If
            Next

            ' تحديث شريط الحالة
            _lblCount.Text = $"عدد المعاملات: {transactions.Count}"
            _lblTotalIQD.Text = $"الإجمالي (د.ع): {totalIQD:N0}"
            _lblTotalUSD.Text = $"الإجمالي ($): {totalUSD:N2}"
            _lblStatus.Text = "تم تحميل البيانات بنجاح"

        Catch ex As Exception
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            _lblStatus.Text = "خطأ في تحميل البيانات"
        End Try
    End Sub

    ''' <summary>
    ''' الحصول على اسم الكيان
    ''' </summary>
    Private Function GetEntityName(entityType As String, entityId As Integer) As String
        Try
            If entityType = "Customer" Then
                Dim customer = _context.Customers.Find(entityId)
                Return customer?.Name ?? "عميل غير معروف"
            ElseIf entityType = "Supplier" Then
                Dim supplier = _context.Suppliers.Find(entityId)
                Return supplier?.Name ?? "مورد غير معروف"
            End If
        Catch
        End Try

        Return "غير محدد"
    End Function

    ''' <summary>
    ''' الحصول على اسم الصندوق
    ''' </summary>
    Private Function GetCashBoxName(cashBoxId As Integer) As String
        Try
            Dim cashBox = _context.CashBoxes.Find(cashBoxId)
            Return cashBox?.Name ?? "صندوق غير معروف"
        Catch
        End Try

        Return "غير محدد"
    End Function

#End Region

#Region "Event Handlers"

    ''' <summary>
    ''' إضافة معاملة جديدة
    ''' </summary>
    Private Sub BtnAdd_Click(sender As Object, e As EventArgs)
        Dim addForm As New TransactionAddEditForm(_context, _currentUser)
        If addForm.ShowDialog() = DialogResult.OK Then
            LoadTransactions()
        End If
    End Sub

    ''' <summary>
    ''' تعديل المعاملة المحددة
    ''' </summary>
    Private Sub BtnEdit_Click(sender As Object, e As EventArgs)
        If _selectedTransaction IsNot Nothing Then
            Dim editForm As New TransactionAddEditForm(_context, _currentUser, _selectedTransaction)
            If editForm.ShowDialog() = DialogResult.OK Then
                LoadTransactions()
            End If
        End If
    End Sub

    ''' <summary>
    ''' حذف المعاملة المحددة
    ''' </summary>
    Private Sub BtnDelete_Click(sender As Object, e As EventArgs)
        If _selectedTransaction IsNot Nothing Then
            If MessageBox.Show($"هل أنت متأكد من حذف المعاملة رقم '{_selectedTransaction.InvoiceNumber}'؟",
                             "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                Try
                    _context.Transactions.Remove(_selectedTransaction)
                    _context.SaveChanges()

                    MessageBox.Show("تم حذف المعاملة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    LoadTransactions()

                Catch ex As Exception
                    MessageBox.Show($"خطأ في حذف المعاملة: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End If
        End If
    End Sub

    ''' <summary>
    ''' تحديث قائمة المعاملات
    ''' </summary>
    Private Sub BtnRefresh_Click(sender As Object, e As EventArgs)
        LoadTransactions()
    End Sub

    ''' <summary>
    ''' طباعة فاتورة المعاملة
    ''' </summary>
    Private Sub BtnPrintInvoice_Click(sender As Object, e As EventArgs)
        If _selectedTransaction IsNot Nothing Then
            MessageBox.Show("سيتم تنفيذ ميزة طباعة الفاتورة قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    ''' <summary>
    ''' تصدير قائمة المعاملات
    ''' </summary>
    Private Sub BtnExport_Click(sender As Object, e As EventArgs)
        MessageBox.Show("سيتم تنفيذ ميزة التصدير قريباً", "قيد التطوير", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    ''' <summary>
    ''' تطبيق الفلاتر
    ''' </summary>
    Private Sub BtnFilter_Click(sender As Object, e As EventArgs)
        LoadTransactions()
    End Sub

    ''' <summary>
    ''' مسح الفلاتر
    ''' </summary>
    Private Sub BtnClearFilter_Click(sender As Object, e As EventArgs)
        _dtpFromDate.Value = DateTime.Today.AddDays(-30)
        _dtpToDate.Value = DateTime.Today
        _cmbTransactionType.SelectedIndex = 0
        _cmbEntityType.SelectedIndex = 0
        _cmbCurrency.SelectedIndex = 0
        LoadTransactions()
    End Sub

    ''' <summary>
    ''' تغيير التحديد في الشبكة
    ''' </summary>
    Private Sub DataGridView_SelectionChanged(sender As Object, e As EventArgs)
        If _dataGridView.SelectedRows.Count > 0 Then
            _selectedTransaction = DirectCast(_dataGridView.SelectedRows(0).Tag, Transaction)
            _btnEdit.Enabled = True
            _btnDelete.Enabled = True
            _btnPrintInvoice.Enabled = True
        Else
            _selectedTransaction = Nothing
            _btnEdit.Enabled = False
            _btnDelete.Enabled = False
            _btnPrintInvoice.Enabled = False
        End If
    End Sub

    ''' <summary>
    ''' النقر المزدوج لتعديل المعاملة
    ''' </summary>
    Private Sub DataGridView_CellDoubleClick(sender As Object, e As DataGridViewCellEventArgs)
        If e.RowIndex >= 0 Then
            BtnEdit_Click(sender, e)
        End If
    End Sub

    ''' <summary>
    ''' تنسيق خلايا الشبكة
    ''' </summary>
    Private Sub DataGridView_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs)
        If e.ColumnIndex = _dataGridView.Columns("Amount").Index AndAlso e.Value IsNot Nothing Then
            Dim amount As Decimal = Convert.ToDecimal(e.Value)
            Dim currency As String = _dataGridView.Rows(e.RowIndex).Cells("Currency").Value.ToString()

            If currency = "IQD" Then
                e.Value = $"{amount:N0} د.ع"
            Else
                e.Value = $"${amount:N2}"
            End If

        ElseIf e.ColumnIndex = _dataGridView.Columns("Type").Index AndAlso e.Value IsNot Nothing Then
            ' تلوين نوع المعاملة
            If e.Value.ToString() = "وارد" Then
                e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69) ' أخضر
            Else
                e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
            End If

        ElseIf e.ColumnIndex = _dataGridView.Columns("Status").Index AndAlso e.Value IsNot Nothing Then
            ' تلوين حالة المعاملة
            Select Case e.Value.ToString()
                Case "مكتملة"
                    e.CellStyle.ForeColor = Color.FromArgb(40, 167, 69) ' أخضر
                Case "معلقة"
                    e.CellStyle.ForeColor = Color.FromArgb(255, 193, 7) ' أصفر
                Case "ملغية"
                    e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69) ' أحمر
            End Select
        End If
    End Sub

#End Region

End Class