<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة عميل جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_customer') {
    try {
        // توليد كود العميل
        $prefix = 'CUST';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM customers");
        $sequence = ($countResult['count'] ?? 0) + 1;
        $customerCode = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);

        $customerData = [
            'code' => $customerCode,
            'name' => Helper::cleanInput($_POST['name']),
            'type' => $_POST['type'],
            'phone' => Helper::cleanInput($_POST['phone'] ?? ''),
            'email' => Helper::cleanInput($_POST['email'] ?? ''),
            'address' => Helper::cleanInput($_POST['address'] ?? ''),
            'tax_number' => Helper::cleanInput($_POST['tax_number'] ?? ''),
            'credit_limit' => (float)($_POST['credit_limit'] ?? 0),
            'currency_id' => (int)$_POST['currency_id'],
            'payment_terms' => (int)($_POST['payment_terms'] ?? 0),
            'discount_percentage' => (float)($_POST['discount_percentage'] ?? 0)
        ];

        $db->insert('customers', $customerData);
        $success = "تم إضافة العميل بنجاح برقم: $customerCode";

    } catch (Exception $e) {
        $error = "خطأ في إضافة العميل: " . $e->getMessage();
    }
}

// معالجة تحديث العميل
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_customer') {
    try {
        $customerId = (int)$_POST['customer_id'];

        $customerData = [
            'name' => Helper::cleanInput($_POST['name']),
            'type' => $_POST['type'],
            'phone' => Helper::cleanInput($_POST['phone'] ?? ''),
            'email' => Helper::cleanInput($_POST['email'] ?? ''),
            'address' => Helper::cleanInput($_POST['address'] ?? ''),
            'tax_number' => Helper::cleanInput($_POST['tax_number'] ?? ''),
            'credit_limit' => (float)($_POST['credit_limit'] ?? 0),
            'currency_id' => (int)$_POST['currency_id'],
            'payment_terms' => (int)($_POST['payment_terms'] ?? 0),
            'discount_percentage' => (float)($_POST['discount_percentage'] ?? 0),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];

        $db->update('customers', $customerData, 'id = ?', [$customerId]);
        $success = "تم تحديث بيانات العميل بنجاح";

    } catch (Exception $e) {
        $error = "خطأ في تحديث العميل: " . $e->getMessage();
    }
}

// معالجة حذف العميل
if (isset($_GET['delete']) && $_GET['delete']) {
    try {
        $customerId = (int)$_GET['delete'];

        // التحقق من وجود فواتير للعميل
        $invoiceCount = $db->fetchOne("SELECT COUNT(*) as count FROM sales_invoices WHERE customer_id = ?", [$customerId]);

        if ($invoiceCount['count'] > 0) {
            $error = "لا يمكن حذف العميل لوجود فواتير مرتبطة به";
        } else {
            $db->delete('customers', 'id = ?', [$customerId]);
            $success = "تم حذف العميل بنجاح";
        }

    } catch (Exception $e) {
        $error = "خطأ في حذف العميل: " . $e->getMessage();
    }
}

// فلاتر البحث
$search = $_GET['search'] ?? '';
$type = $_GET['type'] ?? '';
$status = $_GET['status'] ?? '';

// بناء استعلام البحث
$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(c.name LIKE ? OR c.code LIKE ? OR c.phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($type) {
    $whereConditions[] = "c.type = ?";
    $params[] = $type;
}

if ($status !== '') {
    $whereConditions[] = "c.is_active = ?";
    $params[] = $status;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب العملاء
try {
    $customers = $db->fetchAll("
        SELECT c.*, cur.symbol as currency_symbol,
               (SELECT COUNT(*) FROM sales_invoices si WHERE si.customer_id = c.id) as invoice_count,
               (SELECT SUM(si.remaining_amount) FROM sales_invoices si WHERE si.customer_id = c.id AND si.status != 'cancelled') as outstanding_balance
        FROM customers c
        LEFT JOIN currencies cur ON c.currency_id = cur.id
        $whereClause
        ORDER BY c.created_at DESC
    ", $params);

    // جلب العملات
    $currencies = $db->fetchAll("SELECT id, name, symbol FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");

} catch (Exception $e) {
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
    $customers = [];
    $currencies = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .customer-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .customer-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .balance-positive { color: #28a745; }
        .balance-negative { color: #dc3545; }
        .balance-zero { color: #6c757d; }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-users me-3"></i>إدارة العملاء</h1>
                    <p class="mb-0">إدارة بيانات العملاء والحسابات</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="sales.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- فلاتر البحث -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>البحث والفلترة</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">البحث</label>
                                <input type="text" name="search" class="form-control"
                                       value="<?= htmlspecialchars($search) ?>"
                                       placeholder="الاسم، الكود، أو الهاتف...">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">نوع العميل</label>
                                <select name="type" class="form-select">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual" <?= $type == 'individual' ? 'selected' : '' ?>>فرد</option>
                                    <option value="company" <?= $type == 'company' ? 'selected' : '' ?>>شركة</option>
                                    <option value="institution" <?= $type == 'institution' ? 'selected' : '' ?>>مؤسسة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select">
                                    <option value="">جميع الحالات</option>
                                    <option value="1" <?= $status === '1' ? 'selected' : '' ?>>نشط</option>
                                    <option value="0" <?= $status === '0' ? 'selected' : '' ?>>غير نشط</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>بحث
                            </button>
                        </form>
                    </div>
                </div>

                <!-- إضافة عميل جديد -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus me-2"></i>عميل جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="action" value="add_customer">

                            <div class="mb-3">
                                <label class="form-label">الاسم <span class="text-danger">*</span></label>
                                <input type="text" name="name" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">النوع</label>
                                <select name="type" class="form-select">
                                    <option value="individual">فرد</option>
                                    <option value="company">شركة</option>
                                    <option value="institution">مؤسسة</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الهاتف</label>
                                <input type="text" name="phone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="2"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">حد الائتمان</label>
                                <input type="number" name="credit_limit" class="form-control" step="0.01" value="0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العملة</label>
                                <select name="currency_id" class="form-select">
                                    <?php foreach ($currencies as $currency): ?>
                                        <option value="<?= $currency['id'] ?>" <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>إضافة العميل
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- قائمة العملاء -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة العملاء (<?= count($customers) ?>)</h5>
                            <a href="add_sale.php" class="btn btn-success">
                                <i class="fas fa-plus me-2"></i>فاتورة جديدة
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($customers)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                                <h5>لا توجد عملاء</h5>
                                <p class="text-muted">ابدأ بإضافة عميل جديد من النموذج الجانبي</p>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($customers as $customer): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card customer-card h-100">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0"><?= htmlspecialchars($customer['name']) ?></h6>
                                                    <span class="badge bg-<?= $customer['is_active'] ? 'success' : 'secondary' ?>">
                                                        <?= $customer['is_active'] ? 'نشط' : 'غير نشط' ?>
                                                    </span>
                                                </div>

                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        <i class="fas fa-barcode me-1"></i><?= htmlspecialchars($customer['code']) ?>
                                                    </small>
                                                </p>

                                                <?php if ($customer['phone']): ?>
                                                    <p class="card-text">
                                                        <small><i class="fas fa-phone me-1"></i><?= htmlspecialchars($customer['phone']) ?></small>
                                                    </p>
                                                <?php endif; ?>

                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <?= $customer['invoice_count'] ?> فاتورة
                                                    </small>
                                                    <small class="<?= $customer['outstanding_balance'] > 0 ? 'balance-negative' : 'balance-zero' ?>">
                                                        <?= number_format($customer['outstanding_balance'] ?? 0, 2) ?> <?= $customer['currency_symbol'] ?>
                                                    </small>
                                                </div>

                                                <div class="mt-3">
                                                    <div class="btn-group w-100" role="group">
                                                        <a href="view_customer.php?id=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_customer.php?id=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="add_sale.php?customer_id=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-success">
                                                            <i class="fas fa-plus"></i>
                                                        </a>
                                                        <?php if ($customer['invoice_count'] == 0): ?>
                                                            <a href="?delete=<?= $customer['id'] ?>" class="btn btn-sm btn-outline-danger"
                                                               onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>