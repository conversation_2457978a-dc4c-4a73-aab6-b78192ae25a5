Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج المورد - Supplier Model
''' </summary>
<Table("Suppliers")>
Public Class Supplier
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف المورد الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' اسم المورد
    ''' </summary>
    <Required(ErrorMessage:="اسم المورد مطلوب")>
    <StringLength(100, ErrorMessage:="اسم المورد يجب أن يكون أقل من 100 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Name As String
    
    ''' <summary>
    ''' رقم هاتف المورد
    ''' </summary>
    <StringLength(20, ErrorMessage:="رقم الهاتف يجب أن يكون أقل من 20 رقم")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Phone As String
    
    ''' <summary>
    ''' البريد الإلكتروني للمورد
    ''' </summary>
    <StringLength(100, ErrorMessage:="البريد الإلكتروني يجب أن يكون أقل من 100 حرف")>
    <EmailAddress(ErrorMessage:="البريد الإلكتروني غير صحيح")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Email As String
    
    ''' <summary>
    ''' عنوان المورد
    ''' </summary>
    <StringLength(200, ErrorMessage:="العنوان يجب أن يكون أقل من 200 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Address As String
    
    ''' <summary>
    ''' الرصيد الحالي بالدينار العراقي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property CurrentBalanceIQD As Decimal = 0
    
    ''' <summary>
    ''' الرصيد الحالي بالدولار الأمريكي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property CurrentBalanceUSD As Decimal = 0
    
    ''' <summary>
    ''' الرصيد الافتتاحي بالدينار العراقي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property InitialBalanceIQD As Decimal = 0
    
    ''' <summary>
    ''' الرصيد الافتتاحي بالدولار الأمريكي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property InitialBalanceUSD As Decimal = 0
    
    ''' <summary>
    ''' نوع المورد (شركة، فرد، إلخ)
    ''' </summary>
    <StringLength(50, ErrorMessage:="نوع المورد يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property SupplierType As String
    
    ''' <summary>
    ''' الرقم الضريبي للمورد
    ''' </summary>
    <StringLength(50, ErrorMessage:="الرقم الضريبي يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property TaxNumber As String
    
    ''' <summary>
    ''' ملاحظات حول المورد
    ''' </summary>
    <StringLength(500, ErrorMessage:="الملاحظات يجب أن تكون أقل من 500 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Notes As String
    
    ''' <summary>
    ''' حالة المورد (نشط/غير نشط)
    ''' </summary>
    Public Property IsActive As Boolean = True
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
    ''' <summary>
    ''' معرف المستخدم الذي أنشأ السجل
    ''' </summary>
    Public Property CreatedBy As Integer?
    
    ''' <summary>
    ''' معرف المستخدم الذي حدث السجل
    ''' </summary>
    Public Property UpdatedBy As Integer?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' المعاملات المرتبطة بالمورد
    ''' </summary>
    Public Overridable Property Transactions As ICollection(Of Transaction) = New HashSet(Of Transaction)
    
    ''' <summary>
    ''' الديون المرتبطة بالمورد
    ''' </summary>
    Public Overridable Property Debts As ICollection(Of Debt) = New HashSet(Of Debt)
    
    ''' <summary>
    ''' المستخدم الذي أنشأ السجل
    ''' </summary>
    <ForeignKey("CreatedBy")>
    Public Overridable Property Creator As User
    
    ''' <summary>
    ''' المستخدم الذي حدث السجل
    ''' </summary>
    <ForeignKey("UpdatedBy")>
    Public Overridable Property Updater As User
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' إجمالي الرصيد بالدينار (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TotalBalanceIQD As Decimal
        Get
            Return CurrentBalanceIQD
        End Get
    End Property
    
    ''' <summary>
    ''' إجمالي الرصيد بالدولار (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TotalBalanceUSD As Decimal
        Get
            Return CurrentBalanceUSD
        End Get
    End Property
    
    ''' <summary>
    ''' حالة الرصيد (دائن/مدين)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property BalanceStatus As String
        Get
            If CurrentBalanceIQD > 0 OrElse CurrentBalanceUSD > 0 Then
                Return "دائن"
            ElseIf CurrentBalanceIQD < 0 OrElse CurrentBalanceUSD < 0 Then
                Return "مدين"
            Else
                Return "متوازن"
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' عدد المعاملات (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TransactionCount As Integer
        Get
            Return If(Transactions?.Count, 0)
        End Get
    End Property
    
    ''' <summary>
    ''' آخر معاملة (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property LastTransactionDate As DateTime?
        Get
            Return Transactions?.OrderByDescending(Function(t) t.CreatedAt).FirstOrDefault()?.TransactionDate
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تحديث الرصيد الحالي
    ''' </summary>
    ''' <param name="amount">المبلغ</param>
    ''' <param name="currency">العملة</param>
    ''' <param name="isIncome">هل هو وارد أم صادر</param>
    Public Sub UpdateBalance(amount As Decimal, currency As String, isIncome As Boolean)
        If currency.ToUpper() = "IQD" Then
            If isIncome Then
                CurrentBalanceIQD += amount
            Else
                CurrentBalanceIQD -= amount
            End If
        ElseIf currency.ToUpper() = "USD" Then
            If isIncome Then
                CurrentBalanceUSD += amount
            Else
                CurrentBalanceUSD -= amount
            End If
        End If
        
        UpdatedAt = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' تنسيق الرصيد للعرض
    ''' </summary>
    ''' <param name="currency">العملة</param>
    ''' <returns>الرصيد منسق</returns>
    Public Function GetFormattedBalance(currency As String) As String
        If currency.ToUpper() = "IQD" Then
            Return String.Format("{0:N0} د.ع", CurrentBalanceIQD)
        ElseIf currency.ToUpper() = "USD" Then
            Return String.Format("${0:N2}", CurrentBalanceUSD)
        Else
            Return "0"
        End If
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(Name) Then
            errors.Add("اسم المورد مطلوب")
        End If
        
        If Not String.IsNullOrWhiteSpace(Email) AndAlso Not IsValidEmail(Email) Then
            errors.Add("البريد الإلكتروني غير صحيح")
        End If
        
        If Not String.IsNullOrWhiteSpace(Phone) AndAlso Phone.Length > 20 Then
            errors.Add("رقم الهاتف طويل جداً")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البريد الإلكتروني
    ''' </summary>
    ''' <param name="email">البريد الإلكتروني</param>
    ''' <returns>صحيح إذا كان البريد صحيح</returns>
    Private Function IsValidEmail(email As String) As Boolean
        Try
            Dim addr As New System.Net.Mail.MailAddress(email)
            Return addr.Address = email
        Catch
            Return False
        End Try
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للمورد
    ''' </summary>
    ''' <returns>اسم المورد</returns>
    Public Overrides Function ToString() As String
        Return Name
    End Function
    
#End Region

End Class
