<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة وصفة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_recipe') {
    try {
        $productId = (int)$_POST['product_id'];
        $materialId = (int)$_POST['material_id'];
        $quantityRequired = (float)$_POST['quantity_required'];
        $costPerUnit = (float)$_POST['cost_per_unit'];
        
        // التحقق من عدم وجود الوصفة مسبقاً
        $existing = $db->fetchOne("SELECT id FROM production_recipes WHERE product_id = ? AND material_id = ?", [$productId, $materialId]);
        
        if ($existing) {
            $error = "هذه المادة موجودة بالفعل في وصفة هذا المنتج";
        } else {
            $db->query("INSERT INTO production_recipes (product_id, material_id, quantity_required, cost_per_unit) VALUES (?, ?, ?, ?)", 
                      [$productId, $materialId, $quantityRequired, $costPerUnit]);
            $success = "تم إضافة المادة للوصفة بنجاح";
        }
    } catch (Exception $e) {
        $error = "خطأ في إضافة الوصفة: " . $e->getMessage();
    }
}

// معالجة حذف مادة من الوصفة
if (isset($_GET['delete_recipe'])) {
    try {
        $recipeId = (int)$_GET['delete_recipe'];
        $db->query("DELETE FROM production_recipes WHERE id = ?", [$recipeId]);
        $success = "تم حذف المادة من الوصفة بنجاح";
    } catch (Exception $e) {
        $error = "خطأ في حذف المادة: " . $e->getMessage();
    }
}

// جلب المنتجات النهائية
$products = $db->fetchAll("SELECT id, code, name, unit FROM items WHERE type = 'finished_product' AND is_active = 1 ORDER BY name");

// جلب المواد الخام
$materials = $db->fetchAll("SELECT id, code, name, unit FROM items WHERE type = 'raw_material' AND is_active = 1 ORDER BY name");

// جلب الوصفات مع تفاصيلها
$selectedProduct = $_GET['product_id'] ?? '';
$recipes = [];
if ($selectedProduct) {
    $recipes = $db->fetchAll("
        SELECT pr.*, p.name as product_name, p.code as product_code, p.unit as product_unit,
               m.name as material_name, m.code as material_code, m.unit as material_unit
        FROM production_recipes pr
        JOIN items p ON pr.product_id = p.id
        JOIN items m ON pr.material_id = m.id
        WHERE pr.product_id = ? AND pr.is_active = 1
        ORDER BY m.name
    ", [$selectedProduct]);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصفات الإنتاج - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .recipe-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .recipe-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .cost-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-flask me-3"></i>وصفات الإنتاج</h1>
                    <p class="mb-0">إدارة وصفات ومكونات الإنتاج</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="production.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للإنتاج
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- اختيار المنتج -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>اختيار المنتج</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET">
                            <div class="mb-3">
                                <label class="form-label">المنتج</label>
                                <select name="product_id" class="form-select" onchange="this.form.submit()">
                                    <option value="">اختر المنتج</option>
                                    <?php foreach ($products as $product): ?>
                                        <option value="<?= $product['id'] ?>" <?= $selectedProduct == $product['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($product['code'] . ' - ' . $product['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- إضافة مادة جديدة -->
                <?php if ($selectedProduct): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-plus me-2"></i>إضافة مادة</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="add_recipe">
                                <input type="hidden" name="product_id" value="<?= $selectedProduct ?>">
                                
                                <div class="mb-3">
                                    <label class="form-label">المادة الخام</label>
                                    <select name="material_id" class="form-select" required>
                                        <option value="">اختر المادة</option>
                                        <?php foreach ($materials as $material): ?>
                                            <option value="<?= $material['id'] ?>" data-unit="<?= $material['unit'] ?>">
                                                <?= htmlspecialchars($material['code'] . ' - ' . $material['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">الكمية المطلوبة</label>
                                    <input type="number" name="quantity_required" class="form-control" 
                                           step="0.001" min="0.001" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تكلفة الوحدة (د.ع)</label>
                                    <input type="number" name="cost_per_unit" class="form-control" 
                                           step="0.01" min="0" required>
                                </div>
                                
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-plus me-2"></i>إضافة المادة
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- عرض الوصفة -->
            <div class="col-lg-8">
                <?php if ($selectedProduct && !empty($recipes)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                وصفة إنتاج: <?= htmlspecialchars($recipes[0]['product_name']) ?>
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php 
                            $totalCost = 0;
                            foreach ($recipes as $recipe): 
                                $totalCost += $recipe['quantity_required'] * $recipe['cost_per_unit'];
                            ?>
                                <div class="recipe-item">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1"><?= htmlspecialchars($recipe['material_name']) ?></h6>
                                            <small class="text-muted"><?= htmlspecialchars($recipe['material_code']) ?></small>
                                        </div>
                                        <div class="col-md-3 text-center">
                                            <strong><?= number_format($recipe['quantity_required'], 3) ?></strong>
                                            <br><small class="text-muted"><?= htmlspecialchars($recipe['material_unit']) ?></small>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <span class="cost-badge">
                                                <?= number_format($recipe['quantity_required'] * $recipe['cost_per_unit'], 2) ?> د.ع
                                            </span>
                                        </div>
                                        <div class="col-md-1 text-end">
                                            <a href="?product_id=<?= $selectedProduct ?>&delete_recipe=<?= $recipe['id'] ?>" 
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه المادة؟')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                            
                            <div class="mt-4 p-3 bg-light rounded">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>التكلفة الإجمالية لوحدة واحدة:</h5>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <h5 class="text-success"><?= number_format($totalCost, 2) ?> د.ع</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php elseif ($selectedProduct): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-flask fa-4x text-muted mb-3"></i>
                            <h5>لا توجد وصفة لهذا المنتج</h5>
                            <p class="text-muted">ابدأ بإضافة المواد الخام المطلوبة لإنتاج هذا المنتج</p>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-search fa-4x text-muted mb-3"></i>
                            <h5>اختر منتجاً لعرض وصفة الإنتاج</h5>
                            <p class="text-muted">حدد المنتج من القائمة الجانبية لعرض أو تعديل وصفة الإنتاج</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
