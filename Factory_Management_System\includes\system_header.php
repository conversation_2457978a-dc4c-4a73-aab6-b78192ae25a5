<?php
// تطبيق إعدادات النظام على جميع الصفحات
require_once __DIR__ . '/../classes/SystemSettings.php';

// إنشاء كائن الإعدادات
$systemSettings = new SystemSettings();

// تطبيق المنطقة الزمنية
$systemSettings->setTimezone();

// الحصول على معلومات الشركة
$companyInfo = $systemSettings->getCompanyInfo();

// دالة لعرض شعار الشركة
function getCompanyLogo($class = '', $style = '') {
    global $systemSettings;
    $logo = $systemSettings->get('company_logo');
    
    if ($logo && file_exists(__DIR__ . '/../' . $logo)) {
        return "<img src='$logo' alt='شعار الشركة' class='$class' style='$style'>";
    } else {
        // شعار افتراضي
        return "<i class='fas fa-industry fa-2x' style='color: " . $systemSettings->get('theme_color', '#6f42c1') . "; $style'></i>";
    }
}

// دالة لعرض اسم الشركة
function getCompanyName() {
    global $systemSettings;
    return $systemSettings->get('company_name', 'شركة المعامل المتقدمة');
}

// دالة لعرض اسم النظام
function getSystemName() {
    global $systemSettings;
    return $systemSettings->get('system_name', 'نظام إدارة المعامل');
}

// دالة لتنسيق التاريخ حسب إعدادات النظام
function formatSystemDate($date, $includeTime = false) {
    global $systemSettings;
    return $systemSettings->formatDate($date, $includeTime);
}

// دالة للحصول على CSS المخصص للمظهر
function getSystemThemeCSS() {
    global $systemSettings;
    return $systemSettings->getThemeCSS();
}

// دالة للحصول على معلومات الاتصال
function getContactInfo() {
    global $companyInfo;
    return [
        'phone' => $companyInfo['phone'],
        'email' => $companyInfo['email'],
        'address' => $companyInfo['address'],
        'website' => $companyInfo['website']
    ];
}

// دالة لعرض رأس الصفحة المخصص
function renderSystemHeader($pageTitle = '', $showLogo = true) {
    $systemName = getSystemName();
    $companyName = getCompanyName();
    $logo = $showLogo ? getCompanyLogo('me-3', 'height: 40px;') : '';
    
    echo "
    <div class='system-header'>
        <div class='container-fluid'>
            <div class='row align-items-center'>
                <div class='col-md-6'>
                    <div class='d-flex align-items-center'>
                        $logo
                        <div>
                            <h4 class='mb-0'>$companyName</h4>
                            <small class='text-muted'>$systemName</small>
                        </div>
                    </div>
                </div>
                <div class='col-md-6 text-end'>
                    <div class='system-info'>
                        <small class='text-muted'>" . formatSystemDate(date('Y-m-d H:i:s'), true) . "</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    ";
}

// دالة لعرض تذييل الصفحة المخصص
function renderSystemFooter() {
    $companyName = getCompanyName();
    $contactInfo = getContactInfo();
    $systemName = getSystemName();
    
    echo "
    <footer class='system-footer mt-5 py-4 bg-light'>
        <div class='container'>
            <div class='row'>
                <div class='col-md-6'>
                    <h6>$companyName</h6>
                    <p class='mb-1'><i class='fas fa-map-marker-alt me-2'></i>{$contactInfo['address']}</p>
                    <p class='mb-1'><i class='fas fa-phone me-2'></i>{$contactInfo['phone']}</p>
                    <p class='mb-0'><i class='fas fa-envelope me-2'></i>{$contactInfo['email']}</p>
                </div>
                <div class='col-md-6 text-end'>
                    <p class='mb-0'>$systemName</p>
                    <small class='text-muted'>جميع الحقوق محفوظة © " . date('Y') . "</small>
                </div>
            </div>
        </div>
    </footer>
    ";
}

// CSS مخصص للنظام
$systemCSS = "
<style>
" . getSystemThemeCSS() . "

.system-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 0;
    margin-bottom: 1rem;
    border-radius: 0 0 15px 15px;
}

.system-footer {
    border-top: 3px solid var(--primary-color);
    margin-top: auto;
}

.company-logo {
    max-height: 50px;
    width: auto;
}

.page-title {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.card-header.system-header-style {
    background: var(--gradient-primary) !important;
    color: white;
}

.btn-system-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-system-primary:hover {
    background: var(--primary-color);
    color: white;
}

.alert-system {
    border-left: 4px solid var(--primary-color);
}

.nav-pills .nav-link.active {
    background: var(--gradient-primary) !important;
}

.progress-bar {
    background: var(--gradient-primary);
}

.text-system-primary {
    color: var(--primary-color) !important;
}

.border-system-primary {
    border-color: var(--primary-color) !important;
}

.bg-system-primary {
    background: var(--gradient-primary) !important;
}

/* تحسينات للطباعة */
@media print {
    .system-header, .system-footer, .no-print {
        display: none !important;
    }
    
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 2rem;
        border-bottom: 2px solid #333;
        padding-bottom: 1rem;
    }
    
    .print-company-info {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .print-contact-info {
        font-size: 14px;
        color: #666;
    }
}

.print-header {
    display: none;
}
</style>
";

// إضافة CSS إلى الصفحة
if (!defined('SYSTEM_CSS_LOADED')) {
    echo $systemCSS;
    define('SYSTEM_CSS_LOADED', true);
}
?>

<!-- رأس الطباعة (مخفي في العرض العادي) -->
<div class="print-header">
    <div class="print-company-info"><?= getCompanyName() ?></div>
    <div class="print-contact-info">
        <?= $companyInfo['address'] ?> | <?= $companyInfo['phone'] ?> | <?= $companyInfo['email'] ?>
    </div>
</div>
