<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة البيانات التجريبية
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_sample_data'])) {
    try {
        $db->beginTransaction();
        
        // إضافة أصناف تجريبية
        $sampleItems = [
            ['name' => 'منتج نهائي A', 'code' => 'PROD001', 'type' => 'product', 'unit' => 'قطعة', 'cost_price' => 50000, 'selling_price' => 75000],
            ['name' => 'منتج نهائي B', 'code' => 'PROD002', 'type' => 'product', 'unit' => 'قطعة', 'cost_price' => 30000, 'selling_price' => 45000],
            ['name' => 'مادة خام 1', 'code' => 'RAW001', 'type' => 'raw_material', 'unit' => 'كيلو', 'cost_price' => 5000, 'selling_price' => 0],
            ['name' => 'مادة خام 2', 'code' => 'RAW002', 'type' => 'raw_material', 'unit' => 'متر', 'cost_price' => 8000, 'selling_price' => 0],
            ['name' => 'مادة خام 3', 'code' => 'RAW003', 'type' => 'raw_material', 'unit' => 'لتر', 'cost_price' => 12000, 'selling_price' => 0]
        ];
        
        foreach ($sampleItems as $item) {
            // التحقق من عدم وجود الصنف
            $existing = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$item['code']]);
            if (!$existing) {
                $db->insert('items', $item);
            }
        }
        
        // إضافة مخازن تجريبية
        $sampleWarehouses = [
            ['name' => 'مخزن المواد الخام', 'code' => 'WH001', 'location' => 'الطابق الأرضي', 'is_active' => 1],
            ['name' => 'مخزن المنتجات النهائية', 'code' => 'WH002', 'location' => 'الطابق الأول', 'is_active' => 1]
        ];
        
        foreach ($sampleWarehouses as $warehouse) {
            $existing = $db->fetchOne("SELECT id FROM warehouses WHERE code = ?", [$warehouse['code']]);
            if (!$existing) {
                $db->insert('warehouses', $warehouse);
            }
        }
        
        // إضافة مخزون تجريبي
        $items = $db->fetchAll("SELECT id, type FROM items");
        $warehouses = $db->fetchAll("SELECT id FROM warehouses");
        
        foreach ($items as $item) {
            foreach ($warehouses as $warehouse) {
                $existing = $db->fetchOne("SELECT id FROM inventory WHERE item_id = ? AND warehouse_id = ?", 
                                        [$item['id'], $warehouse['id']]);
                if (!$existing) {
                    $quantity = $item['type'] == 'raw_material' ? rand(100, 500) : rand(10, 50);
                    $db->insert('inventory', [
                        'item_id' => $item['id'],
                        'warehouse_id' => $warehouse['id'],
                        'quantity' => $quantity,
                        'unit_cost' => rand(1000, 10000),
                        'reorder_level' => $item['type'] == 'raw_material' ? 50 : 5
                    ]);
                }
            }
        }
        
        // إضافة أوامر إنتاج تجريبية
        $products = $db->fetchAll("SELECT id FROM items WHERE type = 'product'");
        $rawMaterials = $db->fetchAll("SELECT id FROM items WHERE type = 'raw_material'");
        
        for ($i = 1; $i <= 5; $i++) {
            $orderNumber = 'PO2024' . str_pad($i, 4, '0', STR_PAD_LEFT);
            
            // التحقق من عدم وجود الأمر
            $existing = $db->fetchOne("SELECT id FROM production_orders WHERE order_number = ?", [$orderNumber]);
            if (!$existing) {
                $productId = $products[array_rand($products)]['id'];
                $quantity = rand(10, 100);
                $status = ['pending', 'in_progress', 'completed'][rand(0, 2)];
                $priority = ['low', 'normal', 'high', 'urgent'][rand(0, 3)];
                
                $orderData = [
                    'order_number' => $orderNumber,
                    'product_id' => $productId,
                    'quantity_required' => $quantity,
                    'quantity_produced' => $status == 'completed' ? $quantity : ($status == 'in_progress' ? rand(0, $quantity) : 0),
                    'start_date' => date('Y-m-d', strtotime('-' . rand(1, 30) . ' days')),
                    'due_date' => date('Y-m-d', strtotime('+' . rand(1, 15) . ' days')),
                    'status' => $status,
                    'priority' => $priority,
                    'estimated_cost' => $quantity * rand(1000, 5000),
                    'actual_cost' => $status == 'completed' ? $quantity * rand(1000, 5000) : 0,
                    'notes' => 'أمر إنتاج تجريبي رقم ' . $i,
                    'created_by' => $user['id']
                ];
                
                if ($status == 'completed') {
                    $orderData['completion_date'] = date('Y-m-d', strtotime('-' . rand(1, 10) . ' days'));
                }
                
                $orderId = $db->insert('production_orders', $orderData);
                
                // إضافة مواد للأمر
                $numMaterials = rand(2, 4);
                $selectedMaterials = array_rand($rawMaterials, min($numMaterials, count($rawMaterials)));
                if (!is_array($selectedMaterials)) {
                    $selectedMaterials = [$selectedMaterials];
                }
                
                foreach ($selectedMaterials as $materialIndex) {
                    $materialId = $rawMaterials[$materialIndex]['id'];
                    $materialQuantity = rand(5, 50);
                    
                    $db->insert('production_materials', [
                        'production_order_id' => $orderId,
                        'material_id' => $materialId,
                        'quantity_required' => $materialQuantity,
                        'quantity_used' => $status == 'completed' ? $materialQuantity : ($status == 'in_progress' ? rand(0, $materialQuantity) : 0),
                        'unit_cost' => rand(1000, 3000),
                        'status' => $status == 'completed' ? 'consumed' : ($status == 'in_progress' ? 'allocated' : 'pending')
                    ]);
                }
            }
        }
        
        $db->commit();
        $success = "تم إضافة البيانات التجريبية بنجاح!";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إضافة البيانات التجريبية: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة بيانات تجريبية - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 30px 30px;
        }
        
        .card {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 12px 30px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list i {
            color: #28a745;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-database me-3"></i>إضافة بيانات تجريبية</h1>
                    <p class="mb-0">إضافة بيانات تجريبية لاختبار النظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="dashboard.php" class="btn btn-light">
                        <i class="fas fa-home me-2"></i>لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <div class="mt-2">
                    <a href="production.php" class="btn btn-sm btn-success me-2">
                        <i class="fas fa-industry me-1"></i>عرض الإنتاج
                    </a>
                    <a href="production_reports.php" class="btn btn-sm btn-info">
                        <i class="fas fa-chart-line me-1"></i>عرض التقارير
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>البيانات التجريبية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>سيتم إضافة البيانات التالية:</h6>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i>5 أصناف (منتجات ومواد خام)</li>
                            <li><i class="fas fa-check"></i>2 مخزن (مواد خام ومنتجات نهائية)</li>
                            <li><i class="fas fa-check"></i>مخزون تجريبي للأصناف</li>
                            <li><i class="fas fa-check"></i>5 أوامر إنتاج بحالات مختلفة</li>
                            <li><i class="fas fa-check"></i>مواد مطلوبة لكل أمر إنتاج</li>
                            <li><i class="fas fa-check"></i>بيانات تكاليف وكميات واقعية</li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>فوائد البيانات التجريبية:</h6>
                        <ul class="feature-list">
                            <li><i class="fas fa-chart-line"></i>اختبار تقارير الإنتاج</li>
                            <li><i class="fas fa-cogs"></i>تجربة سير العمل</li>
                            <li><i class="fas fa-boxes"></i>اختبار إدارة المخزون</li>
                            <li><i class="fas fa-industry"></i>تجربة عمليات الإنتاج</li>
                            <li><i class="fas fa-chart-pie"></i>عرض الإحصائيات</li>
                            <li><i class="fas fa-tasks"></i>اختبار جميع الوظائف</li>
                        </ul>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <form method="POST">
                        <button type="submit" name="add_sample_data" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>إضافة البيانات التجريبية
                        </button>
                    </form>
                    
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            ستتم إضافة البيانات فقط إذا لم تكن موجودة مسبقاً
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-link me-2"></i>روابط سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="production.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-industry me-2"></i>نظام الإنتاج
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="production_reports.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-line me-2"></i>تقارير الإنتاج
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="inventory.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-boxes me-2"></i>المخزون
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="items.php" class="btn btn-outline-warning w-100">
                            <i class="fas fa-cube me-2"></i>الأصناف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
