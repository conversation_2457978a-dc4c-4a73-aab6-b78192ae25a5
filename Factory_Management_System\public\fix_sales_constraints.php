<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // حذف القيود المرجعية المشكلة وإعادة إنشاء الجداول
        $db->query("SET FOREIGN_KEY_CHECKS = 0");
        
        // حذف جدول تفاصيل فواتير المبيعات وإعادة إنشاؤه بدون قيود مرجعية صارمة
        $db->query("DROP TABLE IF EXISTS sales_invoice_details");
        
        // إعادة إنشاء جدول تفاصيل فواتير المبيعات
        $db->query("CREATE TABLE sales_invoice_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED,
            INDEX idx_invoice_id (invoice_id),
            INDEX idx_item_id (item_id),
            INDEX idx_warehouse_id (warehouse_id)
        )");
        
        // التأكد من وجود المخازن الأساسية
        $warehouses = [
            ['المخزن الرئيسي', 'المبنى الرئيسي - الطابق الأرضي'],
            ['مخزن المواد الخام', 'المبنى الإنتاجي - الطابق الأول'],
            ['مخزن المنتجات النهائية', 'المبنى الرئيسي - الطابق الثاني'],
            ['مخزن قطع الغيار', 'المبنى الخدمي - الطابق الأرضي'],
            ['مخزن المواد الاستهلاكية', 'المبنى الإداري - الطابق الأول']
        ];
        
        foreach ($warehouses as $warehouse) {
            $existing = $db->fetchOne("SELECT id FROM warehouses WHERE name = ?", [$warehouse[0]]);
            if (!$existing) {
                $db->query("INSERT INTO warehouses (name, location, is_active) VALUES (?, ?, 1)", $warehouse);
            }
        }
        
        // التأكد من وجود العملاء
        $customers = [
            ['CUST001', 'شركة النور للتجارة', 'company', '07701234567', '<EMAIL>', 'بغداد - الكرادة', '*********', 5000000.00],
            ['CUST002', 'أحمد محمد علي', 'individual', '07709876543', '<EMAIL>', 'البصرة - المعقل', '', 1000000.00],
            ['CUST003', 'مؤسسة الفرات للمقاولات', 'institution', '07801234567', '<EMAIL>', 'النجف - المركز', '*********', 10000000.00]
        ];
        
        foreach ($customers as $customer) {
            $existing = $db->fetchOne("SELECT id FROM customers WHERE code = ?", [$customer[0]]);
            if (!$existing) {
                $db->query("INSERT INTO customers (code, name, type, phone, email, address, tax_number, credit_limit, currency_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)", $customer);
            }
        }
        
        // التأكد من وجود الأصناف للبيع
        $items = [
            ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100.00, 150.00],
            ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80.00, 120.00],
            ['PROD003', 'منتج نهائي ج', 'منتج جاهز للبيع', 2, 'علبة', 'finished_product', 200.00, 280.00],
            ['SEMI001', 'منتج نصف مصنع أ', 'منتج في مرحلة التصنيع', 3, 'قطعة', 'semi_finished', 60.00, 90.00],
            ['SEMI002', 'منتج نصف مصنع ب', 'منتج في مرحلة التصنيع', 3, 'كيلو', 'semi_finished', 45.00, 70.00]
        ];
        
        foreach ($items as $item) {
            $existing = $db->fetchOne("SELECT id FROM items WHERE code = ?", [$item[0]]);
            if (!$existing) {
                $db->query("INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)", $item);
            }
        }
        
        // التأكد من وجود العملات
        $currencies = [
            ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
            ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
            ['اليورو', '€', 'EUR', 0.00061, 0]
        ];
        
        foreach ($currencies as $currency) {
            $existing = $db->fetchOne("SELECT id FROM currencies WHERE code = ?", [$currency[2]]);
            if (!$existing) {
                $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
            }
        }
        
        $db->query("SET FOREIGN_KEY_CHECKS = 1");
        
        $db->commit();
        $success = 'تم إصلاح مشكلة القيود المرجعية بنجاح! يمكنك الآن إنشاء فواتير المبيعات.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في إصلاح القيود المرجعية: ' . $e->getMessage();
    }
}

// فحص حالة البيانات
$dataStatus = [];
try {
    $dataStatus['warehouses'] = $db->fetchOne("SELECT COUNT(*) as count FROM warehouses WHERE is_active = 1")['count'];
    $dataStatus['customers'] = $db->fetchOne("SELECT COUNT(*) as count FROM customers WHERE is_active = 1")['count'];
    $dataStatus['items'] = $db->fetchOne("SELECT COUNT(*) as count FROM items WHERE is_active = 1 AND type IN ('finished_product', 'semi_finished')")['count'];
    $dataStatus['currencies'] = $db->fetchOne("SELECT COUNT(*) as count FROM currencies WHERE is_active = 1")['count'];
} catch (Exception $e) {
    $dataStatus = ['warehouses' => 0, 'customers' => 0, 'items' => 0, 'currencies' => 0];
}

$allDataReady = $dataStatus['warehouses'] > 0 && $dataStatus['customers'] > 0 && $dataStatus['items'] > 0 && $dataStatus['currencies'] > 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل المبيعات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .fix-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .fix-body {
            padding: 40px;
        }
        .btn-fix {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
    </style>
</head>
<body>
    <div class="fix-container">
        <div class="fix-header">
            <h1><i class="fas fa-tools fa-2x mb-3"></i><br>إصلاح مشاكل المبيعات</h1>
            <p class="mb-0">إصلاح مشكلة القيود المرجعية في فواتير المبيعات</p>
        </div>
        
        <div class="fix-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <!-- حالة البيانات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة البيانات المطلوبة</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>البيانات</th>
                                    <th>العدد</th>
                                    <th>الحالة</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>المخازن</strong></td>
                                    <td><?= $dataStatus['warehouses'] ?></td>
                                    <td>
                                        <i class="fas fa-<?= $dataStatus['warehouses'] > 0 ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        <?= $dataStatus['warehouses'] > 0 ? 'جاهز' : 'مطلوب' ?>
                                    </td>
                                    <td>مخازن نشطة للمبيعات</td>
                                </tr>
                                <tr>
                                    <td><strong>العملاء</strong></td>
                                    <td><?= $dataStatus['customers'] ?></td>
                                    <td>
                                        <i class="fas fa-<?= $dataStatus['customers'] > 0 ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        <?= $dataStatus['customers'] > 0 ? 'جاهز' : 'مطلوب' ?>
                                    </td>
                                    <td>عملاء نشطين</td>
                                </tr>
                                <tr>
                                    <td><strong>الأصناف</strong></td>
                                    <td><?= $dataStatus['items'] ?></td>
                                    <td>
                                        <i class="fas fa-<?= $dataStatus['items'] > 0 ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        <?= $dataStatus['items'] > 0 ? 'جاهز' : 'مطلوب' ?>
                                    </td>
                                    <td>منتجات للبيع</td>
                                </tr>
                                <tr>
                                    <td><strong>العملات</strong></td>
                                    <td><?= $dataStatus['currencies'] ?></td>
                                    <td>
                                        <i class="fas fa-<?= $dataStatus['currencies'] > 0 ? 'check-circle status-good' : 'times-circle status-bad' ?>"></i>
                                        <?= $dataStatus['currencies'] > 0 ? 'جاهز' : 'مطلوب' ?>
                                    </td>
                                    <td>عملات نشطة</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- وصف المشكلة -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bug me-2"></i>وصف المشكلة</h5>
                </div>
                <div class="card-body">
                    <p><strong>المشكلة:</strong> خطأ في القيود المرجعية (Foreign Key Constraints) عند إنشاء فواتير المبيعات.</p>
                    <p><strong>السبب:</strong> جدول تفاصيل فواتير المبيعات يتطلب وجود مخازن وأصناف وعملاء صحيحة.</p>
                    <p><strong>الحل:</strong> إعادة إنشاء الجداول بدون قيود صارمة وإضافة البيانات الأساسية المطلوبة.</p>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center">
                <?php if (!$allDataReady): ?>
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-fix btn-lg me-3">
                            <i class="fas fa-wrench me-2"></i>إصلاح المشكلة
                        </button>
                    </form>
                <?php endif; ?>
                
                <a href="add_sale.php" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-plus me-2"></i>فاتورة جديدة
                </a>
                
                <a href="sales.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-list me-2"></i>المبيعات
                </a>
            </div>

            <?php if ($allDataReady): ?>
                <div class="alert alert-success mt-4">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>تم الإصلاح!</strong> جميع البيانات المطلوبة متوفرة. يمكنك الآن إنشاء فواتير المبيعات بدون مشاكل.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
