<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جميع الجداول بدون قيود مرجعية صارمة
        
        // جدول العملات
        $db->query("CREATE TABLE IF NOT EXISTS currencies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الأقسام
        $db->query("CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الحضور
        $db->query("CREATE TABLE IF NOT EXISTS attendance (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            attendance_date DATE NOT NULL,
            check_in_time TIME,
            check_out_time TIME,
            break_duration INT DEFAULT 0,
            total_hours DECIMAL(4,2) DEFAULT 0.00,
            overtime_hours DECIMAL(4,2) DEFAULT 0.00,
            status ENUM('present', 'absent', 'late', 'half_day', 'holiday') DEFAULT 'present',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_employee_date (employee_id, attendance_date)
        )");
        
        // جدول الرواتب
        $db->query("CREATE TABLE IF NOT EXISTS payroll (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            pay_period_start DATE NOT NULL,
            pay_period_end DATE NOT NULL,
            basic_salary DECIMAL(10,2) DEFAULT 0.00,
            overtime_amount DECIMAL(10,2) DEFAULT 0.00,
            bonus DECIMAL(10,2) DEFAULT 0.00,
            allowances DECIMAL(10,2) DEFAULT 0.00,
            deductions DECIMAL(10,2) DEFAULT 0.00,
            gross_salary DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            net_salary DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('draft', 'approved', 'paid') DEFAULT 'draft',
            payment_date DATE,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول المصاريف العامة
        $db->query("CREATE TABLE IF NOT EXISTS general_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            category ENUM('utilities', 'rent', 'maintenance', 'office', 'marketing', 'travel', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            department_id INT,
            vendor_name VARCHAR(200),
            invoice_number VARCHAR(100),
            payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') DEFAULT 'cash',
            status ENUM('pending', 'approved', 'paid', 'rejected') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول مصاريف الإنتاج
        $db->query("CREATE TABLE IF NOT EXISTS production_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            production_order_id INT,
            category ENUM('labor', 'utilities', 'maintenance', 'materials', 'overhead', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            employee_id INT,
            cost_center VARCHAR(100),
            allocation_percentage DECIMAL(5,2) DEFAULT 100.00,
            status ENUM('pending', 'approved', 'allocated') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // جدول فئات الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول الأصناف
        $db->query("CREATE TABLE IF NOT EXISTS items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول المخازن
        $db->query("CREATE TABLE IF NOT EXISTS warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول العملاء
        $db->query("CREATE TABLE IF NOT EXISTS customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول فواتير المبيعات
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // جدول تفاصيل فواتير المبيعات (بدون قيود مرجعية صارمة)
        $db->query("CREATE TABLE IF NOT EXISTS sales_invoice_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED
        )");
        
        // إضافة البيانات الأساسية
        
        // العملات
        $currencies = [
            ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
            ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
            ['اليورو', '€', 'EUR', 0.00061, 0]
        ];
        
        foreach ($currencies as $currency) {
            $existing = $db->fetchOne("SELECT id FROM currencies WHERE code = ?", [$currency[2]]);
            if (!$existing) {
                $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
            }
        }
        
        // الأقسام
        $departments = [
            ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
            ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
            ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
            ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
            ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
            ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
            ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
            ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
        ];
        
        foreach ($departments as $dept) {
            $existing = $db->fetchOne("SELECT id FROM departments WHERE name = ?", [$dept[0]]);
            if (!$existing) {
                $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
            }
        }
        
        $db->commit();
        $success = 'تم الإعداد الكامل بنجاح! تم إنشاء جميع الجداول والبيانات الأساسية.';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في الإعداد: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعداد الكامل - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-complete {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-rocket fa-2x mb-3"></i><br>الإعداد الكامل</h1>
            <p class="mb-0">إعداد شامل وآمن لجميع مكونات النظام</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                
                <div class="text-center">
                    <h5 class="mb-3">النظام جاهز للاستخدام!</h5>
                    <div class="d-grid gap-2">
                        <a href="system_diagnosis.php" class="btn btn-info btn-lg">
                            <i class="fas fa-stethoscope me-2"></i>تشخيص النظام
                        </a>
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>ما سيتم إنشاؤه</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الجداول الأساسية:</h6>
                                <ul>
                                    <li>العملات والأقسام</li>
                                    <li>الموظفين والحضور</li>
                                    <li>الرواتب والمصاريف</li>
                                    <li>الأصناف والمخازن</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>البيانات التجريبية:</h6>
                                <ul>
                                    <li>3 عملات (دينار، دولار، يورو)</li>
                                    <li>8 أقسام متنوعة</li>
                                    <li>عملاء وأصناف تجريبية</li>
                                    <li>مخازن افتراضية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-complete btn-lg me-3">
                            <i class="fas fa-play me-2"></i>بدء الإعداد الكامل
                        </button>
                    </form>
                    
                    <a href="system_diagnosis.php" class="btn btn-warning btn-lg">
                        <i class="fas fa-stethoscope me-2"></i>تشخيص أولاً
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
