<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة موظف جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();

        // التأكد من وجود جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            photo VARCHAR(500),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إضافة عمود الصورة إذا لم يكن موجوداً
        try {
            $db->query("ALTER TABLE employees ADD COLUMN photo VARCHAR(500)");
        } catch (Exception $e) {
            // العمود موجود بالفعل
        }

        // توليد كود الموظف
        $prefix = 'EMP';
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM employees");
        $sequence = ($countResult['count'] ?? 0) + 1;
        $employeeCode = $prefix . str_pad($sequence, 3, '0', STR_PAD_LEFT);

        // معالجة رفع الصورة
        $photoPath = '';
        if (isset($_FILES['photo']) && $_FILES['photo']['error'] == 0) {
            $uploadDir = '../uploads/employees/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $fileType = $_FILES['photo']['type'];

            if (in_array($fileType, $allowedTypes)) {
                $extension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
                $fileName = $employeeCode . '_' . time() . '.' . $extension;
                $uploadPath = $uploadDir . $fileName;

                if (move_uploaded_file($_FILES['photo']['tmp_name'], $uploadPath)) {
                    $photoPath = 'uploads/employees/' . $fileName;
                }
            }
        }

        // تنظيف البيانات
        $employeeData = [
            'employee_code' => $employeeCode,
            'full_name' => trim($_POST['full_name']),
            'email' => trim($_POST['email'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'national_id' => trim($_POST['national_id'] ?? ''),
            'birth_date' => !empty($_POST['birth_date']) ? $_POST['birth_date'] : null,
            'hire_date' => $_POST['hire_date'],
            'department_id' => !empty($_POST['department_id']) ? (int)$_POST['department_id'] : null,
            'position' => trim($_POST['position']),
            'salary' => (float)($_POST['salary'] ?? 0),
            'hourly_rate' => (float)($_POST['hourly_rate'] ?? 0),
            'overtime_rate' => (float)($_POST['overtime_rate'] ?? 0),
            'bank_account' => trim($_POST['bank_account'] ?? ''),
            'emergency_contact' => trim($_POST['emergency_contact'] ?? ''),
            'photo' => $photoPath,
            'notes' => trim($_POST['notes'] ?? '')
        ];

        $db->insert('employees', $employeeData);
        $db->commit();

        $success = "تم إضافة الموظف بنجاح برقم: $employeeCode";

    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إضافة الموظف: " . $e->getMessage();
    }
}

// جلب الأقسام
try {
    $departments = $db->fetchAll("SELECT id, name FROM departments WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $departments = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف جديد - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-user-plus me-3"></i>إضافة موظف جديد</h1>
                    <p class="mb-0">إضافة موظف جديد إلى النظام</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="employees.php" class="btn btn-light">
                        <i class="fas fa-users me-2"></i>قائمة الموظفين
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <div class="mt-2">
                    <a href="employees.php" class="btn btn-sm btn-primary">عرض الموظفين</a>
                    <a href="add_employee.php" class="btn btn-sm btn-outline-primary">إضافة موظف آخر</a>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="row">
                <!-- المعلومات الشخصية -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>المعلومات الشخصية</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" name="full_name" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" name="phone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea name="address" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">رقم الهوية</label>
                                <input type="text" name="national_id" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">تاريخ الميلاد</label>
                                <input type="date" name="birth_date" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">صورة الموظف</label>
                                <input type="file" name="photo" class="form-control" accept="image/*">
                                <div class="form-text">اختر صورة للموظف (PNG, JPG, GIF)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات العمل -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">تاريخ التوظيف <span class="text-danger">*</span></label>
                                <input type="date" name="hire_date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                <select name="department_id" class="form-select">
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">المنصب <span class="text-danger">*</span></label>
                                <input type="text" name="position" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي (د.ع)</label>
                                <input type="number" name="salary" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">أجر الساعة (د.ع)</label>
                                <input type="number" name="hourly_rate" class="form-control" step="0.01" min="0">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">أجر الساعة الإضافية (د.ع)</label>
                                <input type="number" name="overtime_rate" class="form-control" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم الحساب البنكي</label>
                            <input type="text" name="bank_account" class="form-control">
                        </div>

                        <div class="col-md-6 mb-3">
                            <label class="form-label">جهة الاتصال في الطوارئ</label>
                            <input type="text" name="emergency_contact" class="form-control">
                        </div>

                        <div class="col-12 mb-3">
                            <label class="form-label">ملاحظات</label>
                            <textarea name="notes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ الموظف
                </button>
                <a href="employees.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
