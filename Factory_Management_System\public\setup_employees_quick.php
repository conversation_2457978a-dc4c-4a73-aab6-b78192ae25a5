<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // إنشاء جدول العملات إذا لم يكن موجوداً
        $db->query("CREATE TABLE IF NOT EXISTS currencies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الأقسام
        $db->query("CREATE TABLE IF NOT EXISTS departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إنشاء جدول الموظفين
        $db->query("CREATE TABLE IF NOT EXISTS employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
        
        // إدراج العملات إذا لم تكن موجودة
        $currencyExists = $db->fetchOne("SELECT COUNT(*) as count FROM currencies");
        if ($currencyExists['count'] == 0) {
            $currencies = [
                ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
                ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
                ['اليورو', '€', 'EUR', 0.00061, 0]
            ];
            
            foreach ($currencies as $currency) {
                $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
            }
        }
        
        // إدراج الأقسام إذا لم تكن موجودة
        $deptExists = $db->fetchOne("SELECT COUNT(*) as count FROM departments");
        if ($deptExists['count'] == 0) {
            $departments = [
                ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
                ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
                ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
                ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
                ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
                ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
                ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
                ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
            ];
            
            foreach ($departments as $dept) {
                $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
            }
        }
        
        // إدراج موظفين تجريبيين إذا لم يكونوا موجودين
        $empExists = $db->fetchOne("SELECT COUNT(*) as count FROM employees");
        if ($empExists['count'] == 0) {
            $employees = [
                ['EMP001', 'أحمد محمد علي', '<EMAIL>', '07701234567', 'بغداد - الكرادة', '12345678901', '1985-05-15', '2020-01-01', 1, 'مدير عام', 2000000.00, 0.00, 0.00],
                ['EMP002', 'فاطمة حسن محمود', '<EMAIL>', '07709876543', 'بغداد - الجادرية', '12345678902', '1990-08-20', '2021-03-15', 2, 'مشرف إنتاج', 1200000.00, 15000.00, 20000.00],
                ['EMP003', 'محمد عبد الله سالم', '<EMAIL>', '07751234567', 'بغداد - المنصور', '12345678903', '1988-12-10', '2019-06-01', 3, 'مندوب مبيعات', 800000.00, 12000.00, 15000.00]
            ];
            
            foreach ($employees as $emp) {
                $db->query("INSERT INTO employees (employee_code, full_name, email, phone, address, national_id, birth_date, hire_date, department_id, position, salary, hourly_rate, overtime_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $emp);
            }
        }
        
        $db->commit();
        $success = 'تم إعداد نظام الموظفين بنجاح!';
        
    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في الإعداد: ' . $e->getMessage();
    }
}

// فحص حالة الجداول
$tablesStatus = [];
try {
    $tables = ['currencies', 'departments', 'employees'];
    foreach ($tables as $table) {
        $result = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
        $tablesStatus[$table] = !empty($result);
    }
} catch (Exception $e) {
    $tablesStatus = ['currencies' => false, 'departments' => false, 'employees' => false];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام الموظفين - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-setup {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
        .status-good { color: #28a745; }
        .status-bad { color: #dc3545; }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-users fa-2x mb-3"></i><br>إعداد نظام الموظفين</h1>
            <p class="mb-0">إعداد سريع لنظام إدارة الموظفين</p>
        </div>
        
        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
                
                <div class="text-center">
                    <h4 class="mb-4">🎉 نظام الموظفين جاهز!</h4>
                    <div class="d-grid gap-2">
                        <a href="employees.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-users me-2"></i>دخول إلى نظام الموظفين
                        </a>
                        <a href="dashboard.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-home me-2"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <!-- حالة الجداول -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-database me-2"></i>حالة الجداول</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 text-center">
                                <i class="fas fa-<?= $tablesStatus['currencies'] ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-2x mb-2"></i>
                                <h6>العملات</h6>
                                <small><?= $tablesStatus['currencies'] ? 'موجود' : 'مفقود' ?></small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-<?= $tablesStatus['departments'] ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-2x mb-2"></i>
                                <h6>الأقسام</h6>
                                <small><?= $tablesStatus['departments'] ? 'موجود' : 'مفقود' ?></small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-<?= $tablesStatus['employees'] ? 'check-circle status-good' : 'times-circle status-bad' ?> fa-2x mb-2"></i>
                                <h6>الموظفين</h6>
                                <small><?= $tablesStatus['employees'] ? 'موجود' : 'مفقود' ?></small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">ما سيتم إنشاؤه:</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>جدول العملات:</strong> الدينار العراقي، الدولار الأمريكي، اليورو</li>
                            <li><strong>جدول الأقسام:</strong> 8 أقسام (الإدارة، الإنتاج، المبيعات، المحاسبة، الموارد البشرية، المخازن، الصيانة، ضمان الجودة)</li>
                            <li><strong>جدول الموظفين:</strong> 3 موظفين تجريبيين</li>
                            <li><strong>البيانات الأساسية:</strong> جميع البيانات المطلوبة لتشغيل النظام</li>
                        </ul>
                    </div>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-setup btn-lg me-3">
                            <i class="fas fa-rocket me-2"></i>إعداد نظام الموظفين
                        </button>
                    </form>
                    
                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
