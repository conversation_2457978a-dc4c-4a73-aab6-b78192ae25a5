'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    'This class was auto-generated by the StronglyTypedResourceBuilder
    'class via a tool like ResGen or Visual Studio.
    'To add or remove a member, edit your .ResX file then rerun ResGen
    'with the /str option, or rebuild your VS project.
    '''<summary>
    '''  A strongly-typed resource class, for looking up localized strings, etc.
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  Returns the cached ResourceManager instance used by this class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("DebtManagementSystem.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  Overrides the current thread's CurrentUICulture property for all
        '''  resource lookups using this strongly-typed resource class.
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property AddIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("AddIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Icon.
        '''</summary>
        Friend ReadOnly Property AppIcon() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("AppIcon", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to نظام إدارة الديون.
        '''</summary>
        Friend ReadOnly Property AppName() As String
            Get
                Return ResourceManager.GetString("AppName", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to 1.0.0.
        '''</summary>
        Friend ReadOnly Property AppVersion() As String
            Get
                Return ResourceManager.GetString("AppVersion", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property CashBoxIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CashBoxIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to شركة إدارة الديون.
        '''</summary>
        Friend ReadOnly Property CompanyName() As String
            Get
                Return ResourceManager.GetString("CompanyName", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property CustomersIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("CustomersIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property DeleteIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("DeleteIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property EditIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("EditIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to خطأ في الاتصال بقاعدة البيانات.
        '''</summary>
        Friend ReadOnly Property ErrorDatabaseConnection() As String
            Get
                Return ResourceManager.GetString("ErrorDatabaseConnection", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to اسم المستخدم أو كلمة المرور غير صحيحة.
        '''</summary>
        Friend ReadOnly Property ErrorInvalidLogin() As String
            Get
                Return ResourceManager.GetString("ErrorInvalidLogin", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to هذا الحقل مطلوب.
        '''</summary>
        Friend ReadOnly Property ErrorRequiredField() As String
            Get
                Return ResourceManager.GetString("ErrorRequiredField", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property ExportIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ExportIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property GenerateIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("GenerateIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property PrintIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("PrintIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property RefreshIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("RefreshIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property ReportsIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("ReportsIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property SettingsIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SettingsIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property StatementIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("StatementIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تم الحذف بنجاح.
        '''</summary>
        Friend ReadOnly Property SuccessDelete() As String
            Get
                Return ResourceManager.GetString("SuccessDelete", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تم الحفظ بنجاح.
        '''</summary>
        Friend ReadOnly Property SuccessSave() As String
            Get
                Return ResourceManager.GetString("SuccessSave", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized string similar to تم التحديث بنجاح.
        '''</summary>
        Friend ReadOnly Property SuccessUpdate() As String
            Get
                Return ResourceManager.GetString("SuccessUpdate", resourceCulture)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property SuppliersIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("SuppliersIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property TransactionsIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("TransactionsIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  Looks up a localized resource of type System.Drawing.Bitmap.
        '''</summary>
        Friend ReadOnly Property TransferIcon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("TransferIcon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
