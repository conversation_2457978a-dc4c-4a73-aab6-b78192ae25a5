<?php
/**
 * صفحة إعداد النظام
 * System Setup Page
 */

$error = '';
$success = '';
$step = $_GET['step'] ?? 1;

// معالجة الإعداد
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $host = $_POST['host'] ?? 'localhost';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    $database = $_POST['database'] ?? 'factory_management';
    
    try {
        // الاتصال بـ MySQL
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // إنشاء قاعدة البيانات
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");
        
        // قراءة وتنفيذ ملف SQL
        $sqlFile = __DIR__ . '/../database/factory_management.sql';
        
        if (file_exists($sqlFile)) {
            $sql = file_get_contents($sqlFile);
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && substr($statement, 0, 2) !== '--') {
                    $pdo->exec($statement);
                }
            }
        }
        
        // إنشاء المستخدمين الافتراضيين
        $users = [
            ['admin', '<EMAIL>', 'admin123', 'مدير النظام', 'admin', '{"all": true}'],
            ['accountant', '<EMAIL>', 'acc123', 'المحاسب', 'accountant', '{"view_dashboard": true, "view_sales": true}'],
            ['warehouse', '<EMAIL>', 'wh123', 'أمين المخزن', 'warehouse', '{"view_dashboard": true, "view_inventory": true}']
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password_hash, full_name, role, permissions, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
            ON DUPLICATE KEY UPDATE password_hash = VALUES(password_hash)
        ");
        
        foreach ($users as $user) {
            $stmt->execute([
                $user[0], $user[1], password_hash($user[2], PASSWORD_DEFAULT),
                $user[3], $user[4], $user[5]
            ]);
        }
        
        // تحديث ملف الإعدادات
        $configContent = "<?php
class Database {
    private \$host = '$host';
    private \$db_name = '$database';
    private \$username = '$username';
    private \$password = '$password';
    private \$charset = 'utf8mb4';
    public \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$dsn = \"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name . \";charset=\" . \$this->charset;
            \$options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => \"SET NAMES utf8mb4\"
            ];
            \$this->conn = new PDO(\$dsn, \$this->username, \$this->password, \$options);
        } catch(PDOException \$exception) {
            echo \"خطأ في الاتصال: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
    
    public function query(\$sql, \$params = []) {
        try {
            \$stmt = \$this->conn->prepare(\$sql);
            \$stmt->execute(\$params);
            return \$stmt;
        } catch(PDOException \$e) {
            throw new Exception(\"خطأ في تنفيذ الاستعلام: \" . \$e->getMessage());
        }
    }
    
    public function fetchOne(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetch();
    }
    
    public function fetchAll(\$sql, \$params = []) {
        \$stmt = \$this->query(\$sql, \$params);
        return \$stmt->fetchAll();
    }
    
    public function count(\$table, \$where = '1=1', \$params = []) {
        \$sql = \"SELECT COUNT(*) as count FROM {\$table} WHERE {\$where}\";
        \$result = \$this->fetchOne(\$sql, \$params);
        return \$result['count'];
    }
}

class Helper {
    public static function formatMoney(\$amount, \$currency = 'IQD', \$decimals = 2) {
        \$formatted = number_format(\$amount, \$decimals);
        switch(\$currency) {
            case 'IQD': return \$formatted . ' د.ع';
            case 'USD': return '$' . \$formatted;
            case 'EUR': return '€' . \$formatted;
            default: return \$formatted . ' ' . \$currency;
        }
    }
    
    public static function formatDate(\$date, \$format = 'd/m/Y') {
        return date(\$format, strtotime(\$date));
    }
    
    public static function cleanInput(\$data) {
        return htmlspecialchars(trim(stripslashes(\$data)));
    }
    
    public static function hashPassword(\$password) {
        return password_hash(\$password, PASSWORD_DEFAULT);
    }
    
    public static function verifyPassword(\$password, \$hash) {
        return password_verify(\$password, \$hash);
    }
}

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>";
        
        file_put_contents(__DIR__ . '/../config/database.php', $configContent);
        
        $success = 'تم إعداد النظام بنجاح! يمكنك الآن تسجيل الدخول.';
        $step = 3;
        
    } catch (Exception $e) {
        $error = 'خطأ في الإعداد: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .setup-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 50px auto;
            padding: 40px;
        }
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            font-weight: bold;
        }
        .step.active {
            background: #667eea;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="text-center mb-4">
                <i class="fas fa-industry fa-3x text-primary mb-3"></i>
                <h2>إعداد نظام إدارة المعامل</h2>
                <p class="text-muted">مرحباً بك! دعنا نقوم بإعداد النظام خطوة بخطوة</p>
            </div>

            <!-- Step Indicator -->
            <div class="step-indicator">
                <div class="step <?= $step >= 1 ? 'active' : '' ?>">1</div>
                <div class="step <?= $step >= 2 ? 'active' : '' ?>">2</div>
                <div class="step <?= $step >= 3 ? 'completed' : '' ?>">3</div>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- Step 1: Welcome -->
                <div class="text-center">
                    <h4>مرحباً بك في نظام إدارة المعامل</h4>
                    <p>هذا النظام يوفر حلولاً متكاملة لإدارة المعامل والإنتاج</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-cogs fa-2x text-primary mb-2"></i>
                                    <h6>إدارة الإنتاج</h6>
                                    <small class="text-muted">تتبع أوامر الإنتاج والمواد الخام</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                                    <h6>التقارير المالية</h6>
                                    <small class="text-muted">أرباح يومية وشهرية وسنوية</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-boxes fa-2x text-info mb-2"></i>
                                    <h6>إدارة المخزون</h6>
                                    <small class="text-muted">تتبع المخزون والحركات</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-users fa-2x text-warning mb-2"></i>
                                    <h6>إدارة الموظفين</h6>
                                    <small class="text-muted">الرواتب والحضور والانصراف</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <a href="?step=2" class="btn btn-primary btn-lg mt-3">
                        <i class="fas fa-arrow-left me-2"></i>
                        ابدأ الإعداد
                    </a>
                </div>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Setup -->
                <form method="POST">
                    <h4 class="mb-4">إعداد قاعدة البيانات</h4>
                    
                    <div class="mb-3">
                        <label for="host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="host" name="host" value="localhost" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" value="root" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                    
                    <div class="mb-3">
                        <label for="database" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" class="form-control" id="database" name="database" value="factory_management" required>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إنشاء قاعدة البيانات تلقائياً إذا لم تكن موجودة
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-database me-2"></i>
                        إنشاء قاعدة البيانات
                    </button>
                </form>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: Complete -->
                <div class="text-center">
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h4>تم إعداد النظام بنجاح!</h4>
                    <p>يمكنك الآن تسجيل الدخول باستخدام البيانات التالية:</p>
                    
                    <div class="alert alert-success">
                        <h6>بيانات الدخول:</h6>
                        <strong>المدير:</strong> admin / admin123<br>
                        <strong>المحاسب:</strong> accountant / acc123<br>
                        <strong>المخزن:</strong> warehouse / wh123
                    </div>
                    
                    <a href="login.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
