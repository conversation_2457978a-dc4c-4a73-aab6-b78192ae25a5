Imports System.Data.Entity
Imports System.Data.Entity.ModelConfiguration.Conventions
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' سياق قاعدة البيانات - Database Context
''' </summary>
Public Class DebtContext
    Inherits DbContext
    
#Region "Constructor"
    
    ''' <summary>
    ''' منشئ السياق
    ''' </summary>
    Public Sub New()
        MyBase.New("name=DebtContext")
        
        ' تكوين قاعدة البيانات
        Database.SetInitializer(New DebtDatabaseInitializer())
        
        ' تعطيل التتبع التلقائي للتغييرات لتحسين الأداء
        Configuration.AutoDetectChangesEnabled = False
        Configuration.ValidateOnSaveEnabled = False
        
        ' تمكين التحميل البطيء
        Configuration.LazyLoadingEnabled = True
        Configuration.ProxyCreationEnabled = True
    End Sub
    
#End Region

#Region "DbSets"
    
    ''' <summary>
    ''' جدول المستخدمين
    ''' </summary>
    Public Property Users As DbSet(Of User)
    
    ''' <summary>
    ''' جدول العملاء
    ''' </summary>
    Public Property Customers As DbSet(Of Customer)
    
    ''' <summary>
    ''' جدول الموردين
    ''' </summary>
    Public Property Suppliers As DbSet(Of Supplier)
    
    ''' <summary>
    ''' جدول المعاملات المالية
    ''' </summary>
    Public Property Transactions As DbSet(Of Transaction)
    
    ''' <summary>
    ''' جدول الصناديق
    ''' </summary>
    Public Property CashBoxes As DbSet(Of CashBox)
    
    ''' <summary>
    ''' جدول العملات
    ''' </summary>
    Public Property Currencies As DbSet(Of Currency)
    
    ''' <summary>
    ''' جدول الديون
    ''' </summary>
    Public Property Debts As DbSet(Of Debt)
    
    ''' <summary>
    ''' جدول إعدادات النظام
    ''' </summary>
    Public Property SystemSettings As DbSet(Of SystemSetting)
    
#End Region

#Region "Model Configuration"
    
    ''' <summary>
    ''' تكوين النماذج
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Protected Overrides Sub OnModelCreating(modelBuilder As DbModelBuilder)
        ' إزالة اتفاقية الجمع للجداول
        modelBuilder.Conventions.Remove(Of PluralizingTableNameConvention)()
        
        ' تكوين نموذج المستخدم
        ConfigureUser(modelBuilder)
        
        ' تكوين نموذج العميل
        ConfigureCustomer(modelBuilder)
        
        ' تكوين نموذج المورد
        ConfigureSupplier(modelBuilder)
        
        ' تكوين نموذج المعاملة
        ConfigureTransaction(modelBuilder)
        
        ' تكوين نموذج الصندوق
        ConfigureCashBox(modelBuilder)
        
        ' تكوين نموذج العملة
        ConfigureCurrency(modelBuilder)
        
        ' تكوين نموذج الدين
        ConfigureDebt(modelBuilder)
        
        ' تكوين نموذج إعدادات النظام
        ConfigureSystemSetting(modelBuilder)
        
        MyBase.OnModelCreating(modelBuilder)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج المستخدم
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureUser(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of User)() _
            .HasKey(Function(u) u.Id) _
            .Property(Function(u) u.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of User)() _
            .Property(Function(u) u.Username) _
            .IsRequired() _
            .HasMaxLength(50)
        
        modelBuilder.Entity(Of User)() _
            .HasIndex(Function(u) u.Username) _
            .IsUnique()
        
        modelBuilder.Entity(Of User)() _
            .HasIndex(Function(u) u.Email) _
            .IsUnique()
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج العميل
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureCustomer(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of Customer)() _
            .HasKey(Function(c) c.Id) _
            .Property(Function(c) c.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of Customer)() _
            .Property(Function(c) c.Name) _
            .IsRequired() _
            .HasMaxLength(100)
        
        modelBuilder.Entity(Of Customer)() _
            .Property(Function(c) c.CurrentBalanceIQD) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Customer)() _
            .Property(Function(c) c.CurrentBalanceUSD) _
            .HasPrecision(18, 2)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج المورد
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureSupplier(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of Supplier)() _
            .HasKey(Function(s) s.Id) _
            .Property(Function(s) s.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of Supplier)() _
            .Property(Function(s) s.Name) _
            .IsRequired() _
            .HasMaxLength(100)
        
        modelBuilder.Entity(Of Supplier)() _
            .Property(Function(s) s.CurrentBalanceIQD) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Supplier)() _
            .Property(Function(s) s.CurrentBalanceUSD) _
            .HasPrecision(18, 2)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج المعاملة
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureTransaction(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of Transaction)() _
            .HasKey(Function(t) t.Id) _
            .Property(Function(t) t.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of Transaction)() _
            .Property(Function(t) t.Amount) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Transaction)() _
            .Property(Function(t) t.ExchangeRate) _
            .HasPrecision(10, 4)
        
        ' العلاقة مع الصندوق
        modelBuilder.Entity(Of Transaction)() _
            .HasRequired(Function(t) t.CashBox) _
            .WithMany(Function(cb) cb.Transactions) _
            .HasForeignKey(Function(t) t.CashBoxId) _
            .WillCascadeOnDelete(False)
        
        ' العلاقة مع المستخدم
        modelBuilder.Entity(Of Transaction)() _
            .HasRequired(Function(t) t.Creator) _
            .WithMany(Function(u) u.CreatedTransactions) _
            .HasForeignKey(Function(t) t.CreatedBy) _
            .WillCascadeOnDelete(False)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج الصندوق
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureCashBox(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of CashBox)() _
            .HasKey(Function(cb) cb.Id) _
            .Property(Function(cb) cb.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of CashBox)() _
            .Property(Function(cb) cb.InitialBalance) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of CashBox)() _
            .Property(Function(cb) cb.CurrentBalance) _
            .HasPrecision(18, 2)
        
        ' العلاقة مع العملة
        modelBuilder.Entity(Of CashBox)() _
            .HasRequired(Function(cb) cb.Currency) _
            .WithMany(Function(c) c.CashBoxes) _
            .HasForeignKey(Function(cb) cb.CurrencyId) _
            .WillCascadeOnDelete(False)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج العملة
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureCurrency(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of Currency)() _
            .HasKey(Function(c) c.Id) _
            .Property(Function(c) c.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of Currency)() _
            .Property(Function(c) c.Code) _
            .IsRequired() _
            .HasMaxLength(3)
        
        modelBuilder.Entity(Of Currency)() _
            .HasIndex(Function(c) c.Code) _
            .IsUnique()
        
        modelBuilder.Entity(Of Currency)() _
            .Property(Function(c) c.ExchangeRate) _
            .HasPrecision(10, 4)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج الدين
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureDebt(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of Debt)() _
            .HasKey(Function(d) d.Id) _
            .Property(Function(d) d.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of Debt)() _
            .Property(Function(d) d.OriginalAmount) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Debt)() _
            .Property(Function(d) d.RemainingAmount) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Debt)() _
            .Property(Function(d) d.PaidAmount) _
            .HasPrecision(18, 2)
        
        modelBuilder.Entity(Of Debt)() _
            .Property(Function(d) d.ExchangeRate) _
            .HasPrecision(10, 4)
    End Sub
    
    ''' <summary>
    ''' تكوين نموذج إعدادات النظام
    ''' </summary>
    ''' <param name="modelBuilder">منشئ النموذج</param>
    Private Sub ConfigureSystemSetting(modelBuilder As DbModelBuilder)
        modelBuilder.Entity(Of SystemSetting)() _
            .HasKey(Function(ss) ss.Id) _
            .Property(Function(ss) ss.Id) _
            .HasDatabaseGeneratedOption(DatabaseGeneratedOption.Identity)
        
        modelBuilder.Entity(Of SystemSetting)() _
            .Property(Function(ss) ss.SettingKey) _
            .IsRequired() _
            .HasMaxLength(100)
        
        modelBuilder.Entity(Of SystemSetting)() _
            .HasIndex(Function(ss) ss.SettingKey) _
            .IsUnique()
    End Sub
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' حفظ التغييرات مع تسجيل الأخطاء
    ''' </summary>
    ''' <returns>عدد السجلات المتأثرة</returns>
    Public Overrides Function SaveChanges() As Integer
        Try
            ' تحديث تواريخ التعديل
            UpdateTimestamps()
            
            Return MyBase.SaveChanges()
        Catch ex As Exception
            ' تسجيل الخطأ
            System.Diagnostics.Debug.WriteLine($"خطأ في حفظ البيانات: {ex.Message}")
            Throw
        End Try
    End Function
    
    ''' <summary>
    ''' تحديث تواريخ التعديل
    ''' </summary>
    Private Sub UpdateTimestamps()
        Dim entries = ChangeTracker.Entries().Where(Function(e) e.State = EntityState.Added OrElse e.State = EntityState.Modified)
        
        For Each entry In entries
            If TypeOf entry.Entity Is Customer Then
                Dim customer = DirectCast(entry.Entity, Customer)
                If entry.State = EntityState.Modified Then
                    customer.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is Supplier Then
                Dim supplier = DirectCast(entry.Entity, Supplier)
                If entry.State = EntityState.Modified Then
                    supplier.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is CashBox Then
                Dim cashBox = DirectCast(entry.Entity, CashBox)
                If entry.State = EntityState.Modified Then
                    cashBox.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is Currency Then
                Dim currency = DirectCast(entry.Entity, Currency)
                If entry.State = EntityState.Modified Then
                    currency.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is Debt Then
                Dim debt = DirectCast(entry.Entity, Debt)
                If entry.State = EntityState.Modified Then
                    debt.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is SystemSetting Then
                Dim setting = DirectCast(entry.Entity, SystemSetting)
                If entry.State = EntityState.Modified Then
                    setting.UpdatedAt = DateTime.Now
                End If
            ElseIf TypeOf entry.Entity Is User Then
                Dim user = DirectCast(entry.Entity, User)
                If entry.State = EntityState.Modified Then
                    user.UpdatedAt = DateTime.Now
                End If
            End If
        Next
    End Sub
    
#End Region

End Class
