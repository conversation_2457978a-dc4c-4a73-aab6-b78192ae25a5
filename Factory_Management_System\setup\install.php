<?php
/**
 * سكريبت إعداد وتثبيت نظام إدارة المعامل
 * Factory Management System Installation Script
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'factory_management';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة البيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ تم الاتصال بـ MySQL بنجاح\n";
    
    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ تم إنشاء قاعدة البيانات: $database\n";
    
    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE `$database`");
    echo "✅ تم الاتصال بقاعدة البيانات\n";
    
    // قراءة وتنفيذ ملف SQL
    $sqlFile = __DIR__ . '/../database/factory_management.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("ملف SQL غير موجود: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // تقسيم الاستعلامات
    $statements = explode(';', $sql);
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        if (empty($statement) || substr($statement, 0, 2) === '--') {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            $errorCount++;
            echo "⚠️ خطأ في تنفيذ الاستعلام: " . $e->getMessage() . "\n";
        }
    }
    
    echo "✅ تم تنفيذ $successCount استعلام بنجاح\n";
    
    if ($errorCount > 0) {
        echo "⚠️ فشل في تنفيذ $errorCount استعلام\n";
    }
    
    // إنشاء المستخدمين الافتراضيين
    createDefaultUsers($pdo);
    
    // إنشاء البيانات الأساسية
    createBasicData($pdo);
    
    echo "\n🎉 تم تثبيت النظام بنجاح!\n";
    echo "🔗 يمكنك الآن الوصول للنظام عبر: http://localhost/Factory_Management_System/public/\n";
    echo "🔑 بيانات الدخول:\n";
    echo "   المدير: admin / admin123\n";
    echo "   المحاسب: accountant / acc123\n";
    echo "   المخزن: warehouse / wh123\n";
    
} catch (Exception $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * إنشاء المستخدمين الافتراضيين
 */
function createDefaultUsers($pdo) {
    echo "\n📝 إنشاء المستخدمين الافتراضيين...\n";
    
    $users = [
        [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
            'full_name' => 'مدير النظام',
            'role' => 'admin',
            'permissions' => json_encode(['all' => true])
        ],
        [
            'username' => 'manager',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('manager123', PASSWORD_DEFAULT),
            'full_name' => 'المدير العام',
            'role' => 'manager',
            'permissions' => json_encode([
                'view_dashboard' => true,
                'view_all_reports' => true,
                'export_reports' => true
            ])
        ],
        [
            'username' => 'accountant',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('acc123', PASSWORD_DEFAULT),
            'full_name' => 'المحاسب',
            'role' => 'accountant',
            'permissions' => json_encode([
                'view_dashboard' => true,
                'view_sales' => true,
                'create_sales_invoice' => true,
                'view_financial' => true,
                'manage_expenses' => true
            ])
        ],
        [
            'username' => 'warehouse',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('wh123', PASSWORD_DEFAULT),
            'full_name' => 'أمين المخزن',
            'role' => 'warehouse',
            'permissions' => json_encode([
                'view_dashboard' => true,
                'view_inventory' => true,
                'manage_items' => true,
                'view_stock_movements' => true
            ])
        ],
        [
            'username' => 'production',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('prod123', PASSWORD_DEFAULT),
            'full_name' => 'مدير الإنتاج',
            'role' => 'production',
            'permissions' => json_encode([
                'view_dashboard' => true,
                'view_production' => true,
                'create_production_order' => true,
                'manage_production_recipes' => true
            ])
        ],
        [
            'username' => 'sales',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('sales123', PASSWORD_DEFAULT),
            'full_name' => 'مدير المبيعات',
            'role' => 'sales',
            'permissions' => json_encode([
                'view_dashboard' => true,
                'view_sales' => true,
                'create_sales_invoice' => true,
                'manage_customers' => true
            ])
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password_hash, full_name, role, permissions, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE 
        password_hash = VALUES(password_hash),
        full_name = VALUES(full_name),
        permissions = VALUES(permissions)
    ");
    
    foreach ($users as $user) {
        try {
            $stmt->execute([
                $user['username'],
                $user['email'],
                $user['password_hash'],
                $user['full_name'],
                $user['role'],
                $user['permissions']
            ]);
            echo "✅ تم إنشاء المستخدم: {$user['username']}\n";
        } catch (PDOException $e) {
            echo "⚠️ خطأ في إنشاء المستخدم {$user['username']}: " . $e->getMessage() . "\n";
        }
    }
}

/**
 * إنشاء البيانات الأساسية
 */
function createBasicData($pdo) {
    echo "\n📊 إنشاء البيانات الأساسية...\n";
    
    // العملات
    $currencies = [
        ['IQD', 'دينار عراقي', 'د.ع', 1.0000, 1],
        ['USD', 'دولار أمريكي', '$', 1320.0000, 0],
        ['EUR', 'يورو', '€', 1450.0000, 0]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO currencies (code, name, symbol, exchange_rate, is_base) 
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        name = VALUES(name),
        symbol = VALUES(symbol),
        exchange_rate = VALUES(exchange_rate)
    ");
    
    foreach ($currencies as $currency) {
        $stmt->execute($currency);
        echo "✅ تم إضافة العملة: {$currency[1]}\n";
    }
    
    // المخازن
    $warehouses = [
        ['المخزن الرئيسي', 'الطابق الأرضي - المبنى الرئيسي'],
        ['مخزن المواد الخام', 'الطابق الأول - المبنى الإنتاجي'],
        ['مخزن المنتجات النهائية', 'الطابق الثاني - المبنى الرئيسي']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO warehouses (name, location) 
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE location = VALUES(location)
    ");
    
    foreach ($warehouses as $warehouse) {
        $stmt->execute($warehouse);
        echo "✅ تم إضافة المخزن: {$warehouse[0]}\n";
    }
    
    // فئات الأصناف
    $categories = [
        ['مواد خام', 'المواد الأولية المستخدمة في الإنتاج'],
        ['منتجات نهائية', 'المنتجات الجاهزة للبيع'],
        ['منتجات نصف مصنعة', 'منتجات في مراحل الإنتاج'],
        ['مواد استهلاكية', 'مواد مساعدة في العملية الإنتاجية']
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO item_categories (name, description) 
        VALUES (?, ?)
        ON DUPLICATE KEY UPDATE description = VALUES(description)
    ");
    
    foreach ($categories as $category) {
        $stmt->execute($category);
        echo "✅ تم إضافة فئة الأصناف: {$category[0]}\n";
    }
    
    // الصناديق
    $cashBoxes = [
        ['الصندوق الرئيسي', 'cash', 1, 1000000.00],
        ['حساب البنك - دولار', 'bank', 2, 5000.00]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO cash_boxes (name, type, currency_id, initial_balance, current_balance) 
        VALUES (?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        type = VALUES(type),
        currency_id = VALUES(currency_id)
    ");
    
    foreach ($cashBoxes as $cashBox) {
        $stmt->execute([$cashBox[0], $cashBox[1], $cashBox[2], $cashBox[3], $cashBox[3]]);
        echo "✅ تم إضافة الصندوق: {$cashBox[0]}\n";
    }
    
    // أصناف تجريبية
    $items = [
        ['RAW001', 'مادة خام أ', 'مادة خام للإنتاج', 1, 'كيلو', 'raw_material', 50.00, 0.00, 1],
        ['RAW002', 'مادة خام ب', 'مادة خام للإنتاج', 1, 'لتر', 'raw_material', 30.00, 0.00, 1],
        ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100.00, 150.00, 1],
        ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80.00, 120.00, 1]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE 
        name = VALUES(name),
        description = VALUES(description)
    ");
    
    foreach ($items as $item) {
        $stmt->execute($item);
        echo "✅ تم إضافة الصنف: {$item[1]}\n";
    }
    
    echo "✅ تم إنشاء جميع البيانات الأساسية بنجاح\n";
}
?>
