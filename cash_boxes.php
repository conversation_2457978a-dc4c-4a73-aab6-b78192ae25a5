<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// معالجة إضافة صندوق جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add') {
    $validation_rules = [
        'name' => ['required' => true, 'message' => 'اسم الصندوق مطلوب'],
        'currency_id' => ['required' => true, 'message' => 'العملة مطلوبة'],
        'initial_balance' => ['type' => 'numeric']
    ];

    $errors = validateInput($_POST, $validation_rules);

    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO cash_boxes (name, description, currency_id, initial_balance, current_balance, created_by)
                VALUES (?, ?, ?, ?, ?, ?)
            ");

            $initial_balance = floatval($_POST['initial_balance'] ?? 0);

            $result = $stmt->execute([
                trim($_POST['name']),
                trim($_POST['description']) ?: null,
                intval($_POST['currency_id']),
                $initial_balance,
                $initial_balance,
                $_SESSION['user_id']
            ]);

            if ($result) {
                $message = 'تم إضافة الصندوق بنجاح';
                $message_type = 'success';
                logActivity('إضافة صندوق', "تم إضافة الصندوق: {$_POST['name']}");
            } else {
                $message = 'حدث خطأ أثناء إضافة الصندوق';
                $message_type = 'danger';
            }
        } catch (PDOException $e) {
            $message = 'حدث خطأ في النظام';
            $message_type = 'danger';
            error_log("خطأ إضافة الصندوق: " . $e->getMessage());
        }
    } else {
        $message = 'يرجى تصحيح الأخطاء في النموذج';
        $message_type = 'danger';
    }
}

// معالجة حذف الصندوق
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $cash_box_id = intval($_GET['delete']);

    try {
        // التحقق من وجود معاملات للصندوق
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM transactions WHERE cash_box_id = ?");
        $stmt->execute([$cash_box_id]);
        $transaction_count = $stmt->fetch()['count'];

        if ($transaction_count > 0) {
            $message = 'لا يمكن حذف الصندوق لوجود معاملات مرتبطة به';
            $message_type = 'warning';
        } else {
            // حذف الصندوق
            $stmt = $pdo->prepare("DELETE FROM cash_boxes WHERE id = ?");
            if ($stmt->execute([$cash_box_id])) {
                $message = 'تم حذف الصندوق بنجاح';
                $message_type = 'success';
                logActivity('حذف صندوق', "تم حذف الصندوق رقم: {$cash_box_id}");
            } else {
                $message = 'حدث خطأ أثناء حذف الصندوق';
                $message_type = 'danger';
            }
        }
    } catch (PDOException $e) {
        $message = 'حدث خطأ في النظام';
        $message_type = 'danger';
        error_log("خطأ حذف الصندوق: " . $e->getMessage());
    }
}

// جلب قائمة الصناديق
try {
    $stmt = $pdo->query("
        SELECT cb.*,
               c.name as currency_name, c.symbol as currency_symbol,
               u.username as created_by_name,
               (SELECT COUNT(*) FROM transactions WHERE cash_box_id = cb.id) as transaction_count
        FROM cash_boxes cb
        LEFT JOIN currencies c ON cb.currency_id = c.id
        LEFT JOIN users u ON cb.created_by = u.id
        WHERE cb.is_active = 1
        ORDER BY cb.name ASC
    ");
    $cash_boxes = $stmt->fetchAll();
} catch (PDOException $e) {
    $cash_boxes = [];
    error_log("خطأ جلب الصناديق: " . $e->getMessage());
}

// جلب قائمة العملات
try {
    $stmt = $pdo->query("SELECT id, name, symbol FROM currencies ORDER BY is_default DESC, name ASC");
    $currencies = $stmt->fetchAll();
} catch (PDOException $e) {
    $currencies = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصناديق - نظام إدارة الديون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-calculator me-2"></i>
                نظام إدارة الديون
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- الشريط الجانبي -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-2"></i>
                                إدارة العملاء
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="suppliers.php">
                                <i class="fas fa-truck me-2"></i>
                                إدارة الموردين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="transactions.php">
                                <i class="fas fa-exchange-alt me-2"></i>
                                المعاملات المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="cash_boxes.php">
                                <i class="fas fa-cash-register me-2"></i>
                                إدارة الصناديق
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="debts.php">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                إدارة الديون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- المحتوى الرئيسي -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">إدارة الصناديق</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCashBoxModal">
                            <i class="fas fa-plus me-1"></i>
                            إضافة صندوق جديد
                        </button>
                    </div>
                </div>

                <!-- رسائل التنبيه -->
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : ($message_type === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- بطاقات الصناديق -->
                <div class="row">
                    <?php foreach ($cash_boxes as $cash_box): ?>
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card border-left-<?php echo $cash_box['current_balance'] >= 0 ? 'success' : 'danger'; ?> shadow h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            <?php echo htmlspecialchars($cash_box['name']); ?>
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                                            <?php echo $cash_box['currency_symbol']; ?><?php echo number_format($cash_box['current_balance'], 2); ?>
                                        </div>
                                        <div class="text-xs text-muted mt-1">
                                            <?php echo htmlspecialchars($cash_box['currency_name']); ?>
                                        </div>
                                        <?php if ($cash_box['description']): ?>
                                        <div class="text-xs text-muted mt-1">
                                            <?php echo htmlspecialchars($cash_box['description']); ?>
                                        </div>
                                        <?php endif; ?>
                                        <div class="text-xs text-muted mt-2">
                                            <i class="fas fa-exchange-alt me-1"></i>
                                            <?php echo $cash_box['transaction_count']; ?> معاملة
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-cash-register fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="btn-group w-100" role="group">
                                        <a href="cash_box_details.php?id=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye me-1"></i>
                                            عرض
                                        </a>
                                        <a href="edit_cash_box.php?id=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit me-1"></i>
                                            تعديل
                                        </a>
                                        <?php if ($cash_box['transaction_count'] == 0): ?>
                                        <a href="?delete=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-danger btn-delete" data-name="<?php echo htmlspecialchars($cash_box['name']); ?>">
                                            <i class="fas fa-trash me-1"></i>
                                            حذف
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- جدول الصناديق -->
                <div class="card shadow mt-4">
                    <div class="card-header">
                        <div class="row align-items-center">
                            <div class="col">
                                <h6 class="m-0 font-weight-bold text-primary">تفاصيل الصناديق</h6>
                            </div>
                            <div class="col-auto">
                                <input type="text" class="form-control table-search" placeholder="البحث..." data-table="#cashBoxesTable">
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="cashBoxesTable">
                                <thead>
                                    <tr>
                                        <th class="sortable">اسم الصندوق</th>
                                        <th>الوصف</th>
                                        <th>العملة</th>
                                        <th class="sortable">الرصيد الحالي</th>
                                        <th>عدد المعاملات</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>المنشئ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($cash_boxes as $cash_box): ?>
                                    <tr>
                                        <td><strong><?php echo htmlspecialchars($cash_box['name']); ?></strong></td>
                                        <td><?php echo htmlspecialchars($cash_box['description'] ?: '-'); ?></td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo htmlspecialchars($cash_box['currency_name']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="<?php echo $cash_box['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $cash_box['currency_symbol']; ?><?php echo number_format($cash_box['current_balance'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary"><?php echo $cash_box['transaction_count']; ?></span>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($cash_box['created_at'])); ?></td>
                                        <td><?php echo htmlspecialchars($cash_box['created_by_name'] ?? 'غير محدد'); ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="cash_box_details.php?id=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="edit_cash_box.php?id=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-warning" title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <?php if ($cash_box['transaction_count'] == 0): ?>
                                                <a href="?delete=<?php echo $cash_box['id']; ?>" class="btn btn-sm btn-danger btn-delete" title="حذف" data-name="<?php echo htmlspecialchars($cash_box['name']); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- مودال إضافة صندوق -->
    <div class="modal fade" id="addCashBoxModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة صندوق جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">

                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الصندوق *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">اسم الصندوق مطلوب</div>
                        </div>

                        <div class="mb-3">
                            <label for="currency_id" class="form-label">العملة *</label>
                            <select class="form-select" id="currency_id" name="currency_id" required>
                                <option value="">اختر العملة</option>
                                <?php foreach ($currencies as $currency): ?>
                                    <option value="<?php echo $currency['id']; ?>">
                                        <?php echo htmlspecialchars($currency['name']); ?> (<?php echo htmlspecialchars($currency['symbol']); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="invalid-feedback">العملة مطلوبة</div>
                        </div>

                        <div class="mb-3">
                            <label for="initial_balance" class="form-label">الرصيد الافتتاحي</label>
                            <input type="number" class="form-control" id="initial_balance" name="initial_balance" value="0" step="0.01">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الصندوق</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
