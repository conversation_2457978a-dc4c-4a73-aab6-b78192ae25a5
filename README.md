# نظام إدارة الديون - Debt Management System

نظام احترافي لإدارة ديون الموردين والزبائن مع دعم العملات المتعددة (دينار عراقي ودولار أمريكي).

## الميزات الرئيسية

### 📊 إدارة شاملة
- **إدارة العملاء**: إضافة وتعديل وحذف العملاء مع تتبع أرصدتهم
- **إدارة الموردين**: إدارة كاملة لبيانات الموردين والتعاملات معهم
- **المعاملات المالية**: تسجيل العمليات الواردة والصادرة
- **إدارة الصناديق**: صناديق متعددة لتنظيم الأموال

### 💰 دعم العملات
- **الدينار العراقي (IQD)**: العملة الأساسية
- **الدولار الأمريكي (USD)**: عملة ثانوية مع سعر صرف قابل للتحديث
- **تحويل تلقائي**: حساب المبالغ بالعملتين

### 📈 التقارير والإحصائيات
- **لوحة تحكم تفاعلية**: عرض الإحصائيات الأساسية
- **تقارير مفصلة**: كشوف حسابات العملاء والموردين
- **تصدير البيانات**: إمكانية تصدير التقارير بصيغ مختلفة

### 🔐 الأمان والصلاحيات
- **نظام تسجيل دخول آمن**: حماية بكلمة مرور مشفرة
- **صلاحيات المستخدمين**: مستويات وصول مختلفة
- **سجل العمليات**: تتبع جميع الأنشطة في النظام

## المتطلبات التقنية

### الخادم
- **PHP**: الإصدار 7.4 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث
- **Apache/Nginx**: خادم ويب
- **PDO Extension**: لقاعدة البيانات
- **JSON Extension**: لمعالجة البيانات

### المتصفح
- متصفح حديث يدعم HTML5 و CSS3
- JavaScript مفعل
- دعم Bootstrap 5

## التثبيت والإعداد

### 1. تحميل الملفات
```bash
# نسخ الملفات إلى مجلد الخادم
cp -r debt-management-system /var/www/html/
```

### 2. إعداد قاعدة البيانات
1. تأكد من تشغيل خادم MySQL
2. قم بتعديل إعدادات قاعدة البيانات في `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'debt_management_system');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### 3. تشغيل الإعداد الأولي
1. افتح المتصفح واذهب إلى: `http://your-domain/setup.php`
2. اتبع التعليمات لإنشاء قاعدة البيانات
3. سيتم إنشاء المستخدم الافتراضي تلقائياً

### 4. تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: قم بتغيير كلمة المرور فور تسجيل الدخول الأول!

## هيكل المشروع

```
debt-management-system/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── includes/
│   └── functions.php         # الوظائف الأساسية
├── assets/
│   ├── css/
│   │   └── style.css         # ملفات التصميم
│   └── js/
│       └── main.js           # ملفات JavaScript
├── logs/                     # ملفات السجلات
├── index.php                 # الصفحة الرئيسية
├── login.php                 # صفحة تسجيل الدخول
├── customers.php             # إدارة العملاء
├── suppliers.php             # إدارة الموردين
├── transactions.php          # المعاملات المالية
├── cash_boxes.php            # إدارة الصناديق
├── debts.php                 # إدارة الديون
├── reports.php               # التقارير
├── setup.php                 # إعداد النظام
└── README.md                 # هذا الملف
```

## الاستخدام

### إضافة عميل جديد
1. اذهب إلى "إدارة العملاء"
2. انقر على "إضافة عميل جديد"
3. املأ البيانات المطلوبة
4. حدد الرصيد الافتتاحي (اختياري)
5. احفظ البيانات

### تسجيل معاملة مالية
1. اذهب إلى "المعاملات المالية"
2. انقر على "إضافة معاملة جديدة"
3. اختر نوع المعاملة (وارد/صادر)
4. حدد العميل أو المورد
5. أدخل المبلغ والعملة
6. اختر الصندوق
7. احفظ المعاملة

### عرض التقارير
1. اذهب إلى "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية والفلاتر
4. اعرض أو صدّر التقرير

## قاعدة البيانات

### الجداول الرئيسية
- **users**: بيانات المستخدمين
- **customers**: بيانات العملاء
- **suppliers**: بيانات الموردين
- **transactions**: المعاملات المالية
- **cash_boxes**: الصناديق
- **debts**: ملخص الديون
- **currencies**: العملات المدعومة
- **system_settings**: إعدادات النظام

## الأمان

### الحماية المطبقة
- **تشفير كلمات المرور**: باستخدام PHP password_hash()
- **حماية من SQL Injection**: استخدام Prepared Statements
- **التحقق من الجلسات**: فحص صحة تسجيل الدخول
- **تسجيل العمليات**: تتبع جميع الأنشطة
- **حماية الملفات**: منع الوصول المباشر للملفات الحساسة

### نصائح الأمان
1. غيّر كلمة مرور المدير الافتراضية
2. استخدم كلمات مرور قوية
3. قم بعمل نسخ احتياطية دورية
4. حدّث النظام بانتظام
5. راقب سجلات النشاط

## الدعم والصيانة

### النسخ الاحتياطي
```sql
-- نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p debt_management_system > backup.sql
```

### استعادة النسخة الاحتياطية
```sql
-- استعادة قاعدة البيانات
mysql -u username -p debt_management_system < backup.sql
```

### تحديث سعر الصرف
1. اذهب إلى "الإعدادات"
2. حدّث سعر صرف الدولار
3. احفظ التغييرات

## المساهمة في التطوير

### إضافة ميزات جديدة
1. انسخ المشروع (Fork)
2. أنشئ فرع جديد للميزة
3. اكتب الكود واختبره
4. أرسل طلب دمج (Pull Request)

### الإبلاغ عن الأخطاء
- استخدم نظام Issues في GitHub
- اذكر تفاصيل الخطأ وخطوات إعادة الإنتاج
- أرفق لقطات شاشة إن أمكن

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الاتصال

للدعم التقني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://example.com

---

**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان العاليين.**
