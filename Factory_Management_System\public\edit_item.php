<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';
require_once '../classes/Helper.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$itemId = $_GET['id'] ?? 0;
if (!$itemId) {
    header('Location: inventory.php');
    exit;
}

$success = '';
$error = '';

// جلب بيانات الصنف
$item = $db->fetchOne("SELECT * FROM items WHERE id = ?", [$itemId]);
if (!$item) {
    header('Location: inventory.php?error=' . urlencode('الصنف غير موجود'));
    exit;
}

// معالجة إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        // التحقق من عدم وجود كود مكرر (باستثناء الصنف الحالي)
        $existingItem = $db->fetchOne("SELECT id FROM items WHERE code = ? AND id != ?", [$_POST['code'], $itemId]);
        if ($existingItem) {
            throw new Exception("كود الصنف موجود مسبقاً");
        }
        
        $itemData = [
            'code' => Helper::cleanInput($_POST['code']),
            'name' => Helper::cleanInput($_POST['name']),
            'description' => Helper::cleanInput($_POST['description'] ?? ''),
            'category_id' => !empty($_POST['category_id']) ? (int)$_POST['category_id'] : null,
            'unit' => Helper::cleanInput($_POST['unit']),
            'type' => $_POST['type'],
            'cost_price' => (float)($_POST['cost_price'] ?? 0),
            'selling_price' => (float)($_POST['selling_price'] ?? 0),
            'currency_id' => (int)$_POST['currency_id'],
            'min_stock_level' => (int)($_POST['min_stock_level'] ?? 0),
            'max_stock_level' => (int)($_POST['max_stock_level'] ?? 0),
            'reorder_level' => (int)($_POST['reorder_level'] ?? 0),
            'barcode' => Helper::cleanInput($_POST['barcode'] ?? ''),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        $db->update('items', $itemData, 'id = ?', [$itemId]);
        
        $success = "تم تحديث الصنف بنجاح";
        
        // إعادة توجيه بعد النجاح
        header("Location: inventory.php?success=" . urlencode($success));
        exit;
        
    } catch (Exception $e) {
        $error = "خطأ في تحديث الصنف: " . $e->getMessage();
    }
}

// جلب الفئات
$categories = $db->fetchAll("SELECT id, name FROM item_categories WHERE is_active = 1 ORDER BY name");

// جلب العملات
$currencies = $db->fetchAll("SELECT id, name, symbol FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");

// جلب إحصائيات الصنف
$stockInfo = $db->fetchOne("
    SELECT 
        COALESCE(SUM(sb.quantity), 0) as total_stock,
        COUNT(DISTINCT sb.warehouse_id) as warehouse_count,
        (SELECT COUNT(*) FROM stock_movements sm WHERE sm.item_id = ?) as movement_count
    FROM stock_balances sb 
    WHERE sb.item_id = ?
", [$itemId, $itemId]);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الصنف: <?= htmlspecialchars($item['name']) ?> - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px 15px 0 0;
            margin: -1px -1px 0 -1px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .btn-secondary {
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .info-box {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .stat-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .type-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-edit me-3"></i>تعديل الصنف</h1>
                    <p class="mb-0"><?= htmlspecialchars($item['code'] . ' - ' . $item['name']) ?></p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="inventory.php" class="btn btn-light">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمخزون
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="itemForm">
            <div class="row">
                <div class="col-lg-8">
                    <!-- المعلومات الأساسية -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نوع الصنف <span class="text-danger">*</span></label>
                                    <select name="type" class="form-select" required>
                                        <option value="">اختر النوع</option>
                                        <option value="raw_material" <?= $item['type'] == 'raw_material' ? 'selected' : '' ?>>مادة خام</option>
                                        <option value="finished_product" <?= $item['type'] == 'finished_product' ? 'selected' : '' ?>>منتج نهائي</option>
                                        <option value="semi_finished" <?= $item['type'] == 'semi_finished' ? 'selected' : '' ?>>نصف مصنع</option>
                                        <option value="consumable" <?= $item['type'] == 'consumable' ? 'selected' : '' ?>>مواد استهلاكية</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">كود الصنف <span class="text-danger">*</span></label>
                                    <input type="text" name="code" class="form-control" required 
                                           value="<?= htmlspecialchars($item['code']) ?>">
                                    <small class="text-muted">كود فريد للصنف</small>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">اسم الصنف <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" required 
                                           value="<?= htmlspecialchars($item['name']) ?>">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">الوصف</label>
                                    <textarea name="description" class="form-control" rows="3"><?= htmlspecialchars($item['description']) ?></textarea>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الفئة</label>
                                    <select name="category_id" class="form-select">
                                        <option value="">اختر الفئة</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= $category['id'] ?>" <?= $item['category_id'] == $category['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($category['name']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">وحدة القياس <span class="text-danger">*</span></label>
                                    <select name="unit" class="form-select" required>
                                        <option value="">اختر الوحدة</option>
                                        <?php 
                                        $units = ['قطعة', 'كيلو', 'لتر', 'متر', 'علبة', 'كرتون', 'طن', 'جرام', 'مليلتر', 'سنتيمتر'];
                                        foreach ($units as $unit): 
                                        ?>
                                            <option value="<?= $unit ?>" <?= $item['unit'] == $unit ? 'selected' : '' ?>><?= $unit ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الباركود</label>
                                    <input type="text" name="barcode" class="form-control" 
                                           value="<?= htmlspecialchars($item['barcode']) ?>">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" name="is_active" id="is_active" 
                                               <?= $item['is_active'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_active">
                                            صنف نشط
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأسعار والتكلفة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>الأسعار والتكلفة</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">العملة</label>
                                    <select name="currency_id" class="form-select">
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?= $currency['id'] ?>" <?= $item['currency_id'] == $currency['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر التكلفة</label>
                                    <input type="number" name="cost_price" class="form-control" 
                                           step="0.01" min="0" value="<?= $item['cost_price'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">سعر البيع</label>
                                    <input type="number" name="selling_price" class="form-control" 
                                           step="0.01" min="0" value="<?= $item['selling_price'] ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مستويات المخزون -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-warehouse me-2"></i>مستويات المخزون</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأدنى</label>
                                    <input type="number" name="min_stock_level" class="form-control" 
                                           min="0" value="<?= $item['min_stock_level'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">الحد الأقصى</label>
                                    <input type="number" name="max_stock_level" class="form-control" 
                                           min="0" value="<?= $item['max_stock_level'] ?>">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">نقطة إعادة الطلب</label>
                                    <input type="number" name="reorder_level" class="form-control" 
                                           min="0" value="<?= $item['reorder_level'] ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <!-- إحصائيات الصنف -->
                    <div class="stat-card">
                        <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات الصنف</h5>
                        <div class="row text-center">
                            <div class="col-12 mb-3">
                                <h3><?= number_format($stockInfo['total_stock'], 3) ?></h3>
                                <p class="mb-0">إجمالي المخزون</p>
                            </div>
                            <div class="col-6">
                                <h4><?= $stockInfo['warehouse_count'] ?></h4>
                                <p class="mb-0">مخازن</p>
                            </div>
                            <div class="col-6">
                                <h4><?= $stockInfo['movement_count'] ?></h4>
                                <p class="mb-0">حركة</p>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="info-box">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الصنف</h6>
                        <p class="mb-2"><strong>تاريخ الإنشاء:</strong><br><?= date('d/m/Y H:i', strtotime($item['created_at'])) ?></p>
                        <p class="mb-2"><strong>آخر تحديث:</strong><br><?= date('d/m/Y H:i', strtotime($item['updated_at'])) ?></p>
                        <p class="mb-0"><strong>النوع:</strong><br>
                            <?php
                            $typeNames = [
                                'raw_material' => 'مادة خام',
                                'finished_product' => 'منتج نهائي',
                                'semi_finished' => 'نصف مصنع',
                                'consumable' => 'مواد استهلاكية'
                            ];
                            $typeColors = [
                                'raw_material' => 'primary',
                                'finished_product' => 'success',
                                'semi_finished' => 'warning',
                                'consumable' => 'info'
                            ];
                            ?>
                            <span class="type-badge bg-<?= $typeColors[$item['type']] ?>">
                                <?= $typeNames[$item['type']] ?>
                            </span>
                        </p>
                    </div>

                    <!-- روابط سريعة -->
                    <div class="card form-card">
                        <div class="form-header">
                            <h5 class="mb-0"><i class="fas fa-link me-2"></i>روابط سريعة</h5>
                        </div>
                        <div class="card-body p-4">
                            <div class="d-grid gap-2">
                                <a href="view_item.php?id=<?= $item['id'] ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>عرض التفاصيل
                                </a>
                                <a href="stock_movements.php?item_id=<?= $item['id'] ?>" class="btn btn-outline-info">
                                    <i class="fas fa-exchange-alt me-2"></i>حركات المخزون
                                </a>
                                <a href="add_stock.php?item_id=<?= $item['id'] ?>" class="btn btn-outline-success">
                                    <i class="fas fa-plus me-2"></i>إضافة مخزون
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mb-4">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
                <a href="inventory.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // التحقق من صحة النموذج
        document.getElementById('itemForm').addEventListener('submit', function(e) {
            const costPrice = parseFloat(document.querySelector('input[name="cost_price"]').value) || 0;
            const sellingPrice = parseFloat(document.querySelector('input[name="selling_price"]').value) || 0;
            const minLevel = parseInt(document.querySelector('input[name="min_stock_level"]').value) || 0;
            const maxLevel = parseInt(document.querySelector('input[name="max_stock_level"]').value) || 0;
            const reorderLevel = parseInt(document.querySelector('input[name="reorder_level"]').value) || 0;
            
            // التحقق من منطقية الأسعار
            if (sellingPrice > 0 && costPrice > 0 && sellingPrice < costPrice) {
                if (!confirm('سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return;
                }
            }
            
            // التحقق من منطقية مستويات المخزون
            if (maxLevel > 0 && minLevel > maxLevel) {
                alert('الحد الأدنى لا يمكن أن يكون أكبر من الحد الأقصى');
                e.preventDefault();
                return;
            }
            
            if (reorderLevel > 0 && minLevel > 0 && reorderLevel < minLevel) {
                if (!confirm('نقطة إعادة الطلب أقل من الحد الأدنى. هل تريد المتابعة؟')) {
                    e.preventDefault();
                    return;
                }
            }
        });
    </script>
</body>
</html>
