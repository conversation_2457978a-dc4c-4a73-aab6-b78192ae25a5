<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

$auth = new Auth();

echo "<h2>اختبار نظام المصادقة</h2>";

// اختبار تسجيل الدخول
echo "<h3>اختبار تسجيل الدخول:</h3>";
$result = $auth->login('admin', '123456');

if ($result['success']) {
    echo "<div style='color: green;'>✅ تم تسجيل الدخول بنجاح</div>";
    
    // اختبار getCurrentUser
    echo "<h3>اختبار getCurrentUser:</h3>";
    try {
        $user = $auth->getCurrentUser();
        echo "<div style='color: green;'>✅ تم جلب بيانات المستخدم بنجاح</div>";
        echo "<pre>";
        print_r($user);
        echo "</pre>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في جلب بيانات المستخدم: " . $e->getMessage() . "</div>";
    }
    
    // اختبار hasPermission
    echo "<h3>اختبار hasPermission:</h3>";
    try {
        $hasPermission = $auth->hasPermission('view_dashboard');
        echo "<div style='color: green;'>✅ تم اختبار الصلاحيات بنجاح: " . ($hasPermission ? 'يملك الصلاحية' : 'لا يملك الصلاحية') . "</div>";
    } catch (Exception $e) {
        echo "<div style='color: red;'>❌ خطأ في اختبار الصلاحيات: " . $e->getMessage() . "</div>";
    }
    
} else {
    echo "<div style='color: red;'>❌ فشل تسجيل الدخول: " . $result['message'] . "</div>";
}

echo "<br><a href='complete_fix.php'>العودة إلى الإصلاح الشامل</a>";
?>
