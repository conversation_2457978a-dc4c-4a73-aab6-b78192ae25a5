<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$sql = "SELECT si.*, c.name as customer_name, curr.symbol as currency_symbol,
               u.full_name as created_by_name
        FROM sales_invoices si
        LEFT JOIN customers c ON si.customer_id = c.id
        LEFT JOIN currencies curr ON si.currency_id = curr.id
        LEFT JOIN users u ON si.created_by = u.id
        WHERE 1=1";

$params = [];

if (!empty($search)) {
    $sql .= " AND (si.invoice_number LIKE :search OR c.name LIKE :search)";
    $params['search'] = "%$search%";
}

if (!empty($status)) {
    $sql .= " AND si.status = :status";
    $params['status'] = $status;
}

if (!empty($date_from)) {
    $sql .= " AND si.invoice_date >= :date_from";
    $params['date_from'] = $date_from;
}

if (!empty($date_to)) {
    $sql .= " AND si.invoice_date <= :date_to";
    $params['date_to'] = $date_to;
}

$sql .= " ORDER BY si.created_at DESC LIMIT :limit OFFSET :offset";
$params['limit'] = $limit;
$params['offset'] = $offset;

try {
    // التحقق من وجود الجداول
    if (!$db->fetchOne("SHOW TABLES LIKE 'sales_invoices'")) {
        $invoices = [];
        $totalInvoices = 0;
        $totalPages = 0;
        $totalSales = 0;
        $error = "جداول المبيعات غير موجودة. يرجى إعداد قاعدة البيانات أولاً.";
    } else {
        $invoices = $db->fetchAll($sql, $params);

        // عدد النتائج الإجمالي
        $countSql = str_replace("SELECT si.*, c.name as customer_name, curr.symbol as currency_symbol,
               u.full_name as created_by_name", "SELECT COUNT(*) as count", $sql);
        $countSql = str_replace("ORDER BY si.created_at DESC LIMIT :limit OFFSET :offset", "", $countSql);
        unset($params['limit'], $params['offset']);
        $totalInvoices = $db->fetchOne($countSql, $params)['count'] ?? 0;
        $totalPages = ceil($totalInvoices / $limit);

        // إجمالي المبيعات
        $totalSalesSql = str_replace("SELECT si.*, c.name as customer_name, curr.symbol as currency_symbol,
               u.full_name as created_by_name", "SELECT SUM(si.total_amount) as total", $sql);
        $totalSalesSql = str_replace("ORDER BY si.created_at DESC LIMIT :limit OFFSET :offset", "", $totalSalesSql);
        $totalSales = $db->fetchOne($totalSalesSql, $params)['total'] ?? 0;
    }
} catch (Exception $e) {
    $invoices = [];
    $totalInvoices = 0;
    $totalPages = 0;
    $totalSales = 0;
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المبيعات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .table th { background-color: #f8f9fa; }
        .status-draft { background-color: #6c757d; }
        .status-confirmed { background-color: #0d6efd; }
        .status-shipped { background-color: #fd7e14; }
        .status-paid { background-color: #198754; }
        .status-cancelled { background-color: #dc3545; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>إدارة المبيعات</h5>
                        <div>
                            <a href="add_sale.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>فاتورة مبيعات جديدة
                            </a>
                            <a href="customers.php" class="btn btn-info">
                                <i class="fas fa-users me-2"></i>إدارة العملاء
                            </a>
                            <a href="setup_sales.php" class="btn btn-warning">
                                <i class="fas fa-cog me-2"></i>إعداد النظام
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                                <br><a href="setup.php" class="btn btn-sm btn-primary mt-2">إعداد قاعدة البيانات</a>
                            </div>
                        <?php endif; ?>

                        <!-- فلاتر البحث -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>" placeholder="رقم الفاتورة أو اسم العميل...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft" <?= $status == 'draft' ? 'selected' : '' ?>>مسودة</option>
                                    <option value="confirmed" <?= $status == 'confirmed' ? 'selected' : '' ?>>مؤكدة</option>
                                    <option value="shipped" <?= $status == 'shipped' ? 'selected' : '' ?>>مشحونة</option>
                                    <option value="paid" <?= $status == 'paid' ? 'selected' : '' ?>>مدفوعة</option>
                                    <option value="cancelled" <?= $status == 'cancelled' ? 'selected' : '' ?>>ملغية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="sales.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> مسح
                                    </a>
                                </div>
                            </div>
                        </form>

                        <!-- إحصائيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6>إجمالي الفواتير</h6>
                                        <h4><?= number_format($totalInvoices) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6>إجمالي المبيعات</h6>
                                        <h4><?= formatMoney($totalSales) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6>متوسط الفاتورة</h6>
                                        <h4><?= $totalInvoices > 0 ? formatMoney($totalSales / $totalInvoices) : formatMoney(0) ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h6>فواتير اليوم</h6>
                                        <h4><?= count(array_filter($invoices, function($inv) { return $inv['invoice_date'] == date('Y-m-d'); })) ?></h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الفواتير -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>تاريخ الفاتورة</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>المبلغ الإجمالي</th>
                                        <th>المبلغ المدفوع</th>
                                        <th>المبلغ المتبقي</th>
                                        <th>الحالة</th>
                                        <th>أنشأ بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($invoices)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center text-muted py-4">
                                                <i class="fas fa-receipt fa-3x mb-3"></i><br>
                                                لا توجد فواتير مبيعات
                                                <?php if (!isset($error)): ?>
                                                    <br><a href="add_sale.php" class="btn btn-primary mt-2">إنشاء أول فاتورة</a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($invoices as $invoice): ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($invoice['invoice_number']) ?></strong>
                                                </td>
                                                <td><?= htmlspecialchars($invoice['customer_name'] ?? 'عميل محذوف') ?></td>
                                                <td><?= formatDate($invoice['invoice_date']) ?></td>
                                                <td>
                                                    <?= $invoice['due_date'] ? formatDate($invoice['due_date']) : '-' ?>
                                                    <?php if ($invoice['due_date'] && $invoice['due_date'] < date('Y-m-d') && $invoice['remaining_amount'] > 0): ?>
                                                        <br><small class="text-danger">متأخر</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= formatMoney($invoice['total_amount'], $invoice['currency_symbol'] ?? 'IQD') ?></td>
                                                <td><?= formatMoney($invoice['paid_amount'], $invoice['currency_symbol'] ?? 'IQD') ?></td>
                                                <td>
                                                    <?= formatMoney($invoice['remaining_amount'], $invoice['currency_symbol'] ?? 'IQD') ?>
                                                    <?php if ($invoice['remaining_amount'] > 0): ?>
                                                        <br><small class="text-warning">غير مسدد</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php
                                                    $statusLabels = [
                                                        'draft' => ['مسودة', 'status-draft'],
                                                        'confirmed' => ['مؤكدة', 'status-confirmed'],
                                                        'shipped' => ['مشحونة', 'status-shipped'],
                                                        'paid' => ['مدفوعة', 'status-paid'],
                                                        'cancelled' => ['ملغية', 'status-cancelled']
                                                    ];
                                                    $statusInfo = $statusLabels[$invoice['status']] ?? [$invoice['status'], 'bg-secondary'];
                                                    ?>
                                                    <span class="badge <?= $statusInfo[1] ?>"><?= $statusInfo[0] ?></span>
                                                </td>
                                                <td>
                                                    <small><?= htmlspecialchars($invoice['created_by_name'] ?? 'غير معروف') ?></small>
                                                    <br><small class="text-muted"><?= formatDateTime($invoice['created_at']) ?></small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="view_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-info" title="عرض">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($invoice['status'] == 'draft'): ?>
                                                            <a href="edit_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-primary" title="تعديل">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="print_sale.php?id=<?= $invoice['id'] ?>" class="btn btn-outline-success" title="طباعة" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                        <?php if ($invoice['remaining_amount'] > 0): ?>
                                                            <a href="payment.php?invoice_id=<?= $invoice['id'] ?>" class="btn btn-outline-warning" title="دفع">
                                                                <i class="fas fa-money-bill"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">السابق</a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status) ?>&date_from=<?= urlencode($date_from) ?>&date_to=<?= urlencode($date_to) ?>">التالي</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
