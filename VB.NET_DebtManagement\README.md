# 💼 نظام إدارة الديون - VB.NET

## 📋 وصف المشروع

نظام إدارة الديون المطور باستخدام **VB.NET** مع **Entity Framework 6** و **SQL Server** لإدارة ديون العملاء والموردين مع دعم العملات المتعددة (دينار عراقي ودولار أمريكي).

## 🛠️ التقنيات المستخدمة

- **VB.NET Framework 4.8**
- **Entity Framework 6.4.4**
- **SQL Server 2019/2022**
- **Windows Forms**
- **DevExpress Controls** (اختياري)
- **Crystal Reports** للتقارير

## 🎯 الميزات الرئيسية

### 📊 إدارة البيانات
- ✅ إدارة العملاء (إضافة، تعديل، حذف، بحث)
- ✅ إدارة الموردين (إضافة، تعديل، حذف، بحث)
- ✅ إدارة المعاملات المالية (وارد/صادر)
- ✅ إدارة الصناديق المتعددة
- ✅ إدارة الديون والأرصدة

### 💰 النظام المالي
- ✅ دعم عملتين (دينار عراقي ودولار أمريكي)
- ✅ سعر صرف قابل للتحديث
- ✅ حساب الأرصدة تلقائياً
- ✅ تتبع المدفوعات والمستحقات

### 📈 التقارير والإحصائيات
- ✅ تقارير العملاء والموردين
- ✅ تقارير المعاملات المالية
- ✅ تقارير الديون والأرصدة
- ✅ تقارير الصناديق
- ✅ إحصائيات شاملة

### 🖨️ الطباعة والتصدير
- ✅ طباعة الفواتير مع شعار الشركة
- ✅ تصدير التقارير (PDF, Excel, Word)
- ✅ طباعة كشوف الحسابات
- ✅ طباعة قوائم العملاء والموردين

### 🔐 الأمان والصلاحيات
- ✅ نظام تسجيل دخول آمن
- ✅ صلاحيات المستخدمين
- ✅ تشفير كلمات المرور
- ✅ سجل العمليات والأنشطة

### ⚙️ الإعدادات
- ✅ إعدادات الشركة (الاسم، الشعار، البيانات)
- ✅ إعدادات العملات وأسعار الصرف
- ✅ إعدادات النسخ الاحتياطي
- ✅ إعدادات التقارير والطباعة

## 📁 هيكل المشروع

```
DebtManagementSystem/
├── 📁 Models/                  # نماذج البيانات (Entity Framework)
│   ├── Customer.vb
│   ├── Supplier.vb
│   ├── Transaction.vb
│   ├── CashBox.vb
│   ├── Debt.vb
│   ├── User.vb
│   ├── Currency.vb
│   └── SystemSetting.vb
├── 📁 Data/                    # طبقة البيانات
│   ├── DebtContext.vb
│   ├── Repositories/
│   └── Migrations/
├── 📁 Forms/                   # النماذج (Windows Forms)
│   ├── MainForm.vb
│   ├── LoginForm.vb
│   ├── CustomersForm.vb
│   ├── SuppliersForm.vb
│   ├── TransactionsForm.vb
│   ├── CashBoxesForm.vb
│   ├── DebtsForm.vb
│   ├── ReportsForm.vb
│   └── SettingsForm.vb
├── 📁 Services/                # الخدمات
│   ├── CustomerService.vb
│   ├── SupplierService.vb
│   ├── TransactionService.vb
│   └── ReportService.vb
├── 📁 Utilities/               # الأدوات المساعدة
│   ├── DatabaseHelper.vb
│   ├── SecurityHelper.vb
│   ├── PrintHelper.vb
│   └── ExportHelper.vb
├── 📁 Reports/                 # التقارير
│   ├── CustomerReport.rpt
│   ├── SupplierReport.rpt
│   ├── TransactionReport.rpt
│   └── InvoiceReport.rpt
└── 📁 Resources/               # الموارد
    ├── Images/
    ├── Icons/
    └── Templates/
```

## 🚀 متطلبات التشغيل

### البرمجيات المطلوبة
- **Visual Studio 2019/2022**
- **.NET Framework 4.8**
- **SQL Server 2019/2022** (أو SQL Server Express)
- **SQL Server Management Studio** (اختياري)

### المكتبات المطلوبة
```xml
<packages>
  <package id="EntityFramework" version="6.4.4" />
  <package id="System.Data.SqlClient" version="4.8.5" />
  <package id="DevExpress.Win.Grid" version="22.2.3" />
  <package id="CrystalReports.Engine" version="13.0.4000" />
  <package id="ClosedXML" version="0.95.4" />
  <package id="iTextSharp" version="5.5.13.3" />
</packages>
```

## 📦 التثبيت والإعداد

### 1. إنشاء قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE DebtManagementSystem;
```

### 2. تكوين Connection String
```xml
<connectionStrings>
  <add name="DebtContext" 
       connectionString="Data Source=.;Initial Catalog=DebtManagementSystem;Integrated Security=True" 
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### 3. تشغيل Migrations
```
Update-Database
```

## 🎨 واجهة المستخدم

### التصميم
- **واجهة عربية** من اليمين إلى اليسار
- **تصميم عصري** مع ألوان احترافية
- **أيقونات واضحة** لسهولة الاستخدام
- **قوائم منسدلة** منظمة
- **شريط أدوات** سريع

### الألوان المستخدمة
- **الأساسي**: #2563eb (أزرق)
- **الثانوي**: #64748b (رمادي)
- **النجاح**: #10b981 (أخضر)
- **الخطر**: #ef4444 (أحمر)
- **التحذير**: #f59e0b (برتقالي)

## 📊 قاعدة البيانات

### الجداول الرئيسية

#### جدول العملاء (Customers)
```sql
CREATE TABLE Customers (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(200),
    CurrentBalanceIQD DECIMAL(18,2) DEFAULT 0,
    CurrentBalanceUSD DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2
);
```

#### جدول الموردين (Suppliers)
```sql
CREATE TABLE Suppliers (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Name NVARCHAR(100) NOT NULL,
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(200),
    CurrentBalanceIQD DECIMAL(18,2) DEFAULT 0,
    CurrentBalanceUSD DECIMAL(18,2) DEFAULT 0,
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2
);
```

#### جدول المعاملات (Transactions)
```sql
CREATE TABLE Transactions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    Type NVARCHAR(10) NOT NULL, -- 'Income' or 'Expense'
    EntityType NVARCHAR(10) NOT NULL, -- 'Customer' or 'Supplier'
    EntityId INT NOT NULL,
    CashBoxId INT NOT NULL,
    Amount DECIMAL(18,2) NOT NULL,
    Currency NVARCHAR(3) NOT NULL, -- 'IQD' or 'USD'
    ExchangeRate DECIMAL(10,4) DEFAULT 1,
    Description NVARCHAR(500),
    ReferenceNumber NVARCHAR(50),
    TransactionDate DATE NOT NULL,
    CreatedBy INT NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE()
);
```

## 🔧 الاستخدام

### تسجيل الدخول
- **المستخدم الافتراضي**: admin
- **كلمة المرور الافتراضية**: admin123

### إضافة عميل جديد
1. اذهب إلى **العملاء** → **إضافة عميل**
2. املأ البيانات المطلوبة
3. اضغط **حفظ**

### تسجيل معاملة مالية
1. اذهب إلى **المعاملات** → **إضافة معاملة**
2. اختر نوع المعاملة (وارد/صادر)
3. حدد العميل أو المورد
4. أدخل المبلغ والعملة
5. اضغط **حفظ**

### طباعة فاتورة
1. اذهب إلى **المعاملات**
2. اختر المعاملة المطلوبة
3. اضغط **طباعة فاتورة**

## 📈 التقارير المتاحة

1. **تقرير العملاء**: قائمة العملاء مع الأرصدة
2. **تقرير الموردين**: قائمة الموردين مع الأرصدة
3. **تقرير المعاملات**: جميع المعاملات خلال فترة محددة
4. **تقرير الديون**: الديون المستحقة
5. **تقرير الصناديق**: أرصدة الصناديق
6. **كشف حساب عميل**: تفاصيل معاملات عميل محدد
7. **كشف حساب مورد**: تفاصيل معاملات مورد محدد

## 🔐 الأمان

- **تشفير كلمات المرور** باستخدام BCrypt
- **التحقق من صحة البيانات** قبل الحفظ
- **حماية من SQL Injection** باستخدام Entity Framework
- **سجل العمليات** لتتبع جميع الأنشطة
- **نسخ احتياطية** تلقائية

## 🎯 الميزات المتقدمة

### 1. البحث المتقدم
- بحث سريع في جميع الجداول
- فلترة متقدمة حسب التاريخ والمبلغ
- حفظ معايير البحث

### 2. التصدير والاستيراد
- تصدير البيانات إلى Excel
- استيراد البيانات من Excel
- تصدير التقارير إلى PDF

### 3. النسخ الاحتياطي
- نسخ احتياطي تلقائي يومي
- نسخ احتياطي يدوي
- استعادة النسخ الاحتياطية

### 4. الإشعارات
- تنبيهات الديون المستحقة
- تنبيهات انتهاء صلاحية العقود
- تنبيهات النسخ الاحتياطي

## 📞 الدعم والصيانة

للحصول على الدعم التقني أو طلب تطوير ميزات إضافية:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964-XXX-XXXX

---

**تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان العاليين مع واجهة مستخدم عربية احترافية.**
