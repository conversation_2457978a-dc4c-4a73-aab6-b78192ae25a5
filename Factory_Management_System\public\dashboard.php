<?php
session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../classes/Auth.php';
require_once '../classes/Financial.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$financial = new Financial();

// الحصول على البيانات للوحة التحكم
$today = date('Y-m-d');
$thisMonth = date('Y-m');
$thisYear = date('Y');

// مؤشرات الأداء اليومية
$dailyKPIs = $financial->getKPIs($today, $today);
$monthlyKPIs = $financial->getKPIs($thisMonth . '-01', date('Y-m-t'));
$yearlyKPIs = $financial->getKPIs($thisYear . '-01-01', $thisYear . '-12-31');

// إحصائيات سريعة
$db = new Database();
$conn = $db->getConnection();

// التحقق من وجود الجداول قبل الاستعلام
function tableExists($db, $tableName) {
    try {
        $result = $db->fetchOne("SHOW TABLES LIKE ?", [$tableName]);
        return !empty($result);
    } catch (Exception $e) {
        return false;
    }
}

$stats = [
    'total_customers' => tableExists($db, 'customers') ? $db->count('customers', 'is_active = 1') : 0,
    'total_suppliers' => tableExists($db, 'suppliers') ? $db->count('suppliers', 'is_active = 1') : 0,
    'total_items' => tableExists($db, 'items') ? $db->count('items', 'is_active = 1') : 0,
    'total_employees' => tableExists($db, 'employees') ? $db->count('employees', 'is_active = 1') : 0,
    'pending_orders' => tableExists($db, 'production_orders') ? $db->count('production_orders', 'status = "planned"') : 0,
    'low_stock_items' => 0
];

// أحدث المعاملات
$recentTransactions = [];
if (tableExists($db, 'sales_invoices') && tableExists($db, 'customers') && tableExists($db, 'currencies')) {
    try {
        $recentTransactions = $db->fetchAll("
            SELECT 'sales' as type, si.invoice_number as number, c.name as party,
                   si.total_amount as amount, curr.symbol, si.invoice_date as date
            FROM sales_invoices si
            JOIN customers c ON si.customer_id = c.id
            JOIN currencies curr ON si.currency_id = curr.id
            WHERE si.status != 'cancelled'
            ORDER BY si.invoice_date DESC
            LIMIT 5
        ");
    } catch (Exception $e) {
        $recentTransactions = [];
    }
}

// أرصدة الصناديق
$cashBoxes = [];
if (tableExists($db, 'cash_boxes') && tableExists($db, 'currencies')) {
    try {
        $cashBoxes = $db->fetchAll("
            SELECT cb.name, cb.current_balance, c.symbol, c.code
            FROM cash_boxes cb
            JOIN currencies c ON cb.currency_id = c.id
            WHERE cb.is_active = 1
            ORDER BY cb.name
        ");
    } catch (Exception $e) {
        $cashBoxes = [];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - نظام إدارة المعامل</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .stat-card.sales {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .stat-card.purchases {
            background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
        }

        .stat-card.profit {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stat-card.expenses {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .stat-icon {
            font-size: 3rem;
            opacity: 0.8;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
        }

        .sidebar {
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            border-radius: 10px;
            margin: 5px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s ease;
            }

            .sidebar.show {
                right: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                <span class="navbar-toggler-icon"></span>
            </button>

            <a class="navbar-brand" href="#">
                <i class="fas fa-industry me-2"></i>
                نظام إدارة المعامل
            </a>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?= htmlspecialchars($user['full_name']) ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user-edit me-2"></i>الملف الشخصي</a></li>
                        <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-2 p-0">
                <div class="sidebar" id="sidebar">
                    <div class="p-3">
                        <h5 class="text-center mb-4">القائمة الرئيسية</h5>
                        <nav class="nav flex-column">
                            <a class="nav-link active" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                            <?php if ($user['role'] === 'admin'): ?>
                            <a class="nav-link" href="control_center.php">
                                <i class="fas fa-cogs me-2"></i>مركز التحكم
                            </a>
                            <?php endif; ?>
                            <a class="nav-link" href="inventory.php">
                                <i class="fas fa-boxes me-2"></i>إدارة المخزون
                            </a>
                            <a class="nav-link" href="sales.php">
                                <i class="fas fa-shopping-cart me-2"></i>المبيعات
                            </a>
                            <a class="nav-link" href="purchases.php">
                                <i class="fas fa-truck me-2"></i>المشتريات
                            </a>
                            <a class="nav-link" href="production.php">
                                <i class="fas fa-cogs me-2"></i>الإنتاج
                            </a>
                            <a class="nav-link" href="financial.php">
                                <i class="fas fa-chart-line me-2"></i>المالية
                            </a>
                            <a class="nav-link" href="employees.php">
                                <i class="fas fa-users me-2"></i>الموظفين
                            </a>
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-file-alt me-2"></i>التقارير
                            </a>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-10">
                <div class="main-content">
                    <!-- Welcome Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="alert alert-primary" role="alert">
                                <h4 class="alert-heading">
                                    <i class="fas fa-sun me-2"></i>
                                    مرحباً <?= htmlspecialchars($user['full_name']) ?>!
                                </h4>
                                <p class="mb-0">اليوم هو <?= formatDate($today) ?> - نتمنى لك يوماً مثمراً</p>
                            </div>
                        </div>
                    </div>

                    <!-- KPI Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card stat-card sales">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">مبيعات اليوم</h6>
                                            <h3 class="mb-0"><?= formatMoney($dailyKPIs['total_sales']) ?></h3>
                                        </div>
                                        <div class="stat-icon">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card stat-card purchases">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">مشتريات الشهر</h6>
                                            <h3 class="mb-0"><?= formatMoney($monthlyKPIs['total_purchases']) ?></h3>
                                        </div>
                                        <div class="stat-icon">
                                            <i class="fas fa-truck"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card stat-card profit">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">صافي الربح الشهري</h6>
                                            <h3 class="mb-0"><?= formatMoney($monthlyKPIs['net_profit']) ?></h3>
                                        </div>
                                        <div class="stat-icon">
                                            <i class="fas fa-coins"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card stat-card expenses">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">مصروفات الشهر</h6>
                                            <h3 class="mb-0"><?= formatMoney($monthlyKPIs['total_expenses']) ?></h3>
                                        </div>
                                        <div class="stat-icon">
                                            <i class="fas fa-credit-card"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="row mb-4">
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        إحصائيات سريعة
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                            <h4><?= $stats['total_customers'] ?></h4>
                                            <p class="text-muted">العملاء</p>
                                        </div>
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-truck fa-2x text-success mb-2"></i>
                                            <h4><?= $stats['total_suppliers'] ?></h4>
                                            <p class="text-muted">الموردين</p>
                                        </div>
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-box fa-2x text-info mb-2"></i>
                                            <h4><?= $stats['total_items'] ?></h4>
                                            <p class="text-muted">الأصناف</p>
                                        </div>
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                                            <h4><?= $stats['total_employees'] ?></h4>
                                            <p class="text-muted">الموظفين</p>
                                        </div>
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-cogs fa-2x text-secondary mb-2"></i>
                                            <h4><?= $stats['pending_orders'] ?></h4>
                                            <p class="text-muted">أوامر الإنتاج المعلقة</p>
                                        </div>
                                        <div class="col-md-4 text-center mb-3">
                                            <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                            <h4><?= $stats['low_stock_items'] ?></h4>
                                            <p class="text-muted">أصناف تحت الحد الأدنى</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-wallet me-2"></i>
                                        أرصدة الصناديق
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($cashBoxes as $cashBox): ?>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <h6 class="mb-0"><?= htmlspecialchars($cashBox['name']) ?></h6>
                                        </div>
                                        <div>
                                            <span class="badge bg-primary">
                                                <?= formatMoney($cashBox['current_balance'], $cashBox['code']) ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Transactions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">
                                        <i class="fas fa-history me-2"></i>
                                        أحدث المعاملات
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>النوع</th>
                                                    <th>رقم الفاتورة</th>
                                                    <th>الطرف</th>
                                                    <th>المبلغ</th>
                                                    <th>التاريخ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recentTransactions as $transaction): ?>
                                                <tr>
                                                    <td>
                                                        <?php if ($transaction['type'] == 'sales'): ?>
                                                            <span class="badge bg-success">مبيعات</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-warning">مشتريات</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($transaction['number']) ?></td>
                                                    <td><?= htmlspecialchars($transaction['party']) ?></td>
                                                    <td><?= number_format($transaction['amount'], 2) ?> <?= $transaction['symbol'] ?></td>
                                                    <td><?= formatDate($transaction['date']) ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // تحديث الوقت كل ثانية
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA');
            document.title = `لوحة التحكم - ${timeString}`;
        }

        setInterval(updateTime, 1000);

        // تفعيل الشريط الجانبي في الشاشات الصغيرة
        document.querySelector('.navbar-toggler').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('show');
        });
    </script>
</body>
</html>
