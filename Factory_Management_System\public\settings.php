<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        // التحقق من وجود جدول الإعدادات
        if (!$db->fetchOne("SHOW TABLES LIKE 'settings'")) {
            $db->query("CREATE TABLE settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) UNIQUE,
                setting_value TEXT,
                setting_group VARCHAR(50) DEFAULT 'general',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");
        }

        switch ($action) {
            case 'company_info':
                // حفظ معلومات الشركة
                $companyData = [
                    'company_name' => cleanInput($_POST['company_name']),
                    'company_name_en' => cleanInput($_POST['company_name_en']),
                    'company_address' => cleanInput($_POST['company_address']),
                    'company_phone' => cleanInput($_POST['company_phone']),
                    'company_fax' => cleanInput($_POST['company_fax']),
                    'company_email' => cleanInput($_POST['company_email']),
                    'company_website' => cleanInput($_POST['company_website']),
                    'tax_number' => cleanInput($_POST['tax_number']),
                    'commercial_register' => cleanInput($_POST['commercial_register']),
                    'establishment_date' => cleanInput($_POST['establishment_date']),
                    'capital' => cleanInput($_POST['capital']),
                    'activity_type' => cleanInput($_POST['activity_type']),
                    'company_logo' => cleanInput($_POST['company_logo'])
                ];

                foreach ($companyData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES (?, ?, 'company')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ معلومات الشركة بنجاح';
                break;

            case 'system_settings':
                // حفظ إعدادات النظام
                $systemData = [
                    'default_currency' => cleanInput($_POST['default_currency']),
                    'decimal_places' => intval($_POST['decimal_places']),
                    'date_format' => cleanInput($_POST['date_format']),
                    'time_format' => cleanInput($_POST['time_format']),
                    'timezone' => cleanInput($_POST['timezone']),
                    'language' => cleanInput($_POST['language']),
                    'items_per_page' => intval($_POST['items_per_page']),
                    'session_timeout' => intval($_POST['session_timeout']),
                    'auto_backup' => isset($_POST['auto_backup']) ? 1 : 0,
                    'backup_frequency' => cleanInput($_POST['backup_frequency']),
                    'maintenance_mode' => isset($_POST['maintenance_mode']) ? 1 : 0,
                    'debug_mode' => isset($_POST['debug_mode']) ? 1 : 0,
                    'cache_enabled' => isset($_POST['cache_enabled']) ? 1 : 0
                ];

                foreach ($systemData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES (?, ?, 'system')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات النظام بنجاح';
                break;

            case 'email_settings':
                // حفظ إعدادات البريد الإلكتروني
                $emailData = [
                    'smtp_enabled' => isset($_POST['smtp_enabled']) ? 1 : 0,
                    'smtp_host' => cleanInput($_POST['smtp_host']),
                    'smtp_port' => intval($_POST['smtp_port']),
                    'smtp_username' => cleanInput($_POST['smtp_username']),
                    'smtp_password' => cleanInput($_POST['smtp_password']),
                    'smtp_encryption' => cleanInput($_POST['smtp_encryption']),
                    'from_email' => cleanInput($_POST['from_email']),
                    'from_name' => cleanInput($_POST['from_name']),
                    'reply_to_email' => cleanInput($_POST['reply_to_email']),
                    'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
                    'email_signature' => cleanInput($_POST['email_signature'])
                ];

                foreach ($emailData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES (?, ?, 'email')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات البريد الإلكتروني بنجاح';
                break;

            case 'security_settings':
                // حفظ إعدادات الأمان
                $securityData = [
                    'password_min_length' => intval($_POST['password_min_length']),
                    'password_require_uppercase' => isset($_POST['password_require_uppercase']) ? 1 : 0,
                    'password_require_lowercase' => isset($_POST['password_require_lowercase']) ? 1 : 0,
                    'password_require_numbers' => isset($_POST['password_require_numbers']) ? 1 : 0,
                    'password_require_symbols' => isset($_POST['password_require_symbols']) ? 1 : 0,
                    'max_login_attempts' => intval($_POST['max_login_attempts']),
                    'lockout_duration' => intval($_POST['lockout_duration']),
                    'two_factor_auth' => isset($_POST['two_factor_auth']) ? 1 : 0,
                    'force_password_change' => intval($_POST['force_password_change']),
                    'login_logging' => isset($_POST['login_logging']) ? 1 : 0,
                    'ip_whitelist' => cleanInput($_POST['ip_whitelist']),
                    'session_encryption' => isset($_POST['session_encryption']) ? 1 : 0
                ];

                foreach ($securityData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES (?, ?, 'security')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات الأمان بنجاح';
                break;

            case 'notification_settings':
                // حفظ إعدادات التنبيهات
                $notificationData = [
                    'low_stock_alert' => isset($_POST['low_stock_alert']) ? 1 : 0,
                    'low_stock_threshold' => intval($_POST['low_stock_threshold']),
                    'overdue_payment_alert' => isset($_POST['overdue_payment_alert']) ? 1 : 0,
                    'overdue_payment_days' => intval($_POST['overdue_payment_days']),
                    'production_delay_alert' => isset($_POST['production_delay_alert']) ? 1 : 0,
                    'birthday_reminder' => isset($_POST['birthday_reminder']) ? 1 : 0,
                    'contract_expiry_alert' => isset($_POST['contract_expiry_alert']) ? 1 : 0,
                    'contract_expiry_days' => intval($_POST['contract_expiry_days']),
                    'system_maintenance_alert' => isset($_POST['system_maintenance_alert']) ? 1 : 0,
                    'daily_report_email' => isset($_POST['daily_report_email']) ? 1 : 0,
                    'weekly_report_email' => isset($_POST['weekly_report_email']) ? 1 : 0,
                    'monthly_report_email' => isset($_POST['monthly_report_email']) ? 1 : 0
                ];

                foreach ($notificationData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES (?, ?, 'notifications')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات التنبيهات بنجاح';
                break;

            case 'backup_now':
                // إنشاء نسخة احتياطية فورية
                $backupDir = '../backups';
                if (!is_dir($backupDir)) {
                    mkdir($backupDir, 0755, true);
                }

                $backupFile = $backupDir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
                $command = "mysqldump --user=root --password= --host=localhost factory_management > $backupFile";
                exec($command, $output, $return_var);

                if ($return_var === 0) {
                    // حفظ معلومات النسخة الاحتياطية
                    $db->query("INSERT INTO settings (setting_key, setting_value, setting_group) VALUES ('last_backup', ?, 'backup')
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [date('Y-m-d H:i:s')]);
                    $success = 'تم إنشاء النسخة الاحتياطية بنجاح في: ' . basename($backupFile);
                } else {
                    $error = 'فشل في إنشاء النسخة الاحتياطية';
                }
                break;

            case 'test_email':
                // اختبار إعدادات البريد الإلكتروني
                $testEmail = cleanInput($_POST['test_email']);
                if (filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                    // هنا يمكن إضافة كود إرسال بريد تجريبي
                    $success = 'تم إرسال بريد تجريبي إلى ' . $testEmail;
                } else {
                    $error = 'عنوان البريد الإلكتروني غير صحيح';
                }
                break;

            case 'clear_cache':
                // مسح الذاكرة المؤقتة
                $cacheDir = '../cache';
                if (is_dir($cacheDir)) {
                    $files = glob($cacheDir . '/*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                        }
                    }
                    $success = 'تم مسح الذاكرة المؤقتة بنجاح';
                } else {
                    $error = 'مجلد الذاكرة المؤقتة غير موجود';
                }
                break;
        }
    } catch (Exception $e) {
        $error = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    $settings = [];
    if ($db->fetchOne("SHOW TABLES LIKE 'settings'")) {
        $settingsData = $db->fetchAll("SELECT setting_key, setting_value FROM settings");
        foreach ($settingsData as $setting) {
            $settings[$setting['setting_key']] = $setting['setting_value'];
        }
    }
} catch (Exception $e) {
    $settings = [];
}

// العملات المتاحة
try {
    if ($db->fetchOne("SHOW TABLES LIKE 'currencies'")) {
        $currencies = $db->fetchAll("SELECT * FROM currencies WHERE is_active = 1 ORDER BY name");
    } else {
        $currencies = [
            ['id' => 1, 'name' => 'الدينار العراقي', 'symbol' => 'IQD'],
            ['id' => 2, 'name' => 'الدولار الأمريكي', 'symbol' => 'USD']
        ];
    }
} catch (Exception $e) {
    $currencies = [
        ['id' => 1, 'name' => 'الدينار العراقي', 'symbol' => 'IQD'],
        ['id' => 2, 'name' => 'الدولار الأمريكي', 'symbol' => 'USD']
    ];
}

// إحصائيات النظام
$systemStats = [
    'total_users' => 0,
    'total_items' => 0,
    'total_customers' => 0,
    'total_suppliers' => 0,
    'database_size' => '0 MB',
    'last_backup' => $settings['last_backup'] ?? 'لم يتم إنشاء نسخة احتياطية',
    'system_uptime' => '0 أيام',
    'php_version' => PHP_VERSION,
    'mysql_version' => 'غير معروف'
];

try {
    // إحصائيات قاعدة البيانات
    $tables = ['users', 'items', 'customers', 'suppliers'];
    foreach ($tables as $table) {
        if ($db->fetchOne("SHOW TABLES LIKE '$table'")) {
            $count = $db->fetchOne("SELECT COUNT(*) as count FROM $table");
            $systemStats['total_' . $table] = $count['count'] ?? 0;
        }
    }

    // حجم قاعدة البيانات
    $dbSize = $db->fetchOne("
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS size_mb
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
    ");
    $systemStats['database_size'] = ($dbSize['size_mb'] ?? 0) . ' MB';

    // إصدار MySQL
    $mysqlVersion = $db->fetchOne("SELECT VERSION() as version");
    $systemStats['mysql_version'] = $mysqlVersion['version'] ?? 'غير معروف';

} catch (Exception $e) {
    // تجاهل الأخطاء
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .navbar-brand { font-weight: bold; }
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .nav-pills .nav-link {
            border-radius: 15px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        .nav-pills .nav-link:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(-5px);
        }
        .settings-icon { font-size: 1.2rem; margin-left: 8px; }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .alert {
            border-radius: 15px;
            border: none;
        }
        .tab-content {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .section-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #667eea;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- إحصائيات النظام -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>إجمالي المستخدمين</h6>
                            <h3><?= number_format($systemStats['total_users']) ?></h3>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>حجم قاعدة البيانات</h6>
                            <h3><?= $systemStats['database_size'] ?></h3>
                        </div>
                        <i class="fas fa-database fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>إصدار PHP</h6>
                            <h3><?= $systemStats['php_version'] ?></h3>
                        </div>
                        <i class="fab fa-php fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6>آخر نسخة احتياطية</h6>
                            <small><?= $systemStats['last_backup'] ?></small>
                        </div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>إعدادات النظام</h5>
                        <div>
                            <button class="btn btn-outline-success" onclick="clearCache()">
                                <i class="fas fa-broom me-2"></i>مسح الذاكرة المؤقتة
                            </button>
                            <button class="btn btn-outline-warning" onclick="createBackup()">
                                <i class="fas fa-download me-2"></i>نسخة احتياطية
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success alert-dismissible fade show">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= htmlspecialchars($success) ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <!-- القائمة الجانبية -->
                            <div class="col-md-3">
                                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                                    <button class="nav-link active text-end" id="v-pills-company-tab" data-bs-toggle="pill" data-bs-target="#v-pills-company" type="button">
                                        <i class="fas fa-building settings-icon"></i>معلومات الشركة
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-system-tab" data-bs-toggle="pill" data-bs-target="#v-pills-system" type="button">
                                        <i class="fas fa-cogs settings-icon"></i>إعدادات النظام
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-email-tab" data-bs-toggle="pill" data-bs-target="#v-pills-email" type="button">
                                        <i class="fas fa-envelope settings-icon"></i>إعدادات البريد
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-security-tab" data-bs-toggle="pill" data-bs-target="#v-pills-security" type="button">
                                        <i class="fas fa-shield-alt settings-icon"></i>الأمان
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-notifications-tab" data-bs-toggle="pill" data-bs-target="#v-pills-notifications" type="button">
                                        <i class="fas fa-bell settings-icon"></i>التنبيهات
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-backup-tab" data-bs-toggle="pill" data-bs-target="#v-pills-backup" type="button">
                                        <i class="fas fa-database settings-icon"></i>النسخ الاحتياطي
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-advanced-tab" data-bs-toggle="pill" data-bs-target="#v-pills-advanced" type="button">
                                        <i class="fas fa-tools settings-icon"></i>إعدادات متقدمة
                                    </button>
                                </div>
                            </div>

                            <!-- المحتوى -->
                            <div class="col-md-9">
                                <div class="tab-content" id="v-pills-tabContent">
                                    <!-- معلومات الشركة -->
                                    <div class="tab-pane fade show active" id="v-pills-company" role="tabpanel">
                                        <h6 class="section-title"><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                                        <form method="POST" id="companyForm">
                                            <input type="hidden" name="action" value="company_info">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم الشركة (عربي)</label>
                                                    <input type="text" class="form-control" name="company_name" value="<?= htmlspecialchars($settings['company_name'] ?? '') ?>" required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم الشركة (إنجليزي)</label>
                                                    <input type="text" class="form-control" name="company_name_en" value="<?= htmlspecialchars($settings['company_name_en'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رقم الهاتف</label>
                                                    <input type="text" class="form-control" name="company_phone" value="<?= htmlspecialchars($settings['company_phone'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رقم الفاكس</label>
                                                    <input type="text" class="form-control" name="company_fax" value="<?= htmlspecialchars($settings['company_fax'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">البريد الإلكتروني</label>
                                                    <input type="email" class="form-control" name="company_email" value="<?= htmlspecialchars($settings['company_email'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الموقع الإلكتروني</label>
                                                    <input type="url" class="form-control" name="company_website" value="<?= htmlspecialchars($settings['company_website'] ?? '') ?>">
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">العنوان</label>
                                                    <textarea class="form-control" name="company_address" rows="3"><?= htmlspecialchars($settings['company_address'] ?? '') ?></textarea>
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label class="form-label">الرقم الضريبي</label>
                                                    <input type="text" class="form-control" name="tax_number" value="<?= htmlspecialchars($settings['tax_number'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label class="form-label">رقم السجل التجاري</label>
                                                    <input type="text" class="form-control" name="commercial_register" value="<?= htmlspecialchars($settings['commercial_register'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-4 mb-3">
                                                    <label class="form-label">تاريخ التأسيس</label>
                                                    <input type="date" class="form-control" name="establishment_date" value="<?= htmlspecialchars($settings['establishment_date'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رأس المال</label>
                                                    <input type="number" class="form-control" name="capital" value="<?= htmlspecialchars($settings['capital'] ?? '') ?>" step="0.01">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">نوع النشاط</label>
                                                    <select class="form-select" name="activity_type">
                                                        <option value="">اختر نوع النشاط</option>
                                                        <option value="manufacturing" <?= ($settings['activity_type'] ?? '') == 'manufacturing' ? 'selected' : '' ?>>تصنيع</option>
                                                        <option value="trading" <?= ($settings['activity_type'] ?? '') == 'trading' ? 'selected' : '' ?>>تجارة</option>
                                                        <option value="services" <?= ($settings['activity_type'] ?? '') == 'services' ? 'selected' : '' ?>>خدمات</option>
                                                        <option value="mixed" <?= ($settings['activity_type'] ?? '') == 'mixed' ? 'selected' : '' ?>>مختلط</option>
                                                    </select>
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">شعار الشركة</label>
                                                    <input type="file" class="form-control" name="company_logo" accept="image/*">
                                                    <?php if (!empty($settings['company_logo'])): ?>
                                                        <small class="text-muted">الشعار الحالي: <?= htmlspecialchars($settings['company_logo']) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-2"></i>حفظ معلومات الشركة
                                                </button>
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                                </button>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- إعدادات النظام -->
                                    <div class="tab-pane fade" id="v-pills-system" role="tabpanel">
                                        <h6 class="section-title"><i class="fas fa-cogs me-2"></i>إعدادات النظام</h6>
                                        <form method="POST" id="systemForm">
                                            <input type="hidden" name="action" value="system_settings">

                                            <!-- إعدادات العملة والتنسيق -->
                                            <div class="card mb-4">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="fas fa-money-bill me-2"></i>إعدادات العملة والتنسيق</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">العملة الافتراضية</label>
                                                            <select class="form-select" name="default_currency" required>
                                                                <?php foreach ($currencies as $currency): ?>
                                                                    <option value="<?= $currency['id'] ?>" <?= ($settings['default_currency'] ?? '') == $currency['id'] ? 'selected' : '' ?>>
                                                                        <?= htmlspecialchars($currency['name']) ?> (<?= htmlspecialchars($currency['symbol']) ?>)
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">عدد الخانات العشرية</label>
                                                            <select class="form-select" name="decimal_places">
                                                                <option value="0" <?= ($settings['decimal_places'] ?? '2') == '0' ? 'selected' : '' ?>>0</option>
                                                                <option value="1" <?= ($settings['decimal_places'] ?? '2') == '1' ? 'selected' : '' ?>>1</option>
                                                                <option value="2" <?= ($settings['decimal_places'] ?? '2') == '2' ? 'selected' : '' ?>>2</option>
                                                                <option value="3" <?= ($settings['decimal_places'] ?? '2') == '3' ? 'selected' : '' ?>>3</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">تنسيق التاريخ</label>
                                                            <select class="form-select" name="date_format">
                                                                <option value="d/m/Y" <?= ($settings['date_format'] ?? 'd/m/Y') == 'd/m/Y' ? 'selected' : '' ?>>يوم/شهر/سنة (<?= date('d/m/Y') ?>)</option>
                                                                <option value="Y-m-d" <?= ($settings['date_format'] ?? 'd/m/Y') == 'Y-m-d' ? 'selected' : '' ?>>سنة-شهر-يوم (<?= date('Y-m-d') ?>)</option>
                                                                <option value="m/d/Y" <?= ($settings['date_format'] ?? 'd/m/Y') == 'm/d/Y' ? 'selected' : '' ?>>شهر/يوم/سنة (<?= date('m/d/Y') ?>)</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">تنسيق الوقت</label>
                                                            <select class="form-select" name="time_format">
                                                                <option value="H:i" <?= ($settings['time_format'] ?? 'H:i') == 'H:i' ? 'selected' : '' ?>>24 ساعة (<?= date('H:i') ?>)</option>
                                                                <option value="h:i A" <?= ($settings['time_format'] ?? 'H:i') == 'h:i A' ? 'selected' : '' ?>>12 ساعة (<?= date('h:i A') ?>)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- إعدادات اللغة والمنطقة -->
                                            <div class="card mb-4">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="fas fa-globe me-2"></i>إعدادات اللغة والمنطقة</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">اللغة الافتراضية</label>
                                                            <select class="form-select" name="language">
                                                                <option value="ar" <?= ($settings['language'] ?? 'ar') == 'ar' ? 'selected' : '' ?>>العربية</option>
                                                                <option value="en" <?= ($settings['language'] ?? 'ar') == 'en' ? 'selected' : '' ?>>English</option>
                                                                <option value="ku" <?= ($settings['language'] ?? 'ar') == 'ku' ? 'selected' : '' ?>>کوردی</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">المنطقة الزمنية</label>
                                                            <select class="form-select" name="timezone">
                                                                <option value="Asia/Baghdad" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Baghdad' ? 'selected' : '' ?>>بغداد (GMT+3)</option>
                                                                <option value="Asia/Riyadh" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Riyadh' ? 'selected' : '' ?>>الرياض (GMT+3)</option>
                                                                <option value="Asia/Dubai" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Dubai' ? 'selected' : '' ?>>دبي (GMT+4)</option>
                                                                <option value="Asia/Kuwait" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Kuwait' ? 'selected' : '' ?>>الكويت (GMT+3)</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- إعدادات الواجهة -->
                                            <div class="card mb-4">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="fas fa-desktop me-2"></i>إعدادات الواجهة</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">عدد العناصر في الصفحة</label>
                                                            <select class="form-select" name="items_per_page">
                                                                <option value="10" <?= ($settings['items_per_page'] ?? '25') == '10' ? 'selected' : '' ?>>10</option>
                                                                <option value="25" <?= ($settings['items_per_page'] ?? '25') == '25' ? 'selected' : '' ?>>25</option>
                                                                <option value="50" <?= ($settings['items_per_page'] ?? '25') == '50' ? 'selected' : '' ?>>50</option>
                                                                <option value="100" <?= ($settings['items_per_page'] ?? '25') == '100' ? 'selected' : '' ?>>100</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">مهلة الجلسة (دقيقة)</label>
                                                            <input type="number" class="form-control" name="session_timeout" value="<?= htmlspecialchars($settings['session_timeout'] ?? '60') ?>" min="5" max="480">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- إعدادات النظام المتقدمة -->
                                            <div class="card mb-4">
                                                <div class="card-header">
                                                    <h6 class="mb-0"><i class="fas fa-tools me-2"></i>إعدادات النظام المتقدمة</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" name="auto_backup" id="auto_backup" <?= ($settings['auto_backup'] ?? 0) ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="auto_backup">
                                                                    النسخ الاحتياطي التلقائي
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">تكرار النسخ الاحتياطي</label>
                                                            <select class="form-select" name="backup_frequency">
                                                                <option value="daily" <?= ($settings['backup_frequency'] ?? 'daily') == 'daily' ? 'selected' : '' ?>>يومي</option>
                                                                <option value="weekly" <?= ($settings['backup_frequency'] ?? 'daily') == 'weekly' ? 'selected' : '' ?>>أسبوعي</option>
                                                                <option value="monthly" <?= ($settings['backup_frequency'] ?? 'daily') == 'monthly' ? 'selected' : '' ?>>شهري</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenance_mode" <?= ($settings['maintenance_mode'] ?? 0) ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="maintenance_mode">
                                                                    وضع الصيانة
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" name="debug_mode" id="debug_mode" <?= ($settings['debug_mode'] ?? 0) ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="debug_mode">
                                                                    وضع التطوير (Debug)
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <div class="form-check form-switch">
                                                                <input class="form-check-input" type="checkbox" name="cache_enabled" id="cache_enabled" <?= ($settings['cache_enabled'] ?? 1) ? 'checked' : '' ?>>
                                                                <label class="form-check-label" for="cache_enabled">
                                                                    تفعيل الذاكرة المؤقتة
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="d-flex gap-2">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-save me-2"></i>حفظ إعدادات النظام
                                                </button>
                                                <button type="reset" class="btn btn-outline-secondary">
                                                    <i class="fas fa-undo me-2"></i>إعادة تعيين
                                                </button>
                                            </div>
                                        </form>
                                    </div>

                                    <!-- إعدادات البريد -->
                                    <div class="tab-pane fade" id="v-pills-email" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-envelope me-2"></i>إعدادات البريد الإلكتروني</h6>
                                        <form method="POST">
                                            <input type="hidden" name="action" value="email_settings">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">خادم SMTP</label>
                                                    <input type="text" class="form-control" name="smtp_host" value="<?= htmlspecialchars($settings['smtp_host'] ?? '') ?>" placeholder="smtp.gmail.com">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">منفذ SMTP</label>
                                                    <input type="number" class="form-control" name="smtp_port" value="<?= htmlspecialchars($settings['smtp_port'] ?? '587') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم المستخدم</label>
                                                    <input type="text" class="form-control" name="smtp_username" value="<?= htmlspecialchars($settings['smtp_username'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">كلمة المرور</label>
                                                    <input type="password" class="form-control" name="smtp_password" value="<?= htmlspecialchars($settings['smtp_password'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">التشفير</label>
                                                    <select class="form-select" name="smtp_encryption">
                                                        <option value="tls" <?= ($settings['smtp_encryption'] ?? 'tls') == 'tls' ? 'selected' : '' ?>>TLS</option>
                                                        <option value="ssl" <?= ($settings['smtp_encryption'] ?? 'tls') == 'ssl' ? 'selected' : '' ?>>SSL</option>
                                                        <option value="none" <?= ($settings['smtp_encryption'] ?? 'tls') == 'none' ? 'selected' : '' ?>>بدون تشفير</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">البريد المرسل</label>
                                                    <input type="email" class="form-control" name="from_email" value="<?= htmlspecialchars($settings['from_email'] ?? '') ?>">
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">اسم المرسل</label>
                                                    <input type="text" class="form-control" name="from_name" value="<?= htmlspecialchars($settings['from_name'] ?? '') ?>">
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات البريد
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="testEmail()">
                                                <i class="fas fa-paper-plane me-2"></i>اختبار البريد
                                            </button>
                                        </form>
                                    </div>

                                    <!-- الأمان -->
                                    <div class="tab-pane fade" id="v-pills-security" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</h6>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            إعدادات الأمان قيد التطوير
                                        </div>
                                    </div>

                                    <!-- النسخ الاحتياطي -->
                                    <div class="tab-pane fade" id="v-pills-backup" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-database me-2"></i>النسخ الاحتياطي</h6>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            إعدادات النسخ الاحتياطي قيد التطوير
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testEmail() {
            alert('سيتم إضافة وظيفة اختبار البريد قريباً');
        }
    </script>
</body>
</html>