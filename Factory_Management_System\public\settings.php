<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        switch ($action) {
            case 'company_info':
                // حفظ معلومات الشركة
                $companyData = [
                    'company_name' => cleanInput($_POST['company_name']),
                    'company_address' => cleanInput($_POST['company_address']),
                    'company_phone' => cleanInput($_POST['company_phone']),
                    'company_email' => cleanInput($_POST['company_email']),
                    'company_website' => cleanInput($_POST['company_website']),
                    'tax_number' => cleanInput($_POST['tax_number']),
                    'commercial_register' => cleanInput($_POST['commercial_register'])
                ];

                // حفظ في جدول الإعدادات أو ملف
                foreach ($companyData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ معلومات الشركة بنجاح';
                break;

            case 'system_settings':
                // حفظ إعدادات النظام
                $systemData = [
                    'default_currency' => cleanInput($_POST['default_currency']),
                    'decimal_places' => intval($_POST['decimal_places']),
                    'date_format' => cleanInput($_POST['date_format']),
                    'timezone' => cleanInput($_POST['timezone']),
                    'items_per_page' => intval($_POST['items_per_page']),
                    'session_timeout' => intval($_POST['session_timeout'])
                ];

                foreach ($systemData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات النظام بنجاح';
                break;

            case 'email_settings':
                // حفظ إعدادات البريد الإلكتروني
                $emailData = [
                    'smtp_host' => cleanInput($_POST['smtp_host']),
                    'smtp_port' => intval($_POST['smtp_port']),
                    'smtp_username' => cleanInput($_POST['smtp_username']),
                    'smtp_password' => cleanInput($_POST['smtp_password']),
                    'smtp_encryption' => cleanInput($_POST['smtp_encryption']),
                    'from_email' => cleanInput($_POST['from_email']),
                    'from_name' => cleanInput($_POST['from_name'])
                ];

                foreach ($emailData as $key => $value) {
                    $db->query("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)
                               ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)",
                               [$key, $value]);
                }

                $success = 'تم حفظ إعدادات البريد الإلكتروني بنجاح';
                break;
        }
    } catch (Exception $e) {
        $error = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
try {
    $settings = [];
    $settingsData = $db->fetchAll("SELECT setting_key, setting_value FROM settings");
    foreach ($settingsData as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    $settings = [];
}

// العملات المتاحة
try {
    $currencies = $db->fetchAll("SELECT * FROM currencies WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $currencies = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
        .navbar-brand { font-weight: bold; }
        .card { border: none; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-bottom: 20px; }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .nav-pills .nav-link.active { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .settings-icon { font-size: 1.2rem; margin-left: 8px; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-industry me-2"></i>نظام إدارة المعامل
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">مرحباً، <?= htmlspecialchars($user['full_name']) ?></span>
                <a class="nav-link" href="logout.php"><i class="fas fa-sign-out-alt"></i> خروج</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>إعدادات النظام</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <?= htmlspecialchars($success) ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?= htmlspecialchars($error) ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <!-- القائمة الجانبية -->
                            <div class="col-md-3">
                                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                                    <button class="nav-link active text-end" id="v-pills-company-tab" data-bs-toggle="pill" data-bs-target="#v-pills-company" type="button">
                                        <i class="fas fa-building settings-icon"></i>معلومات الشركة
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-system-tab" data-bs-toggle="pill" data-bs-target="#v-pills-system" type="button">
                                        <i class="fas fa-cogs settings-icon"></i>إعدادات النظام
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-email-tab" data-bs-toggle="pill" data-bs-target="#v-pills-email" type="button">
                                        <i class="fas fa-envelope settings-icon"></i>إعدادات البريد
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-security-tab" data-bs-toggle="pill" data-bs-target="#v-pills-security" type="button">
                                        <i class="fas fa-shield-alt settings-icon"></i>الأمان
                                    </button>
                                    <button class="nav-link text-end" id="v-pills-backup-tab" data-bs-toggle="pill" data-bs-target="#v-pills-backup" type="button">
                                        <i class="fas fa-database settings-icon"></i>النسخ الاحتياطي
                                    </button>
                                </div>
                            </div>

                            <!-- المحتوى -->
                            <div class="col-md-9">
                                <div class="tab-content" id="v-pills-tabContent">
                                    <!-- معلومات الشركة -->
                                    <div class="tab-pane fade show active" id="v-pills-company" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                                        <form method="POST">
                                            <input type="hidden" name="action" value="company_info">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم الشركة</label>
                                                    <input type="text" class="form-control" name="company_name" value="<?= htmlspecialchars($settings['company_name'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رقم الهاتف</label>
                                                    <input type="text" class="form-control" name="company_phone" value="<?= htmlspecialchars($settings['company_phone'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">البريد الإلكتروني</label>
                                                    <input type="email" class="form-control" name="company_email" value="<?= htmlspecialchars($settings['company_email'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الموقع الإلكتروني</label>
                                                    <input type="url" class="form-control" name="company_website" value="<?= htmlspecialchars($settings['company_website'] ?? '') ?>">
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">العنوان</label>
                                                    <textarea class="form-control" name="company_address" rows="3"><?= htmlspecialchars($settings['company_address'] ?? '') ?></textarea>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الرقم الضريبي</label>
                                                    <input type="text" class="form-control" name="tax_number" value="<?= htmlspecialchars($settings['tax_number'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رقم السجل التجاري</label>
                                                    <input type="text" class="form-control" name="commercial_register" value="<?= htmlspecialchars($settings['commercial_register'] ?? '') ?>">
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ معلومات الشركة
                                            </button>
                                        </form>
                                    </div>

                                    <!-- إعدادات النظام -->
                                    <div class="tab-pane fade" id="v-pills-system" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>إعدادات النظام</h6>
                                        <form method="POST">
                                            <input type="hidden" name="action" value="system_settings">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">العملة الافتراضية</label>
                                                    <select class="form-select" name="default_currency">
                                                        <?php foreach ($currencies as $currency): ?>
                                                            <option value="<?= $currency['id'] ?>" <?= ($settings['default_currency'] ?? '') == $currency['id'] ? 'selected' : '' ?>>
                                                                <?= htmlspecialchars($currency['name']) ?> (<?= htmlspecialchars($currency['symbol']) ?>)
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">عدد الخانات العشرية</label>
                                                    <select class="form-select" name="decimal_places">
                                                        <option value="0" <?= ($settings['decimal_places'] ?? '2') == '0' ? 'selected' : '' ?>>0</option>
                                                        <option value="1" <?= ($settings['decimal_places'] ?? '2') == '1' ? 'selected' : '' ?>>1</option>
                                                        <option value="2" <?= ($settings['decimal_places'] ?? '2') == '2' ? 'selected' : '' ?>>2</option>
                                                        <option value="3" <?= ($settings['decimal_places'] ?? '2') == '3' ? 'selected' : '' ?>>3</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">تنسيق التاريخ</label>
                                                    <select class="form-select" name="date_format">
                                                        <option value="d/m/Y" <?= ($settings['date_format'] ?? 'd/m/Y') == 'd/m/Y' ? 'selected' : '' ?>>يوم/شهر/سنة</option>
                                                        <option value="Y-m-d" <?= ($settings['date_format'] ?? 'd/m/Y') == 'Y-m-d' ? 'selected' : '' ?>>سنة-شهر-يوم</option>
                                                        <option value="m/d/Y" <?= ($settings['date_format'] ?? 'd/m/Y') == 'm/d/Y' ? 'selected' : '' ?>>شهر/يوم/سنة</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">المنطقة الزمنية</label>
                                                    <select class="form-select" name="timezone">
                                                        <option value="Asia/Baghdad" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Baghdad' ? 'selected' : '' ?>>بغداد</option>
                                                        <option value="Asia/Riyadh" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Riyadh' ? 'selected' : '' ?>>الرياض</option>
                                                        <option value="Asia/Dubai" <?= ($settings['timezone'] ?? 'Asia/Baghdad') == 'Asia/Dubai' ? 'selected' : '' ?>>دبي</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">عدد العناصر في الصفحة</label>
                                                    <select class="form-select" name="items_per_page">
                                                        <option value="10" <?= ($settings['items_per_page'] ?? '25') == '10' ? 'selected' : '' ?>>10</option>
                                                        <option value="25" <?= ($settings['items_per_page'] ?? '25') == '25' ? 'selected' : '' ?>>25</option>
                                                        <option value="50" <?= ($settings['items_per_page'] ?? '25') == '50' ? 'selected' : '' ?>>50</option>
                                                        <option value="100" <?= ($settings['items_per_page'] ?? '25') == '100' ? 'selected' : '' ?>>100</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">مهلة الجلسة (دقيقة)</label>
                                                    <input type="number" class="form-control" name="session_timeout" value="<?= htmlspecialchars($settings['session_timeout'] ?? '60') ?>" min="5" max="480">
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات النظام
                                            </button>
                                        </form>
                                    </div>

                                    <!-- إعدادات البريد -->
                                    <div class="tab-pane fade" id="v-pills-email" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-envelope me-2"></i>إعدادات البريد الإلكتروني</h6>
                                        <form method="POST">
                                            <input type="hidden" name="action" value="email_settings">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">خادم SMTP</label>
                                                    <input type="text" class="form-control" name="smtp_host" value="<?= htmlspecialchars($settings['smtp_host'] ?? '') ?>" placeholder="smtp.gmail.com">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">منفذ SMTP</label>
                                                    <input type="number" class="form-control" name="smtp_port" value="<?= htmlspecialchars($settings['smtp_port'] ?? '587') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم المستخدم</label>
                                                    <input type="text" class="form-control" name="smtp_username" value="<?= htmlspecialchars($settings['smtp_username'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">كلمة المرور</label>
                                                    <input type="password" class="form-control" name="smtp_password" value="<?= htmlspecialchars($settings['smtp_password'] ?? '') ?>">
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">التشفير</label>
                                                    <select class="form-select" name="smtp_encryption">
                                                        <option value="tls" <?= ($settings['smtp_encryption'] ?? 'tls') == 'tls' ? 'selected' : '' ?>>TLS</option>
                                                        <option value="ssl" <?= ($settings['smtp_encryption'] ?? 'tls') == 'ssl' ? 'selected' : '' ?>>SSL</option>
                                                        <option value="none" <?= ($settings['smtp_encryption'] ?? 'tls') == 'none' ? 'selected' : '' ?>>بدون تشفير</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">البريد المرسل</label>
                                                    <input type="email" class="form-control" name="from_email" value="<?= htmlspecialchars($settings['from_email'] ?? '') ?>">
                                                </div>
                                                <div class="col-12 mb-3">
                                                    <label class="form-label">اسم المرسل</label>
                                                    <input type="text" class="form-control" name="from_name" value="<?= htmlspecialchars($settings['from_name'] ?? '') ?>">
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ إعدادات البريد
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="testEmail()">
                                                <i class="fas fa-paper-plane me-2"></i>اختبار البريد
                                            </button>
                                        </form>
                                    </div>

                                    <!-- الأمان -->
                                    <div class="tab-pane fade" id="v-pills-security" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</h6>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            إعدادات الأمان قيد التطوير
                                        </div>
                                    </div>

                                    <!-- النسخ الاحتياطي -->
                                    <div class="tab-pane fade" id="v-pills-backup" role="tabpanel">
                                        <h6 class="mb-3"><i class="fas fa-database me-2"></i>النسخ الاحتياطي</h6>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            إعدادات النسخ الاحتياطي قيد التطوير
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testEmail() {
            alert('سيتم إضافة وظيفة اختبار البريد قريباً');
        }
    </script>
</body>
</html>