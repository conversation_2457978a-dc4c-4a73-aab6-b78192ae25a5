<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    header('Location: dashboard.php');
    exit;
}

$db = new Database();
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();

        // حذف جميع الجداول الموجودة وإعادة إنشائها
        $db->query("SET FOREIGN_KEY_CHECKS = 0");

        // حذف الجداول إذا كانت موجودة
        $tables = [
            'sales_invoice_details', 'sales_invoices', 'purchase_invoice_details', 'purchase_invoices',
            'stock_movements', 'stock_balances', 'production_expenses', 'general_expenses',
            'payroll', 'attendance', 'items', 'item_categories', 'warehouses', 'customers',
            'suppliers', 'employees', 'departments', 'currencies'
        ];

        foreach ($tables as $table) {
            $db->query("DROP TABLE IF EXISTS `$table`");
        }

        // إنشاء جدول العملات
        $db->query("CREATE TABLE currencies (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            symbol VARCHAR(10) NOT NULL,
            code VARCHAR(3) NOT NULL UNIQUE,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            is_base BOOLEAN DEFAULT FALSE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الأقسام
        $db->query("CREATE TABLE departments (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            manager_id INT,
            budget DECIMAL(12,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الموظفين
        $db->query("CREATE TABLE employees (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_code VARCHAR(20) NOT NULL UNIQUE,
            full_name VARCHAR(200) NOT NULL,
            email VARCHAR(100),
            phone VARCHAR(20),
            address TEXT,
            national_id VARCHAR(50),
            birth_date DATE,
            hire_date DATE NOT NULL,
            department_id INT,
            position VARCHAR(100),
            salary DECIMAL(10,2) DEFAULT 0.00,
            hourly_rate DECIMAL(8,2) DEFAULT 0.00,
            overtime_rate DECIMAL(8,2) DEFAULT 0.00,
            status ENUM('active', 'inactive', 'terminated') DEFAULT 'active',
            bank_account VARCHAR(50),
            emergency_contact VARCHAR(200),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الحضور
        $db->query("CREATE TABLE attendance (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            attendance_date DATE NOT NULL,
            check_in_time TIME,
            check_out_time TIME,
            break_duration INT DEFAULT 0,
            total_hours DECIMAL(4,2) DEFAULT 0.00,
            overtime_hours DECIMAL(4,2) DEFAULT 0.00,
            status ENUM('present', 'absent', 'late', 'half_day', 'holiday') DEFAULT 'present',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_employee_date (employee_id, attendance_date)
        )");

        // إنشاء جدول الرواتب
        $db->query("CREATE TABLE payroll (
            id INT PRIMARY KEY AUTO_INCREMENT,
            employee_id INT NOT NULL,
            pay_period_start DATE NOT NULL,
            pay_period_end DATE NOT NULL,
            basic_salary DECIMAL(10,2) DEFAULT 0.00,
            overtime_amount DECIMAL(10,2) DEFAULT 0.00,
            bonus DECIMAL(10,2) DEFAULT 0.00,
            allowances DECIMAL(10,2) DEFAULT 0.00,
            deductions DECIMAL(10,2) DEFAULT 0.00,
            gross_salary DECIMAL(10,2) DEFAULT 0.00,
            tax_amount DECIMAL(10,2) DEFAULT 0.00,
            net_salary DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('draft', 'approved', 'paid') DEFAULT 'draft',
            payment_date DATE,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إنشاء جدول المصاريف العامة
        $db->query("CREATE TABLE general_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            category ENUM('utilities', 'rent', 'maintenance', 'office', 'marketing', 'travel', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            department_id INT,
            vendor_name VARCHAR(200),
            invoice_number VARCHAR(100),
            payment_method ENUM('cash', 'bank_transfer', 'check', 'credit_card') DEFAULT 'cash',
            status ENUM('pending', 'approved', 'paid', 'rejected') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إنشاء جدول مصاريف الإنتاج
        $db->query("CREATE TABLE production_expenses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            expense_code VARCHAR(20) NOT NULL UNIQUE,
            production_order_id INT,
            category ENUM('labor', 'utilities', 'maintenance', 'materials', 'overhead', 'other') NOT NULL,
            description TEXT NOT NULL,
            amount DECIMAL(12,2) NOT NULL,
            currency_id INT DEFAULT 1,
            expense_date DATE NOT NULL,
            employee_id INT,
            cost_center VARCHAR(100),
            allocation_percentage DECIMAL(5,2) DEFAULT 100.00,
            status ENUM('pending', 'approved', 'allocated') DEFAULT 'pending',
            approved_by INT,
            approved_at TIMESTAMP NULL,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إنشاء جدول فئات الأصناف
        $db->query("CREATE TABLE item_categories (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            parent_id INT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الأصناف
        $db->query("CREATE TABLE items (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            description TEXT,
            category_id INT,
            unit VARCHAR(20) NOT NULL DEFAULT 'قطعة',
            type ENUM('raw_material', 'finished_product', 'semi_finished', 'consumable') NOT NULL,
            cost_price DECIMAL(10,2) DEFAULT 0.00,
            selling_price DECIMAL(10,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            min_stock_level INT DEFAULT 0,
            max_stock_level INT DEFAULT 0,
            reorder_level INT DEFAULT 0,
            barcode VARCHAR(100),
            image_path VARCHAR(255),
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول المخازن
        $db->query("CREATE TABLE warehouses (
            id INT PRIMARY KEY AUTO_INCREMENT,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            manager_id INT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول أرصدة المخزون
        $db->query("CREATE TABLE stock_balances (
            id INT PRIMARY KEY AUTO_INCREMENT,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) DEFAULT 0.000,
            reserved_quantity DECIMAL(10,3) DEFAULT 0.000,
            available_quantity DECIMAL(10,3) GENERATED ALWAYS AS (quantity - reserved_quantity) STORED,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_item_warehouse (item_id, warehouse_id)
        )");

        // إنشاء جدول حركات المخزون
        $db->query("CREATE TABLE stock_movements (
            id INT PRIMARY KEY AUTO_INCREMENT,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            movement_type ENUM('in', 'out', 'transfer', 'adjustment') NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            reference_type ENUM('purchase', 'sale', 'production', 'adjustment', 'transfer') NOT NULL,
            reference_id INT,
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إنشاء جدول العملاء
        $db->query("CREATE TABLE customers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'individual',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول الموردين
        $db->query("CREATE TABLE suppliers (
            id INT PRIMARY KEY AUTO_INCREMENT,
            code VARCHAR(20) NOT NULL UNIQUE,
            name VARCHAR(200) NOT NULL,
            type ENUM('individual', 'company', 'institution') DEFAULT 'company',
            phone VARCHAR(20),
            email VARCHAR(100),
            address TEXT,
            tax_number VARCHAR(50),
            credit_limit DECIMAL(12,2) DEFAULT 0.00,
            currency_id INT DEFAULT 1,
            payment_terms INT DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول فواتير المبيعات
        $db->query("CREATE TABLE sales_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'shipped', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول تفاصيل فواتير المبيعات
        $db->query("CREATE TABLE sales_invoice_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED
        )");

        // إنشاء جدول فواتير المشتريات
        $db->query("CREATE TABLE purchase_invoices (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_number VARCHAR(20) NOT NULL UNIQUE,
            supplier_id INT NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            currency_id INT DEFAULT 1,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            subtotal DECIMAL(12,2) DEFAULT 0.00,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(12,2) DEFAULT 0.00,
            tax_percentage DECIMAL(5,2) DEFAULT 0.00,
            tax_amount DECIMAL(12,2) DEFAULT 0.00,
            total_amount DECIMAL(12,2) DEFAULT 0.00,
            paid_amount DECIMAL(12,2) DEFAULT 0.00,
            remaining_amount DECIMAL(12,2) DEFAULT 0.00,
            status ENUM('draft', 'confirmed', 'received', 'paid', 'cancelled') DEFAULT 'draft',
            notes TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // إنشاء جدول تفاصيل فواتير المشتريات
        $db->query("CREATE TABLE purchase_invoice_details (
            id INT PRIMARY KEY AUTO_INCREMENT,
            invoice_id INT NOT NULL,
            item_id INT NOT NULL,
            warehouse_id INT NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(10,2) NOT NULL,
            discount_percentage DECIMAL(5,2) DEFAULT 0.00,
            discount_amount DECIMAL(10,2) DEFAULT 0.00,
            line_total DECIMAL(12,2) GENERATED ALWAYS AS (quantity * unit_price - discount_amount) STORED
        )");

        $db->query("SET FOREIGN_KEY_CHECKS = 1");

        // إدراج البيانات الأساسية

        // العملات
        $currencies = [
            ['الدينار العراقي', 'د.ع', 'IQD', 1.0000, 1],
            ['الدولار الأمريكي', '$', 'USD', 0.00068, 0],
            ['اليورو', '€', 'EUR', 0.00061, 0]
        ];

        foreach ($currencies as $currency) {
            $db->query("INSERT INTO currencies (name, symbol, code, exchange_rate, is_base, is_active) VALUES (?, ?, ?, ?, ?, 1)", $currency);
        }

        // الأقسام
        $departments = [
            ['الإدارة العامة', 'الإدارة العليا والتخطيط الاستراتيجي', 50000000.00],
            ['الإنتاج', 'قسم الإنتاج والتصنيع', 100000000.00],
            ['المبيعات والتسويق', 'قسم المبيعات والتسويق', 30000000.00],
            ['المحاسبة والمالية', 'قسم المحاسبة والشؤون المالية', 20000000.00],
            ['الموارد البشرية', 'قسم الموارد البشرية والتدريب', 15000000.00],
            ['المخازن', 'قسم إدارة المخازن والمشتريات', 25000000.00],
            ['الصيانة', 'قسم الصيانة والخدمات الفنية', 20000000.00],
            ['ضمان الجودة', 'قسم ضمان الجودة والمراقبة', 10000000.00]
        ];

        foreach ($departments as $dept) {
            $db->query("INSERT INTO departments (name, description, budget) VALUES (?, ?, ?)", $dept);
        }

        // فئات الأصناف
        $categories = [
            ['مواد خام', 'المواد الأولية المستخدمة في الإنتاج'],
            ['منتجات نهائية', 'المنتجات الجاهزة للبيع'],
            ['منتجات نصف مصنعة', 'منتجات في مراحل التصنيع'],
            ['مواد استهلاكية', 'مواد مساعدة ومكتبية'],
            ['قطع غيار', 'قطع الغيار والصيانة'],
            ['مواد تعبئة وتغليف', 'مواد التعبئة والتغليف']
        ];

        foreach ($categories as $category) {
            $db->query("INSERT INTO item_categories (name, description) VALUES (?, ?)", $category);
        }

        // المخازن
        $warehouses = [
            ['المخزن الرئيسي', 'المبنى الرئيسي - الطابق الأرضي'],
            ['مخزن المواد الخام', 'المبنى الإنتاجي - الطابق الأول'],
            ['مخزن المنتجات النهائية', 'المبنى الرئيسي - الطابق الثاني'],
            ['مخزن قطع الغيار', 'المبنى الخدمي - الطابق الأرضي']
        ];

        foreach ($warehouses as $warehouse) {
            $db->query("INSERT INTO warehouses (name, location) VALUES (?, ?)", $warehouse);
        }

        // العملاء
        $customers = [
            ['CUST001', 'شركة النور للتجارة', 'company', '07701234567', '<EMAIL>', 'بغداد - الكرادة', '*********', 5000000.00],
            ['CUST002', 'أحمد محمد علي', 'individual', '07709876543', '<EMAIL>', 'البصرة - المعقل', '', 1000000.00],
            ['CUST003', 'مؤسسة الفرات للمقاولات', 'institution', '07801234567', '<EMAIL>', 'النجف - المركز', '*********', 10000000.00]
        ];

        foreach ($customers as $customer) {
            $db->query("INSERT INTO customers (code, name, type, phone, email, address, tax_number, credit_limit, currency_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)", $customer);
        }

        // الموردين
        $suppliers = [
            ['SUP001', 'شركة الحديد والصلب', 'company', '07711234567', '<EMAIL>', 'بغداد - الدورة', '*********', 20000000.00],
            ['SUP002', 'مؤسسة المواد الكيماوية', 'institution', '07722345678', '<EMAIL>', 'البصرة - الشعيبة', '*********', 15000000.00],
            ['SUP003', 'علي حسن للمواد الخام', 'individual', '07733456789', '<EMAIL>', 'الموصل - المركز', '', 5000000.00]
        ];

        foreach ($suppliers as $supplier) {
            $db->query("INSERT INTO suppliers (code, name, type, phone, email, address, tax_number, credit_limit, currency_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)", $supplier);
        }

        // الأصناف
        $items = [
            ['PROD001', 'منتج نهائي أ', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 100000.00, 150000.00],
            ['PROD002', 'منتج نهائي ب', 'منتج جاهز للبيع', 2, 'قطعة', 'finished_product', 80000.00, 120000.00],
            ['RAW001', 'حديد خام', 'حديد خام للتصنيع', 1, 'كيلو', 'raw_material', 50000.00, 0.00],
            ['RAW002', 'ألمنيوم خام', 'ألمنيوم خام للتصنيع', 1, 'كيلو', 'raw_material', 80000.00, 0.00],
            ['CONS001', 'مواد تنظيف', 'مواد تنظيف ومكتبية', 4, 'علبة', 'consumable', 5000.00, 0.00]
        ];

        foreach ($items as $item) {
            $db->query("INSERT INTO items (code, name, description, category_id, unit, type, cost_price, selling_price, currency_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, 1)", $item);
        }

        // الموظفين
        $employees = [
            ['EMP001', 'أحمد محمد علي', '<EMAIL>', '07701234567', 'بغداد - الكرادة', '*********01', '1985-05-15', '2020-01-01', 1, 'مدير عام', 2000000.00, 0.00, 0.00],
            ['EMP002', 'فاطمة حسن محمود', '<EMAIL>', '07709876543', 'بغداد - الجادرية', '*********02', '1990-08-20', '2021-03-15', 2, 'مشرف إنتاج', 1200000.00, 15000.00, 20000.00],
            ['EMP003', 'محمد عبد الله سالم', '<EMAIL>', '07751234567', 'بغداد - المنصور', '*********03', '1988-12-10', '2019-06-01', 3, 'مندوب مبيعات', 800000.00, 12000.00, 15000.00]
        ];

        foreach ($employees as $emp) {
            $db->query("INSERT INTO employees (employee_code, full_name, email, phone, address, national_id, birth_date, hire_date, department_id, position, salary, hourly_rate, overtime_rate) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", $emp);
        }

        $db->commit();
        $success = 'تم الإعداد النهائي بنجاح! تم إنشاء جميع الجداول والبيانات الأساسية.';

    } catch (Exception $e) {
        $db->rollback();
        $error = 'خطأ في الإعداد: ' . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعداد النهائي - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .setup-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-body {
            padding: 40px;
        }
        .btn-final {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-rocket fa-2x mb-3"></i><br>الإعداد النهائي الشامل</h1>
            <p class="mb-0">حل نهائي لجميع مشاكل النظام</p>
        </div>

        <div class="setup-body">
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?= htmlspecialchars($success) ?>
                </div>

                <div class="text-center">
                    <h4 class="mb-4">🎉 النظام جاهز للاستخدام!</h4>
                    <div class="d-grid gap-3">
                        <a href="dashboard.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>لوحة التحكم الرئيسية
                        </a>
                        <div class="row">
                            <div class="col-md-4">
                                <a href="employees.php" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-users me-2"></i>الموظفين
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="inventory.php" class="btn btn-outline-success w-100">
                                    <i class="fas fa-warehouse me-2"></i>المخزون
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="sales.php" class="btn btn-outline-warning w-100">
                                    <i class="fas fa-shopping-cart me-2"></i>المبيعات
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php if ($error): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم</h5>
                    <p><strong>هذا الإعداد سيقوم بما يلي:</strong></p>
                    <ul>
                        <li>حذف جميع الجداول الموجودة وإعادة إنشائها</li>
                        <li>إنشاء جميع الجداول المطلوبة للنظام</li>
                        <li>إضافة البيانات الأساسية والتجريبية</li>
                        <li>إصلاح جميع مشاكل قاعدة البيانات</li>
                    </ul>
                    <p class="text-danger"><strong>تحذير:</strong> سيتم فقدان جميع البيانات الموجودة!</p>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ما سيتم إنشاؤه:</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>الجداول الأساسية:</h6>
                                <ul>
                                    <li>العملات والأقسام</li>
                                    <li>الموظفين والحضور والرواتب</li>
                                    <li>المصاريف العامة ومصاريف الإنتاج</li>
                                    <li>الأصناف وفئات الأصناف</li>
                                    <li>المخازن وأرصدة المخزون</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>جداول المبيعات والمشتريات:</h6>
                                <ul>
                                    <li>العملاء والموردين</li>
                                    <li>فواتير المبيعات وتفاصيلها</li>
                                    <li>فواتير المشتريات وتفاصيلها</li>
                                    <li>حركات المخزون</li>
                                    <li>جميع البيانات التجريبية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <form method="POST" class="d-inline">
                        <button type="submit" class="btn btn-final btn-lg me-3" onclick="return confirm('هل أنت متأكد؟ سيتم حذف جميع البيانات الموجودة!')">
                            <i class="fas fa-rocket me-2"></i>تشغيل الإعداد النهائي
                        </button>
                    </form>

                    <a href="dashboard.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>العودة
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
