<?php
require_once '../config/database.php';

/**
 * فئة إدارة المالية والأرباح
 * Financial Management and Profit Analysis Class
 */
class Financial {
    private $db;
    private $conn;
    
    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }
    
    /**
     * حساب الأرباح اليومية
     */
    public function getDailyProfits($date, $currencyId = null) {
        try {
            $sql = "SELECT 
                        DATE(si.invoice_date) as profit_date,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- إجمالي المبيعات
                        COALESCE(SUM(si.total_amount), 0) as total_sales,
                        
                        -- تكلفة البضاعة المباعة
                        COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as cost_of_goods_sold,
                        
                        -- إجمالي المصروفات
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE DATE(e.expense_date) = DATE(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as total_expenses,
                        
                        -- الربح الإجمالي
                        COALESCE(SUM(si.total_amount), 0) - COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as gross_profit,
                        
                        -- صافي الربح
                        COALESCE(SUM(si.total_amount), 0) - 
                        COALESCE(SUM(sid.quantity * i.cost_price * si.exchange_rate), 0) -
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE DATE(e.expense_date) = DATE(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as net_profit
                        
                    FROM sales_invoices si
                    JOIN sales_invoice_details sid ON si.id = sid.invoice_id
                    JOIN items i ON sid.item_id = i.id
                    JOIN currencies c ON si.currency_id = c.id
                    WHERE DATE(si.invoice_date) = :date
                    AND si.status IN ('confirmed', 'shipped', 'paid')";
            
            $params = ['date' => $date];
            
            if ($currencyId) {
                $sql .= " AND si.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY DATE(si.invoice_date), si.currency_id, c.code, c.symbol
                      ORDER BY c.code";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في حساب الأرباح اليومية: ' . $e->getMessage());
        }
    }
    
    /**
     * حساب الأرباح الشهرية
     */
    public function getMonthlyProfits($year, $month, $currencyId = null) {
        try {
            $sql = "SELECT 
                        YEAR(si.invoice_date) as profit_year,
                        MONTH(si.invoice_date) as profit_month,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- إجمالي المبيعات
                        COALESCE(SUM(si.total_amount), 0) as total_sales,
                        
                        -- تكلفة البضاعة المباعة
                        COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as cost_of_goods_sold,
                        
                        -- إجمالي المصروفات
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE YEAR(e.expense_date) = YEAR(si.invoice_date)
                            AND MONTH(e.expense_date) = MONTH(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as total_expenses,
                        
                        -- الربح الإجمالي
                        COALESCE(SUM(si.total_amount), 0) - COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as gross_profit,
                        
                        -- صافي الربح
                        COALESCE(SUM(si.total_amount), 0) - 
                        COALESCE(SUM(sid.quantity * i.cost_price * si.exchange_rate), 0) -
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE YEAR(e.expense_date) = YEAR(si.invoice_date)
                            AND MONTH(e.expense_date) = MONTH(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as net_profit,
                        
                        -- عدد الفواتير
                        COUNT(DISTINCT si.id) as invoices_count,
                        
                        -- متوسط قيمة الفاتورة
                        COALESCE(AVG(si.total_amount), 0) as avg_invoice_value
                        
                    FROM sales_invoices si
                    JOIN sales_invoice_details sid ON si.id = sid.invoice_id
                    JOIN items i ON sid.item_id = i.id
                    JOIN currencies c ON si.currency_id = c.id
                    WHERE YEAR(si.invoice_date) = :year
                    AND MONTH(si.invoice_date) = :month
                    AND si.status IN ('confirmed', 'shipped', 'paid')";
            
            $params = ['year' => $year, 'month' => $month];
            
            if ($currencyId) {
                $sql .= " AND si.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY YEAR(si.invoice_date), MONTH(si.invoice_date), si.currency_id, c.code, c.symbol
                      ORDER BY c.code";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في حساب الأرباح الشهرية: ' . $e->getMessage());
        }
    }
    
    /**
     * حساب الأرباح السنوية
     */
    public function getYearlyProfits($year, $currencyId = null) {
        try {
            $sql = "SELECT 
                        YEAR(si.invoice_date) as profit_year,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- إجمالي المبيعات
                        COALESCE(SUM(si.total_amount), 0) as total_sales,
                        
                        -- تكلفة البضاعة المباعة
                        COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as cost_of_goods_sold,
                        
                        -- إجمالي المصروفات
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE YEAR(e.expense_date) = YEAR(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as total_expenses,
                        
                        -- الربح الإجمالي
                        COALESCE(SUM(si.total_amount), 0) - COALESCE(SUM(
                            sid.quantity * i.cost_price * si.exchange_rate
                        ), 0) as gross_profit,
                        
                        -- صافي الربح
                        COALESCE(SUM(si.total_amount), 0) - 
                        COALESCE(SUM(sid.quantity * i.cost_price * si.exchange_rate), 0) -
                        COALESCE((
                            SELECT SUM(e.amount * e.exchange_rate)
                            FROM expenses e
                            WHERE YEAR(e.expense_date) = YEAR(si.invoice_date)
                            AND e.currency_id = si.currency_id
                        ), 0) as net_profit,
                        
                        -- عدد الفواتير
                        COUNT(DISTINCT si.id) as invoices_count,
                        
                        -- متوسط قيمة الفاتورة
                        COALESCE(AVG(si.total_amount), 0) as avg_invoice_value,
                        
                        -- هامش الربح الإجمالي
                        CASE 
                            WHEN SUM(si.total_amount) > 0 
                            THEN ((SUM(si.total_amount) - SUM(sid.quantity * i.cost_price * si.exchange_rate)) / SUM(si.total_amount)) * 100
                            ELSE 0 
                        END as gross_profit_margin,
                        
                        -- هامش الربح الصافي
                        CASE 
                            WHEN SUM(si.total_amount) > 0 
                            THEN ((SUM(si.total_amount) - SUM(sid.quantity * i.cost_price * si.exchange_rate) - 
                                  COALESCE((SELECT SUM(e.amount * e.exchange_rate) FROM expenses e 
                                           WHERE YEAR(e.expense_date) = YEAR(si.invoice_date) AND e.currency_id = si.currency_id), 0)) 
                                  / SUM(si.total_amount)) * 100
                            ELSE 0 
                        END as net_profit_margin
                        
                    FROM sales_invoices si
                    JOIN sales_invoice_details sid ON si.id = sid.invoice_id
                    JOIN items i ON sid.item_id = i.id
                    JOIN currencies c ON si.currency_id = c.id
                    WHERE YEAR(si.invoice_date) = :year
                    AND si.status IN ('confirmed', 'shipped', 'paid')";
            
            $params = ['year' => $year];
            
            if ($currencyId) {
                $sql .= " AND si.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY YEAR(si.invoice_date), si.currency_id, c.code, c.symbol
                      ORDER BY c.code";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في حساب الأرباح السنوية: ' . $e->getMessage());
        }
    }
    
    /**
     * تحليل الأرباح حسب المنتج
     */
    public function getProductProfitAnalysis($dateFrom, $dateTo, $currencyId = null) {
        try {
            $sql = "SELECT 
                        i.id as item_id,
                        i.name as item_name,
                        i.code as item_code,
                        ic.name as category_name,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- الكمية المباعة
                        SUM(sid.quantity) as total_quantity_sold,
                        
                        -- إجمالي المبيعات
                        SUM(sid.line_total) as total_sales,
                        
                        -- تكلفة البضاعة المباعة
                        SUM(sid.quantity * i.cost_price) as cost_of_goods_sold,
                        
                        -- الربح الإجمالي
                        SUM(sid.line_total) - SUM(sid.quantity * i.cost_price) as gross_profit,
                        
                        -- هامش الربح
                        CASE 
                            WHEN SUM(sid.line_total) > 0 
                            THEN ((SUM(sid.line_total) - SUM(sid.quantity * i.cost_price)) / SUM(sid.line_total)) * 100
                            ELSE 0 
                        END as profit_margin,
                        
                        -- متوسط سعر البيع
                        AVG(sid.unit_price) as avg_selling_price,
                        
                        -- عدد الفواتير
                        COUNT(DISTINCT si.id) as invoices_count
                        
                    FROM sales_invoices si
                    JOIN sales_invoice_details sid ON si.id = sid.invoice_id
                    JOIN items i ON sid.item_id = i.id
                    LEFT JOIN item_categories ic ON i.category_id = ic.id
                    JOIN currencies c ON si.currency_id = c.id
                    WHERE si.invoice_date BETWEEN :date_from AND :date_to
                    AND si.status IN ('confirmed', 'shipped', 'paid')";
            
            $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $sql .= " AND si.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY i.id, i.name, i.code, ic.name, si.currency_id, c.code, c.symbol
                      ORDER BY gross_profit DESC";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في تحليل أرباح المنتجات: ' . $e->getMessage());
        }
    }
    
    /**
     * تحليل الأرباح حسب العميل
     */
    public function getCustomerProfitAnalysis($dateFrom, $dateTo, $currencyId = null) {
        try {
            $sql = "SELECT 
                        cust.id as customer_id,
                        cust.name as customer_name,
                        cust.code as customer_code,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- إجمالي المبيعات
                        SUM(si.total_amount) as total_sales,
                        
                        -- تكلفة البضاعة المباعة
                        SUM(sid.quantity * i.cost_price) as cost_of_goods_sold,
                        
                        -- الربح الإجمالي
                        SUM(si.total_amount) - SUM(sid.quantity * i.cost_price) as gross_profit,
                        
                        -- هامش الربح
                        CASE 
                            WHEN SUM(si.total_amount) > 0 
                            THEN ((SUM(si.total_amount) - SUM(sid.quantity * i.cost_price)) / SUM(si.total_amount)) * 100
                            ELSE 0 
                        END as profit_margin,
                        
                        -- عدد الفواتير
                        COUNT(DISTINCT si.id) as invoices_count,
                        
                        -- متوسط قيمة الفاتورة
                        AVG(si.total_amount) as avg_invoice_value,
                        
                        -- إجمالي الخصومات
                        SUM(si.discount_amount) as total_discounts
                        
                    FROM sales_invoices si
                    JOIN customers cust ON si.customer_id = cust.id
                    JOIN sales_invoice_details sid ON si.id = sid.invoice_id
                    JOIN items i ON sid.item_id = i.id
                    JOIN currencies c ON si.currency_id = c.id
                    WHERE si.invoice_date BETWEEN :date_from AND :date_to
                    AND si.status IN ('confirmed', 'shipped', 'paid')";
            
            $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $sql .= " AND si.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY cust.id, cust.name, cust.code, si.currency_id, c.code, c.symbol
                      ORDER BY gross_profit DESC";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في تحليل أرباح العملاء: ' . $e->getMessage());
        }
    }
    
    /**
     * تحليل المصروفات
     */
    public function getExpenseAnalysis($dateFrom, $dateTo, $currencyId = null) {
        try {
            $sql = "SELECT 
                        e.category,
                        e.department,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- إجمالي المصروفات
                        SUM(e.amount) as total_amount,
                        
                        -- عدد المصروفات
                        COUNT(*) as expenses_count,
                        
                        -- متوسط المصروف
                        AVG(e.amount) as avg_expense,
                        
                        -- أعلى مصروف
                        MAX(e.amount) as max_expense,
                        
                        -- أقل مصروف
                        MIN(e.amount) as min_expense
                        
                    FROM expenses e
                    JOIN currencies c ON e.currency_id = c.id
                    WHERE e.expense_date BETWEEN :date_from AND :date_to
                    AND e.is_approved = 1";
            
            $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $sql .= " AND e.currency_id = :currency_id";
                $params['currency_id'] = $currencyId;
            }
            
            $sql .= " GROUP BY e.category, e.department, e.currency_id, c.code, c.symbol
                      ORDER BY total_amount DESC";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في تحليل المصروفات: ' . $e->getMessage());
        }
    }
    
    /**
     * تقرير التدفق النقدي
     */
    public function getCashFlowReport($dateFrom, $dateTo, $cashBoxId = null) {
        try {
            $sql = "SELECT 
                        DATE(ct.transaction_date) as transaction_date,
                        cb.name as cash_box_name,
                        c.code as currency_code,
                        c.symbol as currency_symbol,
                        
                        -- الوارد
                        SUM(CASE WHEN ct.type IN ('income', 'transfer_in') THEN ct.amount ELSE 0 END) as total_income,
                        
                        -- الصادر
                        SUM(CASE WHEN ct.type IN ('expense', 'transfer_out') THEN ct.amount ELSE 0 END) as total_expense,
                        
                        -- صافي التدفق
                        SUM(CASE WHEN ct.type IN ('income', 'transfer_in') THEN ct.amount ELSE -ct.amount END) as net_flow,
                        
                        -- عدد المعاملات
                        COUNT(*) as transactions_count
                        
                    FROM cash_transactions ct
                    JOIN cash_boxes cb ON ct.cash_box_id = cb.id
                    JOIN currencies c ON cb.currency_id = c.id
                    WHERE ct.transaction_date BETWEEN :date_from AND :date_to";
            
            $params = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($cashBoxId) {
                $sql .= " AND ct.cash_box_id = :cash_box_id";
                $params['cash_box_id'] = $cashBoxId;
            }
            
            $sql .= " GROUP BY DATE(ct.transaction_date), cb.id, cb.name, c.code, c.symbol
                      ORDER BY transaction_date DESC, cb.name";
            
            return $this->db->fetchAll($sql, $params);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في تقرير التدفق النقدي: ' . $e->getMessage());
        }
    }
    
    /**
     * مؤشرات الأداء المالي الرئيسية
     */
    public function getKPIs($dateFrom, $dateTo, $currencyId = null) {
        try {
            // إجمالي المبيعات
            $salesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_sales
                         FROM sales_invoices 
                         WHERE invoice_date BETWEEN :date_from AND :date_to
                         AND status IN ('confirmed', 'shipped', 'paid')";
            
            $salesParams = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $salesSql .= " AND currency_id = :currency_id";
                $salesParams['currency_id'] = $currencyId;
            }
            
            $totalSales = $this->db->fetchOne($salesSql, $salesParams)['total_sales'];
            
            // إجمالي المشتريات
            $purchasesSql = "SELECT COALESCE(SUM(total_amount), 0) as total_purchases
                             FROM purchase_invoices 
                             WHERE invoice_date BETWEEN :date_from AND :date_to
                             AND status IN ('confirmed', 'received', 'paid')";
            
            $purchasesParams = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $purchasesSql .= " AND currency_id = :currency_id";
                $purchasesParams['currency_id'] = $currencyId;
            }
            
            $totalPurchases = $this->db->fetchOne($purchasesSql, $purchasesParams)['total_purchases'];
            
            // إجمالي المصروفات
            $expensesSql = "SELECT COALESCE(SUM(amount), 0) as total_expenses
                            FROM expenses 
                            WHERE expense_date BETWEEN :date_from AND :date_to
                            AND is_approved = 1";
            
            $expensesParams = ['date_from' => $dateFrom, 'date_to' => $dateTo];
            
            if ($currencyId) {
                $expensesSql .= " AND currency_id = :currency_id";
                $expensesParams['currency_id'] = $currencyId;
            }
            
            $totalExpenses = $this->db->fetchOne($expensesSql, $expensesParams)['total_expenses'];
            
            // حساب المؤشرات
            $grossProfit = $totalSales - $totalPurchases;
            $netProfit = $grossProfit - $totalExpenses;
            $grossProfitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;
            $netProfitMargin = $totalSales > 0 ? ($netProfit / $totalSales) * 100 : 0;
            
            return [
                'total_sales' => $totalSales,
                'total_purchases' => $totalPurchases,
                'total_expenses' => $totalExpenses,
                'gross_profit' => $grossProfit,
                'net_profit' => $netProfit,
                'gross_profit_margin' => $grossProfitMargin,
                'net_profit_margin' => $netProfitMargin
            ];
            
        } catch (Exception $e) {
            throw new Exception('خطأ في حساب مؤشرات الأداء: ' . $e->getMessage());
        }
    }
    
    /**
     * تحويل العملة
     */
    public function convertCurrency($amount, $fromCurrencyId, $toCurrencyId) {
        try {
            if ($fromCurrencyId == $toCurrencyId) {
                return $amount;
            }
            
            $fromCurrency = $this->db->fetchOne("SELECT exchange_rate FROM currencies WHERE id = :id", ['id' => $fromCurrencyId]);
            $toCurrency = $this->db->fetchOne("SELECT exchange_rate FROM currencies WHERE id = :id", ['id' => $toCurrencyId]);
            
            if (!$fromCurrency || !$toCurrency) {
                throw new Exception('عملة غير صحيحة');
            }
            
            return Helper::convertCurrency($amount, $fromCurrency['exchange_rate'], $toCurrency['exchange_rate']);
            
        } catch (Exception $e) {
            throw new Exception('خطأ في تحويل العملة: ' . $e->getMessage());
        }
    }
}
?>
