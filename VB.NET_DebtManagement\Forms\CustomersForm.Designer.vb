<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class CustomersForm
    Inherits System.Windows.Forms.Form

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ToolStripMain = New System.Windows.Forms.ToolStrip()
        Me.ToolStripButtonAdd = New System.Windows.Forms.ToolStripButton()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.ToolStripButtonEdit = New System.Windows.Forms.ToolStripButton()
        Me.ToolStripButtonDelete = New System.Windows.Forms.ToolStripButton()
        Me.ToolStripSeparator2 = New System.Windows.Forms.ToolStripSeparator()
        Me.ToolStripButtonRefresh = New System.Windows.Forms.ToolStripButton()
        Me.ToolStripSeparator3 = New System.Windows.Forms.ToolStripSeparator()
        Me.ToolStripButtonExport = New System.Windows.Forms.ToolStripButton()
        Me.ToolStripButtonPrint = New System.Windows.Forms.ToolStripButton()
        Me.PanelSearch = New System.Windows.Forms.Panel()
        Me.LabelSearch = New System.Windows.Forms.Label()
        Me.ComboBoxSearchType = New System.Windows.Forms.ComboBox()
        Me.TextBoxSearch = New System.Windows.Forms.TextBox()
        Me.ButtonSearch = New System.Windows.Forms.Button()
        Me.ButtonClearSearch = New System.Windows.Forms.Button()
        Me.LabelCustomerType = New System.Windows.Forms.Label()
        Me.ComboBoxCustomerType = New System.Windows.Forms.ComboBox()
        Me.DataGridViewCustomers = New System.Windows.Forms.DataGridView()
        Me.StatusStripMain = New System.Windows.Forms.StatusStrip()
        Me.ToolStripStatusLabelStatus = New System.Windows.Forms.ToolStripStatusLabel()
        Me.ToolStripStatusLabelCount = New System.Windows.Forms.ToolStripStatusLabel()
        Me.ToolStripStatusLabelTotalIQD = New System.Windows.Forms.ToolStripStatusLabel()
        Me.ToolStripStatusLabelTotalUSD = New System.Windows.Forms.ToolStripStatusLabel()
        Me.ToolStripMain.SuspendLayout()
        Me.PanelSearch.SuspendLayout()
        CType(Me.DataGridViewCustomers, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.StatusStripMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'ToolStripMain
        '
        Me.ToolStripMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.ToolStripButtonAdd, Me.ToolStripSeparator1, Me.ToolStripButtonEdit, Me.ToolStripButtonDelete, Me.ToolStripSeparator2, Me.ToolStripButtonRefresh, Me.ToolStripSeparator3, Me.ToolStripButtonExport, Me.ToolStripButtonPrint})
        Me.ToolStripMain.Location = New System.Drawing.Point(0, 0)
        Me.ToolStripMain.Name = "ToolStripMain"
        Me.ToolStripMain.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.ToolStripMain.Size = New System.Drawing.Size(1200, 25)
        Me.ToolStripMain.TabIndex = 0
        Me.ToolStripMain.Text = "ToolStrip1"
        '
        'ToolStripButtonAdd
        '
        Me.ToolStripButtonAdd.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonAdd.Name = "ToolStripButtonAdd"
        Me.ToolStripButtonAdd.Size = New System.Drawing.Size(67, 22)
        Me.ToolStripButtonAdd.Text = "إضافة عميل"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(6, 25)
        '
        'ToolStripButtonEdit
        '
        Me.ToolStripButtonEdit.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonEdit.Enabled = False
        Me.ToolStripButtonEdit.Name = "ToolStripButtonEdit"
        Me.ToolStripButtonEdit.Size = New System.Drawing.Size(39, 22)
        Me.ToolStripButtonEdit.Text = "تعديل"
        '
        'ToolStripButtonDelete
        '
        Me.ToolStripButtonDelete.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonDelete.Enabled = False
        Me.ToolStripButtonDelete.Name = "ToolStripButtonDelete"
        Me.ToolStripButtonDelete.Size = New System.Drawing.Size(33, 22)
        Me.ToolStripButtonDelete.Text = "حذف"
        '
        'ToolStripSeparator2
        '
        Me.ToolStripSeparator2.Name = "ToolStripSeparator2"
        Me.ToolStripSeparator2.Size = New System.Drawing.Size(6, 25)
        '
        'ToolStripButtonRefresh
        '
        Me.ToolStripButtonRefresh.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonRefresh.Name = "ToolStripButtonRefresh"
        Me.ToolStripButtonRefresh.Size = New System.Drawing.Size(42, 22)
        Me.ToolStripButtonRefresh.Text = "تحديث"
        '
        'ToolStripSeparator3
        '
        Me.ToolStripSeparator3.Name = "ToolStripSeparator3"
        Me.ToolStripSeparator3.Size = New System.Drawing.Size(6, 25)
        '
        'ToolStripButtonExport
        '
        Me.ToolStripButtonExport.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonExport.Name = "ToolStripButtonExport"
        Me.ToolStripButtonExport.Size = New System.Drawing.Size(40, 22)
        Me.ToolStripButtonExport.Text = "تصدير"
        '
        'ToolStripButtonPrint
        '
        Me.ToolStripButtonPrint.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text
        Me.ToolStripButtonPrint.Name = "ToolStripButtonPrint"
        Me.ToolStripButtonPrint.Size = New System.Drawing.Size(42, 22)
        Me.ToolStripButtonPrint.Text = "طباعة"
        '
        'PanelSearch
        '
        Me.PanelSearch.BackColor = System.Drawing.Color.White
        Me.PanelSearch.Controls.Add(Me.ComboBoxCustomerType)
        Me.PanelSearch.Controls.Add(Me.LabelCustomerType)
        Me.PanelSearch.Controls.Add(Me.ButtonClearSearch)
        Me.PanelSearch.Controls.Add(Me.ButtonSearch)
        Me.PanelSearch.Controls.Add(Me.TextBoxSearch)
        Me.PanelSearch.Controls.Add(Me.ComboBoxSearchType)
        Me.PanelSearch.Controls.Add(Me.LabelSearch)
        Me.PanelSearch.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelSearch.Location = New System.Drawing.Point(0, 25)
        Me.PanelSearch.Name = "PanelSearch"
        Me.PanelSearch.Padding = New System.Windows.Forms.Padding(10)
        Me.PanelSearch.Size = New System.Drawing.Size(1200, 80)
        Me.PanelSearch.TabIndex = 1
        '
        'LabelSearch
        '
        Me.LabelSearch.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelSearch.Location = New System.Drawing.Point(1130, 15)
        Me.LabelSearch.Name = "LabelSearch"
        Me.LabelSearch.Size = New System.Drawing.Size(50, 25)
        Me.LabelSearch.TabIndex = 0
        Me.LabelSearch.Text = "البحث:"
        Me.LabelSearch.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'ComboBoxSearchType
        '
        Me.ComboBoxSearchType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ComboBoxSearchType.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.ComboBoxSearchType.FormattingEnabled = True
        Me.ComboBoxSearchType.Items.AddRange(New Object() {"الاسم", "الهاتف", "البريد الإلكتروني", "العنوان"})
        Me.ComboBoxSearchType.Location = New System.Drawing.Point(1000, 15)
        Me.ComboBoxSearchType.Name = "ComboBoxSearchType"
        Me.ComboBoxSearchType.Size = New System.Drawing.Size(120, 23)
        Me.ComboBoxSearchType.TabIndex = 1
        '
        'TextBoxSearch
        '
        Me.TextBoxSearch.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.TextBoxSearch.Location = New System.Drawing.Point(740, 15)
        Me.TextBoxSearch.Name = "TextBoxSearch"
        Me.TextBoxSearch.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.TextBoxSearch.Size = New System.Drawing.Size(250, 23)
        Me.TextBoxSearch.TabIndex = 2
        '
        'ButtonSearch
        '
        Me.ButtonSearch.BackColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(123, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.ButtonSearch.FlatAppearance.BorderSize = 0
        Me.ButtonSearch.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.ButtonSearch.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.ButtonSearch.ForeColor = System.Drawing.Color.White
        Me.ButtonSearch.Location = New System.Drawing.Point(660, 13)
        Me.ButtonSearch.Name = "ButtonSearch"
        Me.ButtonSearch.Size = New System.Drawing.Size(70, 30)
        Me.ButtonSearch.TabIndex = 3
        Me.ButtonSearch.Text = "بحث"
        Me.ButtonSearch.UseVisualStyleBackColor = False
        '
        'ButtonClearSearch
        '
        Me.ButtonClearSearch.BackColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(125, Byte), Integer))
        Me.ButtonClearSearch.FlatAppearance.BorderSize = 0
        Me.ButtonClearSearch.FlatStyle = System.Windows.Forms.FlatStyle.Flat
        Me.ButtonClearSearch.Font = New System.Drawing.Font("Segoe UI", 9.0!, System.Drawing.FontStyle.Bold)
        Me.ButtonClearSearch.ForeColor = System.Drawing.Color.White
        Me.ButtonClearSearch.Location = New System.Drawing.Point(580, 13)
        Me.ButtonClearSearch.Name = "ButtonClearSearch"
        Me.ButtonClearSearch.Size = New System.Drawing.Size(70, 30)
        Me.ButtonClearSearch.TabIndex = 4
        Me.ButtonClearSearch.Text = "مسح"
        Me.ButtonClearSearch.UseVisualStyleBackColor = False
        '
        'LabelCustomerType
        '
        Me.LabelCustomerType.Font = New System.Drawing.Font("Segoe UI", 10.0!, System.Drawing.FontStyle.Bold)
        Me.LabelCustomerType.Location = New System.Drawing.Point(1090, 50)
        Me.LabelCustomerType.Name = "LabelCustomerType"
        Me.LabelCustomerType.Size = New System.Drawing.Size(90, 25)
        Me.LabelCustomerType.TabIndex = 5
        Me.LabelCustomerType.Text = "نوع العميل:"
        Me.LabelCustomerType.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'ComboBoxCustomerType
        '
        Me.ComboBoxCustomerType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.ComboBoxCustomerType.Font = New System.Drawing.Font("Segoe UI", 9.0!)
        Me.ComboBoxCustomerType.FormattingEnabled = True
        Me.ComboBoxCustomerType.Items.AddRange(New Object() {"الكل", "شركة", "فرد", "مؤسسة", "أخرى"})
        Me.ComboBoxCustomerType.Location = New System.Drawing.Point(950, 50)
        Me.ComboBoxCustomerType.Name = "ComboBoxCustomerType"
        Me.ComboBoxCustomerType.Size = New System.Drawing.Size(130, 23)
        Me.ComboBoxCustomerType.TabIndex = 6
        '
        'DataGridViewCustomers
        '
        Me.DataGridViewCustomers.AllowUserToAddRows = False
        Me.DataGridViewCustomers.AllowUserToDeleteRows = False
        Me.DataGridViewCustomers.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill
        Me.DataGridViewCustomers.BackgroundColor = System.Drawing.Color.White
        Me.DataGridViewCustomers.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.DataGridViewCustomers.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize
        Me.DataGridViewCustomers.Dock = System.Windows.Forms.DockStyle.Fill
        Me.DataGridViewCustomers.Location = New System.Drawing.Point(0, 105)
        Me.DataGridViewCustomers.MultiSelect = False
        Me.DataGridViewCustomers.Name = "DataGridViewCustomers"
        Me.DataGridViewCustomers.ReadOnly = True
        Me.DataGridViewCustomers.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.DataGridViewCustomers.RowHeadersVisible = False
        Me.DataGridViewCustomers.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.DataGridViewCustomers.Size = New System.Drawing.Size(1200, 471)
        Me.DataGridViewCustomers.TabIndex = 2
        '
        'StatusStripMain
        '
        Me.StatusStripMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.ToolStripStatusLabelStatus, Me.ToolStripStatusLabelCount, Me.ToolStripStatusLabelTotalIQD, Me.ToolStripStatusLabelTotalUSD})
        Me.StatusStripMain.Location = New System.Drawing.Point(0, 576)
        Me.StatusStripMain.Name = "StatusStripMain"
        Me.StatusStripMain.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.StatusStripMain.Size = New System.Drawing.Size(1200, 24)
        Me.StatusStripMain.TabIndex = 3
        Me.StatusStripMain.Text = "StatusStrip1"
        '
        'ToolStripStatusLabelStatus
        '
        Me.ToolStripStatusLabelStatus.Name = "ToolStripStatusLabelStatus"
        Me.ToolStripStatusLabelStatus.Size = New System.Drawing.Size(32, 19)
        Me.ToolStripStatusLabelStatus.Spring = True
        Me.ToolStripStatusLabelStatus.Text = "جاهز"
        Me.ToolStripStatusLabelStatus.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'ToolStripStatusLabelCount
        '
        Me.ToolStripStatusLabelCount.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left
        Me.ToolStripStatusLabelCount.Name = "ToolStripStatusLabelCount"
        Me.ToolStripStatusLabelCount.Size = New System.Drawing.Size(79, 19)
        Me.ToolStripStatusLabelCount.Text = "عدد العملاء: 0"
        '
        'ToolStripStatusLabelTotalIQD
        '
        Me.ToolStripStatusLabelTotalIQD.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left
        Me.ToolStripStatusLabelTotalIQD.Name = "ToolStripStatusLabelTotalIQD"
        Me.ToolStripStatusLabelTotalIQD.Size = New System.Drawing.Size(89, 19)
        Me.ToolStripStatusLabelTotalIQD.Text = "الإجمالي (د.ع): 0"
        '
        'ToolStripStatusLabelTotalUSD
        '
        Me.ToolStripStatusLabelTotalUSD.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left
        Me.ToolStripStatusLabelTotalUSD.Name = "ToolStripStatusLabelTotalUSD"
        Me.ToolStripStatusLabelTotalUSD.Size = New System.Drawing.Size(78, 19)
        Me.ToolStripStatusLabelTotalUSD.Text = "الإجمالي ($): 0"
        '
        'CustomersForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(1200, 600)
        Me.Controls.Add(Me.DataGridViewCustomers)
        Me.Controls.Add(Me.StatusStripMain)
        Me.Controls.Add(Me.PanelSearch)
        Me.Controls.Add(Me.ToolStripMain)
        Me.Name = "CustomersForm"
        Me.RightToLeft = System.Windows.Forms.RightToLeft.Yes
        Me.RightToLeftLayout = True
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "إدارة العملاء"
        Me.ToolStripMain.ResumeLayout(False)
        Me.ToolStripMain.PerformLayout()
        Me.PanelSearch.ResumeLayout(False)
        Me.PanelSearch.PerformLayout()
        CType(Me.DataGridViewCustomers, System.ComponentModel.ISupportInitialize).EndInit()
        Me.StatusStripMain.ResumeLayout(False)
        Me.StatusStripMain.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents ToolStripMain As ToolStrip
    Friend WithEvents ToolStripButtonAdd As ToolStripButton
    Friend WithEvents ToolStripSeparator1 As ToolStripSeparator
    Friend WithEvents ToolStripButtonEdit As ToolStripButton
    Friend WithEvents ToolStripButtonDelete As ToolStripButton
    Friend WithEvents ToolStripSeparator2 As ToolStripSeparator
    Friend WithEvents ToolStripButtonRefresh As ToolStripButton
    Friend WithEvents ToolStripSeparator3 As ToolStripSeparator
    Friend WithEvents ToolStripButtonExport As ToolStripButton
    Friend WithEvents ToolStripButtonPrint As ToolStripButton
    Friend WithEvents PanelSearch As Panel
    Friend WithEvents LabelSearch As Label
    Friend WithEvents ComboBoxSearchType As ComboBox
    Friend WithEvents TextBoxSearch As TextBox
    Friend WithEvents ButtonSearch As Button
    Friend WithEvents ButtonClearSearch As Button
    Friend WithEvents LabelCustomerType As Label
    Friend WithEvents ComboBoxCustomerType As ComboBox
    Friend WithEvents DataGridViewCustomers As DataGridView
    Friend WithEvents StatusStripMain As StatusStrip
    Friend WithEvents ToolStripStatusLabelStatus As ToolStripStatusLabel
    Friend WithEvents ToolStripStatusLabelCount As ToolStripStatusLabel
    Friend WithEvents ToolStripStatusLabelTotalIQD As ToolStripStatusLabel
    Friend WithEvents ToolStripStatusLabelTotalUSD As ToolStripStatusLabel
End Class
