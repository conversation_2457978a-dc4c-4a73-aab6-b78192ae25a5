<?php
/**
 * فحص النظام والتأكد من عمل جميع المكونات
 * System Check - Verify all components are working
 */

// تعطيل عرض الأخطاء مؤقتاً لفحص نظيف
error_reporting(0);
ini_set('display_errors', 0);

$checks = [];
$overallStatus = true;

// فحص PHP
$checks['php'] = [
    'name' => 'إصدار PHP',
    'status' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'message' => 'PHP ' . PHP_VERSION,
    'required' => true
];

// فحص الملفات المطلوبة
$requiredFiles = [
    '../config/database.php' => 'ملف قاعدة البيانات',
    '../config/config.php' => 'ملف الإعدادات',
    '../classes/Auth.php' => 'فئة المصادقة',
    '../classes/Helper.php' => 'فئة المساعدة',
    'login.php' => 'صفحة تسجيل الدخول',
    'dashboard.php' => 'لوحة التحكم'
];

foreach ($requiredFiles as $file => $name) {
    $checks['file_' . basename($file)] = [
        'name' => $name,
        'status' => file_exists($file),
        'message' => $file,
        'required' => true
    ];
}

// فحص الامتدادات المطلوبة
$requiredExtensions = [
    'mysqli' => 'MySQL',
    'pdo' => 'PDO',
    'json' => 'JSON',
    'mbstring' => 'Multibyte String',
    'openssl' => 'OpenSSL',
    'curl' => 'cURL'
];

foreach ($requiredExtensions as $ext => $name) {
    $checks['ext_' . $ext] = [
        'name' => "امتداد $name",
        'status' => extension_loaded($ext),
        'message' => $ext,
        'required' => true
    ];
}

// فحص الأذونات
$writableDirs = [
    '../logs' => 'مجلد السجلات',
    '../backups' => 'مجلد النسخ الاحتياطية',
    '../uploads' => 'مجلد الرفع'
];

foreach ($writableDirs as $dir => $name) {
    if (!is_dir($dir)) {
        @mkdir($dir, 0755, true);
    }
    
    $checks['perm_' . basename($dir)] = [
        'name' => "أذونات $name",
        'status' => is_writable($dir),
        'message' => $dir,
        'required' => false
    ];
}

// فحص قاعدة البيانات
try {
    require_once '../config/database.php';
    $db = new Database();
    $connection = $db->getConnection();
    
    $checks['database'] = [
        'name' => 'اتصال قاعدة البيانات',
        'status' => $connection !== null,
        'message' => 'MySQL متصل',
        'required' => true
    ];
    
    // فحص الجداول المطلوبة
    $requiredTables = ['users', 'settings'];
    foreach ($requiredTables as $table) {
        try {
            $result = $db->fetchOne("SHOW TABLES LIKE '$table'");
            $checks['table_' . $table] = [
                'name' => "جدول $table",
                'status' => !empty($result),
                'message' => $table,
                'required' => false
            ];
        } catch (Exception $e) {
            $checks['table_' . $table] = [
                'name' => "جدول $table",
                'status' => false,
                'message' => 'غير موجود',
                'required' => false
            ];
        }
    }
    
} catch (Exception $e) {
    $checks['database'] = [
        'name' => 'اتصال قاعدة البيانات',
        'status' => false,
        'message' => $e->getMessage(),
        'required' => true
    ];
}

// فحص الفئات
try {
    require_once '../classes/Helper.php';
    $checks['helper_class'] = [
        'name' => 'فئة Helper',
        'status' => class_exists('Helper'),
        'message' => 'محملة بنجاح',
        'required' => true
    ];
} catch (Exception $e) {
    $checks['helper_class'] = [
        'name' => 'فئة Helper',
        'status' => false,
        'message' => $e->getMessage(),
        'required' => true
    ];
}

try {
    require_once '../classes/Auth.php';
    $checks['auth_class'] = [
        'name' => 'فئة Auth',
        'status' => class_exists('Auth'),
        'message' => 'محملة بنجاح',
        'required' => true
    ];
} catch (Exception $e) {
    $checks['auth_class'] = [
        'name' => 'فئة Auth',
        'status' => false,
        'message' => $e->getMessage(),
        'required' => true
    ];
}

// حساب الحالة العامة
foreach ($checks as $check) {
    if ($check['required'] && !$check['status']) {
        $overallStatus = false;
        break;
    }
}

// إعادة تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص النظام - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .check-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .status-card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-success { border-right: 5px solid #28a745; }
        .status-danger { border-right: 5px solid #dc3545; }
        .status-warning { border-right: 5px solid #ffc107; }
        .check-item {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .check-item:last-child { border-bottom: none; }
        .check-status {
            font-size: 1.2rem;
        }
        .overall-status {
            font-size: 1.5rem;
            font-weight: bold;
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }
        .btn-action {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container check-container">
        <div class="text-center mb-4">
            <h1><i class="fas fa-cogs me-3"></i>فحص النظام</h1>
            <p class="text-muted">التحقق من جميع مكونات نظام إدارة المعامل</p>
        </div>

        <!-- الحالة العامة -->
        <div class="overall-status <?= $overallStatus ? 'bg-success text-white' : 'bg-danger text-white' ?>">
            <?php if ($overallStatus): ?>
                <i class="fas fa-check-circle fa-2x mb-3"></i><br>
                النظام جاهز للعمل!
            <?php else: ?>
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i><br>
                يوجد مشاكل تحتاج إلى إصلاح
            <?php endif; ?>
        </div>

        <!-- نتائج الفحص -->
        <div class="card status-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>نتائج الفحص التفصيلية</h5>
            </div>
            <div class="card-body p-0">
                <?php foreach ($checks as $key => $check): ?>
                    <div class="check-item">
                        <div>
                            <strong><?= htmlspecialchars($check['name']) ?></strong>
                            <?php if ($check['required']): ?>
                                <span class="badge bg-warning text-dark ms-2">مطلوب</span>
                            <?php endif; ?>
                            <br>
                            <small class="text-muted"><?= htmlspecialchars($check['message']) ?></small>
                        </div>
                        <div class="check-status">
                            <?php if ($check['status']): ?>
                                <i class="fas fa-check-circle text-success"></i>
                            <?php else: ?>
                                <i class="fas fa-times-circle text-danger"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- الإجراءات -->
        <div class="text-center">
            <a href="setup.php" class="btn btn-primary btn-action me-2">
                <i class="fas fa-cog me-2"></i>إعداد النظام
            </a>
            <a href="test.php" class="btn btn-info btn-action me-2">
                <i class="fas fa-vial me-2"></i>اختبار شامل
            </a>
            <a href="login.php" class="btn btn-success btn-action">
                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
            </a>
        </div>

        <!-- معلومات إضافية -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>إصدار PHP:</strong> <?= PHP_VERSION ?><br>
                        <strong>نظام التشغيل:</strong> <?= PHP_OS ?><br>
                        <strong>الخادم:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف' ?>
                    </div>
                    <div class="col-md-6">
                        <strong>الذاكرة المتاحة:</strong> <?= ini_get('memory_limit') ?><br>
                        <strong>حد الرفع:</strong> <?= ini_get('upload_max_filesize') ?><br>
                        <strong>وقت التنفيذ:</strong> <?= ini_get('max_execution_time') ?> ثانية
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
