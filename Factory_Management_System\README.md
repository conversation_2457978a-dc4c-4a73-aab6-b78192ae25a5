# 🏭 نظام إدارة المعامل المتكامل
## Factory Management System - PHP/MySQL

### 🌟 **نظرة عامة**

نظام إدارة معامل شامل ومتطور يوفر حلولاً متكاملة لإدارة جميع جوانب المعمل من الإنتاج إلى المبيعات والمالية.

---

## 🚀 **المميزات الرئيسية**

### 📦 **إدارة المخزون**
- ✅ تتبع شامل للأصناف والمواد الخام
- ✅ إدارة متعددة المخازن
- ✅ تنبيهات المخزون المنخفض
- ✅ حركات المخزون التفصيلية
- ✅ جرد دوري وتسويات

### 🏭 **إدارة الإنتاج**
- ✅ أوامر الإنتاج المتقدمة
- ✅ وصفات الإنتاج (Bill of Materials)
- ✅ تتبع تكلفة الإنتاج
- ✅ مراحل الإنتاج المختلفة
- ✅ تقارير الإنتاجية

### 💰 **إدارة المبيعات**
- ✅ فواتير مبيعات احترافية
- ✅ إدارة العملاء والحسابات
- ✅ نظام خصومات متقدم
- ✅ تتبع المدفوعات
- ✅ تقارير المبيعات التفصيلية

### 🚚 **إدارة المشتريات**
- ✅ فواتير مشتريات شاملة
- ✅ إدارة الموردين
- ✅ أوامر الشراء
- ✅ تتبع التسليم والاستلام
- ✅ تحليل أداء الموردين

### 💸 **إدارة المصروفات**
- ✅ تصنيف المصروفات حسب النوع
- ✅ ربط المصروفات بالأقسام
- ✅ نظام موافقات المصروفات
- ✅ إرفاق المستندات
- ✅ تقارير المصروفات التفصيلية

### 👥 **إدارة الموظفين**
- ✅ ملفات الموظفين الشاملة
- ✅ إدارة الرواتب والحوافز
- ✅ تتبع الحضور والانصراف
- ✅ الإجازات والخصومات
- ✅ تقارير الموارد البشرية

### 💱 **دعم العملات المتعددة**
- ✅ دعم الدينار العراقي والدولار واليورو
- ✅ أسعار صرف قابلة للتحديث
- ✅ تحويل تلقائي بين العملات
- ✅ تقارير بعملات متعددة

### 📈 **تحليل الأرباح**
- ✅ أرباح يومية وشهرية وسنوية
- ✅ تحليل الربحية حسب المنتج
- ✅ تحليل أداء العملاء
- ✅ مؤشرات الأداء المالي (KPIs)
- ✅ هوامش الربح التفصيلية

### 🔐 **نظام الصلاحيات**
- ✅ أدوار متعددة (مدير، محاسب، مخزن، إنتاج، مبيعات)
- ✅ صلاحيات مخصصة لكل مستخدم
- ✅ تسجيل دخول آمن
- ✅ تتبع نشاط المستخدمين

---

## 🛠️ **متطلبات النظام**

### **الخادم (Server)**
- **PHP:** 7.4 أو أحدث
- **MySQL:** 5.7 أو أحدث (أو MariaDB 10.2+)
- **Apache/Nginx:** مع mod_rewrite
- **الذاكرة:** 512MB RAM كحد أدنى
- **التخزين:** 1GB مساحة فارغة

### **المتصفح (Client)**
- **Chrome:** 80+ (مُوصى به)
- **Firefox:** 75+
- **Safari:** 13+
- **Edge:** 80+

---

## 📥 **التثبيت والإعداد**

### **1. تحميل الملفات**
```bash
# استنساخ المشروع
git clone https://github.com/your-repo/factory-management-system.git

# أو تحميل وفك الضغط
unzip Factory_Management_System.zip
```

### **2. إعداد قاعدة البيانات**
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE factory_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل والبيانات
mysql -u root -p factory_management < database/factory_management.sql
```

### **3. تكوين الاتصال**
```php
// تحرير ملف config/database.php
private $host = 'localhost';
private $db_name = 'factory_management';
private $username = 'your_username';
private $password = 'your_password';
```

### **4. إعداد الصلاحيات**
```bash
# إعطاء صلاحيات الكتابة للمجلدات
chmod 755 uploads/
chmod 755 reports/
chmod 755 backups/
```

### **5. تشغيل النظام**
```
http://localhost/Factory_Management_System/public/
```

---

## 🔑 **بيانات الدخول الافتراضية**

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|--------------|-------------|-----------|
| **مدير النظام** | `admin` | `admin123` | جميع الصلاحيات |
| **مدير** | `manager` | `manager123` | عرض جميع التقارير |
| **محاسب** | `accountant` | `acc123` | المالية والمبيعات |
| **مخزن** | `warehouse` | `wh123` | إدارة المخزون |
| **إنتاج** | `production` | `prod123` | إدارة الإنتاج |
| **مبيعات** | `sales` | `sales123` | المبيعات والعملاء |

---

## 📊 **هيكل قاعدة البيانات**

### **الجداول الرئيسية:**

#### **👤 إدارة المستخدمين**
- `users` - المستخدمين والصلاحيات
- `employees` - بيانات الموظفين

#### **💱 العملات والمالية**
- `currencies` - العملات وأسعار الصرف
- `cash_boxes` - الصناديق والحسابات
- `cash_transactions` - حركات الصندوق

#### **📦 المخزون**
- `items` - الأصناف والمنتجات
- `item_categories` - فئات الأصناف
- `warehouses` - المخازن
- `item_stock` - أرصدة المخزون
- `stock_movements` - حركات المخزون

#### **🏭 الإنتاج**
- `production_orders` - أوامر الإنتاج
- `production_recipes` - وصفات الإنتاج
- `production_order_details` - تفاصيل أوامر الإنتاج

#### **💰 المبيعات والمشتريات**
- `customers` - العملاء
- `suppliers` - الموردين
- `sales_invoices` - فواتير المبيعات
- `sales_invoice_details` - تفاصيل فواتير المبيعات
- `purchase_invoices` - فواتير المشتريات
- `purchase_invoice_details` - تفاصيل فواتير المشتريات

#### **💸 المصروفات**
- `expenses` - المصروفات والنفقات

---

## 🎯 **الاستخدام السريع**

### **1. البدء**
1. سجل دخول بحساب المدير
2. أضف العملات المطلوبة
3. أنشئ المخازن الأساسية
4. أضف فئات الأصناف

### **2. إعداد الأصناف**
1. اذهب إلى "إدارة المخزون"
2. أضف الأصناف مع تحديد النوع (مادة خام/منتج نهائي)
3. حدد مستويات المخزون (حد أدنى/أعلى)

### **3. إنشاء وصفة إنتاج**
1. اذهب إلى "الإنتاج" > "وصفات الإنتاج"
2. اختر المنتج النهائي
3. أضف المواد الخام المطلوبة والكميات

### **4. إنشاء أمر إنتاج**
1. اذهب إلى "الإنتاج" > "أوامر الإنتاج"
2. اختر المنتج والكمية المطلوبة
3. ابدأ الإنتاج عند توفر المواد

### **5. إنشاء فاتورة مبيعات**
1. اذهب إلى "المبيعات"
2. أضف عميل جديد أو اختر موجود
3. أضف الأصناف والكميات
4. احفظ وأكد الفاتورة

---

## 📈 **التقارير المتاحة**

### **📊 التقارير المالية**
- تقرير الأرباح اليومية/الشهرية/السنوية
- تحليل الربحية حسب المنتج
- تحليل أداء العملاء
- تقرير التدفق النقدي
- مؤشرات الأداء المالي

### **📦 تقارير المخزون**
- تقرير أرصدة المخزون
- حركات المخزون التفصيلية
- الأصناف تحت الحد الأدنى
- تقرير الجرد

### **🏭 تقارير الإنتاج**
- تقرير أوامر الإنتاج
- تحليل تكلفة الإنتاج
- تقرير الإنتاجية
- استهلاك المواد الخام

### **💰 تقارير المبيعات والمشتريات**
- تقرير المبيعات التفصيلي
- تحليل أداء المبيعات
- تقرير المشتريات
- حسابات العملاء والموردين

---

## 🔧 **الصيانة والنسخ الاحتياطي**

### **النسخ الاحتياطي**
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u username -p factory_management > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz uploads/ reports/
```

### **التحديثات**
1. انسخ الملفات الجديدة
2. شغل سكريبت التحديث إن وجد
3. امسح ذاكرة التخزين المؤقت

---

## 🛡️ **الأمان**

### **إعدادات الأمان المُوصى بها**
- تغيير كلمات المرور الافتراضية
- تفعيل HTTPS
- تحديث PHP وMySQL بانتظام
- إعداد جدار حماية
- نسخ احتياطية دورية

### **صلاحيات الملفات**
```bash
# الملفات
find . -type f -exec chmod 644 {} \;

# المجلدات
find . -type d -exec chmod 755 {} \;

# ملفات خاصة
chmod 600 config/database.php
```

---

## 🆘 **الدعم الفني**

### **المشاكل الشائعة**

#### **خطأ في الاتصال بقاعدة البيانات**
```php
// تحقق من إعدادات database.php
// تأكد من تشغيل MySQL
// تحقق من صلاحيات المستخدم
```

#### **صفحة بيضاء**
```php
// فعل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);
```

#### **مشكلة في الصلاحيات**
```bash
# إعطاء صلاحيات للمجلدات
sudo chown -R www-data:www-data /path/to/project
sudo chmod -R 755 /path/to/project
```

### **طلب المساعدة**
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +964-XXX-XXXX
- 💬 الدردشة المباشرة: متاحة 24/7

---

## 📝 **الترخيص**

هذا النظام مرخص تحت رخصة MIT. يمكنك استخدامه وتعديله بحرية.

---

## 🎉 **شكر خاص**

شكراً لجميع المطورين والمساهمين في هذا المشروع. نتطلع لمساهماتكم وتطوير النظام أكثر.

---

**🚀 نتمنى لك تجربة ممتازة مع نظام إدارة المعامل!**
