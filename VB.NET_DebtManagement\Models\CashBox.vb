Imports System.ComponentModel.DataAnnotations
Imports System.ComponentModel.DataAnnotations.Schema

''' <summary>
''' نموذج الصندوق - CashBox Model
''' </summary>
<Table("CashBoxes")>
Public Class CashBox
    
#Region "Properties"
    
    ''' <summary>
    ''' معرف الصندوق الفريد
    ''' </summary>
    <Key>
    <DatabaseGenerated(DatabaseGeneratedOption.Identity)>
    Public Property Id As Integer
    
    ''' <summary>
    ''' اسم الصندوق
    ''' </summary>
    <Required(ErrorMessage:="اسم الصندوق مطلوب")>
    <StringLength(100, ErrorMessage:="اسم الصندوق يجب أن يكون أقل من 100 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Name As String
    
    ''' <summary>
    ''' وصف الصندوق
    ''' </summary>
    <StringLength(200, ErrorMessage:="وصف الصندوق يجب أن يكون أقل من 200 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property Description As String
    
    ''' <summary>
    ''' معرف العملة
    ''' </summary>
    <Required(ErrorMessage:="عملة الصندوق مطلوبة")>
    Public Property CurrencyId As Integer
    
    ''' <summary>
    ''' الرصيد الافتتاحي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property InitialBalance As Decimal = 0
    
    ''' <summary>
    ''' الرصيد الحالي
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property CurrentBalance As Decimal = 0
    
    ''' <summary>
    ''' الحد الأدنى للرصيد
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property MinimumBalance As Decimal = 0
    
    ''' <summary>
    ''' الحد الأقصى للرصيد
    ''' </summary>
    <Column(TypeName:="DECIMAL(18,2)")>
    Public Property MaximumBalance As Decimal?
    
    ''' <summary>
    ''' نوع الصندوق
    ''' </summary>
    <StringLength(50, ErrorMessage:="نوع الصندوق يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property CashBoxType As String = "Cash" ' Cash, Bank, Digital
    
    ''' <summary>
    ''' رقم الحساب (للبنوك)
    ''' </summary>
    <StringLength(50, ErrorMessage:="رقم الحساب يجب أن يكون أقل من 50 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property AccountNumber As String
    
    ''' <summary>
    ''' اسم البنك
    ''' </summary>
    <StringLength(100, ErrorMessage:="اسم البنك يجب أن يكون أقل من 100 حرف")>
    <Column(TypeName:="NVARCHAR")>
    Public Property BankName As String
    
    ''' <summary>
    ''' حالة الصندوق (نشط/غير نشط)
    ''' </summary>
    Public Property IsActive As Boolean = True
    
    ''' <summary>
    ''' تاريخ إنشاء السجل
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property CreatedAt As DateTime = DateTime.Now
    
    ''' <summary>
    ''' تاريخ آخر تحديث
    ''' </summary>
    <Column(TypeName:="DATETIME2")>
    Public Property UpdatedAt As DateTime?
    
    ''' <summary>
    ''' معرف المستخدم الذي أنشأ السجل
    ''' </summary>
    Public Property CreatedBy As Integer?
    
#End Region

#Region "Navigation Properties"
    
    ''' <summary>
    ''' العملة المرتبطة بالصندوق
    ''' </summary>
    <ForeignKey("CurrencyId")>
    Public Overridable Property Currency As Currency
    
    ''' <summary>
    ''' المعاملات المرتبطة بالصندوق
    ''' </summary>
    <InverseProperty("CashBox")>
    Public Overridable Property Transactions As ICollection(Of Transaction) = New HashSet(Of Transaction)
    
    ''' <summary>
    ''' المستخدم الذي أنشأ السجل
    ''' </summary>
    <ForeignKey("CreatedBy")>
    Public Overridable Property Creator As User
    
#End Region

#Region "Computed Properties"
    
    ''' <summary>
    ''' نوع الصندوق بالعربية (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property CashBoxTypeInArabic As String
        Get
            Select Case CashBoxType.ToLower()
                Case "cash"
                    Return "نقدي"
                Case "bank"
                    Return "بنكي"
                Case "digital"
                    Return "رقمي"
                Case Else
                    Return CashBoxType
            End Select
        End Get
    End Property
    
    ''' <summary>
    ''' حالة الرصيد (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property BalanceStatus As String
        Get
            If CurrentBalance > 0 Then
                Return "موجب"
            ElseIf CurrentBalance < 0 Then
                Return "سالب"
            Else
                Return "صفر"
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' نسبة الرصيد الحالي إلى الحد الأقصى (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property BalancePercentage As Decimal
        Get
            If MaximumBalance.HasValue AndAlso MaximumBalance.Value > 0 Then
                Return (CurrentBalance / MaximumBalance.Value) * 100
            Else
                Return 0
            End If
        End Get
    End Property
    
    ''' <summary>
    ''' هل الرصيد أقل من الحد الأدنى (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property IsBelowMinimum As Boolean
        Get
            Return CurrentBalance < MinimumBalance
        End Get
    End Property
    
    ''' <summary>
    ''' هل الرصيد أكبر من الحد الأقصى (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property IsAboveMaximum As Boolean
        Get
            Return MaximumBalance.HasValue AndAlso CurrentBalance > MaximumBalance.Value
        End Get
    End Property
    
    ''' <summary>
    ''' عدد المعاملات (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property TransactionCount As Integer
        Get
            Return If(Transactions?.Count, 0)
        End Get
    End Property
    
    ''' <summary>
    ''' آخر معاملة (محسوب)
    ''' </summary>
    <NotMapped>
    Public ReadOnly Property LastTransactionDate As DateTime?
        Get
            Return Transactions?.OrderByDescending(Function(t) t.CreatedAt).FirstOrDefault()?.TransactionDate
        End Get
    End Property
    
#End Region

#Region "Methods"
    
    ''' <summary>
    ''' تحديث الرصيد الحالي
    ''' </summary>
    ''' <param name="amount">المبلغ</param>
    ''' <param name="isIncome">هل هو وارد أم صادر</param>
    Public Sub UpdateBalance(amount As Decimal, isIncome As Boolean)
        If isIncome Then
            CurrentBalance += amount
        Else
            CurrentBalance -= amount
        End If
        
        UpdatedAt = DateTime.Now
    End Sub
    
    ''' <summary>
    ''' تنسيق الرصيد للعرض
    ''' </summary>
    ''' <returns>الرصيد منسق</returns>
    Public Function GetFormattedBalance() As String
        If Currency IsNot Nothing Then
            Return String.Format("{0} {1}", CurrentBalance.ToString("N2"), Currency.Symbol)
        Else
            Return CurrentBalance.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' تنسيق الرصيد الافتتاحي للعرض
    ''' </summary>
    ''' <returns>الرصيد الافتتاحي منسق</returns>
    Public Function GetFormattedInitialBalance() As String
        If Currency IsNot Nothing Then
            Return String.Format("{0} {1}", InitialBalance.ToString("N2"), Currency.Symbol)
        Else
            Return InitialBalance.ToString("N2")
        End If
    End Function
    
    ''' <summary>
    ''' حساب إجمالي الوارد
    ''' </summary>
    ''' <returns>إجمالي الوارد</returns>
    Public Function GetTotalIncome() As Decimal
        Return Transactions?.Where(Function(t) t.Type = "Income").Sum(Function(t) t.Amount) ?? 0
    End Function
    
    ''' <summary>
    ''' حساب إجمالي الصادر
    ''' </summary>
    ''' <returns>إجمالي الصادر</returns>
    Public Function GetTotalExpense() As Decimal
        Return Transactions?.Where(Function(t) t.Type = "Expense").Sum(Function(t) t.Amount) ?? 0
    End Function
    
    ''' <summary>
    ''' التحقق من إمكانية السحب
    ''' </summary>
    ''' <param name="amount">المبلغ المراد سحبه</param>
    ''' <returns>صحيح إذا كان السحب ممكن</returns>
    Public Function CanWithdraw(amount As Decimal) As Boolean
        Return (CurrentBalance - amount) >= MinimumBalance
    End Function
    
    ''' <summary>
    ''' التحقق من إمكانية الإيداع
    ''' </summary>
    ''' <param name="amount">المبلغ المراد إيداعه</param>
    ''' <returns>صحيح إذا كان الإيداع ممكن</returns>
    Public Function CanDeposit(amount As Decimal) As Boolean
        If MaximumBalance.HasValue Then
            Return (CurrentBalance + amount) <= MaximumBalance.Value
        Else
            Return True
        End If
    End Function
    
    ''' <summary>
    ''' التحقق من صحة البيانات
    ''' </summary>
    ''' <returns>قائمة بالأخطاء</returns>
    Public Function Validate() As List(Of String)
        Dim errors As New List(Of String)
        
        If String.IsNullOrWhiteSpace(Name) Then
            errors.Add("اسم الصندوق مطلوب")
        End If
        
        If CurrencyId <= 0 Then
            errors.Add("عملة الصندوق مطلوبة")
        End If
        
        If MinimumBalance < 0 Then
            errors.Add("الحد الأدنى للرصيد لا يمكن أن يكون سالب")
        End If
        
        If MaximumBalance.HasValue AndAlso MaximumBalance.Value < MinimumBalance Then
            errors.Add("الحد الأقصى للرصيد يجب أن يكون أكبر من الحد الأدنى")
        End If
        
        If Not String.IsNullOrWhiteSpace(CashBoxType) AndAlso Not {"Cash", "Bank", "Digital"}.Contains(CashBoxType) Then
            errors.Add("نوع الصندوق غير صحيح")
        End If
        
        Return errors
    End Function
    
    ''' <summary>
    ''' تمثيل نصي للصندوق
    ''' </summary>
    ''' <returns>اسم الصندوق</returns>
    Public Overrides Function ToString() As String
        Return Name
    End Function
    
#End Region

End Class
