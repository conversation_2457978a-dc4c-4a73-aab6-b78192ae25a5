<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$user = $auth->getCurrentUser();
if ($user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بهذا الإجراء']);
    exit;
}

$db = new Database();

// قراءة البيانات من JSON
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['expense_id']) || !isset($input['expense_type'])) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
    exit;
}

try {
    $expenseId = (int)$input['expense_id'];
    $expenseType = $input['expense_type'];
    
    $updateData = [
        'status' => 'approved',
        'approved_by' => $user['id'],
        'approved_at' => date('Y-m-d H:i:s')
    ];
    
    if ($expenseType == 'general') {
        $db->update('general_expenses', $updateData, 'id = ?', [$expenseId]);
    } elseif ($expenseType == 'production') {
        $db->update('production_expenses', $updateData, 'id = ?', [$expenseId]);
    } else {
        throw new Exception('نوع مصروف غير صحيح');
    }
    
    echo json_encode(['success' => true, 'message' => 'تم اعتماد المصروف بنجاح']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
