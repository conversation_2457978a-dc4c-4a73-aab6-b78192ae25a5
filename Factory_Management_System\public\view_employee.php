<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$employeeId = (int)($_GET['id'] ?? 0);
if (!$employeeId) {
    header('Location: employees.php');
    exit;
}

// جلب بيانات الموظف
try {
    $employee = $db->fetchOne("
        SELECT e.*, d.name as department_name 
        FROM employees e 
        LEFT JOIN departments d ON e.department_id = d.id 
        WHERE e.id = ?
    ", [$employeeId]);
    
    if (!$employee) {
        header('Location: employees.php');
        exit;
    }
} catch (Exception $e) {
    $error = "خطأ في جلب بيانات الموظف: " . $e->getMessage();
    $employee = null;
}

// حساب مدة الخدمة
$serviceYears = 0;
$serviceMonths = 0;
if ($employee['hire_date']) {
    $hireDate = new DateTime($employee['hire_date']);
    $currentDate = new DateTime();
    $interval = $hireDate->diff($currentDate);
    $serviceYears = $interval->y;
    $serviceMonths = $interval->m;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض بيانات الموظف - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .employee-photo {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #17a2b8;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .photo-placeholder {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 5px solid #17a2b8;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .info-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        .quick-action {
            background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 10px 15px;
            text-decoration: none;
            display: block;
            text-align: center;
            margin-bottom: 10px;
            transition: transform 0.3s ease;
        }
        .quick-action:hover {
            transform: translateY(-2px);
            color: white;
        }
    </style>
</head>
<body>
    <?php if (!$employee): ?>
        <div class="container mt-5">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                الموظف غير موجود أو حدث خطأ في جلب البيانات
                <br><a href="employees.php" class="btn btn-sm btn-primary mt-2">العودة إلى قائمة الموظفين</a>
            </div>
        </div>
    <?php else: ?>
        <div class="main-header">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1><i class="fas fa-user me-3"></i><?= htmlspecialchars($employee['full_name']) ?></h1>
                        <p class="mb-0"><?= htmlspecialchars($employee['position']) ?> - <?= htmlspecialchars($employee['employee_code']) ?></p>
                    </div>
                    <div class="col-md-6 text-end">
                        <a href="edit_employee.php?id=<?= $employee['id'] ?>" class="btn btn-light me-2">
                            <i class="fas fa-edit me-2"></i>تعديل
                        </a>
                        <a href="employees.php" class="btn btn-outline-light">
                            <i class="fas fa-users me-2"></i>قائمة الموظفين
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                <!-- صورة الموظف والمعلومات الأساسية -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <?php if ($employee['photo'] && file_exists($employee['photo'])): ?>
                                <img src="<?= htmlspecialchars($employee['photo']) ?>" class="employee-photo mb-3" alt="صورة الموظف">
                            <?php else: ?>
                                <div class="photo-placeholder mb-3 mx-auto">
                                    <i class="fas fa-user fa-4x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <h4><?= htmlspecialchars($employee['full_name']) ?></h4>
                            <p class="text-muted"><?= htmlspecialchars($employee['position']) ?></p>
                            
                            <?php
                            $statusNames = [
                                'active' => 'نشط',
                                'inactive' => 'غير نشط',
                                'terminated' => 'منتهي الخدمة'
                            ];
                            $statusColors = [
                                'active' => 'success',
                                'inactive' => 'warning',
                                'terminated' => 'danger'
                            ];
                            ?>
                            <span class="badge bg-<?= $statusColors[$employee['status']] ?> fs-6 mb-3">
                                <?= $statusNames[$employee['status']] ?>
                            </span>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="stats-card">
                        <h5>مدة الخدمة</h5>
                        <h3><?= $serviceYears ?> سنة و <?= $serviceMonths ?> شهر</h3>
                    </div>

                    <div class="stats-card">
                        <h5>الراتب الأساسي</h5>
                        <h3><?= number_format($employee['salary'], 0) ?> د.ع</h3>
                    </div>

                    <!-- إجراءات سريعة -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-tools me-2"></i>إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <a href="edit_employee.php?id=<?= $employee['id'] ?>" class="quick-action">
                                <i class="fas fa-edit me-2"></i>تعديل البيانات
                            </a>
                            <a href="employee_salary.php?id=<?= $employee['id'] ?>" class="quick-action">
                                <i class="fas fa-money-bill me-2"></i>إدارة الراتب
                            </a>
                            <a href="employee_attendance.php?id=<?= $employee['id'] ?>" class="quick-action">
                                <i class="fas fa-clock me-2"></i>سجل الحضور
                            </a>
                            <a href="employee_leaves.php?id=<?= $employee['id'] ?>" class="quick-action">
                                <i class="fas fa-calendar-times me-2"></i>الإجازات
                            </a>
                            <a href="employee_documents.php?id=<?= $employee['id'] ?>" class="quick-action">
                                <i class="fas fa-file me-2"></i>المستندات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الموظف -->
                <div class="col-lg-8">
                    <!-- المعلومات الشخصية -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>المعلومات الشخصية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>كود الموظف:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['employee_code']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الاسم الكامل:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['full_name']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>البريد الإلكتروني:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['email'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>رقم الهاتف:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['phone'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>رقم الهوية:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['national_id'] ?: 'غير محدد') ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>تاريخ الميلاد:</strong>
                                        <span class="float-end"><?= $employee['birth_date'] ? date('d/m/Y', strtotime($employee['birth_date'])) : 'غير محدد' ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>العنوان:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['address'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>جهة الاتصال في الطوارئ:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['emergency_contact'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>رقم الحساب البنكي:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['bank_account'] ?: 'غير محدد') ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات العمل -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>معلومات العمل</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>المنصب:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['position']) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>القسم:</strong>
                                        <span class="float-end"><?= htmlspecialchars($employee['department_name'] ?: 'غير محدد') ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>تاريخ التوظيف:</strong>
                                        <span class="float-end"><?= date('d/m/Y', strtotime($employee['hire_date'])) ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الحالة:</strong>
                                        <span class="float-end">
                                            <span class="badge bg-<?= $statusColors[$employee['status']] ?>">
                                                <?= $statusNames[$employee['status']] ?>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>الراتب الأساسي:</strong>
                                        <span class="float-end"><?= number_format($employee['salary'], 2) ?> د.ع</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>أجر الساعة:</strong>
                                        <span class="float-end"><?= number_format($employee['hourly_rate'], 2) ?> د.ع</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>أجر الساعة الإضافية:</strong>
                                        <span class="float-end"><?= number_format($employee['overtime_rate'], 2) ?> د.ع</span>
                                    </div>
                                    <div class="info-item">
                                        <strong>مدة الخدمة:</strong>
                                        <span class="float-end"><?= $serviceYears ?> سنة و <?= $serviceMonths ?> شهر</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <?php if ($employee['notes']): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-sticky-note me-2"></i>ملاحظات</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0"><?= nl2br(htmlspecialchars($employee['notes'])) ?></p>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- معلومات النظام -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>تاريخ الإضافة:</strong>
                                        <span class="float-end"><?= date('d/m/Y H:i', strtotime($employee['created_at'])) ?></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <strong>آخر تحديث:</strong>
                                        <span class="float-end"><?= date('d/m/Y H:i', strtotime($employee['updated_at'])) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
