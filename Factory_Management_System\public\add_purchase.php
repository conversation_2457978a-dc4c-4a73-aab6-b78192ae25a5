<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Auth.php';

// التحقق من تسجيل الدخول
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = $auth->getCurrentUser();
$db = new Database();

$success = '';
$error = '';

// معالجة إضافة فاتورة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $db->beginTransaction();
        
        // توليد رقم الفاتورة
        $prefix = 'PUR';
        $year = date('Y');
        $countResult = $db->fetchOne("SELECT COUNT(*) as count FROM purchase_invoices WHERE YEAR(created_at) = ?", [$year]);
        $sequence = ($countResult['count'] ?? 0) + 1;
        $invoiceNumber = $prefix . $year . str_pad($sequence, 4, '0', STR_PAD_LEFT);
        
        // بيانات الفاتورة
        $invoiceData = [
            'invoice_number' => $invoiceNumber,
            'supplier_id' => (int)$_POST['supplier_id'],
            'invoice_date' => $_POST['invoice_date'],
            'due_date' => $_POST['due_date'] ?? null,
            'currency_id' => (int)$_POST['currency_id'],
            'exchange_rate' => (float)($_POST['exchange_rate'] ?? 1.0000),
            'discount_percentage' => (float)($_POST['discount_percentage'] ?? 0),
            'tax_percentage' => (float)($_POST['tax_percentage'] ?? 0),
            'notes' => $_POST['notes'] ?? '',
            'created_by' => $user['id']
        ];
        
        // إدراج الفاتورة
        $invoiceId = $db->insert('purchase_invoices', $invoiceData);
        
        // معالجة أصناف الفاتورة
        $subtotal = 0;
        $items = $_POST['items'] ?? [];
        
        foreach ($items as $item) {
            if (empty($item['item_id']) || empty($item['quantity']) || empty($item['unit_price'])) {
                continue;
            }
            
            $quantity = (float)$item['quantity'];
            $unitPrice = (float)$item['unit_price'];
            $discountPercentage = (float)($item['discount_percentage'] ?? 0);
            $discountAmount = ($quantity * $unitPrice * $discountPercentage) / 100;
            $lineTotal = ($quantity * $unitPrice) - $discountAmount;
            
            $itemData = [
                'invoice_id' => $invoiceId,
                'item_id' => (int)$item['item_id'],
                'warehouse_id' => (int)$item['warehouse_id'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'discount_percentage' => $discountPercentage,
                'discount_amount' => $discountAmount
            ];
            
            $db->insert('purchase_invoice_details', $itemData);
            $subtotal += $lineTotal;
            
            // تحديث رصيد المخزون
            $stockData = [
                'item_id' => (int)$item['item_id'],
                'warehouse_id' => (int)$item['warehouse_id'],
                'movement_type' => 'in',
                'quantity' => $quantity,
                'reference_type' => 'purchase',
                'reference_id' => $invoiceId,
                'created_by' => $user['id']
            ];
            $db->insert('stock_movements', $stockData);
            
            // تحديث رصيد المخزون الحالي
            $existingBalance = $db->fetchOne("SELECT * FROM stock_balances WHERE item_id = ? AND warehouse_id = ?", 
                [(int)$item['item_id'], (int)$item['warehouse_id']]);
            
            if ($existingBalance) {
                $db->query("UPDATE stock_balances SET quantity = quantity + ? WHERE item_id = ? AND warehouse_id = ?", 
                    [$quantity, (int)$item['item_id'], (int)$item['warehouse_id']]);
            } else {
                $balanceData = [
                    'item_id' => (int)$item['item_id'],
                    'warehouse_id' => (int)$item['warehouse_id'],
                    'quantity' => $quantity
                ];
                $db->insert('stock_balances', $balanceData);
            }
        }
        
        // حساب المبالغ النهائية
        $discountAmount = ($subtotal * $invoiceData['discount_percentage']) / 100;
        $afterDiscount = $subtotal - $discountAmount;
        $taxAmount = ($afterDiscount * $invoiceData['tax_percentage']) / 100;
        $totalAmount = $afterDiscount + $taxAmount;
        
        // تحديث مبالغ الفاتورة
        $updateData = [
            'subtotal' => $subtotal,
            'discount_amount' => $discountAmount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'remaining_amount' => $totalAmount
        ];
        
        $db->update('purchase_invoices', $updateData, 'id = ?', [$invoiceId]);
        
        $db->commit();
        $success = "تم إنشاء فاتورة المشتريات بنجاح برقم: $invoiceNumber";
        
    } catch (Exception $e) {
        $db->rollback();
        $error = "خطأ في إنشاء الفاتورة: " . $e->getMessage();
    }
}

// جلب البيانات المطلوبة
try {
    $suppliers = $db->fetchAll("SELECT id, code, name, phone FROM suppliers WHERE is_active = 1 ORDER BY name");
    $currencies = $db->fetchAll("SELECT id, code, name, symbol, exchange_rate FROM currencies WHERE is_active = 1 ORDER BY is_base DESC, name");
    $items = $db->fetchAll("SELECT id, code, name, unit, cost_price FROM items WHERE is_active = 1 ORDER BY name");
    $warehouses = $db->fetchAll("SELECT id, name, location FROM warehouses WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    $suppliers = [];
    $currencies = [];
    $items = [];
    $warehouses = [];
    $error = "خطأ في جلب البيانات: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة مشتريات جديدة - نظام إدارة المعامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }
        .main-header {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 2rem;
        }
        .card-header {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 1.5rem;
        }
        .btn-primary {
            background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .item-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-plus-circle me-3"></i>فاتورة مشتريات جديدة</h1>
                    <p class="mb-0">إضافة فاتورة مشتريات جديدة</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="purchases.php" class="btn btn-light">
                        <i class="fas fa-list me-2"></i>قائمة المشتريات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <?= htmlspecialchars($success) ?>
                <div class="mt-2">
                    <a href="purchases.php" class="btn btn-sm btn-primary">عرض الفواتير</a>
                    <a href="add_purchase.php" class="btn btn-sm btn-outline-primary">فاتورة جديدة</a>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?= htmlspecialchars($error) ?>
                <?php if (strpos($error, 'Table') !== false): ?>
                    <br><a href="final_setup.php" class="btn btn-sm btn-primary mt-2">إعداد النظام</a>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <form method="POST" id="purchaseForm">
            <div class="row">
                <!-- معلومات الفاتورة -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات الفاتورة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المورد <span class="text-danger">*</span></label>
                                    <select name="supplier_id" class="form-select" required>
                                        <option value="">اختر المورد</option>
                                        <?php foreach ($suppliers as $supplier): ?>
                                            <option value="<?= $supplier['id'] ?>">
                                                <?= htmlspecialchars($supplier['name']) ?>
                                                <?php if ($supplier['phone']): ?>
                                                    - <?= htmlspecialchars($supplier['phone']) ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الفاتورة <span class="text-danger">*</span></label>
                                    <input type="date" name="invoice_date" class="form-control" 
                                           value="<?= date('Y-m-d') ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الاستحقاق</label>
                                    <input type="date" name="due_date" class="form-control">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">العملة</label>
                                    <select name="currency_id" class="form-select">
                                        <?php foreach ($currencies as $currency): ?>
                                            <option value="<?= $currency['id'] ?>" 
                                                    data-rate="<?= $currency['exchange_rate'] ?>"
                                                    <?= $currency['symbol'] == 'د.ع' ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($currency['name'] . ' (' . $currency['symbol'] . ')') ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">سعر الصرف</label>
                                    <input type="number" name="exchange_rate" class="form-control" 
                                           step="0.0001" value="1.0000">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" name="discount_percentage" class="form-control" 
                                           step="0.01" min="0" max="100" value="0">
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label class="form-label">ملاحظات</label>
                                    <textarea name="notes" class="form-control" rows="3" 
                                              placeholder="ملاحظات إضافية"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ملخص الفاتورة -->
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-calculator me-2"></i>ملخص الفاتورة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-primary" id="subtotal">0.00</h4>
                                        <p class="mb-0">المجموع الفرعي</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="text-center p-3 bg-light rounded">
                                        <h4 class="text-success" id="total">0.00</h4>
                                        <p class="mb-0">المجموع الكلي</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <div class="d-flex justify-content-between">
                                    <span>الخصم:</span>
                                    <span id="discount">0.00</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>الضريبة:</span>
                                    <span id="tax">0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أصناف الفاتورة -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-boxes me-2"></i>أصناف الفاتورة</h5>
                        <button type="button" class="btn btn-success" onclick="addItem()">
                            <i class="fas fa-plus me-2"></i>إضافة صنف
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="itemsContainer">
                        <!-- سيتم إضافة الأصناف هنا -->
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" class="btn btn-primary btn-lg me-3">
                    <i class="fas fa-save me-2"></i>حفظ الفاتورة
                </button>
                <a href="purchases.php" class="btn btn-secondary btn-lg">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let itemCounter = 0;
        const items = <?= json_encode($items) ?>;
        const warehouses = <?= json_encode($warehouses) ?>;

        function addItem() {
            itemCounter++;
            const container = document.getElementById('itemsContainer');
            
            const itemHtml = `
                <div class="item-row" id="item-${itemCounter}">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">الصنف</label>
                            <select name="items[${itemCounter}][item_id]" class="form-select" required onchange="updatePrice(${itemCounter})">
                                <option value="">اختر الصنف</option>
                                ${items.map(item => `<option value="${item.id}" data-price="${item.cost_price}">${item.name} (${item.unit})</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">المخزن</label>
                            <select name="items[${itemCounter}][warehouse_id]" class="form-select" required>
                                <option value="">اختر المخزن</option>
                                ${warehouses.map(warehouse => `<option value="${warehouse.id}">${warehouse.name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">الكمية</label>
                            <input type="number" name="items[${itemCounter}][quantity]" class="form-control" 
                                   step="0.001" min="0.001" required onchange="calculateLine(${itemCounter})">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">سعر الوحدة</label>
                            <input type="number" name="items[${itemCounter}][unit_price]" class="form-control" 
                                   step="0.01" min="0.01" required onchange="calculateLine(${itemCounter})">
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">الخصم (%)</label>
                            <input type="number" name="items[${itemCounter}][discount_percentage]" class="form-control" 
                                   step="0.01" min="0" max="100" value="0" onchange="calculateLine(${itemCounter})">
                        </div>
                        <div class="col-md-1 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-danger w-100" onclick="removeItem(${itemCounter})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('beforeend', itemHtml);
        }

        function removeItem(id) {
            document.getElementById(`item-${id}`).remove();
            calculateTotal();
        }

        function updatePrice(id) {
            const select = document.querySelector(`select[name="items[${id}][item_id]"]`);
            const priceInput = document.querySelector(`input[name="items[${id}][unit_price]"]`);
            const selectedOption = select.options[select.selectedIndex];
            
            if (selectedOption.dataset.price) {
                priceInput.value = selectedOption.dataset.price;
                calculateLine(id);
            }
        }

        function calculateLine(id) {
            const quantity = parseFloat(document.querySelector(`input[name="items[${id}][quantity]"]`).value) || 0;
            const unitPrice = parseFloat(document.querySelector(`input[name="items[${id}][unit_price]"]`).value) || 0;
            const discountPercentage = parseFloat(document.querySelector(`input[name="items[${id}][discount_percentage]"]`).value) || 0;
            
            calculateTotal();
        }

        function calculateTotal() {
            let subtotal = 0;
            
            document.querySelectorAll('.item-row').forEach(row => {
                const quantity = parseFloat(row.querySelector('input[name*="[quantity]"]').value) || 0;
                const unitPrice = parseFloat(row.querySelector('input[name*="[unit_price]"]').value) || 0;
                const discountPercentage = parseFloat(row.querySelector('input[name*="[discount_percentage]"]').value) || 0;
                
                const lineTotal = quantity * unitPrice;
                const lineDiscount = (lineTotal * discountPercentage) / 100;
                subtotal += lineTotal - lineDiscount;
            });
            
            const discountPercentage = parseFloat(document.querySelector('input[name="discount_percentage"]').value) || 0;
            const taxPercentage = parseFloat(document.querySelector('input[name="tax_percentage"]').value) || 0;
            
            const discountAmount = (subtotal * discountPercentage) / 100;
            const afterDiscount = subtotal - discountAmount;
            const taxAmount = (afterDiscount * taxPercentage) / 100;
            const total = afterDiscount + taxAmount;
            
            document.getElementById('subtotal').textContent = subtotal.toFixed(2);
            document.getElementById('discount').textContent = discountAmount.toFixed(2);
            document.getElementById('tax').textContent = taxAmount.toFixed(2);
            document.getElementById('total').textContent = total.toFixed(2);
        }

        // إضافة صنف واحد افتراضياً
        addItem();
    </script>
</body>
</html>
